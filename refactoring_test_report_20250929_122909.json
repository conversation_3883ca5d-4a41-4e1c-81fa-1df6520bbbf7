{"test_timestamp": "2025-09-29T12:29:09.405032", "summary": {"total_tests": 5, "passed": 2, "failed": 2, "warnings": 1, "skipped": 0, "success_rate": 40.0}, "detailed_results": {"Unified Logging Structure": {"status": "FAIL", "details": "Missing directories: ['application', 'analysis/workflows', 'analysis/llm_interactions', 'progress/sessions', 'api', 'errors', 'archive']", "timestamp": "2025-09-29T12:29:05.305497"}, "Unified Logger Import": {"status": "PASS", "details": "Successfully imported and used unified logger", "timestamp": "2025-09-29T12:29:05.350189"}, "Legacy File Cleanup": {"status": "WARN", "details": "Legacy files still exist: ['Agent_Trading/test_data_transformation.py', 'Agent_Trading/test_frontend_fixes.py', 'Agent_Trading/test_progress_flow.py', 'Agent_Trading/test_result_debug.py', 'Agent_Trading/test_result_endpoint.py', 'Agent_Trading/test_result_simple.py']", "timestamp": "2025-09-29T12:29:05.352260"}, "Nested Directory Cleanup": {"status": "PASS", "details": "No nested duplicate directories found", "timestamp": "2025-09-29T12:29:05.353419"}, "Authentication": {"status": "FAIL", "details": "HTTPConnectionPool(host='localhost', port=8000): Max retries exceeded with url: /api/v1/auth/login (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002298C538D90>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))", "timestamp": "2025-09-29T12:29:09.401018"}}}