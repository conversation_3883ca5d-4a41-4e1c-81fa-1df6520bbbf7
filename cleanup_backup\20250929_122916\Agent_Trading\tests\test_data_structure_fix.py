#!/usr/bin/env python3
"""
Test script to verify and fix the data structure issues in the AI Trading Analysis app.
This script will:
1. Test the progress broadcasting fix
2. Test the data structure mapping
3. Verify both issues are resolved
"""

import asyncio
import requests
import json
import time
from datetime import datetime

# Test configuration
BASE_URL = "http://localhost:8000"
FRONTEND_URL = "http://localhost:3000"
EMAIL = "<EMAIL>"
PASSWORD = "Bunnych@1627"

async def test_progress_and_data_fix():
    """Test both progress broadcasting and data structure fixes."""
    
    print("🧪 TESTING PROGRESS & DATA STRUCTURE FIXES")
    print("=" * 60)
    
    # Step 1: Login
    print("1️⃣ Logging in...")
    login_response = requests.post(f"{BASE_URL}/api/v1/auth/login", json={
        "email": EMAIL,
        "password": PASSWORD
    })
    
    if login_response.status_code != 200:
        print(f"❌ Login failed: {login_response.status_code}")
        return
    
    token = login_response.json()["access_token"]
    headers = {"Authorization": f"Bearer {token}"}
    print("✅ Login successful")
    
    # Step 2: Start analysis
    print("\n2️⃣ Starting analysis...")
    
    # Use a real test image
    import base64
    with open("Agent_Trading/Testing_images/Screenshot 2025-08-03 190956.png", "rb") as f:
        test_image_b64 = base64.b64encode(f.read()).decode('utf-8')
    
    analysis_request = {
        "images_base64": [test_image_b64],
        "analysis_type": "Positional",
        "market_specialization": "Crypto",
        "preferred_model": "gemini-2.5-flash"
    }
    
    start_response = requests.post(
        f"{BASE_URL}/api/v1/trading/async/start",
        json=analysis_request,
        headers=headers
    )
    
    if start_response.status_code != 200:
        print(f"❌ Analysis start failed: {start_response.status_code}")
        print(start_response.text)
        return
    
    session_id = start_response.json()["session_id"]
    print(f"✅ Analysis started with session: {session_id}")
    
    # Step 3: Monitor progress
    print("\n3️⃣ Monitoring progress...")
    progress_updates = []
    
    for i in range(30):  # Monitor for up to 30 seconds
        try:
            result_response = requests.get(
                f"{BASE_URL}/api/v1/trading/async/result/{session_id}",
                headers=headers
            )
            
            if result_response.status_code == 200:
                result_data = result_response.json()
                
                if result_data.get("status") == "completed":
                    print(f"🎉 Analysis completed!")
                    
                    # Step 4: Analyze data structure
                    print("\n4️⃣ Analyzing data structure...")
                    print(f"📊 Response structure:")
                    print(f"   └─ success: {result_data.get('success')}")
                    print(f"   └─ status: {result_data.get('status')}")
                    print(f"   └─ data keys: {list(result_data.get('data', {}).keys())}")
                    
                    # Check nested data structure
                    data = result_data.get('data', {})
                    if 'data' in data:
                        nested_data = data['data']
                        print(f"   └─ data.data keys: {list(nested_data.keys()) if isinstance(nested_data, dict) else type(nested_data)}")
                        
                        # Check for detected_symbol and market_type
                        if isinstance(nested_data, dict):
                            detected_symbol = nested_data.get('detected_symbol')
                            market_type = nested_data.get('market_type')
                            print(f"   └─ detected_symbol: {detected_symbol}")
                            print(f"   └─ market_type: {market_type}")
                            
                            if detected_symbol and market_type:
                                print("✅ DATA STRUCTURE FIX: Symbol and market detected correctly!")
                            else:
                                print("❌ DATA STRUCTURE ISSUE: Symbol/market still missing")
                    
                    break
                elif result_data.get("status") == "processing":
                    print(f"⏳ Still processing... ({i+1}/30)")
                else:
                    print(f"❓ Status: {result_data.get('status')}")
            
            time.sleep(1)
            
        except Exception as e:
            print(f"❌ Error checking result: {e}")
            break
    
    # Step 5: Check progress logs
    print("\n5️⃣ Checking progress logs...")
    try:
        with open("Agent_Trading/backend/logs/progress_debug.log", "r") as f:
            recent_logs = f.readlines()[-10:]  # Last 10 lines
            
        if any("WORKFLOW_STEP" in line for line in recent_logs):
            print("✅ PROGRESS FIX: Progress updates are being logged!")
            for line in recent_logs:
                if "WORKFLOW_STEP" in line:
                    print(f"   📈 {line.strip()}")
        else:
            print("❌ PROGRESS ISSUE: No progress updates found in logs")
            
    except Exception as e:
        print(f"❌ Error reading progress logs: {e}")
    
    print("\n" + "=" * 60)
    print("🏁 TEST COMPLETED")

if __name__ == "__main__":
    asyncio.run(test_progress_and_data_fix())
