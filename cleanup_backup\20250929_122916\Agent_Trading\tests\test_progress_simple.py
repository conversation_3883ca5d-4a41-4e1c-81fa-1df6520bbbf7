#!/usr/bin/env python3
"""
Simple Progress Tracking Test
============================

Quick test to verify the progress tracking fixes work correctly.
"""

import requests
import json
import time
import base64

def test_progress_tracking():
    """Test the progress tracking system"""
    print("🚀 Testing Progress Tracking Fixes")
    print("="*50)
    
    # Step 1: Login
    print("1️⃣ Authenticating...")
    login_response = requests.post("http://localhost:8000/api/v1/auth/login", json={
        "email": "<EMAIL>",
        "password": "test123"
    })
    
    if login_response.status_code != 200:
        print("❌ Login failed")
        return False
    
    token = login_response.json()["access_token"]
    headers = {"Authorization": f"Bearer {token}"}
    print("✅ Login successful")
    
    # Step 2: Start analysis
    print("\n2️⃣ Starting analysis...")
    try:
        with open("Agent_Trading/Testing_images/Screenshot 2025-08-03 190956.png", "rb") as f:
            image_data = f.read()
            image_b64 = base64.b64encode(image_data).decode('utf-8')
        
        request_data = {
            "images_base64": [image_b64],
            "analysis_type": "Positional", 
            "market_specialization": "Crypto",
            "preferred_model": "gemini-2.5-flash"
        }
        
        response = requests.post(
            "http://localhost:8000/api/v1/trading/async/start",
            json=request_data,
            headers=headers
        )
        
        if response.status_code != 200:
            print(f"❌ Analysis start failed: {response.status_code}")
            return False
            
        session_id = response.json()["session_id"]
        print(f"✅ Analysis started with session: {session_id}")
        
    except Exception as e:
        print(f"❌ Analysis start error: {e}")
        return False
    
    # Step 3: Monitor progress (simplified)
    print(f"\n3️⃣ Monitoring progress for session: {session_id}")
    print("📊 Check the frontend at http://localhost:3000 to see progress updates")
    print("📊 Check backend/progress_debug.log for detailed progress logs")
    
    # Step 4: Wait and check results
    print("\n4️⃣ Waiting for completion...")
    for i in range(60):  # Wait up to 5 minutes
        try:
            result_response = requests.get(
                f"http://localhost:8000/api/v1/trading/async/result/{session_id}",
                headers=headers
            )
            
            if result_response.status_code == 200:
                result_data = result_response.json()
                if result_data.get("success") and result_data.get("status") == "completed":
                    print("✅ Analysis completed successfully!")
                    print(f"🔍 Detected symbol: {result_data.get('data', {}).get('detected_symbol', 'N/A')}")
                    return True
                    
        except Exception as e:
            pass
            
        time.sleep(5)
        if i % 6 == 0:  # Print every 30 seconds
            print(f"⏳ Still waiting... ({i*5}s elapsed)")
    
    print("⚠️ Analysis did not complete within 5 minutes")
    return False

if __name__ == "__main__":
    success = test_progress_tracking()
    if success:
        print("\n🎉 PROGRESS TRACKING TEST PASSED!")
        print("✅ All fixes are working correctly")
    else:
        print("\n❌ PROGRESS TRACKING TEST FAILED")
        print("⚠️ Check logs for issues")
