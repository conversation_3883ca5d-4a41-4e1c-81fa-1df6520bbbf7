#!/usr/bin/env python3
"""
Progress Issue Diagnostic Script
===============================

This script diagnoses the progress bar and result display issues by:
1. Checking the current state of the ProgressBroadcaster
2. Analyzing recent session logs
3. Testing the buffering system
4. Checking result storage and retrieval
"""

import sys
import os
import json
import asyncio
from pathlib import Path
from datetime import datetime

# Add the backend directory to the Python path
backend_dir = Path(__file__).parent / "Agent_Trading" / "backend"
sys.path.insert(0, str(backend_dir))

async def diagnose_progress_broadcaster():
    """Diagnose the ProgressBroadcaster state."""
    print("🔍 DIAGNOSING PROGRESS BROADCASTER")
    print("=" * 50)
    
    try:
        from app.helpers.workflow_management.progress_tracker import ProgressBroadcaster, WorkflowStep, ProgressUpdate
        
        # Create a broadcaster instance (this should be the same singleton)
        broadcaster = ProgressBroadcaster()
        
        print(f"📊 Broadcaster ID: {id(broadcaster)}")
        print(f"📊 Active sessions: {list(broadcaster.clients.keys())}")
        print(f"📊 Buffered sessions: {list(broadcaster.buffered_updates.keys())}")
        
        # Check buffered updates for each session
        for session_id, updates in broadcaster.buffered_updates.items():
            print(f"\n📦 Session {session_id}:")
            print(f"   Buffered updates: {len(updates)}")
            print(f"   Active clients: {len(broadcaster.clients.get(session_id, []))}")
            
            if updates:
                print("   Recent updates:")
                for i, update in enumerate(updates[-3:]):  # Show last 3 updates
                    step = update.get('step', 'unknown')
                    progress = update.get('progress', 0)
                    message = update.get('message', 'No message')[:50]
                    print(f"     {i+1}. {step} ({progress}%) - {message}")
        
        # Test the buffering system
        print(f"\n🧪 TESTING BUFFERING SYSTEM")
        print("-" * 30)
        
        test_session_id = f"test_diagnostic_{int(datetime.now().timestamp())}"
        
        # Create a test progress update
        test_update = ProgressUpdate(
            step=WorkflowStep.CHART_ANALYSIS,
            progress=20,
            message="Test diagnostic update",
            timestamp=datetime.now()
        )
        
        print(f"📝 Broadcasting test update for session: {test_session_id}")
        await broadcaster.broadcast_update(test_session_id, test_update)
        
        # Check if it was buffered
        if test_session_id in broadcaster.buffered_updates:
            buffered_count = len(broadcaster.buffered_updates[test_session_id])
            print(f"✅ Test update buffered successfully ({buffered_count} updates)")
            
            # Now test adding a client
            print(f"📱 Testing client addition and replay...")
            client_queue = await broadcaster.add_client(test_session_id)
            
            # Check if we received the buffered update
            try:
                replayed_update = await asyncio.wait_for(client_queue.get(), timeout=1.0)
                print(f"✅ Buffered update replayed successfully: {replayed_update.get('step')} ({replayed_update.get('progress')}%)")
                return True
            except asyncio.TimeoutError:
                print(f"❌ No buffered update received within timeout")
                return False
        else:
            print(f"❌ Test update was not buffered")
            return False
            
    except Exception as e:
        print(f"❌ Error during broadcaster diagnosis: {e}")
        return False

def analyze_recent_logs():
    """Analyze recent session logs."""
    print(f"\n📋 ANALYZING RECENT LOGS")
    print("=" * 50)
    
    # Check progress debug log
    progress_log = Path("Agent_Trading/backend/logs/progress_debug.log")
    if progress_log.exists():
        print(f"📄 Progress debug log size: {progress_log.stat().st_size:,} bytes")
        
        # Get recent lines
        with open(progress_log, 'r', encoding='utf-8', errors='ignore') as f:
            lines = f.readlines()
            recent_lines = lines[-20:]  # Last 20 lines
            
        print(f"📄 Recent progress debug entries:")
        for line in recent_lines:
            if "BROADCASTING" in line or "CLIENT ADDED" in line or "REPLAYING" in line:
                print(f"   {line.strip()}")
    
    # Check session logs
    session_dir = Path("Agent_Trading/backend/logs/progress/sessions")
    if session_dir.exists():
        session_files = list(session_dir.glob("*.log"))
        if session_files:
            # Get the most recent session log
            recent_session = max(session_files, key=lambda x: x.stat().st_mtime)
            print(f"\n📄 Most recent session log: {recent_session.name}")
            
            with open(recent_session, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
                print(f"📄 Session log content ({len(content)} chars):")
                print(content[:500] + "..." if len(content) > 500 else content)

def check_result_storage():
    """Check result storage and retrieval."""
    print(f"\n💾 CHECKING RESULT STORAGE")
    print("=" * 50)
    
    # Check result debug log for recent entries
    result_log = Path("Agent_Trading/backend/logs/result_debug.log")
    if result_log.exists():
        print(f"📄 Result debug log size: {result_log.stat().st_size:,} bytes")
        
        with open(result_log, 'r', encoding='utf-8', errors='ignore') as f:
            lines = f.readlines()
            
        # Find recent result operations
        recent_results = []
        for line in lines[-50:]:  # Check last 50 lines
            if "RESULT NOT FOUND" in line or "RESULT STORED" in line or "RESULT ENDPOINT REQUEST" in line:
                recent_results.append(line.strip())
        
        print(f"📄 Recent result operations:")
        for result in recent_results[-10:]:  # Show last 10 operations
            print(f"   {result}")

def check_frontend_backend_mismatch():
    """Check for potential frontend-backend mismatches."""
    print(f"\n🔗 CHECKING FRONTEND-BACKEND INTEGRATION")
    print("=" * 50)
    
    # Check the SSE endpoint implementation
    try:
        from app.api.routes.progress import router as progress_router
        print(f"✅ Progress router imported successfully")
        
        # Check if the endpoint exists
        routes = []
        for route in progress_router.routes:
            if hasattr(route, 'path'):
                routes.append(f"{route.methods} {route.path}")
        
        print(f"📡 Available progress routes:")
        for route in routes:
            print(f"   {route}")
            
    except Exception as e:
        print(f"❌ Error checking progress routes: {e}")
    
    # Check the async trading endpoint
    try:
        from app.api.routes.async_trading import router as async_router
        print(f"✅ Async trading router imported successfully")
        
        routes = []
        for route in async_router.routes:
            if hasattr(route, 'path'):
                routes.append(f"{route.methods} {route.path}")
        
        print(f"📡 Available async trading routes:")
        for route in routes:
            print(f"   {route}")
            
    except Exception as e:
        print(f"❌ Error checking async trading routes: {e}")

async def main():
    """Main diagnostic function."""
    print("🔍 COMPREHENSIVE PROGRESS & RESULT DIAGNOSTIC")
    print(f"Start time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"Working directory: {os.getcwd()}")
    
    # Run all diagnostics
    broadcaster_ok = await diagnose_progress_broadcaster()
    analyze_recent_logs()
    check_result_storage()
    check_frontend_backend_mismatch()
    
    print(f"\n{'='*50}")
    print("📊 DIAGNOSTIC SUMMARY:")
    print(f"   Progress Broadcaster: {'✅ WORKING' if broadcaster_ok else '❌ ISSUES FOUND'}")
    
    if not broadcaster_ok:
        print(f"\n🔧 RECOMMENDED FIXES:")
        print(f"   1. Check if ProgressBroadcaster singleton is working correctly")
        print(f"   2. Verify session ID consistency between frontend and backend")
        print(f"   3. Check if buffered updates are being stored and replayed")
        print(f"   4. Verify SSE connection timing")
    
    return broadcaster_ok

if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
