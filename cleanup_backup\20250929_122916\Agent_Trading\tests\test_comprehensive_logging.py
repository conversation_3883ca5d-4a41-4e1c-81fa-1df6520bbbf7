#!/usr/bin/env python3
"""
Test Comprehensive Logging - Track parameter passing and progress steps
"""

import requests
import time
import subprocess
import base64

def test_comprehensive_logging():
    print("🔧 TESTING COMPREHENSIVE LOGGING")
    print("=" * 60)
    
    base_url = "http://localhost:8000"
    
    # Step 1: Login
    print("1️⃣ Logging in...")
    try:
        login_response = requests.post(f"{base_url}/api/v1/auth/login", json={
            "email": "<EMAIL>",
            "password": "Bunnych@1627"
        })
        
        if login_response.status_code != 200:
            print(f"❌ Login failed: {login_response.status_code}")
            return
            
        token = login_response.json()["access_token"]
        headers = {"Authorization": f"Bearer {token}"}
        print("✅ Login successful")
    except Exception as e:
        print(f"❌ Login error: {e}")
        return
    
    # Step 2: Prepare image data (convert to base64 as expected by backend)
    print("\n2️⃣ Preparing image data...")
    try:
        with open("Agent_Trading/Testing_images/Screenshot 2025-08-03 190956.png", "rb") as f:
            image_data = f.read()
            image_base64 = base64.b64encode(image_data).decode('utf-8')
        
        request_data = {
            "images_base64": [image_base64],
            "analysis_type": "Positional",
            "market_specialization": "Crypto",
            "preferred_model": "gemini-2.5-flash",
            "timeframes": ["15m"],
            "ticker_hint": "",
            "context_hint": ""
        }
        
        print("✅ Image data prepared")
    except Exception as e:
        print(f"❌ Image preparation error: {e}")
        return
    
    # Step 3: Start analysis
    print("\n3️⃣ Starting analysis...")
    try:
        analysis_response = requests.post(
            f"{base_url}/api/v1/trading/async/start",
            json=request_data,
            headers=headers
        )
        
        if analysis_response.status_code != 200:
            print(f"❌ Analysis failed: {analysis_response.status_code}")
            print(f"Response: {analysis_response.text}")
            return
            
        session_id = analysis_response.json()["session_id"]
        print(f"✅ Analysis started with session: {session_id}")
    except Exception as e:
        print(f"❌ Analysis error: {e}")
        return
    
    # Step 4: Wait for workflow to start and check logs immediately
    print(f"\n4️⃣ CHECKING PARAMETER PASSING LOGS:")
    print("=" * 60)
    
    time.sleep(3)  # Wait for workflow to start
    
    try:
        # Check for our comprehensive logging
        result = subprocess.run(
            ["grep", "-A", "2", "-B", "2", "PARAM_CREATION\\|SETTING_ON_WORKFLOW\\|WORKFLOW_RECEIVED", "Agent_Trading/backend/logs/backend_debug.log"],
            capture_output=True, text=True
        )
        
        if result.returncode == 0 and result.stdout:
            print("✅ Found parameter passing logs:")
            lines = result.stdout.strip().split('\n')
            for line in lines[-10:]:  # Show last 10 lines
                print(f"   📝 {line}")
        else:
            print("❌ No parameter passing logs found")
            
        # Check for workflow reception logs
        result2 = subprocess.run(
            ["grep", "-A", "3", "WORKFLOW_RECEIVED\\|WORKFLOW_INSTANCE", "Agent_Trading/backend/logs/langgraph_debug.log"],
            capture_output=True, text=True
        )
        
        if result2.returncode == 0 and result2.stdout:
            print("\n✅ Found workflow reception logs:")
            lines = result2.stdout.strip().split('\n')
            for line in lines[-10:]:  # Show last 10 lines
                print(f"   📝 {line}")
        else:
            print("\n❌ No workflow reception logs found")
            
        # Check for progress broadcasting logs
        result3 = subprocess.run(
            ["grep", "PROGRESS_CHECK\\|PROGRESS_BROADCAST\\|PROGRESS_SKIP", "Agent_Trading/backend/logs/langgraph_debug.log"],
            capture_output=True, text=True
        )
        
        if result3.returncode == 0 and result3.stdout:
            print("\n✅ Found progress broadcasting logs:")
            lines = result3.stdout.strip().split('\n')
            for line in lines[-5:]:  # Show last 5 lines
                print(f"   📝 {line}")
        else:
            print("\n❌ No progress broadcasting logs found")
            
    except Exception as e:
        print(f"❌ Log analysis error: {e}")
    
    # Step 5: Monitor progress for a short time
    print(f"\n5️⃣ MONITORING PROGRESS UPDATES:")
    print("=" * 60)
    
    progress_updates = []
    start_time = time.time()
    
    for i in range(10):  # Check 10 times over 30 seconds
        try:
            result_response = requests.get(
                f"{base_url}/api/v1/trading/async/result/{session_id}",
                headers=headers
            )
            
            if result_response.status_code == 200:
                result_data = result_response.json()
                status = result_data.get("status", "unknown")
                elapsed = time.time() - start_time
                
                if status == "completed":
                    print(f"🎉 Analysis completed in {elapsed:.1f}s!")
                    break
                elif status == "running":
                    progress_updates.append(f"{elapsed:.1f}s")
                    print(f"⏳ Progress check #{i+1} at {elapsed:.1f}s - Status: {status}")
                    
            time.sleep(3)
            
        except Exception as e:
            print(f"❌ Monitoring error: {e}")
            break
    
    # Step 6: Final analysis
    print(f"\n6️⃣ FINAL ANALYSIS:")
    print("=" * 60)
    
    print(f"📊 Progress checks made: {len(progress_updates)}")
    
    # Check for session in progress logs
    try:
        result = subprocess.run(
            ["grep", "-c", session_id.replace("async_", ""), "Agent_Trading/backend/logs/progress_debug.log"],
            capture_output=True, text=True
        )
        
        if result.returncode == 0:
            count = result.stdout.strip()
            print(f"📝 Session mentions in progress logs: {count}")
            
        # Check for critical errors
        result2 = subprocess.run(
            ["grep", "CRITICAL", "Agent_Trading/backend/logs/langgraph_debug.log"],
            capture_output=True, text=True
        )
        
        if result2.returncode == 0 and result2.stdout:
            print("\n🚨 Found CRITICAL errors:")
            lines = result2.stdout.strip().split('\n')
            for line in lines[-3:]:  # Show last 3 critical errors
                print(f"   ❌ {line}")
        else:
            print("\n✅ No CRITICAL errors found")
            
    except Exception as e:
        print(f"❌ Final analysis error: {e}")
    
    print(f"\n🎯 SUMMARY:")
    print("=" * 60)
    print("📋 Check the logs above to see:")
    print("   1. Whether parameters are being created correctly")
    print("   2. Whether parameters are being set on workflow instance")
    print("   3. Whether workflow is receiving parameters correctly")
    print("   4. Whether progress broadcasting is working")
    print("   5. Any critical errors in parameter passing")

if __name__ == "__main__":
    test_comprehensive_logging()
