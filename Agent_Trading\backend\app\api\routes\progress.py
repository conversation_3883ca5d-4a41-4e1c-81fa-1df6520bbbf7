"""
FastAPI routes for real-time progress tracking using Server-Sent Events.
Provides live updates on LangGraph workflow execution.
"""

import asyncio
import json
import logging
from typing import AsyncGenerator, Optional
from fastapi import APIRouter, HTTPException, Depends, Query
from fastapi.responses import StreamingResponse
from starlette.responses import Response
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials

from app.helpers.workflow_management.progress_tracker import (
    get_progress_tracker,
    get_progress_broadcaster,
    WorkflowProgressTracker,
    ProgressBroadcaster
)
from app.core.progress_debug import progress_debug
from app.core.security import get_current_user, security_manager
from app.core.database import get_db, AsyncSession
from app.models.user import User as DBUser

logger = logging.getLogger(__name__)
router = APIRouter(prefix="/progress", tags=["Progress Tracking"])

# Custom authentication for progress stream that supports query params
async def get_user_from_token_or_query(
    token: Optional[str] = Query(None),
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(HTTPBearer(auto_error=False)),
    db: AsyncSession = Depends(get_db)
) -> DBUser:
    """Get user from either Authorization header or query parameter token."""
    try:
        # Try Authorization header first
        auth_token = None
        if credentials:
            auth_token = credentials.credentials
        elif token:
            auth_token = token
        
        if not auth_token:
            raise HTTPException(status_code=403, detail="No authentication token provided")
        
        print(f"🔍 DEBUG: Received token: {auth_token[:50]}...")
        token_data = await security_manager.verify_token(auth_token)
        print(f"🔍 DEBUG: Token verified, user_id: {token_data.user_id}")

        # Fetch user from database
        from sqlalchemy import select
        result = await db.execute(
            select(DBUser).where(DBUser.id == token_data.user_id)
        )
        user = result.scalar_one_or_none()

        if not user:
            print(f"❌ DEBUG: User not found in database for ID: {token_data.user_id}")
            raise HTTPException(status_code=403, detail="User not found")

        print(f"✅ DEBUG: User found: {user.email}")
        return user

    except Exception as e:
        print(f"❌ DEBUG: Authentication error: {e}")
        raise HTTPException(status_code=403, detail="Authentication failed")

@router.get("/session/{session_id}")
async def get_session_progress(
    session_id: str,
    current_user: DBUser = Depends(get_user_from_token_or_query),
    tracker: WorkflowProgressTracker = Depends(get_progress_tracker)
):
    """Get current progress for a session."""
    try:
        progress_data = tracker.get_session_progress(session_id)
        
        if not progress_data:
            raise HTTPException(status_code=404, detail="Session not found")
        
        return {
            "success": True,
            "session_id": session_id,
            "progress": progress_data
        }
    
    except Exception as e:
        logger.error(f"❌ Failed to get session progress: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/stream/{session_id}")
async def stream_progress_updates(
    session_id: str,
    current_user: DBUser = Depends(get_user_from_token_or_query),
    broadcaster: ProgressBroadcaster = Depends(get_progress_broadcaster)
):
    """Stream real-time progress updates via Server-Sent Events."""

    # Debug: Log SSE connection attempt
    progress_debug.sse_connection(session_id, {
        "user_id": current_user.id if current_user else None,
        "user_email": current_user.email if current_user else None,
        "endpoint": f"/api/v1/progress/stream/{session_id}"
    })

    async def event_generator() -> AsyncGenerator[str, None]:
        """Generate Server-Sent Events for progress updates."""
        try:
            progress_debug.debug("SSE_GENERATOR", f"Adding client for session {session_id}")
            client_queue = await broadcaster.add_client(session_id)
            progress_debug.debug("SSE_GENERATOR", f"Client queue created for session {session_id}")

            # Send initial connection confirmation
            connection_msg = {'type': 'connected', 'session_id': session_id}
            progress_debug.debug("SSE_GENERATOR", f"Sending connection confirmation: {connection_msg}")
            yield f"data: {json.dumps(connection_msg)}\n\n"

            # Force immediate flush
            await asyncio.sleep(0)
            
            # Track connection state for better debugging
            connection_active = True
            keepalive_count = 0
            max_keepalives = 300  # 5 minutes of keepalives (1 per second)

            while connection_active:
                try:
                    # Wait for progress update with longer timeout for workflow completion
                    progress_debug.debug("SSE_GENERATOR", f"Waiting for progress update for session {session_id}")
                    update_data = await asyncio.wait_for(client_queue.get(), timeout=2.0)  # Increased timeout

                    progress_debug.debug("SSE_GENERATOR", f"Received progress update: {update_data}")

                    # Format as Server-Sent Event
                    event_data = {
                        "type": "progress_update",
                        "data": update_data
                    }

                    progress_debug.debug("SSE_GENERATOR", f"Sending SSE event: {event_data}")
                    yield f"data: {json.dumps(event_data)}\n\n"

                    # Force immediate flush to prevent buffering
                    await asyncio.sleep(0)

                    # Reset keepalive counter on successful update
                    keepalive_count = 0

                    # If workflow is complete, close stream gracefully
                    if update_data.get("step") == "complete":
                        complete_msg = {'type': 'complete', 'session_id': session_id}
                        progress_debug.debug("SSE_GENERATOR", f"Workflow complete, sending: {complete_msg}")
                        yield f"data: {json.dumps(complete_msg)}\n\n"
                        connection_active = False
                        break

                except asyncio.TimeoutError:
                    # Send keepalive ping but don't break connection
                    keepalive_count += 1
                    ping_msg = {'type': 'ping', 'timestamp': asyncio.get_event_loop().time()}
                    progress_debug.debug("SSE_GENERATOR", f"Sending keepalive ping: {ping_msg}")
                    yield f"data: {json.dumps(ping_msg)}\n\n"

                    # Only disconnect after excessive keepalives (workflow likely stuck)
                    if keepalive_count > max_keepalives:
                        progress_debug.debug("SSE_GENERATOR", f"Max keepalives reached ({max_keepalives}), closing connection")
                        connection_active = False
                        break

                except Exception as e:
                    progress_debug.sse_error(session_id, str(e), {"location": "event_stream_loop", "keepalive_count": keepalive_count})
                    logger.error(f"❌ Error in event stream: {e}")
                    # Don't break on minor errors, try to continue
                    yield f"data: {json.dumps({'type': 'error', 'error': str(e)})}\n\n"
                    await asyncio.sleep(0.1)  # Brief pause before retry
                    
        except Exception as e:
            progress_debug.sse_error(session_id, str(e), {"location": "event_generator_setup"})
            logger.error(f"❌ Error setting up event stream: {e}")
            yield f"data: {json.dumps({'type': 'error', 'error': str(e)})}\n\n"

        finally:
            # Enhanced cleanup with better logging
            cleanup_reason = "normal_completion" if connection_active == False else "unexpected_disconnect"
            progress_debug.sse_disconnect(session_id, f"event_generator_cleanup_{cleanup_reason}")

            try:
                broadcaster.remove_client(session_id, client_queue)
                progress_debug.debug("SSE_GENERATOR", f"Successfully cleaned up client for session {session_id}")
            except Exception as cleanup_error:
                progress_debug.sse_error(session_id, str(cleanup_error), {"location": "cleanup_error"})
                logger.error(f"❌ Error during SSE cleanup: {cleanup_error}")
    
    return StreamingResponse(
        event_generator(),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Headers": "Cache-Control",
            "X-Accel-Buffering": "no",  # Disable nginx buffering
            "Transfer-Encoding": "chunked"  # Force chunked encoding
        }
    )

@router.post("/session")
async def create_progress_session(
    current_user = Depends(get_current_user),
    tracker: WorkflowProgressTracker = Depends(get_progress_tracker)
):
    """Create a new progress tracking session."""
    try:
        session_id = tracker.create_session()
        
        return {
            "success": True,
            "session_id": session_id,
            "message": "Progress tracking session created"
        }
    
    except Exception as e:
        logger.error(f"❌ Failed to create progress session: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/session/{session_id}")
async def cleanup_progress_session(
    session_id: str,
    current_user: DBUser = Depends(get_user_from_token_or_query),
    tracker: WorkflowProgressTracker = Depends(get_progress_tracker)
):
    """Clean up a completed progress session."""
    try:
        # Add logging for debugging
        print(f"🧹 Cleaned up session: {session_id}")
        tracker.cleanup_session(session_id)
        
        return {
            "success": True,
            "message": f"Session {session_id} cleaned up"
        }
    
    except Exception as e:
        logger.error(f"❌ Failed to cleanup session: {e}")
        raise HTTPException(status_code=500, detail=str(e))
