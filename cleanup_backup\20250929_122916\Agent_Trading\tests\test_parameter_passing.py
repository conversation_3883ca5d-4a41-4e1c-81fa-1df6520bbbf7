#!/usr/bin/env python3
"""
Test script to verify parameter passing in the SSE progress tracking system.
Checks that progress_tracker, progress_broadcaster, and session_id are passed correctly.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.helpers.workflow_management.progress_tracker import (
    WorkflowProgressTracker, ProgressBroadcaster, get_progress_tracker, get_progress_broadcaster
)
from app.helpers.workflow_management.complete_langgraph_workflow import CompleteLangGraphWorkflow
from app.services.enhanced_langgraph_service import EnhancedLangGraphService

def test_global_instances():
    """Test that global instances are working correctly"""
    print("🧪 Testing global progress tracker instances...")
    
    # Get global instances
    tracker1 = get_progress_tracker()
    tracker2 = get_progress_tracker()
    broadcaster1 = get_progress_broadcaster()
    broadcaster2 = get_progress_broadcaster()
    
    # Verify they are the same instances (singleton pattern)
    assert tracker1 is tracker2, "❌ Progress tracker is not singleton"
    assert broadcaster1 is broadcaster2, "❌ Progress broadcaster is not singleton"
    
    print("✅ Global instances are working correctly")
    return True

def test_parameter_passing_chain():
    """Test parameter passing through the service chain"""
    print("🧪 Testing parameter passing chain...")
    
    try:
        # Create service instance
        service = EnhancedLangGraphService()
        
        # Test with explicit parameters
        tracker = get_progress_tracker()
        broadcaster = get_progress_broadcaster()
        session_id = "test_session_123"
        
        # Create workflow instance
        workflow = CompleteLangGraphWorkflow()
        
        # Test parameter assignment
        workflow.progress_broadcaster = broadcaster
        workflow.session_id = session_id
        
        # Verify parameters are set correctly
        assert hasattr(workflow, 'progress_broadcaster'), "❌ progress_broadcaster not set"
        assert hasattr(workflow, 'session_id'), "❌ session_id not set"
        assert workflow.progress_broadcaster is not None, "❌ progress_broadcaster is None"
        assert workflow.session_id is not None, "❌ session_id is None"
        assert workflow.session_id == session_id, "❌ session_id mismatch"
        
        print("✅ Parameter passing chain is working correctly")
        return True
        
    except Exception as e:
        print(f"❌ Parameter passing test failed: {e}")
        return False

def test_progress_broadcast_method():
    """Test the progress broadcast method"""
    print("🧪 Testing progress broadcast method...")
    
    try:
        # Create workflow with parameters
        workflow = CompleteLangGraphWorkflow()
        workflow.progress_broadcaster = get_progress_broadcaster()
        workflow.session_id = "test_session_broadcast"
        
        # Test synchronous broadcast method
        workflow._broadcast_progress_sync(
            "chart_analysis", 20, "Test progress broadcast", {"test": True}
        )
        
        print("✅ Progress broadcast method is working correctly")
        return True
        
    except Exception as e:
        print(f"❌ Progress broadcast test failed: {e}")
        return False

def test_enhanced_service_parameter_handling():
    """Test parameter handling in EnhancedLangGraphService"""
    print("🧪 Testing EnhancedLangGraphService parameter handling...")
    
    try:
        service = EnhancedLangGraphService()
        
        # Test with None parameters (should use global instances)
        result = service._setup_progress_tracking(None, None, None)
        
        # Verify result contains valid instances
        assert 'progress_tracker' in result, "❌ progress_tracker not in result"
        assert 'progress_broadcaster' in result, "❌ progress_broadcaster not in result"
        assert 'session_id' in result, "❌ session_id not in result"
        
        assert result['progress_tracker'] is not None, "❌ progress_tracker is None"
        assert result['progress_broadcaster'] is not None, "❌ progress_broadcaster is None"
        assert result['session_id'] is not None, "❌ session_id is None"
        
        print("✅ EnhancedLangGraphService parameter handling is working correctly")
        return True
        
    except Exception as e:
        print(f"❌ EnhancedLangGraphService test failed: {e}")
        return False

def main():
    """Run all parameter passing tests"""
    print("🚀 Starting parameter passing tests")
    print("=" * 50)
    
    tests = [
        test_global_instances,
        test_parameter_passing_chain,
        test_progress_broadcast_method,
        test_enhanced_service_parameter_handling
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            print()
        except Exception as e:
            print(f"❌ Test {test.__name__} failed with exception: {e}")
            print()
    
    print("=" * 50)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All parameter passing tests passed!")
        return True
    else:
        print("❌ Some tests failed")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
