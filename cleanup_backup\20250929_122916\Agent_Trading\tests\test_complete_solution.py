#!/usr/bin/env python3
"""
Complete Solution Test - Progress Broadcasting & Analysis Display
Tests both the progress bar fixes and the enhanced analysis display
"""

import requests
import time
import json
import sys

def test_complete_solution():
    print("🎯 TESTING COMPLETE SOLUTION")
    print("=" * 60)
    
    base_url = "http://localhost:8000"
    
    # Step 1: Login
    print("1️⃣ Logging in...")
    login_response = requests.post(f"{base_url}/api/v1/auth/login", json={
        "email": "<EMAIL>",
        "password": "Bunnych@1627"
    })
    
    if login_response.status_code != 200:
        print(f"❌ Login failed: {login_response.status_code}")
        return False
        
    token = login_response.json()["access_token"]
    headers = {"Authorization": f"Bearer {token}"}
    print("✅ Login successful")
    
    # Step 2: Start analysis
    print("\n2️⃣ Starting analysis...")
    with open("chart_sample.png", "rb") as f:
        files = {"chart_images": f}
        data = {
            "analysis_mode": "positional",
            "market_specialization": "Crypto"
        }
        
        analysis_response = requests.post(
            f"{base_url}/api/v1/trading/analyze-async",
            files=files,
            data=data,
            headers=headers
        )
    
    if analysis_response.status_code != 200:
        print(f"❌ Analysis failed: {analysis_response.status_code}")
        return False
        
    session_id = analysis_response.json()["session_id"]
    print(f"✅ Analysis started with session: {session_id}")
    
    # Step 3: Monitor progress with detailed logging
    print("\n3️⃣ Monitoring progress...")
    progress_steps = []
    start_time = time.time()
    
    while True:
        result_response = requests.get(
            f"{base_url}/api/v1/trading/result/{session_id}",
            headers=headers
        )
        
        if result_response.status_code != 200:
            print(f"❌ Result check failed: {result_response.status_code}")
            break
            
        result_data = result_response.json()
        status = result_data.get("status", "unknown")
        
        if status == "completed":
            elapsed = time.time() - start_time
            print(f"🎉 Analysis completed in {elapsed:.1f} seconds!")
            break
        elif status == "running":
            elapsed = time.time() - start_time
            print(f"⏳ Status: running ({elapsed:.1f}s)")
        else:
            print(f"❓ Status: {status}")
            
        time.sleep(3)
        
        if time.time() - start_time > 120:  # 2 minute timeout
            print("⏰ Timeout reached")
            break
    
    # Step 4: Analyze results
    print("\n4️⃣ Analyzing results...")
    
    if result_data.get("status") == "completed":
        data = result_data.get("data", {})
        print(f"📊 Top-level data keys: {list(data.keys())}")
        
        # Check for nested data structure
        if "data" in data:
            nested_data = data["data"]
            print(f"📊 Nested data keys: {list(nested_data.keys())}")
            
            # Check for trading signals
            if "trading_signals" in nested_data:
                trading_signals = nested_data["trading_signals"]
                print(f"📊 Trading signals keys: {list(trading_signals.keys())}")
                
                # Display key information
                print(f"   ✅ Symbol: {nested_data.get('detected_symbol', 'Not found')}")
                print(f"   ✅ Market: {nested_data.get('market_type', 'Not found')}")
                print(f"   ✅ Status: {trading_signals.get('status', 'Not found')}")
                
                # Check for analysis content
                analysis_summary = trading_signals.get('analysis_summary', '')
                if analysis_summary:
                    print(f"   ✅ Analysis Summary: {analysis_summary[:100]}...")
                else:
                    print("   ❌ No analysis summary found")
                    
                # Check for trade ideas
                trade_ideas = trading_signals.get('trade_ideas', [])
                print(f"   ✅ Trade Ideas: {len(trade_ideas)} found")
                
                # Check for key levels
                key_levels = trading_signals.get('key_levels', {})
                support_levels = key_levels.get('support', [])
                resistance_levels = key_levels.get('resistance', [])
                print(f"   ✅ Support Levels: {len(support_levels)} found")
                print(f"   ✅ Resistance Levels: {len(resistance_levels)} found")
                
                return True
            else:
                print("   ❌ No trading_signals found in nested data")
        else:
            print("   ❌ No nested data structure found")
    else:
        print("   ❌ Analysis did not complete successfully")
    
    return False

def check_progress_logs(session_id):
    """Check progress logs for the session"""
    print(f"\n5️⃣ Checking progress logs for session: {session_id}")
    
    try:
        with open("Agent_Trading/backend/logs/progress_debug.log", "r") as f:
            logs = f.read()
            
        if session_id in logs:
            print("   ✅ Session found in progress logs")
            
            # Count progress steps
            progress_steps = []
            for line in logs.split('\n'):
                if session_id in line and 'progress' in line.lower():
                    if '10%' in line or 'progress": 10' in line:
                        progress_steps.append('10%')
                    elif '20%' in line or 'progress": 20' in line:
                        progress_steps.append('20%')
                    elif '40%' in line or 'progress": 40' in line:
                        progress_steps.append('40%')
                    elif '50%' in line or 'progress": 50' in line:
                        progress_steps.append('50%')
                    elif '65%' in line or 'progress": 65' in line:
                        progress_steps.append('65%')
                    elif '80%' in line or 'progress": 80' in line:
                        progress_steps.append('80%')
                    elif '95%' in line or 'progress": 95' in line:
                        progress_steps.append('95%')
                    elif '100%' in line or 'progress": 100' in line:
                        progress_steps.append('100%')
            
            print(f"   📈 Progress steps found: {progress_steps}")
            
            if len(progress_steps) >= 6:  # Should have at least 6 steps
                print("   ✅ Progress broadcasting working correctly")
                return True
            else:
                print("   ❌ Missing intermediate progress steps")
                return False
        else:
            print("   ❌ Session not found in progress logs")
            return False
            
    except Exception as e:
        print(f"   ❌ Error reading progress logs: {e}")
        return False

if __name__ == "__main__":
    print("🧪 COMPLETE SOLUTION TEST")
    print("Testing both progress broadcasting and analysis display fixes")
    print("=" * 60)
    
    # Test the complete solution
    analysis_success = test_complete_solution()
    
    if analysis_success:
        print("\n🎉 ANALYSIS TEST PASSED!")
        print("✅ Data structure is correct")
        print("✅ Rich analysis content is available")
        print("✅ Frontend should display complete analysis")
    else:
        print("\n❌ ANALYSIS TEST FAILED")
        print("❌ Data structure issues detected")
    
    print("\n" + "=" * 60)
    print("🏁 TEST COMPLETED")
    
    if analysis_success:
        print("✅ Solution is working correctly!")
        print("📱 Frontend should now display:")
        print("   • Actual symbol and market type")
        print("   • Complete analysis summary")
        print("   • Trading ideas with entry/exit levels")
        print("   • Support and resistance levels")
        print("   • Market context and insights")
    else:
        print("❌ Issues still need to be resolved")
