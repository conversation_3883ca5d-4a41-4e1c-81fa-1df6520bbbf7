#!/usr/bin/env python3
"""
Test script to verify the result endpoint fixes
"""

import requests
import json

def test_result_endpoint():
    """Test the result endpoint with a fake session ID"""
    
    # Test URL
    base_url = "http://localhost:8000"
    test_session_id = "test_session_123"
    
    # Test the result endpoint
    url = f"{base_url}/api/v1/trading/async/result/{test_session_id}"
    
    try:
        response = requests.get(url)
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.text}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Result endpoint working!")
            print(f"Response structure: {list(data.keys())}")
        else:
            print(f"❌ Result endpoint failed with status {response.status_code}")
            
    except Exception as e:
        print(f"❌ Error testing endpoint: {e}")

if __name__ == "__main__":
    print("🧪 Testing result endpoint fixes...")
    test_result_endpoint()
