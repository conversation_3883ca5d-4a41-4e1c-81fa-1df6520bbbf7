"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/hooks/useRealTimeProgress.ts":
/*!******************************************!*\
  !*** ./src/hooks/useRealTimeProgress.ts ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WORKFLOW_STEPS: () => (/* binding */ WORKFLOW_STEPS),\n/* harmony export */   useRealTimeProgress: () => (/* binding */ useRealTimeProgress)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/**\r\n * Real-time progress tracking hook for LangGraph AI analysis workflow\r\n * Uses Server-Sent Events ONLY for live updates (no polling fallback)\r\n */ \n// ✅ ALIGNED: Frontend mapping now matches actual LangGraph workflow execution\nconst WORKFLOW_STEPS = {\n    CHART_ANALYSIS: {\n        name: 'Chart Analysis',\n        progress: 20,\n        message: '📊 Starting comprehensive AI analysis workflow...'\n    },\n    SYMBOL_DETECTION: {\n        name: 'Symbol Detection',\n        progress: 40,\n        message: '✅ Symbol detected in chart'\n    },\n    TOOL_EXECUTION: {\n        name: 'Data Collection',\n        progress: 50,\n        message: '🛠️ Collecting data from tools...'\n    },\n    TOOL_SUMMARIZATION: {\n        name: 'Data Processing',\n        progress: 60,\n        message: '📋 Processing and summarizing data...'\n    },\n    RAG_INTEGRATION: {\n        name: 'RAG Integration',\n        progress: 70,\n        message: '🧠 Integrating historical context and patterns...'\n    },\n    FINAL_ANALYSIS: {\n        name: 'Final Analysis',\n        progress: 80,\n        message: '🎯 Generating final trading analysis...'\n    },\n    MEMORY_STORAGE: {\n        name: 'Database Storage',\n        progress: 90,\n        message: '💾 Saving analysis...'\n    },\n    COMPLETE: {\n        name: 'Complete',\n        progress: 100,\n        message: '🎉 Analysis complete!'\n    }\n};\nfunction useRealTimeProgress() {\n    const [state, setState] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({\n        sessionId: null,\n        currentStep: 'chart_analysis',\n        progress: 0,\n        message: 'Preparing for analysis...',\n        isConnected: false,\n        isComplete: false,\n        error: null,\n        updates: []\n    });\n    // Add retry state\n    const [retryCount, setRetryCount] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(0);\n    const maxRetries = 3;\n    // 🔧 CRITICAL FIX: Progress smoothing for rapid updates\n    const [progressQueue, setProgressQueue] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [isProcessingQueue, setIsProcessingQueue] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    // Process progress queue with smooth animations\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useRealTimeProgress.useEffect\": ()=>{\n            if (progressQueue.length > 0 && !isProcessingQueue) {\n                setIsProcessingQueue(true);\n                const processNext = {\n                    \"useRealTimeProgress.useEffect.processNext\": ()=>{\n                        setProgressQueue({\n                            \"useRealTimeProgress.useEffect.processNext\": (queue)=>{\n                                if (queue.length === 0) {\n                                    setIsProcessingQueue(false);\n                                    return queue;\n                                }\n                                const [nextUpdate, ...remaining] = queue;\n                                // Apply the update\n                                setState({\n                                    \"useRealTimeProgress.useEffect.processNext\": (prev)=>({\n                                            ...prev,\n                                            currentStep: nextUpdate.step,\n                                            progress: nextUpdate.progress,\n                                            message: nextUpdate.message\n                                        })\n                                }[\"useRealTimeProgress.useEffect.processNext\"]);\n                                // 🔧 IMPROVED: Dynamic delay based on queue length and progress difference\n                                let delay = 400 // Base delay reduced from 800ms to 400ms\n                                ;\n                                if (remaining.length > 0) {\n                                    const nextProgress = remaining[0].progress;\n                                    const currentProgress = nextUpdate.progress;\n                                    const progressDiff = nextProgress - currentProgress;\n                                    // Shorter delay for small progress jumps, longer for big jumps\n                                    if (progressDiff <= 10) {\n                                        delay = 300; // Fast updates for small increments\n                                    } else if (progressDiff <= 20) {\n                                        delay = 500; // Medium delay for medium increments\n                                    } else {\n                                        delay = 700; // Longer delay for big jumps\n                                    }\n                                    // If queue is getting long, speed up processing\n                                    if (remaining.length > 3) {\n                                        delay = Math.max(200, delay * 0.6); // Speed up but not too fast\n                                    }\n                                    console.log(\"\\uD83D\\uDCC8 FRONTEND_DEBUG: Processing queue - current: \".concat(currentProgress, \"%, next: \").concat(nextProgress, \"%, delay: \").concat(delay, \"ms, queue length: \").concat(remaining.length));\n                                    setTimeout(processNext, delay);\n                                } else {\n                                    setIsProcessingQueue(false);\n                                }\n                                return remaining;\n                            }\n                        }[\"useRealTimeProgress.useEffect.processNext\"]);\n                    }\n                }[\"useRealTimeProgress.useEffect.processNext\"];\n                processNext();\n            }\n        }\n    }[\"useRealTimeProgress.useEffect\"], [\n        progressQueue,\n        isProcessingQueue\n    ]);\n    const eventSourceRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const currentSessionRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    // Retry connection function\n    const retryConnection = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useRealTimeProgress.useCallback[retryConnection]\": async function(sessionId) {\n            let attempt = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 1;\n            if (attempt > maxRetries) {\n                console.error(\"❌ FRONTEND_DEBUG: Max retries (\".concat(maxRetries, \") exceeded for session \").concat(sessionId));\n                setState({\n                    \"useRealTimeProgress.useCallback[retryConnection]\": (prev)=>({\n                            ...prev,\n                            error: \"Connection failed after \".concat(maxRetries, \" attempts. Please refresh and try again.\")\n                        })\n                }[\"useRealTimeProgress.useCallback[retryConnection]\"]);\n                return;\n            }\n            console.log(\"\\uD83D\\uDD04 FRONTEND_DEBUG: Retry attempt \".concat(attempt, \"/\").concat(maxRetries, \" for session \").concat(sessionId));\n            setState({\n                \"useRealTimeProgress.useCallback[retryConnection]\": (prev)=>({\n                        ...prev,\n                        error: null,\n                        message: \"Reconnecting... (attempt \".concat(attempt, \"/\").concat(maxRetries, \")\")\n                    })\n            }[\"useRealTimeProgress.useCallback[retryConnection]\"]);\n            // Wait before retry (exponential backoff)\n            const delay = Math.min(1000 * Math.pow(2, attempt - 1), 5000);\n            await new Promise({\n                \"useRealTimeProgress.useCallback[retryConnection]\": (resolve)=>setTimeout(resolve, delay)\n            }[\"useRealTimeProgress.useCallback[retryConnection]\"]);\n            // Retry the connection\n            await connectToProgress(sessionId);\n        }\n    }[\"useRealTimeProgress.useCallback[retryConnection]\"], [\n        maxRetries\n    ]);\n    // Check if analysis is already complete\n    const checkAnalysisStatus = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useRealTimeProgress.useCallback[checkAnalysisStatus]\": async (sessionId)=>{\n            try {\n                const token = localStorage.getItem('access_token');\n                const baseURL = \"http://localhost:8000\" || 0;\n                const response = await fetch(\"\".concat(baseURL, \"/api/v1/async-trading/status/\").concat(sessionId), {\n                    headers: {\n                        'Authorization': \"Bearer \".concat(token),\n                        'Content-Type': 'application/json'\n                    }\n                });\n                if (response.ok) {\n                    const data = await response.json();\n                    if (data.status === 'completed') {\n                        setState({\n                            \"useRealTimeProgress.useCallback[checkAnalysisStatus]\": (prev)=>({\n                                    ...prev,\n                                    isComplete: true,\n                                    progress: 100,\n                                    currentStep: 'complete',\n                                    message: 'Analysis completed successfully!'\n                                })\n                        }[\"useRealTimeProgress.useCallback[checkAnalysisStatus]\"]);\n                        return true;\n                    }\n                }\n            } catch (error) {\n                console.error('Failed to check analysis status:', error);\n            }\n            return false;\n        }\n    }[\"useRealTimeProgress.useCallback[checkAnalysisStatus]\"], []);\n    const connectToProgress = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useRealTimeProgress.useCallback[connectToProgress]\": async (sessionId)=>{\n            // Close existing SSE connection\n            if (eventSourceRef.current) {\n                eventSourceRef.current.close();\n            }\n            // Store current session for retry purposes\n            currentSessionRef.current = sessionId;\n            // 🔧 CRITICAL FIX: Update state with the provided session ID IMMEDIATELY\n            // This ensures the UI shows the correct session ID right away\n            console.log('🔧 FRONTEND_DEBUG: Setting session ID immediately:', sessionId);\n            setState({\n                \"useRealTimeProgress.useCallback[connectToProgress]\": (prev)=>({\n                        ...prev,\n                        sessionId,\n                        error: null,\n                        currentStep: 'chart_analysis',\n                        progress: 0,\n                        message: 'Connecting to progress stream...',\n                        isConnected: false,\n                        isComplete: false\n                    })\n            }[\"useRealTimeProgress.useCallback[connectToProgress]\"]);\n            // Reset retry count for new connection\n            setRetryCount(0);\n            // First check if analysis is already complete\n            const isComplete = await checkAnalysisStatus(sessionId);\n            if (isComplete) {\n                return;\n            }\n            const token = localStorage.getItem('access_token');\n            const baseURL = \"http://localhost:8000\" || 0;\n            if (!token) {\n                console.error('❌ No access token found for SSE connection');\n                setState({\n                    \"useRealTimeProgress.useCallback[connectToProgress]\": (prev)=>({\n                            ...prev,\n                            error: 'Authentication required for progress tracking'\n                        })\n                }[\"useRealTimeProgress.useCallback[connectToProgress]\"]);\n                return;\n            }\n            // Try SSE first - Fixed URL construction and added better error handling\n            const sseUrl = \"\".concat(baseURL, \"/api/v1/progress/stream/\").concat(sessionId, \"?token=\").concat(encodeURIComponent(token));\n            console.log('🔗 FRONTEND_DEBUG: Attempting SSE connection');\n            console.log('🔗 FRONTEND_DEBUG: SSE URL:', sseUrl);\n            console.log('🔗 FRONTEND_DEBUG: Session ID:', sessionId);\n            console.log('🔗 FRONTEND_DEBUG: Token present:', !!token);\n            console.log('🔗 FRONTEND_DEBUG: Token length:', token === null || token === void 0 ? void 0 : token.length);\n            console.log('🔗 FRONTEND_DEBUG: Base URL:', baseURL);\n            // Create EventSource with proper error handling\n            let eventSource;\n            try {\n                eventSource = new EventSource(sseUrl);\n                console.log('✅ FRONTEND_DEBUG: EventSource created successfully');\n            } catch (error) {\n                console.error('❌ FRONTEND_DEBUG: Failed to create EventSource:', error);\n                setState({\n                    \"useRealTimeProgress.useCallback[connectToProgress]\": (prev)=>({\n                            ...prev,\n                            error: \"Failed to create SSE connection: \".concat(error)\n                        })\n                }[\"useRealTimeProgress.useCallback[connectToProgress]\"]);\n                return;\n            }\n            eventSourceRef.current = eventSource;\n            let sseConnected = false;\n            // SSE connection timeout (no polling fallback)\n            const sseTimeout = setTimeout({\n                \"useRealTimeProgress.useCallback[connectToProgress].sseTimeout\": ()=>{\n                    if (!sseConnected) {\n                        console.log('⚠️ SSE connection timeout - no polling fallback');\n                        eventSource.close();\n                        setState({\n                            \"useRealTimeProgress.useCallback[connectToProgress].sseTimeout\": (prev)=>({\n                                    ...prev,\n                                    isConnected: false,\n                                    error: 'SSE connection timeout - check backend connection'\n                                })\n                        }[\"useRealTimeProgress.useCallback[connectToProgress].sseTimeout\"]);\n                    }\n                }\n            }[\"useRealTimeProgress.useCallback[connectToProgress].sseTimeout\"], 10000) // Increased timeout to 10 seconds\n            ;\n            eventSource.onopen = ({\n                \"useRealTimeProgress.useCallback[connectToProgress]\": ()=>{\n                    console.log('✅ FRONTEND_DEBUG: SSE connection established');\n                    console.log('✅ FRONTEND_DEBUG: EventSource readyState:', eventSource.readyState);\n                    console.log('✅ FRONTEND_DEBUG: EventSource URL:', eventSource.url);\n                    sseConnected = true;\n                    clearTimeout(sseTimeout);\n                    setState({\n                        \"useRealTimeProgress.useCallback[connectToProgress]\": (prev)=>({\n                                ...prev,\n                                isConnected: true,\n                                error: null,\n                                message: 'Connected to progress stream'\n                            })\n                    }[\"useRealTimeProgress.useCallback[connectToProgress]\"]);\n                }\n            })[\"useRealTimeProgress.useCallback[connectToProgress]\"];\n            eventSource.onmessage = ({\n                \"useRealTimeProgress.useCallback[connectToProgress]\": (event)=>{\n                    try {\n                        console.log('📨 FRONTEND_DEBUG: Raw SSE message received:', event.data);\n                        const data = JSON.parse(event.data);\n                        console.log('📨 FRONTEND_DEBUG: Parsed SSE data:', data);\n                        console.log('📨 FRONTEND_DEBUG: Event type:', data.type);\n                        switch(data.type){\n                            case 'connected':\n                                console.log('✅ FRONTEND_DEBUG: Progress stream connected for session:', data.session_id);\n                                break;\n                            case 'progress_update':\n                                const update = data.data;\n                                console.log('📈 FRONTEND_DEBUG: SSE Progress update:', update);\n                                // Map backend step to frontend display\n                                const stepInfo = getStepInfo(update.step);\n                                console.log('📈 FRONTEND_DEBUG: Step info mapped:', stepInfo);\n                                // 🔧 CRITICAL DEBUG: Log the exact state update\n                                console.log('📈 FRONTEND_DEBUG: About to update state with:', {\n                                    currentStep: update.step,\n                                    progress: stepInfo.progress,\n                                    message: stepInfo.message,\n                                    backendProgress: update.progress,\n                                    frontendProgress: stepInfo.progress\n                                });\n                                // 🔧 CRITICAL FIX: Use progress queue for smooth animations\n                                setState({\n                                    \"useRealTimeProgress.useCallback[connectToProgress]\": (prev)=>{\n                                        // Only queue if this is actually a new step or higher progress\n                                        const isNewStep = update.step !== prev.currentStep;\n                                        const isHigherProgress = stepInfo.progress > prev.progress;\n                                        if (isNewStep || isHigherProgress) {\n                                            console.log('📈 FRONTEND_DEBUG: Queueing progress update:', stepInfo.progress, 'step:', update.step);\n                                            // Add to progress queue for smooth processing\n                                            setProgressQueue({\n                                                \"useRealTimeProgress.useCallback[connectToProgress]\": (queue)=>[\n                                                        ...queue,\n                                                        {\n                                                            step: update.step,\n                                                            progress: stepInfo.progress,\n                                                            message: stepInfo.message\n                                                        }\n                                                    ]\n                                            }[\"useRealTimeProgress.useCallback[connectToProgress]\"]);\n                                            // Update the updates array immediately\n                                            return {\n                                                ...prev,\n                                                updates: [\n                                                    ...prev.updates,\n                                                    update\n                                                ]\n                                            };\n                                        } else {\n                                            console.log('📈 FRONTEND_DEBUG: Skipping duplicate/lower progress update:', stepInfo.progress, 'current:', prev.progress);\n                                            return prev;\n                                        }\n                                    }\n                                }[\"useRealTimeProgress.useCallback[connectToProgress]\"]);\n                                break;\n                            case 'complete':\n                                console.log('🎉 SSE Analysis complete!');\n                                setState({\n                                    \"useRealTimeProgress.useCallback[connectToProgress]\": (prev)=>({\n                                            ...prev,\n                                            isComplete: true,\n                                            progress: 100,\n                                            currentStep: 'complete',\n                                            message: 'Analysis completed successfully!'\n                                        })\n                                }[\"useRealTimeProgress.useCallback[connectToProgress]\"]);\n                                eventSource.close();\n                                break;\n                            case 'ping':\n                                break;\n                            case 'error':\n                                console.error('❌ SSE Progress stream error:', data.error);\n                                setState({\n                                    \"useRealTimeProgress.useCallback[connectToProgress]\": (prev)=>({\n                                            ...prev,\n                                            error: data.error,\n                                            isConnected: false\n                                        })\n                                }[\"useRealTimeProgress.useCallback[connectToProgress]\"]);\n                                eventSource.close();\n                                break;\n                        }\n                    } catch (error) {\n                        console.error('Failed to parse SSE progress update:', error);\n                    }\n                }\n            })[\"useRealTimeProgress.useCallback[connectToProgress]\"];\n            eventSource.onerror = ({\n                \"useRealTimeProgress.useCallback[connectToProgress]\": (error)=>{\n                    console.error('❌ FRONTEND_DEBUG: SSE EventSource error:', error);\n                    console.error('❌ FRONTEND_DEBUG: EventSource readyState:', eventSource.readyState);\n                    console.error('❌ FRONTEND_DEBUG: EventSource URL:', sseUrl);\n                    clearTimeout(sseTimeout);\n                    setState({\n                        \"useRealTimeProgress.useCallback[connectToProgress]\": (prev)=>({\n                                ...prev,\n                                isConnected: false\n                            })\n                    }[\"useRealTimeProgress.useCallback[connectToProgress]\"]);\n                    eventSource.close();\n                    // Attempt retry if we have a current session and haven't exceeded max retries\n                    if (currentSessionRef.current && retryCount < maxRetries) {\n                        setRetryCount({\n                            \"useRealTimeProgress.useCallback[connectToProgress]\": (prev)=>prev + 1\n                        }[\"useRealTimeProgress.useCallback[connectToProgress]\"]);\n                        retryConnection(currentSessionRef.current, retryCount + 1);\n                    } else {\n                        // Provide more specific error messages based on readyState\n                        let errorMessage = 'SSE connection failed';\n                        if (eventSource.readyState === EventSource.CONNECTING) {\n                            errorMessage = 'Failed to establish SSE connection - check backend status';\n                        } else if (eventSource.readyState === EventSource.CLOSED) {\n                            errorMessage = 'SSE connection was closed unexpectedly';\n                        }\n                        setState({\n                            \"useRealTimeProgress.useCallback[connectToProgress]\": (prev)=>({\n                                    ...prev,\n                                    error: errorMessage\n                                })\n                        }[\"useRealTimeProgress.useCallback[connectToProgress]\"]);\n                    }\n                }\n            })[\"useRealTimeProgress.useCallback[connectToProgress]\"];\n        }\n    }[\"useRealTimeProgress.useCallback[connectToProgress]\"], []);\n    // Helper function to map backend steps to frontend display\n    const getStepInfo = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useRealTimeProgress.useCallback[getStepInfo]\": (step)=>{\n            switch(step){\n                case 'chart_analysis':\n                    return WORKFLOW_STEPS.CHART_ANALYSIS;\n                case 'symbol_detection':\n                    return WORKFLOW_STEPS.SYMBOL_DETECTION;\n                case 'tool_execution':\n                    return WORKFLOW_STEPS.TOOL_EXECUTION;\n                case 'tool_summarization':\n                    return WORKFLOW_STEPS.TOOL_SUMMARIZATION;\n                case 'rag_integration':\n                    return WORKFLOW_STEPS.RAG_INTEGRATION;\n                case 'final_analysis':\n                    return WORKFLOW_STEPS.FINAL_ANALYSIS;\n                case 'memory_storage':\n                    return WORKFLOW_STEPS.MEMORY_STORAGE;\n                case 'complete':\n                    return WORKFLOW_STEPS.COMPLETE;\n                default:\n                    return {\n                        name: 'Processing',\n                        progress: 50,\n                        message: 'Processing...'\n                    };\n            }\n        }\n    }[\"useRealTimeProgress.useCallback[getStepInfo]\"], []);\n    // Polling removed - SSE only implementation\n    const startProgressTracking = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useRealTimeProgress.useCallback[startProgressTracking]\": async (backendSessionId)=>{\n            if (backendSessionId) {\n                // Use backend session ID directly\n                await connectToProgress(backendSessionId);\n                return backendSessionId;\n            } else {\n                // Fallback: generate frontend session ID if backend doesn't provide one\n                const sessionId = \"frontend_\".concat(Date.now(), \"_\").concat(Math.random().toString(36).substring(2, 11));\n                setState({\n                    \"useRealTimeProgress.useCallback[startProgressTracking]\": (prev)=>({\n                            ...prev,\n                            sessionId,\n                            error: null\n                        })\n                }[\"useRealTimeProgress.useCallback[startProgressTracking]\"]);\n                await connectToProgress(sessionId);\n                console.log('📊 Generated fallback progress session:', sessionId);\n                return sessionId;\n            }\n        }\n    }[\"useRealTimeProgress.useCallback[startProgressTracking]\"], [\n        connectToProgress\n    ]);\n    const cleanup = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useRealTimeProgress.useCallback[cleanup]\": async (sessionId)=>{\n            // Close EventSource connection\n            if (eventSourceRef.current) {\n                eventSourceRef.current.close();\n                eventSourceRef.current = null;\n            }\n            // No polling to clear - SSE only\n            console.log('📊 Progress session cleanup completed:', sessionId || state.sessionId);\n            // Reset state\n            setState({\n                sessionId: null,\n                currentStep: 'chart_analysis',\n                progress: 0,\n                message: 'Preparing for analysis...',\n                isConnected: false,\n                isComplete: false,\n                error: null,\n                updates: []\n            });\n        }\n    }[\"useRealTimeProgress.useCallback[cleanup]\"], [\n        state.sessionId\n    ]);\n    // Cleanup on unmount\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useRealTimeProgress.useEffect\": ()=>{\n            return ({\n                \"useRealTimeProgress.useEffect\": ()=>{\n                    if (eventSourceRef.current) {\n                        eventSourceRef.current.close();\n                    }\n                }\n            })[\"useRealTimeProgress.useEffect\"];\n        }\n    }[\"useRealTimeProgress.useEffect\"], []);\n    return {\n        ...state,\n        startProgressTracking,\n        connectToProgress,\n        cleanup,\n        WORKFLOW_STEPS\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useRealTimeProgress.ts\n"));

/***/ })

});