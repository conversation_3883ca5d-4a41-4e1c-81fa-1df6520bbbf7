2025-09-29 16:29:38 | INFO     | log_session_start    | ================================================================================
2025-09-29 16:29:38 | INFO     | log_session_start    | 🚀 PROGRESS ANALYSIS SESSION STARTED
2025-09-29 16:29:38 | INFO     | log_session_start    | 📋 Session ID: async_4f169b5f15ab
2025-09-29 16:29:38 | INFO     | log_session_start    | 📁 Log File: Agent_Trading\backend\logs\progress_bar\progress_analysis_20250929_162938_async_4f169b5f15ab.log
2025-09-29 16:29:38 | INFO     | log_session_start    | ⏰ Start Time: 2025-09-29T16:29:38.971228
2025-09-29 16:29:38 | INFO     | log_session_start    | ================================================================================
2025-09-29 16:29:38 | INFO     | log_parameter_passing | 🔧 PARAMETER_PASSING | Component: WORKFLOW_INIT
2025-09-29 16:29:38 | INFO     | log_parameter_passing | ✅ PARAM_OK | session_id: str = async_4f169b5f15ab
2025-09-29 16:29:38 | INFO     | log_parameter_passing | ✅ PARAM_OK | progress_broadcaster: ProgressBroadcaster = <app.helpers.workflow_management.progress_tracker.ProgressBroadcaster object at 0x000001A0B3C22A10>
2025-09-29 16:29:38 | INFO     | log_parameter_passing | ✅ PARAM_OK | progress_broadcaster_type: str = ProgressBroadcaster
2025-09-29 16:29:39 | INFO     | log_parameter_passing | ✅ PARAM_OK | analysis_mode: str = positional
2025-09-29 16:29:39 | INFO     | log_parameter_passing | ✅ PARAM_OK | market_specialization: str = Crypto
2025-09-29 16:29:39 | INFO     | log_parameter_passing | ✅ PARAM_OK | chart_images_count: int = 1
2025-09-29 16:29:39 | INFO     | log_parameter_passing | ✅ PARAM_OK | user_query: str = Positional trading analysis for Crypto market | User tier: free - Provide comprehensive analysis wit...
2025-09-29 16:29:39 | INFO     | log_parameter_passing | 🔧 PARAMETER_PASSING | Component: WORKFLOW_VERIFY
2025-09-29 16:29:39 | INFO     | log_parameter_passing | ✅ PARAM_OK | instance_progress_broadcaster: bool = True
2025-09-29 16:29:39 | INFO     | log_parameter_passing | ✅ PARAM_OK | instance_session_id: str = async_4f169b5f15ab
2025-09-29 16:29:39 | INFO     | log_parameter_passing | ✅ PARAM_OK | instance_progress_broadcaster_type: str = ProgressBroadcaster
2025-09-29 16:29:39 | INFO     | log_sse_event        | 📡 SSE_EVENT | PROGRESS_BROADCAST_ATTEMPT - SUCCESS
2025-09-29 16:29:39 | DEBUG    | log_sse_event        |    📋 step: chart_analysis
2025-09-29 16:29:39 | DEBUG    | log_sse_event        |    📋 progress: 20
2025-09-29 16:29:39 | DEBUG    | log_sse_event        |    📋 message: 📊 Chart analysis completed
2025-09-29 16:29:39 | DEBUG    | log_sse_event        |    📋 details: {'node': 'chart_analysis'}
2025-09-29 16:29:39 | DEBUG    | log_sse_event        |    📋 session_id: async_4f169b5f15ab
2025-09-29 16:29:39 | INFO     | log_sse_event        | 📡 SSE_EVENT | PROGRESS_BROADCAST_SUCCESS - SUCCESS
2025-09-29 16:29:39 | DEBUG    | log_sse_event        |    📋 workflow_step: chart_analysis
2025-09-29 16:29:39 | DEBUG    | log_sse_event        |    📋 progress: 20
2025-09-29 16:29:39 | DEBUG    | log_sse_event        |    📋 message: 📊 Chart analysis completed
2025-09-29 16:29:39 | DEBUG    | log_sse_event        |    📋 session_id: async_4f169b5f15ab
