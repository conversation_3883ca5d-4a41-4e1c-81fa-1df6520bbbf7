"""
Unified Logging System for Agent Trading
=======================================

Centralized logging configuration that consolidates all scattered logging
into a single, organized structure with consistent formatting and management.

Features:
- Unified log directory structure
- Automatic log rotation and cleanup
- Session-specific progress tracking
- Structured JSON logging for production
- Easy debugging with contextual information
"""

import os
import json
import logging
import logging.handlers
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, Any, Optional
from enum import Enum
import uuid

class LogCategory(Enum):
    """Log categories for organized logging."""
    APPLICATION = "application"
    ANALYSIS = "analysis" 
    PROGRESS = "progress"
    API = "api"
    ERROR = "error"

class UnifiedLogger:
    """Centralized logging system with organized structure."""
    
    def __init__(self, base_log_dir: str = "Agent_Trading/backend/logs"):
        """Initialize unified logging system."""
        self.base_log_dir = Path(base_log_dir)
        self.session_loggers: Dict[str, logging.Logger] = {}
        self._setup_directory_structure()
        self._setup_base_loggers()
    
    def _setup_directory_structure(self):
        """Create organized log directory structure."""
        directories = [
            self.base_log_dir / "application",
            self.base_log_dir / "analysis" / "workflows",
            self.base_log_dir / "analysis" / "llm_interactions", 
            self.base_log_dir / "progress" / "sessions",
            self.base_log_dir / "api",
            self.base_log_dir / "errors",
            self.base_log_dir / "archive"
        ]
        
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)
    
    def _setup_base_loggers(self):
        """Setup base loggers for each category."""
        # Application logger
        self.app_logger = self._create_logger(
            "application",
            self.base_log_dir / "application" / "app.log",
            level=logging.INFO
        )
        
        # Analysis logger
        self.analysis_logger = self._create_logger(
            "analysis", 
            self.base_log_dir / "analysis" / "workflows" / "analysis.log",
            level=logging.DEBUG
        )
        
        # Progress logger
        self.progress_logger = self._create_logger(
            "progress",
            self.base_log_dir / "progress" / "progress.log", 
            level=logging.DEBUG
        )
        
        # API logger
        self.api_logger = self._create_logger(
            "api",
            self.base_log_dir / "api" / "requests.log",
            level=logging.INFO
        )
        
        # Error logger
        self.error_logger = self._create_logger(
            "errors",
            self.base_log_dir / "errors" / "errors.log",
            level=logging.ERROR
        )
    
    def _create_logger(self, name: str, log_file: Path, level: int = logging.INFO) -> logging.Logger:
        """Create a logger with file rotation."""
        logger = logging.getLogger(f"unified.{name}")
        logger.setLevel(level)
        
        # Remove existing handlers to avoid duplicates
        for handler in logger.handlers[:]:
            logger.removeHandler(handler)
        
        # File handler with rotation
        file_handler = logging.handlers.RotatingFileHandler(
            log_file,
            maxBytes=10*1024*1024,  # 10MB
            backupCount=5,
            encoding='utf-8'
        )
        file_handler.setLevel(level)
        
        # Formatter
        formatter = logging.Formatter(
            '%(asctime)s | %(levelname)-8s | %(name)-20s | %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        file_handler.setFormatter(formatter)
        
        logger.addHandler(file_handler)
        logger.propagate = False
        
        return logger
    
    def create_session_logger(self, session_id: str) -> logging.Logger:
        """Create session-specific logger for progress tracking."""
        if session_id in self.session_loggers:
            return self.session_loggers[session_id]
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        log_file = self.base_log_dir / "progress" / "sessions" / f"session_{timestamp}_{session_id}.log"
        
        logger = self._create_logger(f"session.{session_id}", log_file, logging.DEBUG)
        self.session_loggers[session_id] = logger
        
        # Log session start
        logger.info(f"=== SESSION STARTED: {session_id} ===")
        logger.info(f"Timestamp: {datetime.now().isoformat()}")
        
        return logger
    
    def log_application(self, level: str, message: str, **kwargs):
        """Log application events."""
        getattr(self.app_logger, level.lower())(self._format_message(message, kwargs))
    
    def log_analysis(self, level: str, message: str, **kwargs):
        """Log analysis workflow events."""
        getattr(self.analysis_logger, level.lower())(self._format_message(message, kwargs))
    
    def log_progress(self, session_id: str, step: str, progress: int, message: str, **kwargs):
        """Log progress tracking events."""
        session_logger = self.create_session_logger(session_id)
        
        log_message = f"PROGRESS | {step} ({progress}%) - {message}"
        if kwargs:
            log_message += f" | {self._format_kwargs(kwargs)}"
        
        session_logger.info(log_message)
        self.progress_logger.info(f"[{session_id}] {log_message}")
    
    def log_api(self, method: str, path: str, status_code: int, execution_time: float, **kwargs):
        """Log API requests."""
        message = f"{method} {path} - {status_code} ({execution_time:.3f}s)"
        if kwargs:
            message += f" | {self._format_kwargs(kwargs)}"
        
        self.api_logger.info(message)
    
    def log_error(self, error: Exception, context: Dict[str, Any] = None):
        """Log errors with context."""
        import traceback
        
        error_data = {
            "error_type": type(error).__name__,
            "error_message": str(error),
            "traceback": traceback.format_exc(),
            "context": context or {}
        }
        
        self.error_logger.error(self._format_message("ERROR OCCURRED", error_data))
    
    def log_llm_interaction(self, model: str, prompt_tokens: int, completion_tokens: int, **kwargs):
        """Log LLM API interactions."""
        llm_file = self.base_log_dir / "analysis" / "llm_interactions" / "llm_calls.jsonl"
        
        interaction_data = {
            "timestamp": datetime.now().isoformat(),
            "model": model,
            "prompt_tokens": prompt_tokens,
            "completion_tokens": completion_tokens,
            **kwargs
        }
        
        with open(llm_file, 'a', encoding='utf-8') as f:
            f.write(json.dumps(interaction_data) + '\n')
    
    def log_tool_execution(self, tool_name: str, execution_time: float, success: bool, **kwargs):
        """Log tool execution events."""
        tool_file = self.base_log_dir / "analysis" / "tool_calls.jsonl"
        
        tool_data = {
            "timestamp": datetime.now().isoformat(),
            "tool_name": tool_name,
            "execution_time": execution_time,
            "success": success,
            **kwargs
        }
        
        with open(tool_file, 'a', encoding='utf-8') as f:
            f.write(json.dumps(tool_data) + '\n')
    
    def _format_message(self, message: str, data: Dict[str, Any]) -> str:
        """Format log message with structured data."""
        if not data:
            return message
        
        return f"{message} | {self._format_kwargs(data)}"
    
    def _format_kwargs(self, kwargs: Dict[str, Any]) -> str:
        """Format kwargs for logging."""
        formatted_parts = []
        for key, value in kwargs.items():
            if isinstance(value, (dict, list)):
                value = json.dumps(value, default=str)
            formatted_parts.append(f"{key}={value}")
        
        return " | ".join(formatted_parts)
    
    def cleanup_old_logs(self, days_to_keep: int = 30):
        """Clean up old log files."""
        cutoff_date = datetime.now() - timedelta(days=days_to_keep)
        archive_dir = self.base_log_dir / "archive"
        
        # Find old log files
        for log_file in self.base_log_dir.rglob("*.log"):
            if log_file.parent.name == "archive":
                continue
                
            try:
                file_time = datetime.fromtimestamp(log_file.stat().st_mtime)
                if file_time < cutoff_date:
                    # Move to archive
                    archive_file = archive_dir / f"{file_time.strftime('%Y%m%d')}_{log_file.name}"
                    log_file.rename(archive_file)
                    self.app_logger.info(f"Archived old log file: {log_file.name}")
            except Exception as e:
                self.error_logger.error(f"Failed to archive log file {log_file}: {e}")
    
    def get_session_summary(self, session_id: str) -> Dict[str, Any]:
        """Get summary of session logs."""
        if session_id not in self.session_loggers:
            return {"error": "Session not found"}
        
        session_file = None
        for log_file in (self.base_log_dir / "progress" / "sessions").glob(f"*{session_id}.log"):
            session_file = log_file
            break
        
        if not session_file or not session_file.exists():
            return {"error": "Session log file not found"}
        
        try:
            with open(session_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            return {
                "session_id": session_id,
                "log_file": str(session_file),
                "total_lines": len(lines),
                "start_time": lines[0].split('|')[0].strip() if lines else None,
                "end_time": lines[-1].split('|')[0].strip() if lines else None,
                "progress_updates": len([line for line in lines if "PROGRESS |" in line])
            }
        except Exception as e:
            return {"error": f"Failed to read session log: {e}"}

# Global unified logger instance
_unified_logger = None

def get_unified_logger() -> UnifiedLogger:
    """Get the global unified logger instance."""
    global _unified_logger
    if _unified_logger is None:
        _unified_logger = UnifiedLogger()
    return _unified_logger

def setup_unified_logging():
    """Setup unified logging system."""
    logger = get_unified_logger()
    logger.app_logger.info("=== UNIFIED LOGGING SYSTEM INITIALIZED ===")
    logger.app_logger.info(f"Log directory: {logger.base_log_dir}")
    return logger
