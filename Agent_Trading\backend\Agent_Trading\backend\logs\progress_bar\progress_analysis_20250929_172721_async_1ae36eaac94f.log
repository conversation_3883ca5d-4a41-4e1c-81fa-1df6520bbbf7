2025-09-29 17:27:21 | INFO     | log_session_start    | ================================================================================
2025-09-29 17:27:21 | INFO     | log_session_start    | 🚀 PROGRESS ANALYSIS SESSION STARTED
2025-09-29 17:27:21 | INFO     | log_session_start    | 📋 Session ID: async_1ae36eaac94f
2025-09-29 17:27:21 | INFO     | log_session_start    | 📁 Log File: Agent_Trading\backend\logs\progress_bar\progress_analysis_20250929_172721_async_1ae36eaac94f.log
2025-09-29 17:27:21 | INFO     | log_session_start    | ⏰ Start Time: 2025-09-29T17:27:21.805677
2025-09-29 17:27:21 | INFO     | log_session_start    | ================================================================================
2025-09-29 17:27:21 | INFO     | log_parameter_passing | 🔧 PARAMETER_PASSING | Component: ASYNC_SERVICE_START
2025-09-29 17:27:21 | INFO     | log_parameter_passing | ✅ PARAM_OK | session_id: str = async_1ae36eaac94f
2025-09-29 17:27:21 | INFO     | log_parameter_passing | ✅ PARAM_OK | user_id: str = 9fbc7a39-ae9c-455a-ad81-53ed905e004b
2025-09-29 17:27:21 | INFO     | log_parameter_passing | ✅ PARAM_OK | user_email: str = <EMAIL>
2025-09-29 17:27:21 | INFO     | log_parameter_passing | ✅ PARAM_OK | user_tier: str = free
2025-09-29 17:27:21 | INFO     | log_parameter_passing | ✅ PARAM_OK | analysis_type: str = Positional
2025-09-29 17:27:21 | INFO     | log_parameter_passing | ✅ PARAM_OK | market_specialization: str = Crypto
2025-09-29 17:27:21 | INFO     | log_parameter_passing | ✅ PARAM_OK | preferred_model: str = gemini-2.5-flash
2025-09-29 17:27:21 | INFO     | log_parameter_passing | ✅ PARAM_OK | image_count: int = 1
2025-09-29 17:27:21 | INFO     | log_parameter_passing | ✅ PARAM_OK | progress_tracker: bool = True
2025-09-29 17:27:21 | INFO     | log_parameter_passing | ✅ PARAM_OK | progress_tracker_type: str = WorkflowProgressTracker
2025-09-29 17:27:21 | INFO     | log_parameter_passing | ✅ PARAM_OK | progress_broadcaster: bool = True
2025-09-29 17:27:21 | INFO     | log_parameter_passing | ✅ PARAM_OK | progress_broadcaster_type: str = ProgressBroadcaster
2025-09-29 17:27:24 | INFO     | log_parameter_passing | 🔧 PARAMETER_PASSING | Component: TRADING_SERVICE_CALL
2025-09-29 17:27:24 | INFO     | log_parameter_passing | ✅ PARAM_OK | user_id: str = 9fbc7a39-ae9c-455a-ad81-53ed905e004b
2025-09-29 17:27:24 | INFO     | log_parameter_passing | ✅ PARAM_OK | chart_images_count: int = 1
2025-09-29 17:27:24 | INFO     | log_parameter_passing | ✅ PARAM_OK | analysis_type: str = Positional
2025-09-29 17:27:24 | INFO     | log_parameter_passing | ✅ PARAM_OK | market_specialization: str = Crypto
2025-09-29 17:27:24 | INFO     | log_parameter_passing | ✅ PARAM_OK | preferred_model: str = gemini-2.5-flash
2025-09-29 17:27:24 | INFO     | log_parameter_passing | ✅ PARAM_OK | session_id: str = async_1ae36eaac94f
2025-09-29 17:27:24 | INFO     | log_parameter_passing | ✅ PARAM_OK | progress_tracker: bool = True
2025-09-29 17:27:24 | INFO     | log_parameter_passing | ✅ PARAM_OK | progress_broadcaster: bool = True
2025-09-29 17:27:24 | INFO     | log_parameter_passing | ✅ PARAM_OK | progress_tracker_id: int = 1682378139024
2025-09-29 17:27:24 | INFO     | log_parameter_passing | ✅ PARAM_OK | progress_broadcaster_id: int = 1682378140816
2025-09-29 17:27:24 | INFO     | log_parameter_passing | 🔧 PARAMETER_PASSING | Component: ENHANCED_LANGGRAPH_SERVICE
2025-09-29 17:27:24 | INFO     | log_parameter_passing | ✅ PARAM_OK | session_id: str = async_1ae36eaac94f
2025-09-29 17:27:24 | INFO     | log_parameter_passing | ✅ PARAM_OK | progress_tracker: bool = True
2025-09-29 17:27:24 | INFO     | log_parameter_passing | ✅ PARAM_OK | progress_tracker_type: str = WorkflowProgressTracker
2025-09-29 17:27:24 | INFO     | log_parameter_passing | ✅ PARAM_OK | progress_tracker_id: int = 1682378139024
2025-09-29 17:27:24 | INFO     | log_parameter_passing | ✅ PARAM_OK | progress_broadcaster: bool = True
2025-09-29 17:27:24 | INFO     | log_parameter_passing | ✅ PARAM_OK | progress_broadcaster_type: str = ProgressBroadcaster
2025-09-29 17:27:24 | INFO     | log_parameter_passing | ✅ PARAM_OK | progress_broadcaster_id: int = 1682378140816
2025-09-29 17:27:24 | INFO     | log_parameter_passing | ✅ PARAM_OK | user_id: str = 9fbc7a39-ae9c-455a-ad81-53ed905e004b
2025-09-29 17:27:24 | INFO     | log_parameter_passing | ✅ PARAM_OK | user_tier: str = free
2025-09-29 17:27:24 | INFO     | log_parameter_passing | ✅ PARAM_OK | analysis_mode: str = positional
2025-09-29 17:27:24 | INFO     | log_parameter_passing | ✅ PARAM_OK | market_specialization: str = Crypto
2025-09-29 17:27:24 | INFO     | log_parameter_passing | 🔧 PARAMETER_PASSING | Component: WORKFLOW_ANALYZE_CHART_CALL
2025-09-29 17:27:24 | INFO     | log_parameter_passing | ✅ PARAM_OK | chart_images_count: int = 1
2025-09-29 17:27:24 | INFO     | log_parameter_passing | ✅ PARAM_OK | analysis_mode: str = positional
2025-09-29 17:27:24 | INFO     | log_parameter_passing | ✅ PARAM_OK | user_query_length: int = 169
2025-09-29 17:27:24 | INFO     | log_parameter_passing | ✅ PARAM_OK | market_specialization: str = Crypto
2025-09-29 17:27:24 | INFO     | log_parameter_passing | ✅ PARAM_OK | progress_broadcaster: bool = True
2025-09-29 17:27:24 | INFO     | log_parameter_passing | ✅ PARAM_OK | progress_broadcaster_id: int = 1682378140816
2025-09-29 17:27:24 | INFO     | log_parameter_passing | ✅ PARAM_OK | session_id: str = async_1ae36eaac94f
2025-09-29 17:27:24 | INFO     | log_parameter_passing | ✅ PARAM_OK | workflow_instance_id: int = 1681498877392
2025-09-29 17:27:24 | INFO     | log_session_end      | ================================================================================
2025-09-29 17:27:24 | INFO     | log_session_end      | 🏁 PROGRESS ANALYSIS SESSION ENDED
2025-09-29 17:27:24 | INFO     | log_session_end      | 📋 Session ID: async_1ae36eaac94f
2025-09-29 17:27:24 | INFO     | log_session_end      | ⏰ Duration: 2.90 seconds
2025-09-29 17:27:24 | INFO     | log_session_end      | 📊 Workflow Steps: 0
2025-09-29 17:27:24 | INFO     | log_session_end      | 📡 SSE Events: 0
2025-09-29 17:27:24 | INFO     | log_session_end      | ❌ Errors: 0
2025-09-29 17:27:24 | INFO     | log_session_end      | 🎯 Status: replaced
2025-09-29 17:27:24 | INFO     | log_session_end      | ================================================================================
2025-09-29 17:27:24 | INFO     | log_session_end      | 💾 Session summary saved: Agent_Trading\backend\logs\progress_bar\progress_analysis_20250929_172721_async_1ae36eaac94f.json
