{"workflow_id": "trading_analysis_1759091901", "start_time": "2025-09-29T02:08:21.765168", "context": {"analysis_mode": "positional", "market_specialization": "Crypto", "chart_count": 1}, "steps": [{"step_number": 1, "step_name": "Chart Analysis", "step_type": "llm_call", "timestamp": "2025-09-29T02:08:21.783524", "execution_time": null, "status": "success", "error": null, "input_summary": "Chart image for symbol detection"}, {"step_number": 2, "step_name": "Step 2: Tool Selection", "step_type": "api_calls", "timestamp": "2025-09-29T02:08:22.356791", "execution_time": 4.078574180603027, "status": "success", "error": null, "output_summary": "Dict with keys: ['success', 'tool_results', 'tool_usage_log', 'execution_time']"}, {"step_number": 3, "step_name": "Step 3: Flash Summarization", "step_type": "llm_call", "timestamp": "2025-09-29T02:08:22.366611", "execution_time": null, "status": "success", "error": null, "input_summary": "Tool results for summarization"}, {"step_number": 4, "step_name": "Chart Analysis", "step_type": "llm_call", "timestamp": "2025-09-29T02:08:27.584127", "execution_time": 5.800603866577148, "status": "success", "error": null, "output_summary": "Dict with keys: ['detected_symbol', 'market_type', 'timeframe', 'current_price', 'trend_direction']..."}, {"step_number": 5, "step_name": "Step 2: Tool Selection", "step_type": "api_calls", "timestamp": "2025-09-29T02:08:27.590188", "execution_time": null, "status": "success", "error": null, "input_summary": "Market: N/A - Not a trading chart, Symbol: N/A - Not a trading chart"}, {"step_number": 6, "step_name": "Step 2: Tool Selection", "step_type": "api_calls", "timestamp": "2025-09-29T02:08:30.813374", "execution_time": 3.2231860160827637, "status": "success", "error": null, "output_summary": "Dict with keys: ['success', 'tool_results', 'tool_usage_log', 'execution_time']"}, {"step_number": 7, "step_name": "Step 3: Flash Summarization", "step_type": "llm_call", "timestamp": "2025-09-29T02:08:30.824207", "execution_time": null, "status": "success", "error": null, "input_summary": "Tool results for summarization"}, {"step_number": 8, "step_name": "Step 3: Flash Summarization", "step_type": "llm_call", "timestamp": "2025-09-29T02:08:38.153917", "execution_time": 15.787305355072021, "status": "success", "error": null, "output_summary": "Dict with keys: ['success', 'summarized_tools', 'tool_count', 'comprehensive_summary']"}, {"step_number": 9, "step_name": "Step 4: Final Analysis", "step_type": "llm_call", "timestamp": "2025-09-29T02:08:44.407286", "execution_time": null, "status": "success", "error": null, "input_summary": "Chart + Tool summaries + RAG tool available"}, {"step_number": 10, "step_name": "Step 3: Flash Summarization", "step_type": "llm_call", "timestamp": "2025-09-29T02:08:46.613857", "execution_time": 15.789650201797485, "status": "success", "error": null, "output_summary": "Dict with keys: ['success', 'summarized_tools', 'tool_count', 'comprehensive_summary']"}, {"step_number": 11, "step_name": "Step 4: Final Analysis", "step_type": "llm_call", "timestamp": "2025-09-29T02:08:47.010384", "execution_time": null, "status": "success", "error": null, "input_summary": "Chart + Tool summaries + RAG tool available"}, {"step_number": 12, "step_name": "Final Analysis", "step_type": "llm_call", "timestamp": "2025-09-29T02:08:57.279032", "execution_time": 12.871746063232422, "status": "success", "error": null, "output_summary": "Dict with keys: ['success', 'analysis', 'analysis_notes', 'trading_signals', 'status']..."}], "llm_calls": [], "summary": {"total_steps": 12, "total_llm_calls": 0, "total_tokens": 0, "total_cost": 0.0, "execution_time": 35.518874168395996}, "end_time": "2025-09-29T02:08:57.284042", "status": "success", "error": null}