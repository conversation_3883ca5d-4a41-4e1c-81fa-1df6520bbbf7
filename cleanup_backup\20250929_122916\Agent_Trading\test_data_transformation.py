#!/usr/bin/env python3
"""
Test script to validate the data transformation fixes for the Agent Trading application.
This script tests the complete flow from backend response to frontend display.
"""

import json
import requests
import time
import sys
import os

# Add backend to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

def test_backend_response_structure():
    """Test the actual backend response structure"""
    print("🔍 Testing Backend Response Structure")
    print("=" * 50)
    
    # Test the async result endpoint
    try:
        # Use a recent session ID from logs
        test_session_id = "async_82d95fa09413"  # From the user's logs
        
        response = requests.get(f"http://localhost:8000/api/v1/trading/async/result/{test_session_id}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Response Status: {response.status_code}")
            print(f"📊 Response Keys: {list(data.keys())}")
            
            if 'data' in data:
                print(f"📊 Data Keys: {list(data['data'].keys())}")
                
                if 'data' in data['data']:
                    print(f"📊 Data.Data Keys: {list(data['data']['data'].keys())}")
                    
                    # Check for key fields
                    lang_graph_data = data['data']['data']
                    print(f"🔍 LangGraph Data Analysis:")
                    print(f"  - Has analysis_notes: {'analysis_notes' in lang_graph_data}")
                    print(f"  - Has trading_signals: {'trading_signals' in lang_graph_data}")
                    print(f"  - Has trade_ideas: {'trade_ideas' in lang_graph_data}")
                    print(f"  - Has detected_symbol: {'detected_symbol' in lang_graph_data}")
                    print(f"  - Has chart_patterns: {'chart_patterns' in lang_graph_data}")
                    
                    if 'analysis_notes' in lang_graph_data:
                        analysis_notes = lang_graph_data['analysis_notes']
                        print(f"  - analysis_notes type: {type(analysis_notes)}")
                        if isinstance(analysis_notes, str) and analysis_notes.strip().startswith('{'):
                            print("  - analysis_notes appears to be JSON string")
                        elif isinstance(analysis_notes, dict):
                            print(f"  - analysis_notes keys: {list(analysis_notes.keys())}")
                    
                    if 'trading_signals' in lang_graph_data:
                        trading_signals = lang_graph_data['trading_signals']
                        print(f"  - trading_signals type: {type(trading_signals)}")
                        if isinstance(trading_signals, dict):
                            print(f"  - trading_signals keys: {list(trading_signals.keys())}")
                    
                    if 'trade_ideas' in lang_graph_data:
                        trade_ideas = lang_graph_data['trade_ideas']
                        print(f"  - trade_ideas type: {type(trade_ideas)}")
                        print(f"  - trade_ideas length: {len(trade_ideas) if isinstance(trade_ideas, list) else 'not a list'}")
                        
            return data
        else:
            print(f"❌ Response Status: {response.status_code}")
            print(f"❌ Response: {response.text}")
            return None
            
    except requests.exceptions.ConnectionError:
        print("❌ Backend server is not running. Please start the backend first.")
        return None
    except Exception as e:
        print(f"❌ Error testing backend: {e}")
        return None

def test_sse_connection():
    """Test SSE connection for progress updates"""
    print("\n🔍 Testing SSE Connection")
    print("=" * 50)
    
    try:
        # Test SSE endpoint
        test_session_id = "test_session_123"
        
        response = requests.get(
            f"http://localhost:8000/api/v1/progress/stream/{test_session_id}",
            stream=True,
            timeout=5
        )
        
        if response.status_code == 200:
            print("✅ SSE endpoint is accessible")
            print(f"📊 Response headers: {dict(response.headers)}")
            
            # Read a few lines
            lines_read = 0
            for line in response.iter_lines(decode_unicode=True):
                if line and lines_read < 3:
                    print(f"📡 SSE Line: {line}")
                    lines_read += 1
                elif lines_read >= 3:
                    break
                    
        else:
            print(f"❌ SSE endpoint error: {response.status_code}")
            
    except requests.exceptions.Timeout:
        print("⚠️ SSE connection timeout (expected for test)")
    except requests.exceptions.ConnectionError:
        print("❌ Backend server is not running")
    except Exception as e:
        print(f"❌ Error testing SSE: {e}")

def test_progress_tracker():
    """Test the progress tracker functionality"""
    print("\n🔍 Testing Progress Tracker")
    print("=" * 50)
    
    try:
        from app.helpers.workflow_management.progress_tracker import (
            WorkflowProgressTracker,
            ProgressBroadcaster,
            WorkflowStep
        )
        
        # Test WorkflowProgressTracker
        tracker = WorkflowProgressTracker()
        session_id = tracker.create_session()
        print(f"✅ Created session: {session_id}")
        
        # Test progress updates
        update = tracker.update_progress(
            session_id,
            WorkflowStep.CHART_UPLOAD,
            "Testing chart upload",
            details={"test": True}
        )
        print(f"✅ Progress update: {update.step.value} - {update.progress}%")
        
        # Test all workflow steps
        steps = [
            (WorkflowStep.CHART_UPLOAD, "Chart uploaded"),
            (WorkflowStep.SYMBOL_DETECTION, "Symbol detected"),
            (WorkflowStep.TOOL_SELECTION, "Tools selected"),
            (WorkflowStep.TOOL_EXECUTION, "Tools executing"),
            (WorkflowStep.TOOL_SUMMARIZATION, "Tools summarized"),
            (WorkflowStep.FINAL_ANALYSIS, "Final analysis"),
            (WorkflowStep.COMPLETE, "Complete")
        ]
        
        for step, message in steps:
            update = tracker.update_progress(session_id, step, message)
            print(f"  📊 {step.value}: {update.progress}% - {message}")
        
        print("✅ Progress tracker working correctly")
        return True
        
    except Exception as e:
        print(f"❌ Error testing progress tracker: {e}")
        return False

def main():
    """Main test function"""
    print("🚀 Agent Trading Data Transformation Test")
    print("=" * 60)
    
    # Test 1: Backend Response Structure
    backend_data = test_backend_response_structure()
    
    # Test 2: SSE Connection
    test_sse_connection()
    
    # Test 3: Progress Tracker
    progress_working = test_progress_tracker()
    
    # Summary
    print("\n📋 Test Summary")
    print("=" * 50)
    print(f"✅ Backend Response: {'✅ Working' if backend_data else '❌ Failed'}")
    print(f"✅ Progress Tracker: {'✅ Working' if progress_working else '❌ Failed'}")
    
    if backend_data:
        print("\n🎯 Key Findings:")
        print("- Backend is returning data in response.data.data structure")
        print("- Frontend should use response.data.data (not response.data.data.data)")
        print("- Data transformation needs to handle various field formats")
        
    print("\n🔧 Next Steps:")
    print("1. Start the frontend and test the complete flow")
    print("2. Upload a chart and monitor console logs")
    print("3. Verify SSE progress updates are working")
    print("4. Check that analysis results display correctly")

if __name__ == "__main__":
    main()
