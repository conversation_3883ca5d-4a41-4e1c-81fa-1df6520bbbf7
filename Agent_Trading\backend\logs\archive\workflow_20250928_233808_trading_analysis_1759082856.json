{"workflow_id": "trading_analysis_1759082856", "start_time": "2025-09-28T23:37:36.976402", "context": {"analysis_mode": "positional", "market_specialization": "Crypto", "chart_count": 1}, "steps": [{"step_number": 1, "step_name": "Chart Analysis", "step_type": "llm_call", "timestamp": "2025-09-28T23:37:37.072192", "execution_time": null, "status": "success", "error": null, "input_summary": "Chart image for symbol detection"}, {"step_number": 2, "step_name": "Step 4: Final Analysis", "step_type": "llm_call", "timestamp": "2025-09-28T23:37:44.041124", "execution_time": null, "status": "success", "error": null, "input_summary": "Chart + Tool summaries + RAG tool available"}, {"step_number": 3, "step_name": "Chart Analysis", "step_type": "llm_call", "timestamp": "2025-09-28T23:37:48.347846", "execution_time": 11.273708581924438, "status": "success", "error": null, "output_summary": "Dict with keys: ['detected_symbol', 'market_type', 'timeframe', 'current_price', 'trend_direction']..."}, {"step_number": 4, "step_name": "Step 2: Tool Selection", "step_type": "api_calls", "timestamp": "2025-09-28T23:37:48.402291", "execution_time": null, "status": "success", "error": null, "input_summary": "Market: crypto, Symbol: BTCUSD"}, {"step_number": 5, "step_name": "Step 2: Tool Selection", "step_type": "api_calls", "timestamp": "2025-09-28T23:37:52.614309", "execution_time": 4.212018251419067, "status": "success", "error": null, "output_summary": "Dict with keys: ['success', 'tool_results', 'tool_usage_log', 'execution_time']"}, {"step_number": 6, "step_name": "Step 3: Flash Summarization", "step_type": "llm_call", "timestamp": "2025-09-28T23:37:52.653546", "execution_time": null, "status": "success", "error": null, "input_summary": "Tool results for summarization"}, {"step_number": 7, "step_name": "Step 3: Flash Summarization", "step_type": "llm_call", "timestamp": "2025-09-28T23:38:05.145891", "execution_time": 12.491248607635498, "status": "success", "error": null, "output_summary": "Dict with keys: ['success', 'summarized_tools', 'tool_count', 'comprehensive_summary']"}, {"step_number": 8, "step_name": "Final Analysis", "step_type": "llm_call", "timestamp": "2025-09-28T23:38:07.848033", "execution_time": 23.802809715270996, "status": "success", "error": null, "output_summary": "Dict with keys: ['success', 'analysis', 'analysis_notes', 'trading_signals', 'status']..."}], "llm_calls": [], "summary": {"total_steps": 8, "total_llm_calls": 0, "total_tokens": 0, "total_cost": 0.0, "execution_time": 31.038848876953125}, "end_time": "2025-09-28T23:38:08.015250", "status": "success", "error": null}