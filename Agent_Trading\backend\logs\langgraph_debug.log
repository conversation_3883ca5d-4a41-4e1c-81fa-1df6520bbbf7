# Progress Debug Log
# This file will capture all progress-related events for debugging
# Format: [TIMESTAMP] [LEVEL] [COMPONENT] MESSAGE

[2025-09-28 17:23:32] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Broadcasting update for session async_a4648a305b64 | Data: {'step': 'chart_upload', 'progress': 5, 'message': 'Analysis request received, starting background processing...', 'has_clients': False, 'client_count': 0}
[2025-09-28 17:23:32] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | No clients found for session async_a4648a305b64 | Data: {}
[2025-09-28 17:23:32] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Broadcasting update for session async_a4648a305b64 | Data: {'step': 'chart_upload', 'progress': 5, 'message': '📊 Processing uploaded chart images...', 'has_clients': False, 'client_count': 0}
[2025-09-28 17:23:32] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | No clients found for session async_a4648a305b64 | Data: {}
[2025-09-28 17:23:32] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Broadcasting update for session async_a4648a305b64 | Data: {'step': 'chart_upload', 'progress': 5, 'message': '🖼️ Processed chart image 1/1 (83KB)', 'has_clients': False, 'client_count': 0}
[2025-09-28 17:23:32] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | No clients found for session async_a4648a305b64 | Data: {}
[2025-09-28 17:23:32] [INFO] [progress_debug] 🔗 SSE_CONNECTION | Session: async_a4648a305b64 | Client: {'user_id': UUID('cd218226-a7f5-4bd8-a242-eadc1d480326'), 'user_email': '<EMAIL>', 'endpoint': '/api/v1/progress/stream/async_a4648a305b64'}
[2025-09-28 17:23:32] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Adding client for session async_a4648a305b64 | Data: {}
[2025-09-28 17:23:32] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Added client for session async_a4648a305b64 | Data: {'total_clients': 1, 'all_sessions': ['async_a4648a305b64']}
[2025-09-28 17:23:32] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Client queue created for session async_a4648a305b64 | Data: {}
[2025-09-28 17:23:32] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending connection confirmation: {'type': 'connected', 'session_id': 'async_a4648a305b64'} | Data: {}
[2025-09-28 17:23:32] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_a4648a305b64 | Data: {}
[2025-09-28 17:23:33] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Broadcasting update for session async_a4648a305b64 | Data: {'step': 'symbol_detection', 'progress': 40, 'message': '🔍 Analyzing chart content and detecting symbols...', 'has_clients': True, 'client_count': 1}
[2025-09-28 17:23:33] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Sent update to client 0 for session async_a4648a305b64 | Data: {}
[2025-09-28 17:23:33] [INFO] [progress_debug] 🔄 SESSION_LIFECYCLE | Session: unknown | Event: workflow_start | Details: {'analysis_mode': 'positional', 'market_specialization': 'Crypto', 'has_progress_broadcaster': False, 'chart_images_count': 1}
[2025-09-28 17:23:33] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Received progress update: {'step': 'symbol_detection', 'progress': 40, 'message': '🔍 Analyzing chart content and detecting symbols...', 'timestamp': '2025-09-28T11:53:33.260464', 'details': {'stage': 'analysis_start'}, 'tool_results': None} | Data: {}
[2025-09-28 17:23:33] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending SSE event: {'type': 'progress_update', 'data': {'step': 'symbol_detection', 'progress': 40, 'message': '🔍 Analyzing chart content and detecting symbols...', 'timestamp': '2025-09-28T11:53:33.260464', 'details': {'stage': 'analysis_start'}, 'tool_results': None}} | Data: {}
[2025-09-28 17:23:33] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_a4648a305b64 | Data: {}
[2025-09-28 17:24:03] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 168358.671} | Data: {}
[2025-09-28 17:24:03] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_a4648a305b64 | Data: {}
[2025-09-28 17:24:33] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 168388.656} | Data: {}
[2025-09-28 17:24:33] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_a4648a305b64 | Data: {}
[2025-09-28 17:24:49] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Broadcasting update for session async_a4648a305b64 | Data: {'step': 'complete', 'progress': 100, 'message': '🎉 Analysis completed successfully!', 'has_clients': True, 'client_count': 1}
[2025-09-28 17:24:49] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Sent update to client 0 for session async_a4648a305b64 | Data: {}
[2025-09-28 17:24:49] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Received progress update: {'step': 'complete', 'progress': 100, 'message': '🎉 Analysis completed successfully!', 'timestamp': '2025-09-28T11:54:49.180053', 'details': {'execution_time': 75.90758776664734, 'result_available': True}, 'tool_results': None} | Data: {}
[2025-09-28 17:24:49] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending SSE event: {'type': 'progress_update', 'data': {'step': 'complete', 'progress': 100, 'message': '🎉 Analysis completed successfully!', 'timestamp': '2025-09-28T11:54:49.180053', 'details': {'execution_time': 75.90758776664734, 'result_available': True}, 'tool_results': None}} | Data: {}
[2025-09-28 17:24:49] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Workflow complete, sending: {'type': 'complete', 'session_id': 'async_a4648a305b64'} | Data: {}
[2025-09-28 17:24:49] [INFO] [progress_debug] 🚫 SSE_DISCONNECT | Session: async_a4648a305b64 | Reason: event_generator_cleanup
[2025-09-28 19:18:04] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Broadcasting update for session async_c79237becd64 | Data: {'step': 'chart_upload', 'progress': 5, 'message': 'Analysis request received, starting background processing...', 'has_clients': False, 'client_count': 0}
[2025-09-28 19:18:04] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | No clients found for session async_c79237becd64 | Data: {}
[2025-09-28 19:18:04] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Broadcasting update for session async_c79237becd64 | Data: {'step': 'chart_upload', 'progress': 5, 'message': '📊 Processing uploaded chart images...', 'has_clients': False, 'client_count': 0}
[2025-09-28 19:18:04] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | No clients found for session async_c79237becd64 | Data: {}
[2025-09-28 19:18:04] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Broadcasting update for session async_c79237becd64 | Data: {'step': 'chart_upload', 'progress': 5, 'message': '🖼️ Processed chart image 1/1 (75KB)', 'has_clients': False, 'client_count': 0}
[2025-09-28 19:18:04] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | No clients found for session async_c79237becd64 | Data: {}
[2025-09-28 19:18:04] [INFO] [progress_debug] 🔗 SSE_CONNECTION | Session: async_c79237becd64 | Client: {'user_id': UUID('cd218226-a7f5-4bd8-a242-eadc1d480326'), 'user_email': '<EMAIL>', 'endpoint': '/api/v1/progress/stream/async_c79237becd64'}
[2025-09-28 19:18:04] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Adding client for session async_c79237becd64 | Data: {}
[2025-09-28 19:18:04] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Added client for session async_c79237becd64 | Data: {'total_clients': 1, 'all_sessions': ['async_c79237becd64']}
[2025-09-28 19:18:04] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Client queue created for session async_c79237becd64 | Data: {}
[2025-09-28 19:18:04] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending connection confirmation: {'type': 'connected', 'session_id': 'async_c79237becd64'} | Data: {}
[2025-09-28 19:18:04] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_c79237becd64 | Data: {}
[2025-09-28 19:18:05] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Broadcasting update for session async_c79237becd64 | Data: {'step': 'symbol_detection', 'progress': 40, 'message': '🔍 Analyzing chart content and detecting symbols...', 'has_clients': True, 'client_count': 1}
[2025-09-28 19:18:05] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Sent update to client 0 for session async_c79237becd64 | Data: {}
[2025-09-28 19:18:05] [INFO] [progress_debug] 🔄 SESSION_LIFECYCLE | Session: unknown | Event: workflow_start | Details: {'analysis_mode': 'positional', 'market_specialization': 'Crypto', 'has_progress_broadcaster': False, 'chart_images_count': 1}
[2025-09-28 19:18:05] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Received progress update: {'step': 'symbol_detection', 'progress': 40, 'message': '🔍 Analyzing chart content and detecting symbols...', 'timestamp': '2025-09-28T13:48:05.039398', 'details': {'stage': 'analysis_start'}, 'tool_results': None} | Data: {}
[2025-09-28 19:18:05] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending SSE event: {'type': 'progress_update', 'data': {'step': 'symbol_detection', 'progress': 40, 'message': '🔍 Analyzing chart content and detecting symbols...', 'timestamp': '2025-09-28T13:48:05.039398', 'details': {'stage': 'analysis_start'}, 'tool_results': None}} | Data: {}
[2025-09-28 19:18:05] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_c79237becd64 | Data: {}
[2025-09-28 19:18:35] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 175230.64} | Data: {}
[2025-09-28 19:18:35] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_c79237becd64 | Data: {}
[2025-09-28 19:19:05] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 175260.656} | Data: {}
[2025-09-28 19:19:05] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_c79237becd64 | Data: {}
[2025-09-28 19:19:29] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Broadcasting update for session async_c79237becd64 | Data: {'step': 'complete', 'progress': 100, 'message': '🎉 Analysis completed successfully!', 'has_clients': True, 'client_count': 1}
[2025-09-28 19:19:29] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Sent update to client 0 for session async_c79237becd64 | Data: {}
[2025-09-28 19:19:29] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Received progress update: {'step': 'complete', 'progress': 100, 'message': '🎉 Analysis completed successfully!', 'timestamp': '2025-09-28T13:49:29.306702', 'details': {'execution_time': 84.25714039802551, 'result_available': True}, 'tool_results': None} | Data: {}
[2025-09-28 19:19:29] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending SSE event: {'type': 'progress_update', 'data': {'step': 'complete', 'progress': 100, 'message': '🎉 Analysis completed successfully!', 'timestamp': '2025-09-28T13:49:29.306702', 'details': {'execution_time': 84.25714039802551, 'result_available': True}, 'tool_results': None}} | Data: {}
[2025-09-28 19:19:29] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Workflow complete, sending: {'type': 'complete', 'session_id': 'async_c79237becd64'} | Data: {}
[2025-09-28 19:19:29] [INFO] [progress_debug] 🔌 SSE_DISCONNECT | Session: async_c79237becd64 | Reason: event_generator_cleanup
[2025-09-28 19:32:28] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Broadcasting update for session async_12fd63c6a061 | Data: {'step': 'chart_upload', 'progress': 5, 'message': 'Analysis request received, starting background processing...', 'has_clients': False, 'client_count': 0}
[2025-09-28 19:32:28] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | No clients found for session async_12fd63c6a061 | Data: {}
[2025-09-28 19:32:28] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Broadcasting update for session async_12fd63c6a061 | Data: {'step': 'chart_upload', 'progress': 5, 'message': '📊 Processing uploaded chart images...', 'has_clients': False, 'client_count': 0}
[2025-09-28 19:32:28] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | No clients found for session async_12fd63c6a061 | Data: {}
[2025-09-28 19:32:28] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Broadcasting update for session async_12fd63c6a061 | Data: {'step': 'chart_upload', 'progress': 5, 'message': '🖼️ Processed chart image 1/1 (75KB)', 'has_clients': False, 'client_count': 0}
[2025-09-28 19:32:28] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | No clients found for session async_12fd63c6a061 | Data: {}
[2025-09-28 19:32:28] [INFO] [progress_debug] 🔗 SSE_CONNECTION | Session: async_12fd63c6a061 | Client: {'user_id': UUID('cd218226-a7f5-4bd8-a242-eadc1d480326'), 'user_email': '<EMAIL>', 'endpoint': '/api/v1/progress/stream/async_12fd63c6a061'}
[2025-09-28 19:32:28] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Adding client for session async_12fd63c6a061 | Data: {}
[2025-09-28 19:32:28] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Added client for session async_12fd63c6a061 | Data: {'total_clients': 1, 'all_sessions': ['async_12fd63c6a061']}
[2025-09-28 19:32:28] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Client queue created for session async_12fd63c6a061 | Data: {}
[2025-09-28 19:32:28] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending connection confirmation: {'type': 'connected', 'session_id': 'async_12fd63c6a061'} | Data: {}
[2025-09-28 19:32:28] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_12fd63c6a061 | Data: {}
[2025-09-28 19:32:28] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Broadcasting update for session async_12fd63c6a061 | Data: {'step': 'symbol_detection', 'progress': 40, 'message': '🔍 Analyzing chart content and detecting symbols...', 'has_clients': True, 'client_count': 1}
[2025-09-28 19:32:28] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Sent update to client 0 for session async_12fd63c6a061 | Data: {}
[2025-09-28 19:32:29] [INFO] [progress_debug] 🔄 SESSION_LIFECYCLE | Session: unknown | Event: workflow_start | Details: {'analysis_mode': 'positional', 'market_specialization': 'Crypto', 'has_progress_broadcaster': False, 'chart_images_count': 1}
[2025-09-28 19:32:29] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Received progress update: {'step': 'symbol_detection', 'progress': 40, 'message': '🔍 Analyzing chart content and detecting symbols...', 'timestamp': '2025-09-28T14:02:28.576472', 'details': {'stage': 'analysis_start'}, 'tool_results': None} | Data: {}
[2025-09-28 19:32:29] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending SSE event: {'type': 'progress_update', 'data': {'step': 'symbol_detection', 'progress': 40, 'message': '🔍 Analyzing chart content and detecting symbols...', 'timestamp': '2025-09-28T14:02:28.576472', 'details': {'stage': 'analysis_start'}, 'tool_results': None}} | Data: {}
[2025-09-28 19:32:29] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_12fd63c6a061 | Data: {}
[2025-09-28 19:32:59] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 176094.453} | Data: {}
[2025-09-28 19:32:59] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_12fd63c6a061 | Data: {}
[2025-09-28 19:34:21] [INFO] [progress_debug] 🔌 SSE_DISCONNECT | Session: async_12fd63c6a061 | Reason: event_generator_cleanup
[2025-09-28 19:46:52] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Broadcasting update for session async_d4edc02a7fcf | Data: {'step': 'chart_upload', 'progress': 5, 'message': 'Analysis request received, starting background processing...', 'has_clients': False, 'client_count': 0}
[2025-09-28 19:46:52] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | No clients found for session async_d4edc02a7fcf | Data: {}
[2025-09-28 19:46:52] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Broadcasting update for session async_d4edc02a7fcf | Data: {'step': 'chart_upload', 'progress': 5, 'message': '📊 Processing uploaded chart images...', 'has_clients': False, 'client_count': 0}
[2025-09-28 19:46:52] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | No clients found for session async_d4edc02a7fcf | Data: {}
[2025-09-28 19:46:52] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Broadcasting update for session async_d4edc02a7fcf | Data: {'step': 'chart_upload', 'progress': 5, 'message': '🖼️ Processed chart image 1/1 (75KB)', 'has_clients': False, 'client_count': 0}
[2025-09-28 19:46:52] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | No clients found for session async_d4edc02a7fcf | Data: {}
[2025-09-28 19:46:53] [INFO] [progress_debug] 🔗 SSE_CONNECTION | Session: async_d4edc02a7fcf | Client: {'user_id': UUID('cd218226-a7f5-4bd8-a242-eadc1d480326'), 'user_email': '<EMAIL>', 'endpoint': '/api/v1/progress/stream/async_d4edc02a7fcf'}
[2025-09-28 19:46:53] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Adding client for session async_d4edc02a7fcf | Data: {}
[2025-09-28 19:46:53] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Added client for session async_d4edc02a7fcf | Data: {'total_clients': 1, 'all_sessions': ['async_d4edc02a7fcf']}
[2025-09-28 19:46:53] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Client queue created for session async_d4edc02a7fcf | Data: {}
[2025-09-28 19:46:53] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending connection confirmation: {'type': 'connected', 'session_id': 'async_d4edc02a7fcf'} | Data: {}
[2025-09-28 19:46:53] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_d4edc02a7fcf | Data: {}
[2025-09-28 19:46:53] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Broadcasting update for session async_d4edc02a7fcf | Data: {'step': 'symbol_detection', 'progress': 40, 'message': '🔍 Analyzing chart content and detecting symbols...', 'has_clients': True, 'client_count': 1}
[2025-09-28 19:46:53] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Sent update to client 0 for session async_d4edc02a7fcf | Data: {}
[2025-09-28 19:46:53] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Received progress update: {'step': 'symbol_detection', 'progress': 40, 'message': '🔍 Analyzing chart content and detecting symbols...', 'timestamp': '2025-09-28T14:16:53.416441', 'details': {'stage': 'analysis_start'}, 'tool_results': None} | Data: {}
[2025-09-28 19:46:53] [INFO] [progress_debug] 🔄 SESSION_LIFECYCLE | Session: unknown | Event: workflow_start | Details: {'analysis_mode': 'positional', 'market_specialization': 'Crypto', 'has_progress_broadcaster': False, 'chart_images_count': 1}
[2025-09-28 19:46:53] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending SSE event: {'type': 'progress_update', 'data': {'step': 'symbol_detection', 'progress': 40, 'message': '🔍 Analyzing chart content and detecting symbols...', 'timestamp': '2025-09-28T14:16:53.416441', 'details': {'stage': 'analysis_start'}, 'tool_results': None}} | Data: {}
[2025-09-28 19:46:53] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_d4edc02a7fcf | Data: {}
[2025-09-28 19:47:23] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 176958.921} | Data: {}
[2025-09-28 19:47:23] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_d4edc02a7fcf | Data: {}
[2025-09-28 19:47:53] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 176988.921} | Data: {}
[2025-09-28 19:47:53] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_d4edc02a7fcf | Data: {}
[2025-09-28 19:48:18] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Broadcasting update for session async_d4edc02a7fcf | Data: {'step': 'complete', 'progress': 100, 'message': '🎉 Analysis completed successfully!', 'has_clients': True, 'client_count': 1}
[2025-09-28 19:48:18] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Sent update to client 0 for session async_d4edc02a7fcf | Data: {}
[2025-09-28 19:48:18] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Received progress update: {'step': 'complete', 'progress': 100, 'message': '🎉 Analysis completed successfully!', 'timestamp': '2025-09-28T14:18:18.394412', 'details': {'execution_time': 84.96214580535889, 'result_available': True}, 'tool_results': None} | Data: {}
[2025-09-28 19:48:18] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending SSE event: {'type': 'progress_update', 'data': {'step': 'complete', 'progress': 100, 'message': '🎉 Analysis completed successfully!', 'timestamp': '2025-09-28T14:18:18.394412', 'details': {'execution_time': 84.96214580535889, 'result_available': True}, 'tool_results': None}} | Data: {}
[2025-09-28 19:48:18] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Workflow complete, sending: {'type': 'complete', 'session_id': 'async_d4edc02a7fcf'} | Data: {}
[2025-09-28 19:48:18] [INFO] [progress_debug] 🔌 SSE_DISCONNECT | Session: async_d4edc02a7fcf | Reason: event_generator_cleanup
[2025-09-28 20:03:03] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Broadcasting update for session async_aef778b959ed | Data: {'step': 'chart_upload', 'progress': 5, 'message': 'Analysis request received, starting background processing...', 'has_clients': False, 'client_count': 0}
[2025-09-28 20:03:03] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | No clients found for session async_aef778b959ed | Data: {}
[2025-09-28 20:03:03] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Broadcasting update for session async_aef778b959ed | Data: {'step': 'chart_upload', 'progress': 5, 'message': '📊 Processing uploaded chart images...', 'has_clients': False, 'client_count': 0}
[2025-09-28 20:03:03] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | No clients found for session async_aef778b959ed | Data: {}
[2025-09-28 20:03:03] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Broadcasting update for session async_aef778b959ed | Data: {'step': 'chart_upload', 'progress': 5, 'message': '🖼️ Processed chart image 1/1 (75KB)', 'has_clients': False, 'client_count': 0}
[2025-09-28 20:03:03] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | No clients found for session async_aef778b959ed | Data: {}
[2025-09-28 20:03:03] [INFO] [progress_debug] 🔗 SSE_CONNECTION | Session: async_aef778b959ed | Client: {'user_id': UUID('cd218226-a7f5-4bd8-a242-eadc1d480326'), 'user_email': '<EMAIL>', 'endpoint': '/api/v1/progress/stream/async_aef778b959ed'}
[2025-09-28 20:03:03] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Adding client for session async_aef778b959ed | Data: {}
[2025-09-28 20:03:03] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Added client for session async_aef778b959ed | Data: {'total_clients': 1, 'all_sessions': ['async_aef778b959ed']}
[2025-09-28 20:03:03] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Client queue created for session async_aef778b959ed | Data: {}
[2025-09-28 20:03:03] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending connection confirmation: {'type': 'connected', 'session_id': 'async_aef778b959ed'} | Data: {}
[2025-09-28 20:03:03] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_aef778b959ed | Data: {}
[2025-09-28 20:03:03] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Broadcasting update for session async_aef778b959ed | Data: {'step': 'symbol_detection', 'progress': 40, 'message': '🔍 Analyzing chart content and detecting symbols...', 'has_clients': True, 'client_count': 1}
[2025-09-28 20:03:03] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Sent update to client 0 for session async_aef778b959ed | Data: {}
[2025-09-28 20:03:04] [INFO] [progress_debug] 🔄 SESSION_LIFECYCLE | Session: unknown | Event: workflow_start | Details: {'analysis_mode': 'positional', 'market_specialization': 'Crypto', 'has_progress_broadcaster': False, 'chart_images_count': 1}
[2025-09-28 20:03:04] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Received progress update: {'step': 'symbol_detection', 'progress': 40, 'message': '🔍 Analyzing chart content and detecting symbols...', 'timestamp': '2025-09-28T14:33:03.989709', 'details': {'stage': 'analysis_start'}, 'tool_results': None} | Data: {}
[2025-09-28 20:03:04] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending SSE event: {'type': 'progress_update', 'data': {'step': 'symbol_detection', 'progress': 40, 'message': '🔍 Analyzing chart content and detecting symbols...', 'timestamp': '2025-09-28T14:33:03.989709', 'details': {'stage': 'analysis_start'}, 'tool_results': None}} | Data: {}
[2025-09-28 20:03:04] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_aef778b959ed | Data: {}
[2025-09-28 20:03:34] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 177929.968} | Data: {}
[2025-09-28 20:03:34] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_aef778b959ed | Data: {}
[2025-09-28 20:04:04] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 177959.968} | Data: {}
[2025-09-28 20:04:04] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_aef778b959ed | Data: {}
[2025-09-28 20:04:24] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Broadcasting update for session async_aef778b959ed | Data: {'step': 'complete', 'progress': 100, 'message': '🎉 Analysis completed successfully!', 'has_clients': True, 'client_count': 1}
[2025-09-28 20:04:24] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Sent update to client 0 for session async_aef778b959ed | Data: {}
[2025-09-28 20:04:24] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Received progress update: {'step': 'complete', 'progress': 100, 'message': '🎉 Analysis completed successfully!', 'timestamp': '2025-09-28T14:34:24.799612', 'details': {'execution_time': 80.78007817268372, 'result_available': True}, 'tool_results': None} | Data: {}
[2025-09-28 20:04:24] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending SSE event: {'type': 'progress_update', 'data': {'step': 'complete', 'progress': 100, 'message': '🎉 Analysis completed successfully!', 'timestamp': '2025-09-28T14:34:24.799612', 'details': {'execution_time': 80.78007817268372, 'result_available': True}, 'tool_results': None}} | Data: {}
[2025-09-28 20:04:24] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Workflow complete, sending: {'type': 'complete', 'session_id': 'async_aef778b959ed'} | Data: {}
[2025-09-28 20:04:24] [INFO] [progress_debug] 🔌 SSE_DISCONNECT | Session: async_aef778b959ed | Reason: event_generator_cleanup
[2025-09-28 21:10:19] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Broadcasting update for session async_3ec2f3de7ec2 | Data: {'step': 'chart_upload', 'progress': 5, 'message': 'Analysis request received, starting background processing...', 'has_clients': False, 'client_count': 0}
[2025-09-28 21:10:19] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | No clients found for session async_3ec2f3de7ec2 | Data: {}
[2025-09-28 21:10:19] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Broadcasting update for session async_3ec2f3de7ec2 | Data: {'step': 'chart_upload', 'progress': 5, 'message': '📊 Processing uploaded chart images...', 'has_clients': False, 'client_count': 0}
[2025-09-28 21:10:19] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | No clients found for session async_3ec2f3de7ec2 | Data: {}
[2025-09-28 21:10:19] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Broadcasting update for session async_3ec2f3de7ec2 | Data: {'step': 'chart_upload', 'progress': 5, 'message': '🖼️ Processed chart image 1/1 (75KB)', 'has_clients': False, 'client_count': 0}
[2025-09-28 21:10:19] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | No clients found for session async_3ec2f3de7ec2 | Data: {}
[2025-09-28 21:10:19] [INFO] [progress_debug] 🔗 SSE_CONNECTION | Session: async_3ec2f3de7ec2 | Client: {'user_id': UUID('cd218226-a7f5-4bd8-a242-eadc1d480326'), 'user_email': '<EMAIL>', 'endpoint': '/api/v1/progress/stream/async_3ec2f3de7ec2'}
[2025-09-28 21:10:19] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Adding client for session async_3ec2f3de7ec2 | Data: {}
[2025-09-28 21:10:19] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Added client for session async_3ec2f3de7ec2 | Data: {'total_clients': 1, 'all_sessions': ['async_3ec2f3de7ec2']}
[2025-09-28 21:10:19] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Client queue created for session async_3ec2f3de7ec2 | Data: {}
[2025-09-28 21:10:19] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending connection confirmation: {'type': 'connected', 'session_id': 'async_3ec2f3de7ec2'} | Data: {}
[2025-09-28 21:10:19] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_3ec2f3de7ec2 | Data: {}
[2025-09-28 21:10:19] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Broadcasting update for session async_3ec2f3de7ec2 | Data: {'step': 'symbol_detection', 'progress': 40, 'message': '🔍 Analyzing chart content and detecting symbols...', 'has_clients': True, 'client_count': 1}
[2025-09-28 21:10:19] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Sent update to client 0 for session async_3ec2f3de7ec2 | Data: {}
[2025-09-28 21:10:20] [INFO] [progress_debug] 🔄 SESSION_LIFECYCLE | Session: unknown | Event: workflow_start | Details: {'analysis_mode': 'positional', 'market_specialization': 'Crypto', 'has_progress_broadcaster': False, 'chart_images_count': 1}
[2025-09-28 21:10:20] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Received progress update: {'step': 'symbol_detection', 'progress': 40, 'message': '🔍 Analyzing chart content and detecting symbols...', 'timestamp': '2025-09-28T15:40:19.696778', 'details': {'stage': 'analysis_start'}, 'tool_results': None} | Data: {}
[2025-09-28 21:10:20] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending SSE event: {'type': 'progress_update', 'data': {'step': 'symbol_detection', 'progress': 40, 'message': '🔍 Analyzing chart content and detecting symbols...', 'timestamp': '2025-09-28T15:40:19.696778', 'details': {'stage': 'analysis_start'}, 'tool_results': None}} | Data: {}
[2025-09-28 21:10:20] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_3ec2f3de7ec2 | Data: {}
[2025-09-28 21:11:06] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 181981.718} | Data: {}
[2025-09-28 21:11:06] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_3ec2f3de7ec2 | Data: {}
[2025-09-28 21:11:37] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 182012.703} | Data: {}
[2025-09-28 21:11:37] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_3ec2f3de7ec2 | Data: {}
[2025-09-28 21:12:12] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 182048.187} | Data: {}
[2025-09-28 21:12:12] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_3ec2f3de7ec2 | Data: {}
[2025-09-28 21:12:45] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 182080.921} | Data: {}
[2025-09-28 21:12:45] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_3ec2f3de7ec2 | Data: {}
[2025-09-28 21:12:46] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Broadcasting update for session async_3ec2f3de7ec2 | Data: {'step': 'complete', 'progress': 100, 'message': '🎉 Analysis completed successfully!', 'has_clients': True, 'client_count': 1}
[2025-09-28 21:12:46] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Sent update to client 0 for session async_3ec2f3de7ec2 | Data: {}
[2025-09-28 21:12:46] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Received progress update: {'step': 'complete', 'progress': 100, 'message': '🎉 Analysis completed successfully!', 'timestamp': '2025-09-28T15:42:46.350957', 'details': {'execution_time': 146.63715147972107, 'result_available': True}, 'tool_results': None} | Data: {}
[2025-09-28 21:12:46] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending SSE event: {'type': 'progress_update', 'data': {'step': 'complete', 'progress': 100, 'message': '🎉 Analysis completed successfully!', 'timestamp': '2025-09-28T15:42:46.350957', 'details': {'execution_time': 146.63715147972107, 'result_available': True}, 'tool_results': None}} | Data: {}
[2025-09-28 21:12:46] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Workflow complete, sending: {'type': 'complete', 'session_id': 'async_3ec2f3de7ec2'} | Data: {}
[2025-09-28 21:12:46] [INFO] [progress_debug] 🔌 SSE_DISCONNECT | Session: async_3ec2f3de7ec2 | Reason: event_generator_cleanup
[2025-09-28 21:30:55] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Broadcasting update for session async_3768a28457c5 | Data: {'step': 'chart_upload', 'progress': 5, 'message': 'Analysis request received, starting background processing...', 'has_clients': False, 'client_count': 0}
[2025-09-28 21:30:55] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | No clients found for session async_3768a28457c5 | Data: {}
[2025-09-28 21:30:55] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Broadcasting update for session async_3768a28457c5 | Data: {'step': 'chart_upload', 'progress': 5, 'message': '📊 Processing uploaded chart images...', 'has_clients': False, 'client_count': 0}
[2025-09-28 21:30:55] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | No clients found for session async_3768a28457c5 | Data: {}
[2025-09-28 21:30:55] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Broadcasting update for session async_3768a28457c5 | Data: {'step': 'chart_upload', 'progress': 5, 'message': '🖼️ Processed chart image 1/1 (75KB)', 'has_clients': False, 'client_count': 0}
[2025-09-28 21:30:55] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | No clients found for session async_3768a28457c5 | Data: {}
[2025-09-28 21:31:02] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Broadcasting update for session async_3768a28457c5 | Data: {'step': 'symbol_detection', 'progress': 40, 'message': '🔍 Analyzing chart content and detecting symbols...', 'has_clients': False, 'client_count': 0}
[2025-09-28 21:31:02] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | No clients found for session async_3768a28457c5 | Data: {}
[2025-09-28 21:31:02] [INFO] [progress_debug] 🔄 SESSION_LIFECYCLE | Session: unknown | Event: workflow_start | Details: {'analysis_mode': 'positional', 'market_specialization': 'Crypto', 'has_progress_broadcaster': False, 'chart_images_count': 1}
[2025-09-28 21:31:12] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Broadcasting update for session async_cb3229beba46 | Data: {'step': 'chart_upload', 'progress': 5, 'message': 'Analysis request received, starting background processing...', 'has_clients': False, 'client_count': 0}
[2025-09-28 21:31:12] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | No clients found for session async_cb3229beba46 | Data: {}
[2025-09-28 21:31:12] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Broadcasting update for session async_cb3229beba46 | Data: {'step': 'chart_upload', 'progress': 5, 'message': '📊 Processing uploaded chart images...', 'has_clients': False, 'client_count': 0}
[2025-09-28 21:31:12] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | No clients found for session async_cb3229beba46 | Data: {}
[2025-09-28 21:31:12] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Broadcasting update for session async_cb3229beba46 | Data: {'step': 'chart_upload', 'progress': 5, 'message': '🖼️ Processed chart image 1/1 (75KB)', 'has_clients': False, 'client_count': 0}
[2025-09-28 21:31:12] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | No clients found for session async_cb3229beba46 | Data: {}
[2025-09-28 21:31:20] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Broadcasting update for session async_cb3229beba46 | Data: {'step': 'symbol_detection', 'progress': 40, 'message': '🔍 Analyzing chart content and detecting symbols...', 'has_clients': False, 'client_count': 0}
[2025-09-28 21:31:20] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | No clients found for session async_cb3229beba46 | Data: {}
[2025-09-28 21:31:20] [INFO] [progress_debug] 🔄 SESSION_LIFECYCLE | Session: unknown | Event: workflow_start | Details: {'analysis_mode': 'positional', 'market_specialization': 'Crypto', 'has_progress_broadcaster': False, 'chart_images_count': 1}
[2025-09-28 21:31:21] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Broadcasting update for session async_3dd8dad43ead | Data: {'step': 'chart_upload', 'progress': 5, 'message': 'Analysis request received, starting background processing...', 'has_clients': False, 'client_count': 0}
[2025-09-28 21:31:21] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | No clients found for session async_3dd8dad43ead | Data: {}
[2025-09-28 21:31:21] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Broadcasting update for session async_3dd8dad43ead | Data: {'step': 'chart_upload', 'progress': 5, 'message': '📊 Processing uploaded chart images...', 'has_clients': False, 'client_count': 0}
[2025-09-28 21:31:21] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | No clients found for session async_3dd8dad43ead | Data: {}
[2025-09-28 21:31:21] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Broadcasting update for session async_3dd8dad43ead | Data: {'step': 'chart_upload', 'progress': 5, 'message': '🖼️ Processed chart image 1/1 (75KB)', 'has_clients': False, 'client_count': 0}
[2025-09-28 21:31:21] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | No clients found for session async_3dd8dad43ead | Data: {}
[2025-09-28 21:31:33] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Broadcasting update for session async_3dd8dad43ead | Data: {'step': 'symbol_detection', 'progress': 40, 'message': '🔍 Analyzing chart content and detecting symbols...', 'has_clients': False, 'client_count': 0}
[2025-09-28 21:31:33] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | No clients found for session async_3dd8dad43ead | Data: {}
[2025-09-28 21:31:33] [INFO] [progress_debug] 🔄 SESSION_LIFECYCLE | Session: unknown | Event: workflow_start | Details: {'analysis_mode': 'positional', 'market_specialization': 'Crypto', 'has_progress_broadcaster': False, 'chart_images_count': 1}
[2025-09-28 21:31:33] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Broadcasting update for session async_3768a28457c5 | Data: {'step': 'complete', 'progress': 100, 'message': '🎉 Analysis completed successfully!', 'has_clients': False, 'client_count': 0}
[2025-09-28 21:31:33] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | No clients found for session async_3768a28457c5 | Data: {}
[2025-09-28 21:31:41] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Broadcasting update for session async_cb3229beba46 | Data: {'step': 'complete', 'progress': 100, 'message': '🎉 Analysis completed successfully!', 'has_clients': False, 'client_count': 0}
[2025-09-28 21:31:41] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | No clients found for session async_cb3229beba46 | Data: {}
[2025-09-28 21:31:57] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Broadcasting update for session async_3dd8dad43ead | Data: {'step': 'complete', 'progress': 100, 'message': '🎉 Analysis completed successfully!', 'has_clients': False, 'client_count': 0}
[2025-09-28 21:31:57] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | No clients found for session async_3dd8dad43ead | Data: {}
[2025-09-28 21:32:18] [INFO] [progress_debug] 🔗 SSE_CONNECTION | Session: async_3dd8dad43ead | Client: {'user_id': UUID('cd218226-a7f5-4bd8-a242-eadc1d480326'), 'user_email': '<EMAIL>', 'endpoint': '/api/v1/progress/stream/async_3dd8dad43ead'}
[2025-09-28 21:32:18] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Adding client for session async_3dd8dad43ead | Data: {}
[2025-09-28 21:32:18] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Added client for session async_3dd8dad43ead | Data: {'total_clients': 1, 'all_sessions': ['async_3dd8dad43ead']}
[2025-09-28 21:32:18] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Client queue created for session async_3dd8dad43ead | Data: {}
[2025-09-28 21:32:18] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending connection confirmation: {'type': 'connected', 'session_id': 'async_3dd8dad43ead'} | Data: {}
[2025-09-28 21:32:18] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_3dd8dad43ead | Data: {}
[2025-09-28 21:32:18] [INFO] [progress_debug] 🔌 SSE_DISCONNECT | Session: async_3dd8dad43ead | Reason: event_generator_cleanup
[2025-09-28 21:41:52] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Broadcasting update for session async_7c841c9f417c | Data: {'step': 'chart_upload', 'progress': 5, 'message': 'Analysis request received, starting background processing...', 'has_clients': False, 'client_count': 0}
[2025-09-28 21:41:52] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | No clients found for session async_7c841c9f417c | Data: {}
[2025-09-28 21:41:52] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Broadcasting update for session async_7c841c9f417c | Data: {'step': 'chart_upload', 'progress': 5, 'message': '📊 Processing uploaded chart images...', 'has_clients': False, 'client_count': 0}
[2025-09-28 21:41:52] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | No clients found for session async_7c841c9f417c | Data: {}
[2025-09-28 21:41:52] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Broadcasting update for session async_7c841c9f417c | Data: {'step': 'chart_upload', 'progress': 5, 'message': '🖼️ Processed chart image 1/1 (75KB)', 'has_clients': False, 'client_count': 0}
[2025-09-28 21:41:52] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | No clients found for session async_7c841c9f417c | Data: {}
[2025-09-28 21:41:52] [INFO] [progress_debug] 🔗 SSE_CONNECTION | Session: async_7c841c9f417c | Client: {'user_id': UUID('cd218226-a7f5-4bd8-a242-eadc1d480326'), 'user_email': '<EMAIL>', 'endpoint': '/api/v1/progress/stream/async_7c841c9f417c'}
[2025-09-28 21:41:52] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Adding client for session async_7c841c9f417c | Data: {}
[2025-09-28 21:41:52] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Added client for session async_7c841c9f417c | Data: {'total_clients': 1, 'all_sessions': ['async_7c841c9f417c']}
[2025-09-28 21:41:52] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Client queue created for session async_7c841c9f417c | Data: {}
[2025-09-28 21:41:52] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending connection confirmation: {'type': 'connected', 'session_id': 'async_7c841c9f417c'} | Data: {}
[2025-09-28 21:41:52] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_7c841c9f417c | Data: {}
[2025-09-28 21:41:52] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Broadcasting update for session async_7c841c9f417c | Data: {'step': 'symbol_detection', 'progress': 40, 'message': '🔍 Analyzing chart content and detecting symbols...', 'has_clients': True, 'client_count': 1}
[2025-09-28 21:41:52] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Sent update to client 0 for session async_7c841c9f417c | Data: {}
[2025-09-28 21:41:53] [INFO] [progress_debug] 🔄 SESSION_LIFECYCLE | Session: unknown | Event: workflow_start | Details: {'analysis_mode': 'positional', 'market_specialization': 'Crypto', 'has_progress_broadcaster': False, 'chart_images_count': 1}
[2025-09-28 21:41:53] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Received progress update: {'step': 'symbol_detection', 'progress': 40, 'message': '🔍 Analyzing chart content and detecting symbols...', 'timestamp': '2025-09-28T16:11:52.956421', 'details': {'stage': 'analysis_start'}, 'tool_results': None} | Data: {}
[2025-09-28 21:41:53] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending SSE event: {'type': 'progress_update', 'data': {'step': 'symbol_detection', 'progress': 40, 'message': '🔍 Analyzing chart content and detecting symbols...', 'timestamp': '2025-09-28T16:11:52.956421', 'details': {'stage': 'analysis_start'}, 'tool_results': None}} | Data: {}
[2025-09-28 21:41:53] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_7c841c9f417c | Data: {}
[2025-09-28 21:42:21] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Broadcasting update for session async_7c841c9f417c | Data: {'step': 'complete', 'progress': 100, 'message': '🎉 Analysis completed successfully!', 'has_clients': True, 'client_count': 1}
[2025-09-28 21:42:21] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Sent update to client 0 for session async_7c841c9f417c | Data: {}
[2025-09-28 21:42:21] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Received progress update: {'step': 'complete', 'progress': 100, 'message': '🎉 Analysis completed successfully!', 'timestamp': '2025-09-28T16:12:21.511542', 'details': {'execution_time': 28.51130723953247, 'result_available': True}, 'tool_results': None} | Data: {}
[2025-09-28 21:42:21] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending SSE event: {'type': 'progress_update', 'data': {'step': 'complete', 'progress': 100, 'message': '🎉 Analysis completed successfully!', 'timestamp': '2025-09-28T16:12:21.511542', 'details': {'execution_time': 28.51130723953247, 'result_available': True}, 'tool_results': None}} | Data: {}
[2025-09-28 21:42:21] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Workflow complete, sending: {'type': 'complete', 'session_id': 'async_7c841c9f417c'} | Data: {}
[2025-09-28 21:42:21] [INFO] [progress_debug] 🔌 SSE_DISCONNECT | Session: async_7c841c9f417c | Reason: event_generator_cleanup
[2025-09-28 21:56:08] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Broadcasting update for session async_aab194f6fcdf | Data: {'step': 'chart_upload', 'progress': 5, 'message': 'Analysis request received, starting background processing...', 'has_clients': False, 'client_count': 0}
[2025-09-28 21:56:08] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | No clients found for session async_aab194f6fcdf | Data: {}
[2025-09-28 21:56:08] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Broadcasting update for session async_aab194f6fcdf | Data: {'step': 'chart_upload', 'progress': 5, 'message': '📊 Processing uploaded chart images...', 'has_clients': False, 'client_count': 0}
[2025-09-28 21:56:08] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | No clients found for session async_aab194f6fcdf | Data: {}
[2025-09-28 21:56:08] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Broadcasting update for session async_aab194f6fcdf | Data: {'step': 'chart_upload', 'progress': 5, 'message': '🖼️ Processed chart image 1/1 (151KB)', 'has_clients': False, 'client_count': 0}
[2025-09-28 21:56:08] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | No clients found for session async_aab194f6fcdf | Data: {}
[2025-09-28 21:56:08] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Broadcasting update for session async_aab194f6fcdf | Data: {'step': 'symbol_detection', 'progress': 40, 'message': '🔍 Analyzing chart content and detecting symbols...', 'has_clients': False, 'client_count': 0}
[2025-09-28 21:56:08] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | No clients found for session async_aab194f6fcdf | Data: {}
[2025-09-28 21:56:09] [INFO] [progress_debug] 🔄 SESSION_LIFECYCLE | Session: unknown | Event: workflow_start | Details: {'analysis_mode': 'positional', 'market_specialization': 'Crypto', 'has_progress_broadcaster': False, 'chart_images_count': 1}
[2025-09-28 21:56:15] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Broadcasting update for session async_aab194f6fcdf | Data: {'step': 'complete', 'progress': 100, 'message': '🎉 Analysis completed successfully!', 'has_clients': False, 'client_count': 0}
[2025-09-28 21:56:15] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | No clients found for session async_aab194f6fcdf | Data: {}
[2025-09-28 22:02:53] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Broadcasting update for session async_1d395ae01004 | Data: {'step': 'chart_upload', 'progress': 5, 'message': 'Analysis request received, starting background processing...', 'has_clients': False, 'client_count': 0}
[2025-09-28 22:02:53] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | No clients found for session async_1d395ae01004 | Data: {}
[2025-09-28 22:02:53] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Broadcasting update for session async_1d395ae01004 | Data: {'step': 'chart_upload', 'progress': 5, 'message': '📊 Processing uploaded chart images...', 'has_clients': False, 'client_count': 0}
[2025-09-28 22:02:53] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | No clients found for session async_1d395ae01004 | Data: {}
[2025-09-28 22:02:53] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Broadcasting update for session async_1d395ae01004 | Data: {'step': 'chart_upload', 'progress': 5, 'message': '🖼️ Processed chart image 1/1 (151KB)', 'has_clients': False, 'client_count': 0}
[2025-09-28 22:02:53] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | No clients found for session async_1d395ae01004 | Data: {}
[2025-09-28 22:02:54] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Broadcasting update for session async_1d395ae01004 | Data: {'step': 'symbol_detection', 'progress': 40, 'message': '🔍 Analyzing chart content and detecting symbols...', 'has_clients': False, 'client_count': 0}
[2025-09-28 22:02:54] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | No clients found for session async_1d395ae01004 | Data: {}
[2025-09-28 22:02:54] [INFO] [progress_debug] 🔄 SESSION_LIFECYCLE | Session: unknown | Event: workflow_start | Details: {'analysis_mode': 'positional', 'market_specialization': 'Crypto', 'has_progress_broadcaster': False, 'chart_images_count': 1}
[2025-09-28 22:02:57] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Broadcasting update for session async_1d395ae01004 | Data: {'step': 'complete', 'progress': 100, 'message': '🎉 Analysis completed successfully!', 'has_clients': False, 'client_count': 0}
[2025-09-28 22:02:57] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | No clients found for session async_1d395ae01004 | Data: {}
[2025-09-28 22:03:32] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Broadcasting update for session async_4ba1b92a41b1 | Data: {'step': 'chart_upload', 'progress': 5, 'message': 'Analysis request received, starting background processing...', 'has_clients': False, 'client_count': 0}
[2025-09-28 22:03:32] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | No clients found for session async_4ba1b92a41b1 | Data: {}
[2025-09-28 22:03:32] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Broadcasting update for session async_4ba1b92a41b1 | Data: {'step': 'chart_upload', 'progress': 5, 'message': '📊 Processing uploaded chart images...', 'has_clients': False, 'client_count': 0}
[2025-09-28 22:03:32] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | No clients found for session async_4ba1b92a41b1 | Data: {}
[2025-09-28 22:03:32] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Broadcasting update for session async_4ba1b92a41b1 | Data: {'step': 'chart_upload', 'progress': 5, 'message': '🖼️ Processed chart image 1/1 (75KB)', 'has_clients': False, 'client_count': 0}
[2025-09-28 22:03:32] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | No clients found for session async_4ba1b92a41b1 | Data: {}
[2025-09-28 22:03:32] [INFO] [progress_debug] 🔗 SSE_CONNECTION | Session: async_4ba1b92a41b1 | Client: {'user_id': UUID('cd218226-a7f5-4bd8-a242-eadc1d480326'), 'user_email': '<EMAIL>', 'endpoint': '/api/v1/progress/stream/async_4ba1b92a41b1'}
[2025-09-28 22:03:32] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Adding client for session async_4ba1b92a41b1 | Data: {}
[2025-09-28 22:03:32] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Added client for session async_4ba1b92a41b1 | Data: {'total_clients': 1, 'all_sessions': ['async_4ba1b92a41b1']}
[2025-09-28 22:03:32] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Client queue created for session async_4ba1b92a41b1 | Data: {}
[2025-09-28 22:03:32] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending connection confirmation: {'type': 'connected', 'session_id': 'async_4ba1b92a41b1'} | Data: {}
[2025-09-28 22:03:32] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_4ba1b92a41b1 | Data: {}
[2025-09-28 22:03:32] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Broadcasting update for session async_4ba1b92a41b1 | Data: {'step': 'symbol_detection', 'progress': 40, 'message': '🔍 Analyzing chart content and detecting symbols...', 'has_clients': True, 'client_count': 1}
[2025-09-28 22:03:32] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Sent update to client 0 for session async_4ba1b92a41b1 | Data: {}
[2025-09-28 22:03:32] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Received progress update: {'step': 'symbol_detection', 'progress': 40, 'message': '🔍 Analyzing chart content and detecting symbols...', 'timestamp': '2025-09-28T16:33:32.741207', 'details': {'stage': 'analysis_start'}, 'tool_results': None} | Data: {}
[2025-09-28 22:03:32] [INFO] [progress_debug] 🔄 SESSION_LIFECYCLE | Session: unknown | Event: workflow_start | Details: {'analysis_mode': 'positional', 'market_specialization': 'Crypto', 'has_progress_broadcaster': False, 'chart_images_count': 1}
[2025-09-28 22:03:32] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending SSE event: {'type': 'progress_update', 'data': {'step': 'symbol_detection', 'progress': 40, 'message': '🔍 Analyzing chart content and detecting symbols...', 'timestamp': '2025-09-28T16:33:32.741207', 'details': {'stage': 'analysis_start'}, 'tool_results': None}} | Data: {}
[2025-09-28 22:03:32] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_4ba1b92a41b1 | Data: {}
[2025-09-28 22:03:33] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Broadcasting update for session async_4ba1b92a41b1 | Data: {'step': 'complete', 'progress': 100, 'message': '🎉 Analysis completed successfully!', 'has_clients': True, 'client_count': 1}
[2025-09-28 22:03:33] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Sent update to client 0 for session async_4ba1b92a41b1 | Data: {}
[2025-09-28 22:03:33] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Received progress update: {'step': 'complete', 'progress': 100, 'message': '🎉 Analysis completed successfully!', 'timestamp': '2025-09-28T16:33:33.124787', 'details': {'execution_time': 0.37805771827697754, 'result_available': True}, 'tool_results': None} | Data: {}
[2025-09-28 22:03:33] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending SSE event: {'type': 'progress_update', 'data': {'step': 'complete', 'progress': 100, 'message': '🎉 Analysis completed successfully!', 'timestamp': '2025-09-28T16:33:33.124787', 'details': {'execution_time': 0.37805771827697754, 'result_available': True}, 'tool_results': None}} | Data: {}
[2025-09-28 22:03:33] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Workflow complete, sending: {'type': 'complete', 'session_id': 'async_4ba1b92a41b1'} | Data: {}
[2025-09-28 22:03:33] [INFO] [progress_debug] 🔌 SSE_DISCONNECT | Session: async_4ba1b92a41b1 | Reason: event_generator_cleanup
[2025-09-28 22:13:55] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Broadcasting update for session async_b73030fe3cb4 | Data: {'step': 'chart_upload', 'progress': 5, 'message': 'Analysis request received, starting background processing...', 'has_clients': False, 'client_count': 0}
[2025-09-28 22:13:55] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | No clients found for session async_b73030fe3cb4 | Data: {}
[2025-09-28 22:13:55] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Broadcasting update for session async_b73030fe3cb4 | Data: {'step': 'chart_upload', 'progress': 5, 'message': '📊 Processing uploaded chart images...', 'has_clients': False, 'client_count': 0}
[2025-09-28 22:13:55] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | No clients found for session async_b73030fe3cb4 | Data: {}
[2025-09-28 22:13:55] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Broadcasting update for session async_b73030fe3cb4 | Data: {'step': 'chart_upload', 'progress': 5, 'message': '🖼️ Processed chart image 1/1 (151KB)', 'has_clients': False, 'client_count': 0}
[2025-09-28 22:13:55] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | No clients found for session async_b73030fe3cb4 | Data: {}
[2025-09-28 22:13:55] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Broadcasting update for session async_b73030fe3cb4 | Data: {'step': 'symbol_detection', 'progress': 40, 'message': '🔍 Analyzing chart content and detecting symbols...', 'has_clients': False, 'client_count': 0}
[2025-09-28 22:13:55] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | No clients found for session async_b73030fe3cb4 | Data: {}
[2025-09-28 22:13:55] [INFO] [progress_debug] 🔄 SESSION_LIFECYCLE | Session: unknown | Event: workflow_start | Details: {'analysis_mode': 'positional', 'market_specialization': 'Crypto', 'has_progress_broadcaster': False, 'chart_images_count': 1}
[2025-09-28 22:16:38] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Broadcasting update for session async_b73030fe3cb4 | Data: {'step': 'complete', 'progress': 100, 'message': '🎉 Analysis completed successfully!', 'has_clients': False, 'client_count': 0}
[2025-09-28 22:16:38] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | No clients found for session async_b73030fe3cb4 | Data: {}
2025-09-28 22:20:38 | INFO     | [debug_LANGGRAPH] | 🚀 LANGGRAPH FLOW STARTED [async_61ec654fbe9c]
2025-09-28 22:20:38 | INFO     | [debug_LANGGRAPH] |    └─ Workflow ID: workflow_async_61ec654fbe9c
2025-09-28 22:20:38 | INFO     | [debug_LANGGRAPH] |    └─ Model: gemini-2.5-flash
2025-09-28 22:20:38 | INFO     | [debug_LANGGRAPH] |    └─ Start Time: 2025-09-28T22:20:38.792469
2025-09-28 22:21:50 | INFO     | [debug_LANGGRAPH] | 🏁 LANGGRAPH FLOW COMPLETE [async_61ec654fbe9c]: ✅ SUCCESS
2025-09-28 22:21:50 | INFO     | [debug_LANGGRAPH] |    └─ Execution Time: 72.13s
2025-09-28 22:21:50 | INFO     | [debug_LANGGRAPH] |    └─ End Time: 2025-09-28T22:21:50.919569
2025-09-28 22:35:16 | INFO     | [debug_LANGGRAPH] | 🚀 LANGGRAPH FLOW STARTED [async_798b1b925ad3]
2025-09-28 22:35:16 | INFO     | [debug_LANGGRAPH] |    └─ Workflow ID: workflow_async_798b1b925ad3
2025-09-28 22:35:16 | INFO     | [debug_LANGGRAPH] |    └─ Model: gemini-2.5-flash
2025-09-28 22:35:16 | INFO     | [debug_LANGGRAPH] |    └─ Start Time: 2025-09-28T22:35:16.480244
2025-09-28 22:35:17 | INFO     | [debug_LANGGRAPH] | 🚀 LANGGRAPH FLOW STARTED [async_c05001dcdca7]
2025-09-28 22:35:17 | INFO     | [debug_LANGGRAPH] |    └─ Workflow ID: workflow_async_c05001dcdca7
2025-09-28 22:35:17 | INFO     | [debug_LANGGRAPH] |    └─ Model: gemini-2.5-flash
2025-09-28 22:35:17 | INFO     | [debug_LANGGRAPH] |    └─ Start Time: 2025-09-28T22:35:17.630522
2025-09-28 22:35:17 | INFO     | [debug_LANGGRAPH] | 🚀 LANGGRAPH FLOW STARTED [async_3ab985c5aaa2]
2025-09-28 22:35:17 | INFO     | [debug_LANGGRAPH] |    └─ Workflow ID: workflow_async_3ab985c5aaa2
2025-09-28 22:35:17 | INFO     | [debug_LANGGRAPH] |    └─ Model: gemini-2.5-flash
2025-09-28 22:35:17 | INFO     | [debug_LANGGRAPH] |    └─ Start Time: 2025-09-28T22:35:17.853931
2025-09-28 22:36:23 | INFO     | [debug_LANGGRAPH] | 🏁 LANGGRAPH FLOW COMPLETE [async_3ab985c5aaa2]: ✅ SUCCESS
2025-09-28 22:36:23 | INFO     | [debug_LANGGRAPH] |    └─ Execution Time: 66.11s
2025-09-28 22:36:23 | INFO     | [debug_LANGGRAPH] |    └─ End Time: 2025-09-28T22:36:23.962813
2025-09-28 22:36:26 | INFO     | [debug_LANGGRAPH] | 🏁 LANGGRAPH FLOW COMPLETE [async_798b1b925ad3]: ✅ SUCCESS
2025-09-28 22:36:26 | INFO     | [debug_LANGGRAPH] |    └─ Execution Time: 69.90s
2025-09-28 22:36:26 | INFO     | [debug_LANGGRAPH] |    └─ End Time: 2025-09-28T22:36:26.380309
2025-09-28 22:36:29 | INFO     | [debug_LANGGRAPH] | 🏁 LANGGRAPH FLOW COMPLETE [async_c05001dcdca7]: ✅ SUCCESS
2025-09-28 22:36:29 | INFO     | [debug_LANGGRAPH] |    └─ Execution Time: 72.06s
2025-09-28 22:36:29 | INFO     | [debug_LANGGRAPH] |    └─ End Time: 2025-09-28T22:36:29.693450
2025-09-28 22:46:52 | INFO     | [debug_LANGGRAPH] | 🚀 LANGGRAPH FLOW STARTED [async_eab55a3dfa35]
2025-09-28 22:46:52 | INFO     | [debug_LANGGRAPH] |    └─ Workflow ID: workflow_async_eab55a3dfa35
2025-09-28 22:46:52 | INFO     | [debug_LANGGRAPH] |    └─ Model: gemini-2.5-flash
2025-09-28 22:46:52 | INFO     | [debug_LANGGRAPH] |    └─ Start Time: 2025-09-28T22:46:52.909942
2025-09-28 22:48:15 | INFO     | [debug_LANGGRAPH] | 🏁 LANGGRAPH FLOW COMPLETE [async_eab55a3dfa35]: ✅ SUCCESS
2025-09-28 22:48:15 | INFO     | [debug_LANGGRAPH] |    └─ Execution Time: 82.70s
2025-09-28 22:48:15 | INFO     | [debug_LANGGRAPH] |    └─ End Time: 2025-09-28T22:48:15.613742
2025-09-28 23:02:37 | INFO     | [debug_LANGGRAPH] | 🚀 LANGGRAPH FLOW STARTED [async_b683e9c7448f]
2025-09-28 23:02:37 | INFO     | [debug_LANGGRAPH] |    └─ Workflow ID: workflow_async_b683e9c7448f
2025-09-28 23:02:37 | INFO     | [debug_LANGGRAPH] |    └─ Model: gemini-2.5-flash
2025-09-28 23:02:37 | INFO     | [debug_LANGGRAPH] |    └─ Start Time: 2025-09-28T23:02:37.621749
2025-09-28 23:02:38 | INFO     | [debug_LANGGRAPH] | 🚀 LANGGRAPH FLOW STARTED [async_1f9e22f900ce]
2025-09-28 23:02:38 | INFO     | [debug_LANGGRAPH] |    └─ Workflow ID: workflow_async_1f9e22f900ce
2025-09-28 23:02:38 | INFO     | [debug_LANGGRAPH] |    └─ Model: gemini-2.5-flash
2025-09-28 23:02:38 | INFO     | [debug_LANGGRAPH] |    └─ Start Time: 2025-09-28T23:02:38.210916
2025-09-28 23:04:31 | INFO     | [debug_LANGGRAPH] | 🏁 LANGGRAPH FLOW COMPLETE [async_b683e9c7448f]: ✅ SUCCESS
2025-09-28 23:04:31 | INFO     | [debug_LANGGRAPH] |    └─ Execution Time: 113.60s
2025-09-28 23:04:31 | INFO     | [debug_LANGGRAPH] |    └─ End Time: 2025-09-28T23:04:31.223497
2025-09-28 23:04:37 | INFO     | [debug_LANGGRAPH] | 🏁 LANGGRAPH FLOW COMPLETE [async_1f9e22f900ce]: ✅ SUCCESS
2025-09-28 23:04:37 | INFO     | [debug_LANGGRAPH] |    └─ Execution Time: 118.87s
2025-09-28 23:04:37 | INFO     | [debug_LANGGRAPH] |    └─ End Time: 2025-09-28T23:04:37.090210
2025-09-28 23:06:09 | INFO     | [debug_LANGGRAPH] | 🚀 LANGGRAPH FLOW STARTED [async_e209775adeed]
2025-09-28 23:06:09 | INFO     | [debug_LANGGRAPH] |    └─ Workflow ID: workflow_async_e209775adeed
2025-09-28 23:06:09 | INFO     | [debug_LANGGRAPH] |    └─ Model: gemini-2.5-flash
2025-09-28 23:06:09 | INFO     | [debug_LANGGRAPH] |    └─ Start Time: 2025-09-28T23:06:09.896456
2025-09-28 23:07:33 | INFO     | [debug_LANGGRAPH] | 🏁 LANGGRAPH FLOW COMPLETE [async_e209775adeed]: ✅ SUCCESS
2025-09-28 23:07:33 | INFO     | [debug_LANGGRAPH] |    └─ Execution Time: 83.61s
2025-09-28 23:07:33 | INFO     | [debug_LANGGRAPH] |    └─ End Time: 2025-09-28T23:07:33.509579
2025-09-28 23:15:27 | INFO     | [debug_LANGGRAPH] | 🚀 LANGGRAPH FLOW STARTED [async_ca359a0f58e5]
2025-09-28 23:15:27 | INFO     | [debug_LANGGRAPH] |    └─ Workflow ID: workflow_async_ca359a0f58e5
2025-09-28 23:15:27 | INFO     | [debug_LANGGRAPH] |    └─ Model: gemini-2.5-flash
2025-09-28 23:15:27 | INFO     | [debug_LANGGRAPH] |    └─ Start Time: 2025-09-28T23:15:27.827273
2025-09-28 23:17:01 | INFO     | [debug_LANGGRAPH] | 🚀 LANGGRAPH FLOW STARTED [async_786d224ce7f0]
2025-09-28 23:17:01 | INFO     | [debug_LANGGRAPH] |    └─ Workflow ID: workflow_async_786d224ce7f0
2025-09-28 23:17:01 | INFO     | [debug_LANGGRAPH] |    └─ Model: gemini-2.5-flash
2025-09-28 23:17:01 | INFO     | [debug_LANGGRAPH] |    └─ Start Time: 2025-09-28T23:17:01.888611
2025-09-28 23:21:52 | INFO     | [debug_LANGGRAPH] | 🚀 LANGGRAPH FLOW STARTED [async_b7670f1b912f]
2025-09-28 23:21:52 | INFO     | [debug_LANGGRAPH] |    └─ Workflow ID: workflow_async_b7670f1b912f
2025-09-28 23:21:52 | INFO     | [debug_LANGGRAPH] |    └─ Model: gemini-2.5-flash
2025-09-28 23:21:52 | INFO     | [debug_LANGGRAPH] |    └─ Start Time: 2025-09-28T23:21:52.971664
2025-09-28 23:22:00 | INFO     | [debug_LANGGRAPH] | 🚀 LANGGRAPH FLOW STARTED [async_599271a9f171]
2025-09-28 23:22:00 | INFO     | [debug_LANGGRAPH] |    └─ Workflow ID: workflow_async_599271a9f171
2025-09-28 23:22:00 | INFO     | [debug_LANGGRAPH] |    └─ Model: gemini-2.5-flash
2025-09-28 23:22:00 | INFO     | [debug_LANGGRAPH] |    └─ Start Time: 2025-09-28T23:22:00.022161
2025-09-28 23:23:11 | INFO     | [debug_LANGGRAPH] | 🚀 LANGGRAPH FLOW STARTED [async_aaa8be1c6726]
2025-09-28 23:23:11 | INFO     | [debug_LANGGRAPH] |    └─ Workflow ID: workflow_async_aaa8be1c6726
2025-09-28 23:23:11 | INFO     | [debug_LANGGRAPH] |    └─ Model: gemini-2.5-flash
2025-09-28 23:23:11 | INFO     | [debug_LANGGRAPH] |    └─ Start Time: 2025-09-28T23:23:11.102116
2025-09-28 23:25:18 | INFO     | [debug_LANGGRAPH] | 🚀 LANGGRAPH FLOW STARTED [async_880d87551d8a]
2025-09-28 23:25:18 | INFO     | [debug_LANGGRAPH] |    └─ Workflow ID: workflow_async_880d87551d8a
2025-09-28 23:25:18 | INFO     | [debug_LANGGRAPH] |    └─ Model: gemini-2.5-flash
2025-09-28 23:25:18 | INFO     | [debug_LANGGRAPH] |    └─ Start Time: 2025-09-28T23:25:18.328691
2025-09-28 23:29:20 | INFO     | [debug_LANGGRAPH] | 🚀 LANGGRAPH FLOW STARTED [async_747c2ad7d6f8]
2025-09-28 23:29:20 | INFO     | [debug_LANGGRAPH] |    └─ Workflow ID: workflow_async_747c2ad7d6f8
2025-09-28 23:29:20 | INFO     | [debug_LANGGRAPH] |    └─ Model: gemini-2.5-flash
2025-09-28 23:29:20 | INFO     | [debug_LANGGRAPH] |    └─ Start Time: 2025-09-28T23:29:20.012609
2025-09-28 23:29:41 | INFO     | [debug_LANGGRAPH] | 🚀 LANGGRAPH FLOW STARTED [async_92c32b2a2779]
2025-09-28 23:29:41 | INFO     | [debug_LANGGRAPH] |    └─ Workflow ID: workflow_async_92c32b2a2779
2025-09-28 23:29:41 | INFO     | [debug_LANGGRAPH] |    └─ Model: gemini-2.5-flash
2025-09-28 23:29:41 | INFO     | [debug_LANGGRAPH] |    └─ Start Time: 2025-09-28T23:29:41.654165
2025-09-28 23:30:38 | INFO     | [debug_LANGGRAPH] | 🚀 LANGGRAPH FLOW STARTED [async_9aa2bd7ee7ff]
2025-09-28 23:30:38 | INFO     | [debug_LANGGRAPH] |    └─ Workflow ID: workflow_async_9aa2bd7ee7ff
2025-09-28 23:30:38 | INFO     | [debug_LANGGRAPH] |    └─ Model: gemini-2.5-flash
2025-09-28 23:30:38 | INFO     | [debug_LANGGRAPH] |    └─ Start Time: 2025-09-28T23:30:38.969535
2025-09-28 23:36:46 | INFO     | [debug_LANGGRAPH] | 🚀 LANGGRAPH FLOW STARTED [async_7dfdaa664c93]
2025-09-28 23:36:46 | INFO     | [debug_LANGGRAPH] |    └─ Workflow ID: workflow_async_7dfdaa664c93
2025-09-28 23:36:46 | INFO     | [debug_LANGGRAPH] |    └─ Model: gemini-2.5-flash
2025-09-28 23:36:46 | INFO     | [debug_LANGGRAPH] |    └─ Start Time: 2025-09-28T23:36:46.463226
2025-09-28 23:37:36 | INFO     | [debug_LANGGRAPH] | 🚀 LANGGRAPH FLOW STARTED [async_5dc6f0645c51]
2025-09-28 23:37:36 | INFO     | [debug_LANGGRAPH] |    └─ Workflow ID: workflow_async_5dc6f0645c51
2025-09-28 23:37:36 | INFO     | [debug_LANGGRAPH] |    └─ Model: gemini-2.5-flash
2025-09-28 23:37:36 | INFO     | [debug_LANGGRAPH] |    └─ Start Time: 2025-09-28T23:37:36.734121
2025-09-28 23:38:11 | INFO     | [debug_LANGGRAPH] | 🏁 LANGGRAPH FLOW COMPLETE [async_7dfdaa664c93]: ✅ SUCCESS
2025-09-28 23:38:11 | INFO     | [debug_LANGGRAPH] |    └─ Execution Time: 85.06s
2025-09-28 23:38:11 | INFO     | [debug_LANGGRAPH] |    └─ End Time: 2025-09-28T23:38:11.531235
2025-09-28 23:40:00 | INFO     | [debug_LANGGRAPH] | 🏁 LANGGRAPH FLOW COMPLETE [async_5dc6f0645c51]: ✅ SUCCESS
2025-09-28 23:40:00 | INFO     | [debug_LANGGRAPH] |    └─ Execution Time: 143.28s
2025-09-28 23:40:00 | INFO     | [debug_LANGGRAPH] |    └─ End Time: 2025-09-28T23:40:00.020815
2025-09-29 00:28:54 | INFO     | [debug_LANGGRAPH] | 🚀 LANGGRAPH FLOW STARTED [async_fec524a30428]
2025-09-29 00:28:54 | INFO     | [debug_LANGGRAPH] |    └─ Workflow ID: workflow_async_fec524a30428
2025-09-29 00:28:54 | INFO     | [debug_LANGGRAPH] |    └─ Model: gemini-2.5-flash
2025-09-29 00:28:54 | INFO     | [debug_LANGGRAPH] |    └─ Start Time: 2025-09-29T00:28:54.222510
2025-09-29 00:30:42 | INFO     | [debug_LANGGRAPH] | 🏁 LANGGRAPH FLOW COMPLETE [async_fec524a30428]: ✅ SUCCESS
2025-09-29 00:30:42 | INFO     | [debug_LANGGRAPH] |    └─ Execution Time: 107.78s
2025-09-29 00:30:42 | INFO     | [debug_LANGGRAPH] |    └─ End Time: 2025-09-29T00:30:42.010043
2025-09-29 01:01:31 | INFO     | [debug_LANGGRAPH] | 🚀 LANGGRAPH FLOW STARTED [async_afa577b8cf34]
2025-09-29 01:01:31 | INFO     | [debug_LANGGRAPH] |    └─ Workflow ID: workflow_async_afa577b8cf34
2025-09-29 01:01:31 | INFO     | [debug_LANGGRAPH] |    └─ Model: gemini-2.5-flash
2025-09-29 01:01:31 | INFO     | [debug_LANGGRAPH] |    └─ Start Time: 2025-09-29T01:01:31.326961
2025-09-29 01:04:27 | INFO     | [debug_LANGGRAPH] | 🏁 LANGGRAPH FLOW COMPLETE [async_afa577b8cf34]: ✅ SUCCESS
2025-09-29 01:04:27 | INFO     | [debug_LANGGRAPH] |    └─ Execution Time: 176.07s
2025-09-29 01:04:27 | INFO     | [debug_LANGGRAPH] |    └─ End Time: 2025-09-29T01:04:27.401958
2025-09-29 01:13:57 | INFO     | [debug_LANGGRAPH] | 🚀 LANGGRAPH FLOW STARTED [async_5db019865433]
2025-09-29 01:13:57 | INFO     | [debug_LANGGRAPH] |    └─ Workflow ID: workflow_async_5db019865433
2025-09-29 01:13:57 | INFO     | [debug_LANGGRAPH] |    └─ Model: gemini-2.5-flash
2025-09-29 01:13:57 | INFO     | [debug_LANGGRAPH] |    └─ Start Time: 2025-09-29T01:13:57.696924
2025-09-29 01:15:12 | INFO     | [debug_LANGGRAPH] | 🏁 LANGGRAPH FLOW COMPLETE [async_5db019865433]: ✅ SUCCESS
2025-09-29 01:15:12 | INFO     | [debug_LANGGRAPH] |    └─ Execution Time: 74.36s
2025-09-29 01:15:12 | INFO     | [debug_LANGGRAPH] |    └─ End Time: 2025-09-29T01:15:12.057755
2025-09-29 01:33:22 | INFO     | [debug_LANGGRAPH] | 🚀 LANGGRAPH FLOW STARTED [async_2dd9fc3e987b]
2025-09-29 01:33:22 | INFO     | [debug_LANGGRAPH] |    └─ Workflow ID: workflow_async_2dd9fc3e987b
2025-09-29 01:33:22 | INFO     | [debug_LANGGRAPH] |    └─ Model: gemini-2.5-flash
2025-09-29 01:33:22 | INFO     | [debug_LANGGRAPH] |    └─ Start Time: 2025-09-29T01:33:22.468184
2025-09-29 01:34:39 | INFO     | [debug_LANGGRAPH] | 🏁 LANGGRAPH FLOW COMPLETE [async_2dd9fc3e987b]: ✅ SUCCESS
2025-09-29 01:34:39 | INFO     | [debug_LANGGRAPH] |    └─ Execution Time: 77.07s
2025-09-29 01:34:39 | INFO     | [debug_LANGGRAPH] |    └─ End Time: 2025-09-29T01:34:39.534740
2025-09-29 01:59:40 | INFO     | [debug_LANGGRAPH] | 🚀 LANGGRAPH FLOW STARTED [async_40c14e819bf2]
2025-09-29 01:59:40 | INFO     | [debug_LANGGRAPH] |    └─ Workflow ID: workflow_async_40c14e819bf2
2025-09-29 01:59:40 | INFO     | [debug_LANGGRAPH] |    └─ Model: gemini-2.5-flash
2025-09-29 01:59:40 | INFO     | [debug_LANGGRAPH] |    └─ Start Time: 2025-09-29T01:59:40.018533
2025-09-29 02:00:36 | INFO     | [debug_LANGGRAPH] | 🏁 LANGGRAPH FLOW COMPLETE [async_40c14e819bf2]: ✅ SUCCESS
2025-09-29 02:00:36 | INFO     | [debug_LANGGRAPH] |    └─ Execution Time: 56.56s
2025-09-29 02:00:36 | INFO     | [debug_LANGGRAPH] |    └─ End Time: 2025-09-29T02:00:36.582086
2025-09-29 02:08:11 | INFO     | [debug_LANGGRAPH] | 🚀 LANGGRAPH FLOW STARTED [async_cde76545b38a]
2025-09-29 02:08:11 | INFO     | [debug_LANGGRAPH] |    └─ Workflow ID: workflow_async_cde76545b38a
2025-09-29 02:08:11 | INFO     | [debug_LANGGRAPH] |    └─ Model: gemini-2.5-flash
2025-09-29 02:08:11 | INFO     | [debug_LANGGRAPH] |    └─ Start Time: 2025-09-29T02:08:11.852360
2025-09-29 02:08:21 | INFO     | [debug_LANGGRAPH] | 🚀 LANGGRAPH FLOW STARTED [async_328d05c6f330]
2025-09-29 02:08:21 | INFO     | [debug_LANGGRAPH] |    └─ Workflow ID: workflow_async_328d05c6f330
2025-09-29 02:08:21 | INFO     | [debug_LANGGRAPH] |    └─ Model: gemini-2.5-flash
2025-09-29 02:08:21 | INFO     | [debug_LANGGRAPH] |    └─ Start Time: 2025-09-29T02:08:21.733938
2025-09-29 02:08:57 | INFO     | [debug_LANGGRAPH] | 🏁 LANGGRAPH FLOW COMPLETE [async_cde76545b38a]: ✅ SUCCESS
2025-09-29 02:08:57 | INFO     | [debug_LANGGRAPH] |    └─ Execution Time: 46.00s
2025-09-29 02:08:57 | INFO     | [debug_LANGGRAPH] |    └─ End Time: 2025-09-29T02:08:57.849784
2025-09-29 02:09:03 | INFO     | [debug_LANGGRAPH] | 🏁 LANGGRAPH FLOW COMPLETE [async_328d05c6f330]: ✅ SUCCESS
2025-09-29 02:09:03 | INFO     | [debug_LANGGRAPH] |    └─ Execution Time: 41.53s
2025-09-29 02:09:03 | INFO     | [debug_LANGGRAPH] |    └─ End Time: 2025-09-29T02:09:03.262465
2025-09-29 02:11:56 | INFO     | [debug_LANGGRAPH] | 🚀 LANGGRAPH FLOW STARTED [async_97b1923234b1]
2025-09-29 02:11:56 | INFO     | [debug_LANGGRAPH] |    └─ Workflow ID: workflow_async_97b1923234b1
2025-09-29 02:11:56 | INFO     | [debug_LANGGRAPH] |    └─ Model: gemini-2.5-flash
2025-09-29 02:11:56 | INFO     | [debug_LANGGRAPH] |    └─ Start Time: 2025-09-29T02:11:56.960600
2025-09-29 02:13:26 | INFO     | [debug_LANGGRAPH] | 🏁 LANGGRAPH FLOW COMPLETE [async_97b1923234b1]: ✅ SUCCESS
2025-09-29 02:13:26 | INFO     | [debug_LANGGRAPH] |    └─ Execution Time: 89.14s
2025-09-29 02:13:26 | INFO     | [debug_LANGGRAPH] |    └─ End Time: 2025-09-29T02:13:26.104799
2025-09-29 02:26:39 | INFO     | [debug_LANGGRAPH] | 🚀 LANGGRAPH FLOW STARTED [async_9b9b0fe6ef93]
2025-09-29 02:26:39 | INFO     | [debug_LANGGRAPH] |    └─ Workflow ID: workflow_async_9b9b0fe6ef93
2025-09-29 02:26:39 | INFO     | [debug_LANGGRAPH] |    └─ Model: gemini-2.5-flash
2025-09-29 02:26:39 | INFO     | [debug_LANGGRAPH] |    └─ Start Time: 2025-09-29T02:26:39.587117
2025-09-29 02:27:53 | INFO     | [debug_LANGGRAPH] | 🏁 LANGGRAPH FLOW COMPLETE [async_9b9b0fe6ef93]: ✅ SUCCESS
2025-09-29 02:27:53 | INFO     | [debug_LANGGRAPH] |    └─ Execution Time: 73.89s
2025-09-29 02:27:53 | INFO     | [debug_LANGGRAPH] |    └─ End Time: 2025-09-29T02:27:53.482720
2025-09-29 02:32:40 | INFO     | [debug_LANGGRAPH] | 🚀 LANGGRAPH FLOW STARTED [async_a5ae2ef7f44e]
2025-09-29 02:32:40 | INFO     | [debug_LANGGRAPH] |    └─ Workflow ID: workflow_async_a5ae2ef7f44e
2025-09-29 02:32:40 | INFO     | [debug_LANGGRAPH] |    └─ Model: gemini-2.5-flash
2025-09-29 02:32:40 | INFO     | [debug_LANGGRAPH] |    └─ Start Time: 2025-09-29T02:32:40.174412
2025-09-29 02:34:05 | INFO     | [debug_LANGGRAPH] | 🏁 LANGGRAPH FLOW COMPLETE [async_a5ae2ef7f44e]: ✅ SUCCESS
2025-09-29 02:34:05 | INFO     | [debug_LANGGRAPH] |    └─ Execution Time: 85.53s
2025-09-29 02:34:05 | INFO     | [debug_LANGGRAPH] |    └─ End Time: 2025-09-29T02:34:05.705748
2025-09-29 02:39:06 | INFO     | [debug_LANGGRAPH] | 🚀 LANGGRAPH FLOW STARTED [async_c63465d53fd3]
2025-09-29 02:39:06 | INFO     | [debug_LANGGRAPH] |    └─ Workflow ID: workflow_async_c63465d53fd3
2025-09-29 02:39:06 | INFO     | [debug_LANGGRAPH] |    └─ Model: gemini-2.5-flash
2025-09-29 02:39:06 | INFO     | [debug_LANGGRAPH] |    └─ Start Time: 2025-09-29T02:39:06.619696
2025-09-29 02:42:20 | INFO     | [debug_LANGGRAPH] | 🚀 LANGGRAPH FLOW STARTED [async_39487be8168b]
2025-09-29 02:42:20 | INFO     | [debug_LANGGRAPH] |    └─ Workflow ID: workflow_async_39487be8168b
2025-09-29 02:42:20 | INFO     | [debug_LANGGRAPH] |    └─ Model: gemini-2.5-flash
2025-09-29 02:42:20 | INFO     | [debug_LANGGRAPH] |    └─ Start Time: 2025-09-29T02:42:20.894627
2025-09-29 02:46:32 | INFO     | [debug_LANGGRAPH] | 🚀 LANGGRAPH FLOW STARTED [async_4c3c5aea615e]
2025-09-29 02:46:32 | INFO     | [debug_LANGGRAPH] |    └─ Workflow ID: workflow_async_4c3c5aea615e
2025-09-29 02:46:32 | INFO     | [debug_LANGGRAPH] |    └─ Model: gemini-2.5-flash
2025-09-29 02:46:32 | INFO     | [debug_LANGGRAPH] |    └─ Start Time: 2025-09-29T02:46:32.934947
2025-09-29 02:59:15 | INFO     | [debug_LANGGRAPH] | 🚀 LANGGRAPH FLOW STARTED [async_84b5ccec7f7b]
2025-09-29 02:59:15 | INFO     | [debug_LANGGRAPH] |    └─ Workflow ID: workflow_async_84b5ccec7f7b
2025-09-29 02:59:15 | INFO     | [debug_LANGGRAPH] |    └─ Model: gemini-2.5-flash
2025-09-29 02:59:15 | INFO     | [debug_LANGGRAPH] |    └─ Start Time: 2025-09-29T02:59:15.356033
2025-09-29 03:02:43 | INFO     | [debug_LANGGRAPH] | 🚀 LANGGRAPH FLOW STARTED [async_db49593a6419]
2025-09-29 03:02:43 | INFO     | [debug_LANGGRAPH] |    └─ Workflow ID: workflow_async_db49593a6419
2025-09-29 03:02:43 | INFO     | [debug_LANGGRAPH] |    └─ Model: gemini-2.5-flash
2025-09-29 03:02:43 | INFO     | [debug_LANGGRAPH] |    └─ Start Time: 2025-09-29T03:02:43.262471
2025-09-29 09:00:55 | INFO     | [debug_LANGGRAPH] | 🚀 LANGGRAPH FLOW STARTED [async_f2da9bb1f2db]
2025-09-29 09:00:55 | INFO     | [debug_LANGGRAPH] |    └─ Workflow ID: workflow_async_f2da9bb1f2db
2025-09-29 09:00:55 | INFO     | [debug_LANGGRAPH] |    └─ Model: gemini-2.5-flash
2025-09-29 09:00:55 | INFO     | [debug_LANGGRAPH] |    └─ Start Time: 2025-09-29T09:00:55.490431
2025-09-29 09:40:34 | INFO     | [debug_LANGGRAPH] | 🚀 LANGGRAPH FLOW STARTED [async_36c1a608c13e]
2025-09-29 09:40:34 | INFO     | [debug_LANGGRAPH] |    └─ Workflow ID: workflow_async_36c1a608c13e
2025-09-29 09:40:34 | INFO     | [debug_LANGGRAPH] |    └─ Model: gemini-2.5-flash
2025-09-29 09:40:34 | INFO     | [debug_LANGGRAPH] |    └─ Start Time: 2025-09-29T09:40:34.563549
2025-09-29 09:40:44 | INFO     | [debug_LANGGRAPH] | 🚀 LANGGRAPH FLOW STARTED [async_a3d6ccc67456]
2025-09-29 09:40:44 | INFO     | [debug_LANGGRAPH] |    └─ Workflow ID: workflow_async_a3d6ccc67456
2025-09-29 09:40:44 | INFO     | [debug_LANGGRAPH] |    └─ Model: gemini-2.5-flash
2025-09-29 09:40:44 | INFO     | [debug_LANGGRAPH] |    └─ Start Time: 2025-09-29T09:40:44.586576
2025-09-29 10:18:25 | INFO     | [debug_LANGGRAPH] | 🚀 LANGGRAPH FLOW STARTED [async_6952a8bae988]
2025-09-29 10:18:25 | INFO     | [debug_LANGGRAPH] |    └─ Workflow ID: workflow_async_6952a8bae988
2025-09-29 10:18:25 | INFO     | [debug_LANGGRAPH] |    └─ Model: gemini-2.5-flash
2025-09-29 10:18:25 | INFO     | [debug_LANGGRAPH] |    └─ Start Time: 2025-09-29T10:18:25.093338
2025-09-29 10:19:55 | INFO     | [debug_LANGGRAPH] | 🏁 LANGGRAPH FLOW COMPLETE [async_6952a8bae988]: ✅ SUCCESS
2025-09-29 10:19:55 | INFO     | [debug_LANGGRAPH] |    └─ Execution Time: 90.04s
2025-09-29 10:19:55 | INFO     | [debug_LANGGRAPH] |    └─ End Time: 2025-09-29T10:19:55.156502
2025-09-29 12:35:36 | INFO     | [debug_LANGGRAPH] | 🚀 LANGGRAPH FLOW STARTED [async_c166a9acfe64]
2025-09-29 12:35:36 | INFO     | [debug_LANGGRAPH] |    └─ Workflow ID: workflow_async_c166a9acfe64
2025-09-29 12:35:36 | INFO     | [debug_LANGGRAPH] |    └─ Model: gemini-2.5-flash
2025-09-29 12:35:36 | INFO     | [debug_LANGGRAPH] |    └─ Start Time: 2025-09-29T12:35:36.381825
2025-09-29 12:37:31 | INFO     | [debug_LANGGRAPH] | 🏁 LANGGRAPH FLOW COMPLETE [async_c166a9acfe64]: ✅ SUCCESS
2025-09-29 12:37:31 | INFO     | [debug_LANGGRAPH] |    └─ Execution Time: 114.66s
2025-09-29 12:37:31 | INFO     | [debug_LANGGRAPH] |    └─ End Time: 2025-09-29T12:37:31.120510
2025-09-29 13:39:29 | INFO     | [debug_LANGGRAPH] | 🚀 LANGGRAPH FLOW STARTED [async_4ca491b4062c]
2025-09-29 13:39:29 | INFO     | [debug_LANGGRAPH] |    └─ Workflow ID: workflow_async_4ca491b4062c
2025-09-29 13:39:29 | INFO     | [debug_LANGGRAPH] |    └─ Model: gemini-2.5-flash
2025-09-29 13:39:29 | INFO     | [debug_LANGGRAPH] |    └─ Start Time: 2025-09-29T13:39:29.130507
2025-09-29 13:40:30 | INFO     | [debug_LANGGRAPH] | 🏁 LANGGRAPH FLOW COMPLETE [async_4ca491b4062c]: ✅ SUCCESS
2025-09-29 13:40:30 | INFO     | [debug_LANGGRAPH] |    └─ Execution Time: 61.17s
2025-09-29 13:40:30 | INFO     | [debug_LANGGRAPH] |    └─ End Time: 2025-09-29T13:40:30.315977
2025-09-29 15:08:25 | INFO     | [debug_LANGGRAPH] | 🚀 LANGGRAPH FLOW STARTED [async_a36ec6ebb42e]
2025-09-29 15:08:25 | INFO     | [debug_LANGGRAPH] |    └─ Workflow ID: workflow_async_a36ec6ebb42e
2025-09-29 15:08:25 | INFO     | [debug_LANGGRAPH] |    └─ Model: gemini-2.5-flash
2025-09-29 15:08:25 | INFO     | [debug_LANGGRAPH] |    └─ Start Time: 2025-09-29T15:08:25.542424
2025-09-29 15:09:34 | INFO     | [debug_LANGGRAPH] | 🏁 LANGGRAPH FLOW COMPLETE [async_a36ec6ebb42e]: ✅ SUCCESS
2025-09-29 15:09:34 | INFO     | [debug_LANGGRAPH] |    └─ Execution Time: 68.68s
2025-09-29 15:09:34 | INFO     | [debug_LANGGRAPH] |    └─ End Time: 2025-09-29T15:09:34.234223
2025-09-29 15:43:09 | INFO     | [debug_LANGGRAPH] | 🚀 LANGGRAPH FLOW STARTED [async_11c262c36dbb]
2025-09-29 15:43:09 | INFO     | [debug_LANGGRAPH] |    └─ Workflow ID: workflow_async_11c262c36dbb
2025-09-29 15:43:09 | INFO     | [debug_LANGGRAPH] |    └─ Model: gemini-2.5-flash
2025-09-29 15:43:09 | INFO     | [debug_LANGGRAPH] |    └─ Start Time: 2025-09-29T15:43:09.703370
2025-09-29 15:44:11 | INFO     | [debug_LANGGRAPH] | 🏁 LANGGRAPH FLOW COMPLETE [async_11c262c36dbb]: ✅ SUCCESS
2025-09-29 15:44:11 | INFO     | [debug_LANGGRAPH] |    └─ Execution Time: 62.12s
2025-09-29 15:44:11 | INFO     | [debug_LANGGRAPH] |    └─ End Time: 2025-09-29T15:44:11.835834
2025-09-29 15:57:17 | INFO     | [debug_LANGGRAPH] | 🚀 LANGGRAPH FLOW STARTED [async_0a3a571ab098]
2025-09-29 15:57:17 | INFO     | [debug_LANGGRAPH] |    └─ Workflow ID: workflow_async_0a3a571ab098
2025-09-29 15:57:17 | INFO     | [debug_LANGGRAPH] |    └─ Model: gemini-2.5-flash
2025-09-29 15:57:17 | INFO     | [debug_LANGGRAPH] |    └─ Start Time: 2025-09-29T15:57:17.274662
2025-09-29 15:58:01 | INFO     | [debug_LANGGRAPH] | 🏁 LANGGRAPH FLOW COMPLETE [async_0a3a571ab098]: ✅ SUCCESS
2025-09-29 15:58:01 | INFO     | [debug_LANGGRAPH] |    └─ Execution Time: 44.33s
2025-09-29 15:58:01 | INFO     | [debug_LANGGRAPH] |    └─ End Time: 2025-09-29T15:58:01.614130
2025-09-29 15:58:49 | INFO     | [debug_LANGGRAPH] | 🚀 LANGGRAPH FLOW STARTED [async_56ff7d1c2fe1]
2025-09-29 15:58:49 | INFO     | [debug_LANGGRAPH] |    └─ Workflow ID: workflow_async_56ff7d1c2fe1
2025-09-29 15:58:49 | INFO     | [debug_LANGGRAPH] |    └─ Model: gemini-2.5-flash
2025-09-29 15:58:49 | INFO     | [debug_LANGGRAPH] |    └─ Start Time: 2025-09-29T15:58:49.295702
2025-09-29 15:59:04 | INFO     | [debug_LANGGRAPH] | 🏁 LANGGRAPH FLOW COMPLETE [async_56ff7d1c2fe1]: ✅ SUCCESS
2025-09-29 15:59:04 | INFO     | [debug_LANGGRAPH] |    └─ Execution Time: 15.46s
2025-09-29 15:59:04 | INFO     | [debug_LANGGRAPH] |    └─ End Time: 2025-09-29T15:59:04.771130
2025-09-29 16:11:26 | INFO     | [debug_LANGGRAPH] | 🚀 LANGGRAPH FLOW STARTED [async_c50e7081c22c]
2025-09-29 16:11:26 | INFO     | [debug_LANGGRAPH] |    └─ Workflow ID: workflow_async_c50e7081c22c
2025-09-29 16:11:26 | INFO     | [debug_LANGGRAPH] |    └─ Model: gemini-2.5-flash
2025-09-29 16:11:26 | INFO     | [debug_LANGGRAPH] |    └─ Start Time: 2025-09-29T16:11:26.362448
2025-09-29 16:11:38 | INFO     | [debug_LANGGRAPH] | 🏁 LANGGRAPH FLOW COMPLETE [async_c50e7081c22c]: ✅ SUCCESS
2025-09-29 16:11:38 | INFO     | [debug_LANGGRAPH] |    └─ Execution Time: 12.37s
2025-09-29 16:11:38 | INFO     | [debug_LANGGRAPH] |    └─ End Time: 2025-09-29T16:11:38.746521
2025-09-29 16:29:38 | INFO     | [debug_LANGGRAPH] | 🚀 LANGGRAPH FLOW STARTED [async_4f169b5f15ab]
2025-09-29 16:29:38 | INFO     | [debug_LANGGRAPH] |    └─ Workflow ID: workflow_async_4f169b5f15ab
2025-09-29 16:29:38 | INFO     | [debug_LANGGRAPH] |    └─ Model: gemini-2.5-flash
2025-09-29 16:29:38 | INFO     | [debug_LANGGRAPH] |    └─ Start Time: 2025-09-29T16:29:38.462054
2025-09-29 16:30:06 | INFO     | [debug_LANGGRAPH] | 🏁 LANGGRAPH FLOW COMPLETE [async_4f169b5f15ab]: ✅ SUCCESS
2025-09-29 16:30:06 | INFO     | [debug_LANGGRAPH] |    └─ Execution Time: 27.60s
2025-09-29 16:30:06 | INFO     | [debug_LANGGRAPH] |    └─ End Time: 2025-09-29T16:30:06.072931
2025-09-29 16:50:44 | INFO     | [debug_LANGGRAPH] | 🚀 LANGGRAPH FLOW STARTED [async_98d0b880defc]
2025-09-29 16:50:44 | INFO     | [debug_LANGGRAPH] |    └─ Workflow ID: workflow_async_98d0b880defc
2025-09-29 16:50:44 | INFO     | [debug_LANGGRAPH] |    └─ Model: gemini-2.5-flash
2025-09-29 16:50:44 | INFO     | [debug_LANGGRAPH] |    └─ Start Time: 2025-09-29T16:50:44.198154
2025-09-29 16:51:09 | INFO     | [debug_LANGGRAPH] | 🏁 LANGGRAPH FLOW COMPLETE [async_98d0b880defc]: ✅ SUCCESS
2025-09-29 16:51:09 | INFO     | [debug_LANGGRAPH] |    └─ Execution Time: 25.18s
2025-09-29 16:51:09 | INFO     | [debug_LANGGRAPH] |    └─ End Time: 2025-09-29T16:51:09.428225
