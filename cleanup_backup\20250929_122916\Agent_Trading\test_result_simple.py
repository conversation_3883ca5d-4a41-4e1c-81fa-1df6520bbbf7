#!/usr/bin/env python3
"""
Simple test to check the async service state directly
"""
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

def test_async_service():
    """Test the async service directly"""
    try:
        from app.services.async_trading_service import async_trading_service
        
        print("🧪 Testing async service state...")
        print(f"✅ Service initialized: {async_trading_service is not None}")
        
        # Check active tasks
        active_tasks = list(async_trading_service.active_tasks.keys())
        print(f"📊 Active tasks: {len(active_tasks)}")
        if active_tasks:
            print(f"📊 Active task IDs: {active_tasks}")
        
        # Check cached results
        cached_results = list(async_trading_service.results_cache.keys())
        print(f"📊 Cached results: {len(cached_results)}")
        if cached_results:
            print(f"📊 Cached result IDs: {cached_results}")
            
            # Check the specific session from logs
            test_session = "async_e549415c6691"
            if test_session in cached_results:
                print(f"✅ Found target session: {test_session}")
                result = async_trading_service.get_analysis_result(test_session)
                if result:
                    print(f"📊 Result structure: {list(result.keys())}")
                    print(f"📊 Result success: {result.get('success')}")
                    print(f"📊 Result has data: {result.get('data') is not None}")
                    if result.get('data'):
                        data_keys = list(result['data'].keys()) if isinstance(result['data'], dict) else 'not a dict'
                        print(f"📊 Data keys: {data_keys}")
                else:
                    print(f"❌ No result found for session {test_session}")
            else:
                print(f"❌ Target session {test_session} not in cache")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing async service: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🧪 Testing async service state...")
    success = test_async_service()
    if success:
        print("✅ Test completed successfully!")
    else:
        print("❌ Test failed!")
