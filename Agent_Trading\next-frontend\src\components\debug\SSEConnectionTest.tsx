/**
 * SSE Connection Test Component
 * Debug component to test and verify SSE connection fixes
 */

import React, { useState } from 'react'
import { useRealTimeProgress } from '@/hooks/useRealTimeProgress'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { AlertCircle, CheckCircle, Loader2, Wifi, WifiOff } from 'lucide-react'

export default function SSEConnectionTest() {
  const {
    sessionId,
    currentStep,
    progress,
    message,
    isConnected,
    isComplete,
    error,
    updates,
    connectToProgress,
    checkConnectionHealth,
    retryConnection,
    cleanup
  } = useRealTimeProgress()

  const [testSessionId, setTestSessionId] = useState('')
  const [healthStatus, setHealthStatus] = useState<'unknown' | 'healthy' | 'unhealthy'>('unknown')
  const [isTestingHealth, setIsTestingHealth] = useState(false)

  const handleTestConnection = async () => {
    if (!testSessionId.trim()) {
      alert('Please enter a session ID to test')
      return
    }
    
    console.log('🧪 Testing SSE connection with session:', testSessionId)
    await connectToProgress(testSessionId.trim())
  }

  const handleHealthCheck = async () => {
    setIsTestingHealth(true)
    try {
      const isHealthy = await checkConnectionHealth()
      setHealthStatus(isHealthy ? 'healthy' : 'unhealthy')
    } catch (error) {
      console.error('Health check failed:', error)
      setHealthStatus('unhealthy')
    } finally {
      setIsTestingHealth(false)
    }
  }

  const handleRetry = async () => {
    if (sessionId) {
      await retryConnection(sessionId, 1)
    }
  }

  const handleCleanup = () => {
    cleanup()
    setTestSessionId('')
  }

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Wifi className="w-5 h-5" />
            SSE Connection Test
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Connection Status */}
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2">
              {isConnected ? (
                <CheckCircle className="w-5 h-5 text-green-500" />
              ) : error ? (
                <AlertCircle className="w-5 h-5 text-red-500" />
              ) : (
                <Loader2 className="w-5 h-5 text-yellow-500 animate-spin" />
              )}
              <span className="font-medium">
                {isConnected ? 'Connected' : error ? 'Error' : 'Connecting...'}
              </span>
            </div>
            
            {/* Health Status */}
            <div className="flex items-center gap-2">
              <Badge variant={
                healthStatus === 'healthy' ? 'default' : 
                healthStatus === 'unhealthy' ? 'destructive' : 
                'secondary'
              }>
                Backend: {healthStatus}
              </Badge>
              <Button
                size="sm"
                variant="outline"
                onClick={handleHealthCheck}
                disabled={isTestingHealth}
              >
                {isTestingHealth ? (
                  <Loader2 className="w-4 h-4 animate-spin" />
                ) : (
                  'Check Health'
                )}
              </Button>
            </div>
          </div>

          {/* Session Information */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="text-sm font-medium text-gray-700">Current Session ID</label>
              <div className="mt-1 p-2 bg-gray-100 rounded border">
                {sessionId || 'N/A'}
              </div>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-700">Current Step</label>
              <div className="mt-1 p-2 bg-gray-100 rounded border">
                {currentStep} ({progress}%)
              </div>
            </div>
          </div>

          {/* Current Message */}
          <div>
            <label className="text-sm font-medium text-gray-700">Current Message</label>
            <div className="mt-1 p-2 bg-gray-100 rounded border">
              {message}
            </div>
          </div>

          {/* Error Display */}
          {error && (
            <div className="p-3 bg-red-50 border border-red-200 rounded">
              <div className="flex items-center gap-2">
                <AlertCircle className="w-5 h-5 text-red-500" />
                <span className="font-medium text-red-700">Error</span>
              </div>
              <p className="text-red-600 text-sm mt-1">{error}</p>
            </div>
          )}

          {/* Test Controls */}
          <div className="space-y-3">
            <div className="flex gap-2">
              <input
                type="text"
                placeholder="Enter session ID to test (e.g., async_1234567890ab)"
                value={testSessionId}
                onChange={(e) => setTestSessionId(e.target.value)}
                className="flex-1 px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
              <Button onClick={handleTestConnection}>
                Test Connection
              </Button>
            </div>
            
            <div className="flex gap-2">
              <Button
                variant="outline"
                onClick={handleRetry}
                disabled={!sessionId}
              >
                Retry Connection
              </Button>
              <Button
                variant="outline"
                onClick={handleCleanup}
              >
                Cleanup
              </Button>
            </div>
          </div>

          {/* Recent Updates */}
          {updates.length > 0 && (
            <div>
              <label className="text-sm font-medium text-gray-700">Recent Updates</label>
              <div className="mt-1 space-y-2 max-h-40 overflow-y-auto">
                {updates.slice(-5).map((update, index) => (
                  <div key={`${update.timestamp}-${index}`} className="p-2 bg-gray-50 rounded border text-sm">
                    <div className="flex justify-between items-start">
                      <span className="font-medium">{update.step}</span>
                      <span className="text-gray-500 text-xs">
                        {new Date(update.timestamp).toLocaleTimeString()}
                      </span>
                    </div>
                    <p className="text-gray-600">{update.message}</p>
                    {update.details && (
                      <div className="text-xs text-gray-500 mt-1">
                        {JSON.stringify(update.details, null, 2)}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Debug Information */}
          <details className="mt-4">
            <summary className="cursor-pointer text-sm font-medium text-gray-700">
              Debug Information
            </summary>
            <div className="mt-2 p-3 bg-gray-50 rounded border">
              <pre className="text-xs text-gray-600 whitespace-pre-wrap">
                {JSON.stringify({
                  sessionId,
                  currentStep,
                  progress,
                  message,
                  isConnected,
                  isComplete,
                  error,
                  updatesCount: updates.length,
                  healthStatus
                }, null, 2)}
              </pre>
            </div>
          </details>
        </CardContent>
      </Card>
    </div>
  )
}
