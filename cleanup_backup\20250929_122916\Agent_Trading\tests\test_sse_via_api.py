#!/usr/bin/env python3
"""
SSE Test via API Endpoint
=========================

This script tests SSE by triggering a real analysis via the API
to see if the progress tracking works in the actual application flow.
"""

import asyncio
import aiohttp
import json
import base64
from PIL import Image
import io
import time
from datetime import datetime

# Your credentials
EMAIL = "<EMAIL>"
PASSWORD = "Bunnych@1627"
BASE_URL = "http://localhost:8000"

async def create_test_image():
    """Create a proper test chart image"""
    # Create a 400x300 chart-like image
    img = Image.new('RGB', (400, 300), color='white')
    
    # Add some basic chart elements
    from PIL import ImageDraw
    draw = ImageDraw.Draw(img)
    
    # Draw axes
    draw.line([(50, 250), (350, 250)], fill='black', width=2)  # X-axis
    draw.line([(50, 50), (50, 250)], fill='black', width=2)   # Y-axis
    
    # Draw a simple price line
    points = [(50, 200), (100, 180), (150, 160), (200, 140), (250, 120), (300, 100), (350, 80)]
    for i in range(len(points) - 1):
        draw.line([points[i], points[i+1]], fill='blue', width=3)
    
    # Convert to base64
    buffer = io.BytesIO()
    img.save(buffer, format='JPEG')
    img_data = buffer.getvalue()
    img_base64 = base64.b64encode(img_data).decode('utf-8')
    
    return img_base64

async def test_real_analysis_sse():
    """Test SSE with a real analysis request"""
    print("🚀 Testing SSE with Real Analysis")
    print("=" * 50)
    
    # Step 1: Login
    print("1️⃣ Authenticating...")
    async with aiohttp.ClientSession() as session:
        login_data = {"email": EMAIL, "password": PASSWORD}
        
        async with session.post(f"{BASE_URL}/api/v1/auth/login", json=login_data) as response:
            if response.status == 200:
                result = await response.json()
                token = result["access_token"]
                print(f"✅ Authenticated as {result['user']['email']}")
            else:
                print(f"❌ Authentication failed: {response.status}")
                return
    
    # Step 2: Create test image
    print("\n2️⃣ Creating test chart image...")
    test_image = await create_test_image()
    print(f"✅ Test image created")
    
    # Step 3: Start analysis and SSE monitoring concurrently
    print("\n3️⃣ Starting analysis and SSE monitoring...")
    
    async def start_analysis():
        """Start the analysis"""
        await asyncio.sleep(2)  # Wait for SSE to connect first
        
        print("📤 Starting analysis...")
        headers = {
            "Authorization": f"Bearer {token}",
            "Content-Type": "application/json"
        }
        
        analysis_data = {
            "images_base64": [test_image],
            "analysis_type": "Positional",
            "market_specialization": "Crypto",
            "preferred_model": "gemini-2.5-flash"
        }
        
        async with aiohttp.ClientSession() as session:
            async with session.post(f"{BASE_URL}/api/v1/trading/async/start", 
                                   headers=headers, json=analysis_data) as response:
                if response.status == 200:
                    result = await response.json()
                    session_id = result["session_id"]
                    print(f"✅ Analysis started with session: {session_id}")
                    return session_id
                else:
                    error = await response.text()
                    print(f"❌ Analysis failed: {response.status} - {error}")
                    return None
    
    async def monitor_sse(session_id):
        """Monitor SSE events"""
        if not session_id:
            print("❌ No session ID for SSE monitoring")
            return
        
        sse_url = f"{BASE_URL}/api/v1/progress/stream/{session_id}?token={token}"
        print(f"📡 SSE URL: {sse_url}")
        
        async with aiohttp.ClientSession() as session:
            try:
                async with session.get(sse_url, headers={"Accept": "text/event-stream"}) as response:
                    if response.status != 200:
                        print(f"❌ SSE connection failed: {response.status}")
                        return
                    
                    print("📡 SSE connection established")
                    event_count = 0
                    start_time = time.time()
                    progress_events = []
                    
                    async for line in response.content:
                        line = line.decode('utf-8').strip()
                        
                        if line.startswith('data: '):
                            data_str = line[6:]
                            
                            try:
                                data = json.loads(data_str)
                                event_count += 1
                                current_time = time.time()
                                elapsed = current_time - start_time
                                
                                timestamp = datetime.now().strftime("%H:%M:%S.%f")[:-3]
                                
                                if data.get('type') == 'progress_update':
                                    update = data.get('data', {})
                                    step = update.get('step', 'unknown')
                                    progress = update.get('progress', 0)
                                    message = update.get('message', '')
                                    
                                    print(f"[{timestamp}] 📈 Event #{event_count} (+{elapsed:.1f}s): {step} ({progress}%) - {message}")
                                    progress_events.append({
                                        'timestamp': timestamp,
                                        'elapsed': elapsed,
                                        'step': step,
                                        'progress': progress,
                                        'message': message
                                    })
                                    
                                elif data.get('type') == 'connected':
                                    print(f"[{timestamp}] 🔗 Connected to session")
                                    
                                elif data.get('type') == 'complete':
                                    print(f"[{timestamp}] 🎉 Analysis complete")
                                    break
                                    
                                elif data.get('type') == 'ping':
                                    print(f"[{timestamp}] 🏓 Keepalive ping")
                                    
                                else:
                                    print(f"[{timestamp}] 📨 Event #{event_count}: {data.get('type', 'unknown')}")
                                    
                            except json.JSONDecodeError:
                                print(f"⚠️ Failed to parse: {data_str}")
                    
                    # Analyze results
                    print("\n" + "=" * 50)
                    print("📊 SSE ANALYSIS RESULTS")
                    print("=" * 50)
                    
                    if progress_events:
                        print(f"✅ Received {len(progress_events)} progress events")
                        print("\n📋 Progress Timeline:")
                        
                        for i, event in enumerate(progress_events, 1):
                            print(f"   {i}. [{event['timestamp']}] {event['step']} ({event['progress']}%) - {event['message']}")
                        
                        # Check timing
                        if len(progress_events) > 1:
                            time_gaps = []
                            for i in range(1, len(progress_events)):
                                gap = progress_events[i]['elapsed'] - progress_events[i-1]['elapsed']
                                time_gaps.append(gap)
                            
                            avg_gap = sum(time_gaps) / len(time_gaps)
                            print(f"\n⏱️ Average time between events: {avg_gap:.1f} seconds")
                            
                            if avg_gap < 2:
                                print("✅ Events are streaming in real-time!")
                            else:
                                print("⚠️ Events may be buffered or delayed")
                        
                        # Check progress sequence
                        progress_values = [event['progress'] for event in progress_events]
                        if progress_values == sorted(progress_values):
                            print("✅ Progress values are in correct order")
                        else:
                            print("❌ Progress values are out of order")
                    
                    else:
                        print("❌ No progress events received!")
                        print("💡 This indicates the SSE connection is not working properly")
                        
            except Exception as e:
                print(f"❌ SSE monitoring error: {e}")
    
    # Start analysis first, then monitor SSE
    session_id = await start_analysis()
    if session_id:
        await monitor_sse(session_id)
    
    print("\n🎯 CONCLUSION:")
    print("If you see progress events streaming in real-time, SSE is working!")
    print("If no events or all events at once, there's still an SSE issue.")

if __name__ == "__main__":
    asyncio.run(test_real_analysis_sse())
