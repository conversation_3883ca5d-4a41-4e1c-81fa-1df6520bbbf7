2025-09-29 15:57:17 | INFO     | log_session_start    | ================================================================================
2025-09-29 15:57:17 | INFO     | log_session_start    | 🚀 PROGRESS ANALYSIS SESSION STARTED
2025-09-29 15:57:17 | INFO     | log_session_start    | 📋 Session ID: async_0a3a571ab098
2025-09-29 15:57:17 | INFO     | log_session_start    | 📁 Log File: Agent_Trading\backend\logs\progress_bar\progress_analysis_20250929_155717_async_0a3a571ab098.log
2025-09-29 15:57:17 | INFO     | log_session_start    | ⏰ Start Time: 2025-09-29T15:57:17.444847
2025-09-29 15:57:17 | INFO     | log_session_start    | ================================================================================
2025-09-29 15:57:17 | INFO     | log_parameter_passing | 🔧 PARAMETER_PASSING | Component: WORKFLOW_INIT
2025-09-29 15:57:17 | INFO     | log_parameter_passing | ✅ PARAM_OK | session_id: str = async_0a3a571ab098
2025-09-29 15:57:17 | INFO     | log_parameter_passing | ✅ PARAM_OK | progress_broadcaster: ProgressBroadcaster = <app.helpers.workflow_management.progress_tracker.ProgressBroadcaster object at 0x000001995990AB10>
2025-09-29 15:57:17 | INFO     | log_parameter_passing | ✅ PARAM_OK | progress_broadcaster_type: str = ProgressBroadcaster
2025-09-29 15:57:17 | INFO     | log_parameter_passing | ✅ PARAM_OK | analysis_mode: str = positional
2025-09-29 15:57:17 | INFO     | log_parameter_passing | ✅ PARAM_OK | market_specialization: str = Crypto
2025-09-29 15:57:17 | INFO     | log_parameter_passing | ✅ PARAM_OK | chart_images_count: int = 1
2025-09-29 15:57:17 | INFO     | log_parameter_passing | ✅ PARAM_OK | user_query: str = Positional trading analysis for Crypto market | User tier: free - Provide comprehensive analysis wit...
2025-09-29 15:57:17 | INFO     | log_parameter_passing | 🔧 PARAMETER_PASSING | Component: WORKFLOW_VERIFY
2025-09-29 15:57:17 | INFO     | log_parameter_passing | ✅ PARAM_OK | instance_progress_broadcaster: bool = True
2025-09-29 15:57:17 | INFO     | log_parameter_passing | ✅ PARAM_OK | instance_session_id: str = async_0a3a571ab098
2025-09-29 15:57:17 | INFO     | log_parameter_passing | ✅ PARAM_OK | instance_progress_broadcaster_type: str = ProgressBroadcaster
2025-09-29 15:57:17 | INFO     | log_sse_event        | 📡 SSE_EVENT | PROGRESS_BROADCAST_ATTEMPT - SUCCESS
2025-09-29 15:57:17 | DEBUG    | log_sse_event        |    📋 step: chart_analysis
2025-09-29 15:57:17 | DEBUG    | log_sse_event        |    📋 progress: 20
2025-09-29 15:57:17 | DEBUG    | log_sse_event        |    📋 message: 📊 Chart analysis completed
2025-09-29 15:57:17 | DEBUG    | log_sse_event        |    📋 details: {'node': 'chart_analysis'}
2025-09-29 15:57:17 | DEBUG    | log_sse_event        |    📋 session_id: async_0a3a571ab098
2025-09-29 15:57:17 | INFO     | log_sse_event        | 📡 SSE_EVENT | PROGRESS_BROADCAST_SUCCESS - SUCCESS
2025-09-29 15:57:17 | DEBUG    | log_sse_event        |    📋 workflow_step: chart_analysis
2025-09-29 15:57:17 | DEBUG    | log_sse_event        |    📋 progress: 20
2025-09-29 15:57:17 | DEBUG    | log_sse_event        |    📋 message: 📊 Chart analysis completed
2025-09-29 15:57:17 | DEBUG    | log_sse_event        |    📋 session_id: async_0a3a571ab098
2025-09-29 15:57:47 | INFO     | log_sse_event        | 📡 SSE_EVENT | PROGRESS_BROADCAST_ATTEMPT - SUCCESS
2025-09-29 15:57:47 | DEBUG    | log_sse_event        |    📋 step: symbol_detection
2025-09-29 15:57:47 | DEBUG    | log_sse_event        |    📋 progress: 30
2025-09-29 15:57:47 | DEBUG    | log_sse_event        |    📋 message: 🔍 Symbol detection completed
2025-09-29 15:57:47 | DEBUG    | log_sse_event        |    📋 details: {'node': 'symbol_detection'}
2025-09-29 15:57:47 | DEBUG    | log_sse_event        |    📋 session_id: async_0a3a571ab098
2025-09-29 15:57:47 | INFO     | log_sse_event        | 📡 SSE_EVENT | PROGRESS_BROADCAST_SUCCESS - SUCCESS
2025-09-29 15:57:47 | DEBUG    | log_sse_event        |    📋 workflow_step: symbol_detection
2025-09-29 15:57:47 | DEBUG    | log_sse_event        |    📋 progress: 40
2025-09-29 15:57:47 | DEBUG    | log_sse_event        |    📋 message: 🔍 Symbol detection completed
2025-09-29 15:57:47 | DEBUG    | log_sse_event        |    📋 session_id: async_0a3a571ab098
2025-09-29 15:57:47 | INFO     | log_sse_event        | 📡 SSE_EVENT | PROGRESS_BROADCAST_ATTEMPT - SUCCESS
2025-09-29 15:57:47 | DEBUG    | log_sse_event        |    📋 step: tool_execution
2025-09-29 15:57:47 | DEBUG    | log_sse_event        |    📋 progress: 50
2025-09-29 15:57:47 | DEBUG    | log_sse_event        |    📋 message: 🛠️ Data collection completed
2025-09-29 15:57:47 | DEBUG    | log_sse_event        |    📋 details: {'node': 'tool_execution'}
2025-09-29 15:57:47 | DEBUG    | log_sse_event        |    📋 session_id: async_0a3a571ab098
2025-09-29 15:57:47 | INFO     | log_sse_event        | 📡 SSE_EVENT | PROGRESS_BROADCAST_SUCCESS - SUCCESS
2025-09-29 15:57:47 | DEBUG    | log_sse_event        |    📋 workflow_step: tool_execution
2025-09-29 15:57:47 | DEBUG    | log_sse_event        |    📋 progress: 50
2025-09-29 15:57:47 | DEBUG    | log_sse_event        |    📋 message: 🛠️ Data collection completed
2025-09-29 15:57:47 | DEBUG    | log_sse_event        |    📋 session_id: async_0a3a571ab098
2025-09-29 15:57:47 | INFO     | log_sse_event        | 📡 SSE_EVENT | PROGRESS_BROADCAST_ATTEMPT - SUCCESS
2025-09-29 15:57:47 | DEBUG    | log_sse_event        |    📋 step: tool_summarization
2025-09-29 15:57:47 | DEBUG    | log_sse_event        |    📋 progress: 60
2025-09-29 15:57:47 | DEBUG    | log_sse_event        |    📋 message: 📋 Data processing completed
2025-09-29 15:57:47 | DEBUG    | log_sse_event        |    📋 details: {'node': 'tool_summarization'}
2025-09-29 15:57:47 | DEBUG    | log_sse_event        |    📋 session_id: async_0a3a571ab098
2025-09-29 15:57:47 | INFO     | log_sse_event        | 📡 SSE_EVENT | PROGRESS_BROADCAST_SUCCESS - SUCCESS
2025-09-29 15:57:47 | DEBUG    | log_sse_event        |    📋 workflow_step: tool_summarization
2025-09-29 15:57:47 | DEBUG    | log_sse_event        |    📋 progress: 60
2025-09-29 15:57:47 | DEBUG    | log_sse_event        |    📋 message: 📋 Data processing completed
2025-09-29 15:57:47 | DEBUG    | log_sse_event        |    📋 session_id: async_0a3a571ab098
2025-09-29 15:57:47 | INFO     | log_sse_event        | 📡 SSE_EVENT | PROGRESS_BROADCAST_ATTEMPT - SUCCESS
2025-09-29 15:57:47 | DEBUG    | log_sse_event        |    📋 step: rag_integration
2025-09-29 15:57:47 | DEBUG    | log_sse_event        |    📋 progress: 70
2025-09-29 15:57:47 | DEBUG    | log_sse_event        |    📋 message: 🧠 RAG integration completed
2025-09-29 15:57:47 | DEBUG    | log_sse_event        |    📋 details: {'node': 'rag_integration'}
2025-09-29 15:57:47 | DEBUG    | log_sse_event        |    📋 session_id: async_0a3a571ab098
2025-09-29 15:57:47 | INFO     | log_sse_event        | 📡 SSE_EVENT | PROGRESS_BROADCAST_SUCCESS - SUCCESS
2025-09-29 15:57:47 | DEBUG    | log_sse_event        |    📋 workflow_step: rag_integration
2025-09-29 15:57:47 | DEBUG    | log_sse_event        |    📋 progress: 70
2025-09-29 15:57:47 | DEBUG    | log_sse_event        |    📋 message: 🧠 RAG integration completed
2025-09-29 15:57:47 | DEBUG    | log_sse_event        |    📋 session_id: async_0a3a571ab098
2025-09-29 15:57:51 | INFO     | log_sse_event        | 📡 SSE_EVENT | PROGRESS_BROADCAST_ATTEMPT - SUCCESS
2025-09-29 15:57:51 | DEBUG    | log_sse_event        |    📋 step: final_analysis
2025-09-29 15:57:51 | DEBUG    | log_sse_event        |    📋 progress: 80
2025-09-29 15:57:51 | DEBUG    | log_sse_event        |    📋 message: 🎯 AI analysis completed
2025-09-29 15:57:51 | DEBUG    | log_sse_event        |    📋 details: {'node': 'final_analysis'}
2025-09-29 15:57:51 | DEBUG    | log_sse_event        |    📋 session_id: async_0a3a571ab098
2025-09-29 15:57:51 | INFO     | log_sse_event        | 📡 SSE_EVENT | PROGRESS_BROADCAST_SUCCESS - SUCCESS
2025-09-29 15:57:51 | DEBUG    | log_sse_event        |    📋 workflow_step: final_analysis
2025-09-29 15:57:51 | DEBUG    | log_sse_event        |    📋 progress: 80
2025-09-29 15:57:51 | DEBUG    | log_sse_event        |    📋 message: 🎯 AI analysis completed
2025-09-29 15:57:51 | DEBUG    | log_sse_event        |    📋 session_id: async_0a3a571ab098
2025-09-29 15:57:51 | INFO     | log_workflow_step    | 📊 WORKFLOW_STEP | memory_storage (90%) - 💾 Storing analysis for future reference...
2025-09-29 15:57:51 | DEBUG    | log_workflow_step    |    📋 step: memory_storage
2025-09-29 15:57:51 | INFO     | log_sse_event        | 📡 SSE_EVENT | PROGRESS_BROADCAST_ATTEMPT - SUCCESS
2025-09-29 15:57:51 | DEBUG    | log_sse_event        |    📋 step: memory_storage
2025-09-29 15:57:51 | DEBUG    | log_sse_event        |    📋 progress: 90
2025-09-29 15:57:51 | DEBUG    | log_sse_event        |    📋 message: 💾 Storing analysis for future reference...
2025-09-29 15:57:51 | DEBUG    | log_sse_event        |    📋 details: {'step': 'memory_storage'}
2025-09-29 15:57:51 | DEBUG    | log_sse_event        |    📋 session_id: async_0a3a571ab098
2025-09-29 15:57:51 | INFO     | log_sse_event        | 📡 SSE_EVENT | PROGRESS_BROADCAST_SUCCESS - SUCCESS
2025-09-29 15:57:51 | DEBUG    | log_sse_event        |    📋 workflow_step: memory_storage
2025-09-29 15:57:51 | DEBUG    | log_sse_event        |    📋 progress: 90
2025-09-29 15:57:51 | DEBUG    | log_sse_event        |    📋 message: 💾 Storing analysis for future reference...
2025-09-29 15:57:51 | DEBUG    | log_sse_event        |    📋 session_id: async_0a3a571ab098
2025-09-29 15:57:51 | INFO     | log_workflow_step    | 📊 WORKFLOW_STEP | complete (100%) - 🎉 Analysis completed successfully!
2025-09-29 15:57:51 | DEBUG    | log_workflow_step    |    📋 step: complete
2025-09-29 15:57:51 | DEBUG    | log_workflow_step    |    📋 workflow_status: complete
2025-09-29 15:57:51 | INFO     | log_sse_event        | 📡 SSE_EVENT | PROGRESS_BROADCAST_ATTEMPT - SUCCESS
2025-09-29 15:57:51 | DEBUG    | log_sse_event        |    📋 step: complete
2025-09-29 15:57:51 | DEBUG    | log_sse_event        |    📋 progress: 100
2025-09-29 15:57:51 | DEBUG    | log_sse_event        |    📋 message: 🎉 Analysis completed successfully!
2025-09-29 15:57:51 | DEBUG    | log_sse_event        |    📋 details: {'step': 'complete', 'workflow_status': 'complete'}
2025-09-29 15:57:51 | DEBUG    | log_sse_event        |    📋 session_id: async_0a3a571ab098
2025-09-29 15:57:51 | INFO     | log_sse_event        | 📡 SSE_EVENT | PROGRESS_BROADCAST_SUCCESS - SUCCESS
2025-09-29 15:57:51 | DEBUG    | log_sse_event        |    📋 workflow_step: complete
2025-09-29 15:57:51 | DEBUG    | log_sse_event        |    📋 progress: 100
2025-09-29 15:57:52 | DEBUG    | log_sse_event        |    📋 message: 🎉 Analysis completed successfully!
2025-09-29 15:57:52 | DEBUG    | log_sse_event        |    📋 session_id: async_0a3a571ab098
2025-09-29 15:57:52 | INFO     | log_sse_event        | 📡 SSE_EVENT | PROGRESS_BROADCAST_ATTEMPT - SUCCESS
2025-09-29 15:57:52 | DEBUG    | log_sse_event        |    📋 step: memory_storage
2025-09-29 15:57:52 | DEBUG    | log_sse_event        |    📋 progress: 90
2025-09-29 15:57:52 | DEBUG    | log_sse_event        |    📋 message: 💾 Memory storage completed
2025-09-29 15:57:52 | DEBUG    | log_sse_event        |    📋 details: {'node': 'memory_storage'}
2025-09-29 15:57:52 | DEBUG    | log_sse_event        |    📋 session_id: async_0a3a571ab098
2025-09-29 15:57:52 | INFO     | log_sse_event        | 📡 SSE_EVENT | PROGRESS_BROADCAST_SUCCESS - SUCCESS
2025-09-29 15:57:52 | DEBUG    | log_sse_event        |    📋 workflow_step: memory_storage
2025-09-29 15:57:52 | DEBUG    | log_sse_event        |    📋 progress: 90
2025-09-29 15:57:52 | DEBUG    | log_sse_event        |    📋 message: 💾 Memory storage completed
2025-09-29 15:57:52 | DEBUG    | log_sse_event        |    📋 session_id: async_0a3a571ab098
