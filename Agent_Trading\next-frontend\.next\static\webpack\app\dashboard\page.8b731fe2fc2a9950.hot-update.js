"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/hooks/useRealTimeProgress.ts":
/*!******************************************!*\
  !*** ./src/hooks/useRealTimeProgress.ts ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WORKFLOW_STEPS: () => (/* binding */ WORKFLOW_STEPS),\n/* harmony export */   useRealTimeProgress: () => (/* binding */ useRealTimeProgress)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/**\r\n * Real-time progress tracking hook for LangGraph AI analysis workflow\r\n * Uses Server-Sent Events ONLY for live updates (no polling fallback)\r\n */ \n// ✅ ALIGNED: Frontend mapping now matches actual LangGraph workflow execution\nconst WORKFLOW_STEPS = {\n    CHART_ANALYSIS: {\n        name: 'Chart Analysis',\n        progress: 20,\n        message: '📊 Starting comprehensive AI analysis workflow...'\n    },\n    SYMBOL_DETECTION: {\n        name: 'Symbol Detection',\n        progress: 40,\n        message: '✅ Symbol detected in chart'\n    },\n    TOOL_EXECUTION: {\n        name: 'Data Collection',\n        progress: 50,\n        message: '🛠️ Collecting data from tools...'\n    },\n    TOOL_SUMMARIZATION: {\n        name: 'Data Processing',\n        progress: 60,\n        message: '📋 Processing and summarizing data...'\n    },\n    RAG_INTEGRATION: {\n        name: 'RAG Integration',\n        progress: 70,\n        message: '🧠 Integrating historical context and patterns...'\n    },\n    FINAL_ANALYSIS: {\n        name: 'Final Analysis',\n        progress: 80,\n        message: '🎯 Generating final trading analysis...'\n    },\n    MEMORY_STORAGE: {\n        name: 'Database Storage',\n        progress: 90,\n        message: '💾 Saving analysis...'\n    },\n    COMPLETE: {\n        name: 'Complete',\n        progress: 100,\n        message: '🎉 Analysis complete!'\n    }\n};\nfunction useRealTimeProgress() {\n    const [state, setState] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({\n        sessionId: null,\n        currentStep: 'chart_analysis',\n        progress: 0,\n        message: 'Preparing for analysis...',\n        isConnected: false,\n        isComplete: false,\n        error: null,\n        updates: []\n    });\n    // Add retry state\n    const [retryCount, setRetryCount] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(0);\n    const maxRetries = 3;\n    // 🔧 CRITICAL FIX: Progress smoothing for rapid updates\n    const [progressQueue, setProgressQueue] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [isProcessingQueue, setIsProcessingQueue] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    // Process progress queue with smooth animations\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useRealTimeProgress.useEffect\": ()=>{\n            if (progressQueue.length > 0 && !isProcessingQueue) {\n                setIsProcessingQueue(true);\n                const processNext = {\n                    \"useRealTimeProgress.useEffect.processNext\": ()=>{\n                        setProgressQueue({\n                            \"useRealTimeProgress.useEffect.processNext\": (queue)=>{\n                                if (queue.length === 0) {\n                                    setIsProcessingQueue(false);\n                                    return queue;\n                                }\n                                const [nextUpdate, ...remaining] = queue;\n                                // Apply the update\n                                setState({\n                                    \"useRealTimeProgress.useEffect.processNext\": (prev)=>({\n                                            ...prev,\n                                            currentStep: nextUpdate.step,\n                                            progress: nextUpdate.progress,\n                                            message: nextUpdate.message\n                                        })\n                                }[\"useRealTimeProgress.useEffect.processNext\"]);\n                                // 🔧 IMPROVED: Dynamic delay based on queue length and progress difference\n                                let delay = 400 // Base delay reduced from 800ms to 400ms\n                                ;\n                                if (remaining.length > 0) {\n                                    const nextProgress = remaining[0].progress;\n                                    const currentProgress = nextUpdate.progress;\n                                    const progressDiff = nextProgress - currentProgress;\n                                    // Shorter delay for small progress jumps, longer for big jumps\n                                    if (progressDiff <= 10) {\n                                        delay = 300; // Fast updates for small increments\n                                    } else if (progressDiff <= 20) {\n                                        delay = 500; // Medium delay for medium increments\n                                    } else {\n                                        delay = 700; // Longer delay for big jumps\n                                    }\n                                    // If queue is getting long, speed up processing\n                                    if (remaining.length > 3) {\n                                        delay = Math.max(200, delay * 0.6); // Speed up but not too fast\n                                    }\n                                    console.log(\"\\uD83D\\uDCC8 FRONTEND_DEBUG: Processing queue - current: \".concat(currentProgress, \"%, next: \").concat(nextProgress, \"%, delay: \").concat(delay, \"ms, queue length: \").concat(remaining.length));\n                                    setTimeout(processNext, delay);\n                                } else {\n                                    setIsProcessingQueue(false);\n                                }\n                                return remaining;\n                            }\n                        }[\"useRealTimeProgress.useEffect.processNext\"]);\n                    }\n                }[\"useRealTimeProgress.useEffect.processNext\"];\n                processNext();\n            }\n        }\n    }[\"useRealTimeProgress.useEffect\"], [\n        progressQueue,\n        isProcessingQueue\n    ]);\n    const eventSourceRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const currentSessionRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    // Retry connection function\n    const retryConnection = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useRealTimeProgress.useCallback[retryConnection]\": async function(sessionId) {\n            let attempt = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 1;\n            if (attempt > maxRetries) {\n                console.error(\"❌ FRONTEND_DEBUG: Max retries (\".concat(maxRetries, \") exceeded for session \").concat(sessionId));\n                setState({\n                    \"useRealTimeProgress.useCallback[retryConnection]\": (prev)=>({\n                            ...prev,\n                            error: \"Connection failed after \".concat(maxRetries, \" attempts. Please refresh and try again.\")\n                        })\n                }[\"useRealTimeProgress.useCallback[retryConnection]\"]);\n                return;\n            }\n            console.log(\"\\uD83D\\uDD04 FRONTEND_DEBUG: Retry attempt \".concat(attempt, \"/\").concat(maxRetries, \" for session \").concat(sessionId));\n            setState({\n                \"useRealTimeProgress.useCallback[retryConnection]\": (prev)=>({\n                        ...prev,\n                        error: null,\n                        message: \"Reconnecting... (attempt \".concat(attempt, \"/\").concat(maxRetries, \")\")\n                    })\n            }[\"useRealTimeProgress.useCallback[retryConnection]\"]);\n            // Wait before retry (exponential backoff)\n            const delay = Math.min(1000 * Math.pow(2, attempt - 1), 5000);\n            await new Promise({\n                \"useRealTimeProgress.useCallback[retryConnection]\": (resolve)=>setTimeout(resolve, delay)\n            }[\"useRealTimeProgress.useCallback[retryConnection]\"]);\n            // Retry the connection\n            await connectToProgress(sessionId);\n        }\n    }[\"useRealTimeProgress.useCallback[retryConnection]\"], [\n        maxRetries\n    ]);\n    // Check if analysis is already complete\n    const checkAnalysisStatus = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useRealTimeProgress.useCallback[checkAnalysisStatus]\": async (sessionId)=>{\n            try {\n                const token = localStorage.getItem('access_token');\n                const baseURL = \"http://localhost:8000\" || 0;\n                const response = await fetch(\"\".concat(baseURL, \"/api/v1/async-trading/status/\").concat(sessionId), {\n                    headers: {\n                        'Authorization': \"Bearer \".concat(token),\n                        'Content-Type': 'application/json'\n                    }\n                });\n                if (response.ok) {\n                    const data = await response.json();\n                    if (data.status === 'completed') {\n                        setState({\n                            \"useRealTimeProgress.useCallback[checkAnalysisStatus]\": (prev)=>({\n                                    ...prev,\n                                    isComplete: true,\n                                    progress: 100,\n                                    currentStep: 'complete',\n                                    message: 'Analysis completed successfully!'\n                                })\n                        }[\"useRealTimeProgress.useCallback[checkAnalysisStatus]\"]);\n                        return true;\n                    }\n                }\n            } catch (error) {\n                console.error('Failed to check analysis status:', error);\n            }\n            return false;\n        }\n    }[\"useRealTimeProgress.useCallback[checkAnalysisStatus]\"], []);\n    const connectToProgress = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useRealTimeProgress.useCallback[connectToProgress]\": async (sessionId)=>{\n            // Close existing SSE connection\n            if (eventSourceRef.current) {\n                eventSourceRef.current.close();\n            }\n            // Store current session for retry purposes\n            currentSessionRef.current = sessionId;\n            // 🔧 CRITICAL FIX: Update state with the provided session ID IMMEDIATELY\n            // This ensures the UI shows the correct session ID right away\n            console.log('🔧 FRONTEND_DEBUG: Setting session ID immediately:', sessionId);\n            setState({\n                \"useRealTimeProgress.useCallback[connectToProgress]\": (prev)=>({\n                        ...prev,\n                        sessionId,\n                        error: null,\n                        currentStep: 'chart_analysis',\n                        progress: 0,\n                        message: 'Connecting to progress stream...',\n                        isConnected: false,\n                        isComplete: false\n                    })\n            }[\"useRealTimeProgress.useCallback[connectToProgress]\"]);\n            // Reset retry count for new connection\n            setRetryCount(0);\n            // First check if analysis is already complete\n            const isComplete = await checkAnalysisStatus(sessionId);\n            if (isComplete) {\n                return;\n            }\n            const token = localStorage.getItem('access_token');\n            const baseURL = \"http://localhost:8000\" || 0;\n            if (!token) {\n                console.error('❌ No access token found for SSE connection');\n                setState({\n                    \"useRealTimeProgress.useCallback[connectToProgress]\": (prev)=>({\n                            ...prev,\n                            error: 'Authentication required for progress tracking'\n                        })\n                }[\"useRealTimeProgress.useCallback[connectToProgress]\"]);\n                return;\n            }\n            // Try SSE first - Fixed URL construction and added better error handling\n            const sseUrl = \"\".concat(baseURL, \"/api/v1/progress/stream/\").concat(sessionId, \"?token=\").concat(encodeURIComponent(token));\n            console.log('🔗 FRONTEND_DEBUG: Attempting SSE connection');\n            console.log('🔗 FRONTEND_DEBUG: SSE URL:', sseUrl);\n            console.log('🔗 FRONTEND_DEBUG: Session ID:', sessionId);\n            console.log('🔗 FRONTEND_DEBUG: Token present:', !!token);\n            console.log('🔗 FRONTEND_DEBUG: Token length:', token === null || token === void 0 ? void 0 : token.length);\n            console.log('🔗 FRONTEND_DEBUG: Base URL:', baseURL);\n            // Create EventSource with proper error handling\n            let eventSource;\n            try {\n                eventSource = new EventSource(sseUrl);\n                console.log('✅ FRONTEND_DEBUG: EventSource created successfully');\n            } catch (error) {\n                console.error('❌ FRONTEND_DEBUG: Failed to create EventSource:', error);\n                setState({\n                    \"useRealTimeProgress.useCallback[connectToProgress]\": (prev)=>({\n                            ...prev,\n                            error: \"Failed to create SSE connection: \".concat(error)\n                        })\n                }[\"useRealTimeProgress.useCallback[connectToProgress]\"]);\n                return;\n            }\n            eventSourceRef.current = eventSource;\n            let sseConnected = false;\n            // SSE connection timeout (no polling fallback)\n            const sseTimeout = setTimeout({\n                \"useRealTimeProgress.useCallback[connectToProgress].sseTimeout\": ()=>{\n                    if (!sseConnected) {\n                        console.log('⚠️ SSE connection timeout - no polling fallback');\n                        eventSource.close();\n                        setState({\n                            \"useRealTimeProgress.useCallback[connectToProgress].sseTimeout\": (prev)=>({\n                                    ...prev,\n                                    isConnected: false,\n                                    error: 'SSE connection timeout - check backend connection'\n                                })\n                        }[\"useRealTimeProgress.useCallback[connectToProgress].sseTimeout\"]);\n                    }\n                }\n            }[\"useRealTimeProgress.useCallback[connectToProgress].sseTimeout\"], 10000) // Increased timeout to 10 seconds\n            ;\n            eventSource.onopen = ({\n                \"useRealTimeProgress.useCallback[connectToProgress]\": ()=>{\n                    console.log('✅ FRONTEND_DEBUG: SSE connection established');\n                    console.log('✅ FRONTEND_DEBUG: EventSource readyState:', eventSource.readyState);\n                    console.log('✅ FRONTEND_DEBUG: EventSource URL:', eventSource.url);\n                    sseConnected = true;\n                    clearTimeout(sseTimeout);\n                    setState({\n                        \"useRealTimeProgress.useCallback[connectToProgress]\": (prev)=>({\n                                ...prev,\n                                isConnected: true,\n                                error: null,\n                                message: 'Connected to progress stream'\n                            })\n                    }[\"useRealTimeProgress.useCallback[connectToProgress]\"]);\n                }\n            })[\"useRealTimeProgress.useCallback[connectToProgress]\"];\n            eventSource.onmessage = ({\n                \"useRealTimeProgress.useCallback[connectToProgress]\": (event)=>{\n                    try {\n                        console.log('📨 FRONTEND_DEBUG: Raw SSE message received:', event.data);\n                        const data = JSON.parse(event.data);\n                        console.log('📨 FRONTEND_DEBUG: Parsed SSE data:', data);\n                        console.log('📨 FRONTEND_DEBUG: Event type:', data.type);\n                        switch(data.type){\n                            case 'connected':\n                                console.log('✅ FRONTEND_DEBUG: Progress stream connected for session:', data.session_id);\n                                break;\n                            case 'progress_update':\n                                const update = data.data;\n                                console.log('📈 FRONTEND_DEBUG: SSE Progress update:', update);\n                                // Map backend step to frontend display\n                                const stepInfo = getStepInfo(update.step);\n                                console.log('📈 FRONTEND_DEBUG: Step info mapped:', stepInfo);\n                                // 🔧 CRITICAL DEBUG: Log the exact state update\n                                console.log('📈 FRONTEND_DEBUG: About to update state with:', {\n                                    currentStep: update.step,\n                                    progress: stepInfo.progress,\n                                    message: stepInfo.message,\n                                    backendProgress: update.progress,\n                                    frontendProgress: stepInfo.progress\n                                });\n                                // 🔧 CRITICAL FIX: Use progress queue for smooth animations\n                                setState({\n                                    \"useRealTimeProgress.useCallback[connectToProgress]\": (prev)=>{\n                                        // Only queue if this is actually a new step or higher progress\n                                        const isNewStep = update.step !== prev.currentStep;\n                                        const isHigherProgress = stepInfo.progress > prev.progress;\n                                        if (isNewStep || isHigherProgress) {\n                                            console.log('📈 FRONTEND_DEBUG: Queueing progress update:', stepInfo.progress, 'step:', update.step);\n                                            // Add to progress queue for smooth processing\n                                            setProgressQueue({\n                                                \"useRealTimeProgress.useCallback[connectToProgress]\": (queue)=>[\n                                                        ...queue,\n                                                        {\n                                                            step: update.step,\n                                                            progress: stepInfo.progress,\n                                                            message: stepInfo.message\n                                                        }\n                                                    ]\n                                            }[\"useRealTimeProgress.useCallback[connectToProgress]\"]);\n                                            // Update the updates array immediately\n                                            return {\n                                                ...prev,\n                                                updates: [\n                                                    ...prev.updates,\n                                                    update\n                                                ]\n                                            };\n                                        } else {\n                                            console.log('📈 FRONTEND_DEBUG: Skipping duplicate/lower progress update:', stepInfo.progress, 'current:', prev.progress);\n                                            return prev;\n                                        }\n                                    }\n                                }[\"useRealTimeProgress.useCallback[connectToProgress]\"]);\n                                break;\n                            case 'complete':\n                                console.log('🎉 SSE Analysis complete!');\n                                setState({\n                                    \"useRealTimeProgress.useCallback[connectToProgress]\": (prev)=>({\n                                            ...prev,\n                                            isComplete: true,\n                                            progress: 100,\n                                            currentStep: 'complete',\n                                            message: 'Analysis completed successfully!'\n                                        })\n                                }[\"useRealTimeProgress.useCallback[connectToProgress]\"]);\n                                eventSource.close();\n                                break;\n                            case 'ping':\n                                break;\n                            case 'error':\n                                console.error('❌ SSE Progress stream error:', data.error);\n                                setState({\n                                    \"useRealTimeProgress.useCallback[connectToProgress]\": (prev)=>({\n                                            ...prev,\n                                            error: data.error,\n                                            isConnected: false\n                                        })\n                                }[\"useRealTimeProgress.useCallback[connectToProgress]\"]);\n                                eventSource.close();\n                                break;\n                        }\n                    } catch (error) {\n                        console.error('Failed to parse SSE progress update:', error);\n                    }\n                }\n            })[\"useRealTimeProgress.useCallback[connectToProgress]\"];\n            eventSource.onerror = ({\n                \"useRealTimeProgress.useCallback[connectToProgress]\": (error)=>{\n                    console.error('❌ FRONTEND_DEBUG: SSE EventSource error:', error);\n                    console.error('❌ FRONTEND_DEBUG: EventSource readyState:', eventSource.readyState);\n                    console.error('❌ FRONTEND_DEBUG: EventSource URL:', sseUrl);\n                    clearTimeout(sseTimeout);\n                    setState({\n                        \"useRealTimeProgress.useCallback[connectToProgress]\": (prev)=>({\n                                ...prev,\n                                isConnected: false\n                            })\n                    }[\"useRealTimeProgress.useCallback[connectToProgress]\"]);\n                    eventSource.close();\n                    // Attempt retry if we have a current session and haven't exceeded max retries\n                    if (currentSessionRef.current && retryCount < maxRetries) {\n                        setRetryCount({\n                            \"useRealTimeProgress.useCallback[connectToProgress]\": (prev)=>prev + 1\n                        }[\"useRealTimeProgress.useCallback[connectToProgress]\"]);\n                        retryConnection(currentSessionRef.current, retryCount + 1);\n                    } else {\n                        // Provide more specific error messages based on readyState\n                        let errorMessage = 'SSE connection failed';\n                        if (eventSource.readyState === EventSource.CONNECTING) {\n                            errorMessage = 'Failed to establish SSE connection - check backend status';\n                        } else if (eventSource.readyState === EventSource.CLOSED) {\n                            errorMessage = 'SSE connection was closed unexpectedly';\n                        }\n                        setState({\n                            \"useRealTimeProgress.useCallback[connectToProgress]\": (prev)=>({\n                                    ...prev,\n                                    error: errorMessage\n                                })\n                        }[\"useRealTimeProgress.useCallback[connectToProgress]\"]);\n                    }\n                }\n            })[\"useRealTimeProgress.useCallback[connectToProgress]\"];\n        }\n    }[\"useRealTimeProgress.useCallback[connectToProgress]\"], []);\n    // Connection health check\n    const checkConnectionHealth = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useRealTimeProgress.useCallback[checkConnectionHealth]\": async ()=>{\n            try {\n                const baseURL = \"http://localhost:8000\" || 0;\n                const response = await fetch(\"\".concat(baseURL, \"/api/v1/health\"), {\n                    method: 'GET',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    }\n                });\n                if (response.ok) {\n                    console.log('✅ FRONTEND_DEBUG: Backend health check passed');\n                    return true;\n                } else {\n                    console.error('❌ FRONTEND_DEBUG: Backend health check failed:', response.status);\n                    return false;\n                }\n            } catch (error) {\n                console.error('❌ FRONTEND_DEBUG: Backend health check error:', error);\n                return false;\n            }\n        }\n    }[\"useRealTimeProgress.useCallback[checkConnectionHealth]\"], []);\n    // Helper function to map backend steps to frontend display\n    const getStepInfo = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useRealTimeProgress.useCallback[getStepInfo]\": (step)=>{\n            switch(step){\n                case 'chart_analysis':\n                    return WORKFLOW_STEPS.CHART_ANALYSIS;\n                case 'symbol_detection':\n                    return WORKFLOW_STEPS.SYMBOL_DETECTION;\n                case 'tool_execution':\n                    return WORKFLOW_STEPS.TOOL_EXECUTION;\n                case 'tool_summarization':\n                    return WORKFLOW_STEPS.TOOL_SUMMARIZATION;\n                case 'rag_integration':\n                    return WORKFLOW_STEPS.RAG_INTEGRATION;\n                case 'final_analysis':\n                    return WORKFLOW_STEPS.FINAL_ANALYSIS;\n                case 'memory_storage':\n                    return WORKFLOW_STEPS.MEMORY_STORAGE;\n                case 'complete':\n                    return WORKFLOW_STEPS.COMPLETE;\n                default:\n                    return {\n                        name: 'Processing',\n                        progress: 50,\n                        message: 'Processing...'\n                    };\n            }\n        }\n    }[\"useRealTimeProgress.useCallback[getStepInfo]\"], []);\n    // Polling removed - SSE only implementation\n    const startProgressTracking = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useRealTimeProgress.useCallback[startProgressTracking]\": async (backendSessionId)=>{\n            if (backendSessionId) {\n                // Use backend session ID directly\n                await connectToProgress(backendSessionId);\n                return backendSessionId;\n            } else {\n                // Fallback: generate frontend session ID if backend doesn't provide one\n                const sessionId = \"frontend_\".concat(Date.now(), \"_\").concat(Math.random().toString(36).substring(2, 11));\n                setState({\n                    \"useRealTimeProgress.useCallback[startProgressTracking]\": (prev)=>({\n                            ...prev,\n                            sessionId,\n                            error: null\n                        })\n                }[\"useRealTimeProgress.useCallback[startProgressTracking]\"]);\n                await connectToProgress(sessionId);\n                console.log('📊 Generated fallback progress session:', sessionId);\n                return sessionId;\n            }\n        }\n    }[\"useRealTimeProgress.useCallback[startProgressTracking]\"], [\n        connectToProgress\n    ]);\n    const cleanup = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useRealTimeProgress.useCallback[cleanup]\": async (sessionId)=>{\n            // Close EventSource connection\n            if (eventSourceRef.current) {\n                eventSourceRef.current.close();\n                eventSourceRef.current = null;\n            }\n            // No polling to clear - SSE only\n            console.log('📊 Progress session cleanup completed:', sessionId || state.sessionId);\n            // Reset state\n            setState({\n                sessionId: null,\n                currentStep: 'chart_analysis',\n                progress: 0,\n                message: 'Preparing for analysis...',\n                isConnected: false,\n                isComplete: false,\n                error: null,\n                updates: []\n            });\n        }\n    }[\"useRealTimeProgress.useCallback[cleanup]\"], [\n        state.sessionId\n    ]);\n    // Cleanup on unmount\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useRealTimeProgress.useEffect\": ()=>{\n            return ({\n                \"useRealTimeProgress.useEffect\": ()=>{\n                    if (eventSourceRef.current) {\n                        eventSourceRef.current.close();\n                    }\n                }\n            })[\"useRealTimeProgress.useEffect\"];\n        }\n    }[\"useRealTimeProgress.useEffect\"], []);\n    return {\n        ...state,\n        startProgressTracking,\n        connectToProgress,\n        cleanup,\n        WORKFLOW_STEPS\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useRealTimeProgress.ts\n"));

/***/ })

});