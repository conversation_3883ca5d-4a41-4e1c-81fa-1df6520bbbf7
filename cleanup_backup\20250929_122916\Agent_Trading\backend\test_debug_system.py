#!/usr/bin/env python3
"""
Test script to verify the debug logging system is working
"""

import sys
import os
import asyncio

# Add the backend directory to the path
sys.path.append(os.path.dirname(__file__))

async def test_debug_system():
    """Test the debug logging system."""
    print("🔍 Testing Debug System...")
    
    try:
        # Test progress debug logger
        from app.core.progress_debug import progress_debug
        print("✅ Progress debug logger imported successfully")
        
        # Test logging functions
        progress_debug.session_lifecycle("test_session_123", "test_start", {"test": True})
        progress_debug.sse_connection("test_session_123", {"user_id": "test_user"})
        progress_debug.workflow_step("test_session_123", "chart_analysis", 20, "Testing workflow step")
        progress_debug.progress_broadcast("test_session_123", "chart_analysis", True, {"test": "data"})
        progress_debug.debug("TEST_COMPONENT", "Testing debug message", {"key": "value"})
        
        print("✅ All debug logging functions work correctly")
        
        # Test progress tracker
        from app.helpers.workflow_management.progress_tracker import (
            get_progress_tracker, 
            WorkflowStep,
            ProgressBroadcaster
        )
        print("✅ Progress tracker imported successfully")
        
        # Test progress tracker functionality
        tracker = get_progress_tracker()
        session_id = tracker.create_session("test_session_456")
        print(f"✅ Created test session: {session_id}")
        
        # Test progress update
        update = tracker.update_progress(
            session_id,
            WorkflowStep.CHART_ANALYSIS,
            "Testing chart analysis step",
            details={"test": True}
        )
        print(f"✅ Progress update created: {update.step.value} - {update.progress}%")
        
        # Test broadcaster
        broadcaster = ProgressBroadcaster()
        client_queue = await broadcaster.add_client(session_id)
        print("✅ SSE client added successfully")
        
        # Test broadcasting
        await broadcaster.broadcast_update(session_id, update)
        print("✅ Progress update broadcasted successfully")
        
        # Check if update was received
        try:
            received_update = await asyncio.wait_for(client_queue.get(), timeout=1.0)
            print(f"✅ Update received by client: {received_update}")
        except asyncio.TimeoutError:
            print("⚠️ No update received by client (this might be expected)")
        
        print("\n🎉 Debug system test completed successfully!")
        print("📁 Check progress_debug.log for debug output")
        
    except Exception as e:
        print(f"❌ Debug system test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_debug_system())
