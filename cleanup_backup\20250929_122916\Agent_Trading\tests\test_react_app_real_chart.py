#!/usr/bin/env python3
"""
Test React App with Real Trading Chart
=====================================

This script tests the React app by:
1. Starting an analysis with a real trading chart
2. Monitoring SSE events in real-time
3. Checking if the frontend receives events properly
"""

import asyncio
import aiohttp
import json
import base64
import time
from datetime import datetime
from pathlib import Path

# Your credentials
EMAIL = "<EMAIL>"
PASSWORD = "Bunnych@1627"
BASE_URL = "http://localhost:8000"

def load_real_trading_chart():
    """Load a real trading chart from the testing images"""
    chart_path = Path("Agent_Trading/Testing_images/Screenshot 2025-08-03 190956.png")
    
    if not chart_path.exists():
        print(f"❌ Chart not found at {chart_path}")
        return None
    
    with open(chart_path, "rb") as f:
        img_data = f.read()
        img_base64 = base64.b64encode(img_data).decode('utf-8')
        print(f"✅ Loaded real trading chart: {len(img_data)} bytes")
        return img_base64

async def test_react_app_with_real_chart():
    """Test the React app with a real trading chart"""
    print("🚀 Testing React App with Real Trading Chart")
    print("=" * 60)
    
    # Step 1: Load real chart
    print("1️⃣ Loading real trading chart...")
    chart_image = load_real_trading_chart()
    if not chart_image:
        print("❌ Failed to load trading chart")
        return
    
    # Step 2: Login
    print("\n2️⃣ Authenticating...")
    async with aiohttp.ClientSession() as session:
        login_data = {"email": EMAIL, "password": PASSWORD}
        
        async with session.post(f"{BASE_URL}/api/v1/auth/login", json=login_data) as response:
            if response.status == 200:
                result = await response.json()
                token = result["access_token"]
                print(f"✅ Authenticated as {result['user']['email']}")
            else:
                print(f"❌ Authentication failed: {response.status}")
                return
    
    # Step 3: Start analysis and monitor SSE
    print("\n3️⃣ Starting analysis with real trading chart...")
    
    async def start_analysis():
        """Start analysis with real chart"""
        await asyncio.sleep(1)  # Wait for SSE to connect
        
        headers = {
            "Authorization": f"Bearer {token}",
            "Content-Type": "application/json"
        }
        
        analysis_data = {
            "images_base64": [chart_image],
            "analysis_type": "Positional",
            "market_specialization": "Crypto",
            "preferred_model": "gemini-2.5-flash"
        }
        
        print("📤 Starting analysis with real trading chart...")
        async with aiohttp.ClientSession() as session:
            async with session.post(f"{BASE_URL}/api/v1/trading/async/start", 
                                   headers=headers, json=analysis_data) as response:
                if response.status == 200:
                    result = await response.json()
                    session_id = result["session_id"]
                    print(f"✅ Analysis started with session: {session_id}")
                    return session_id
                else:
                    error = await response.text()
                    print(f"❌ Analysis failed: {response.status} - {error}")
                    return None
    
    async def monitor_sse_detailed(session_id):
        """Monitor SSE with detailed analysis"""
        if not session_id:
            return
        
        sse_url = f"{BASE_URL}/api/v1/progress/stream/{session_id}?token={token}"
        print(f"📡 SSE URL: {sse_url}")
        
        async with aiohttp.ClientSession() as session:
            try:
                async with session.get(sse_url, headers={"Accept": "text/event-stream"}) as response:
                    if response.status != 200:
                        print(f"❌ SSE connection failed: {response.status}")
                        return
                    
                    print("📡 SSE connection established - monitoring events...")
                    
                    events = []
                    start_time = time.time()
                    last_event_time = start_time
                    
                    async for line in response.content:
                        line = line.decode('utf-8').strip()
                        
                        if line.startswith('data: '):
                            data_str = line[6:]
                            
                            try:
                                data = json.loads(data_str)
                                current_time = time.time()
                                time_since_start = current_time - start_time
                                time_since_last = current_time - last_event_time
                                last_event_time = current_time
                                
                                timestamp = datetime.now().strftime("%H:%M:%S.%f")[:-3]
                                
                                if data.get('type') == 'progress_update':
                                    update = data.get('data', {})
                                    step = update.get('step', 'unknown')
                                    progress = update.get('progress', 0)
                                    message = update.get('message', '')
                                    
                                    # Check for duplicates
                                    is_duplicate = any(
                                        e.get('step') == step and e.get('progress') == progress 
                                        for e in events
                                    )
                                    
                                    duplicate_marker = " 🔄 DUPLICATE" if is_duplicate else ""
                                    
                                    print(f"[{timestamp}] 📈 {step} ({progress}%) - {message}{duplicate_marker}")
                                    print(f"    ⏱️  +{time_since_start:.1f}s total, +{time_since_last:.3f}s since last")
                                    
                                    events.append({
                                        'timestamp': timestamp,
                                        'step': step,
                                        'progress': progress,
                                        'message': message,
                                        'time_since_start': time_since_start,
                                        'time_since_last': time_since_last,
                                        'is_duplicate': is_duplicate
                                    })
                                    
                                elif data.get('type') == 'complete':
                                    print(f"[{timestamp}] 🎉 Analysis complete!")
                                    break
                                    
                                elif data.get('type') == 'connected':
                                    print(f"[{timestamp}] 🔗 Connected to session")
                                    
                                elif data.get('type') == 'ping':
                                    # Don't log pings unless there are no progress events
                                    if len(events) == 0:
                                        print(f"[{timestamp}] 🏓 Keepalive (no progress events yet)")
                                    
                            except json.JSONDecodeError:
                                print(f"⚠️ Failed to parse: {data_str}")
                    
                    # Analysis
                    print("\n" + "=" * 60)
                    print("📊 DETAILED SSE ANALYSIS")
                    print("=" * 60)
                    
                    if events:
                        print(f"✅ Total events received: {len(events)}")
                        
                        # Check for duplicates
                        duplicates = [e for e in events if e['is_duplicate']]
                        if duplicates:
                            print(f"⚠️  Duplicate events found: {len(duplicates)}")
                            for dup in duplicates:
                                print(f"    🔄 {dup['step']} ({dup['progress']}%)")
                        else:
                            print("✅ No duplicate events detected")
                        
                        # Check timing
                        if len(events) > 1:
                            gaps = [e['time_since_last'] for e in events[1:]]
                            avg_gap = sum(gaps) / len(gaps)
                            max_gap = max(gaps)
                            min_gap = min(gaps)
                            
                            print(f"\n⏱️  Timing Analysis:")
                            print(f"    Average gap: {avg_gap:.3f}s")
                            print(f"    Max gap: {max_gap:.3f}s")
                            print(f"    Min gap: {min_gap:.3f}s")
                            
                            if max_gap > 2.0:
                                print("⚠️  Large gaps detected - events may be buffered")
                            elif avg_gap < 0.1:
                                print("⚠️  Very rapid events - may appear as 'all at once' in UI")
                            else:
                                print("✅ Good timing distribution")
                        
                        # Check progress sequence
                        progress_values = [e['progress'] for e in events if not e['is_duplicate']]
                        if progress_values == sorted(progress_values):
                            print("✅ Progress values in correct ascending order")
                        else:
                            print("❌ Progress values out of order")
                            print(f"    Sequence: {progress_values}")
                        
                        # Steps analysis
                        unique_steps = list(dict.fromkeys([e['step'] for e in events if not e['is_duplicate']]))
                        print(f"\n📋 Workflow Steps ({len(unique_steps)}):")
                        for i, step in enumerate(unique_steps, 1):
                            step_events = [e for e in events if e['step'] == step and not e['is_duplicate']]
                            if len(step_events) > 1:
                                print(f"    {i}. {step} (⚠️ {len(step_events)} events)")
                            else:
                                print(f"    {i}. {step}")
                    
                    else:
                        print("❌ NO PROGRESS EVENTS RECEIVED!")
                        print("💡 This indicates a serious SSE or progress tracking issue")
                        
                    print("\n🎯 REACT APP IMPLICATIONS:")
                    if events:
                        if any(e['time_since_last'] < 0.1 for e in events[1:]):
                            print("⚠️  Events arrive very rapidly - React may show 'stuck then complete'")
                            print("💡 This is normal for fast analysis - frontend should handle rapid updates")
                        else:
                            print("✅ Events have good spacing - React should show smooth progress")
                    else:
                        print("❌ No events means React will definitely appear stuck")
                        
            except Exception as e:
                print(f"❌ SSE monitoring error: {e}")
    
    # Run analysis and monitoring
    session_id = await start_analysis()
    if session_id:
        await monitor_sse_detailed(session_id)
    
    print(f"\n🌐 React App URL: http://localhost:3000")
    print("💡 Test the React app with the same credentials to compare behavior!")

if __name__ == "__main__":
    asyncio.run(test_react_app_with_real_chart())
