2025-09-29 16:50:45 | INFO     | log_session_start    | ================================================================================
2025-09-29 16:50:45 | INFO     | log_session_start    | 🚀 PROGRESS ANALYSIS SESSION STARTED
2025-09-29 16:50:45 | INFO     | log_session_start    | 📋 Session ID: async_98d0b880defc
2025-09-29 16:50:45 | INFO     | log_session_start    | 📁 Log File: Agent_Trading\backend\logs\progress_bar\progress_analysis_20250929_165045_async_98d0b880defc.log
2025-09-29 16:50:45 | INFO     | log_session_start    | ⏰ Start Time: 2025-09-29T16:50:45.375705
2025-09-29 16:50:45 | INFO     | log_session_start    | ================================================================================
2025-09-29 16:50:45 | INFO     | log_parameter_passing | 🔧 PARAMETER_PASSING | Component: WORKFLOW_INIT
2025-09-29 16:50:45 | INFO     | log_parameter_passing | ✅ PARAM_OK | session_id: str = async_98d0b880defc
2025-09-29 16:50:45 | INFO     | log_parameter_passing | ✅ PARAM_OK | progress_broadcaster: ProgressBroadcaster = <app.helpers.workflow_management.progress_tracker.ProgressBroadcaster object at 0x00000267BBC9AD50>
2025-09-29 16:50:45 | INFO     | log_parameter_passing | ✅ PARAM_OK | progress_broadcaster_type: str = ProgressBroadcaster
2025-09-29 16:50:45 | INFO     | log_parameter_passing | ✅ PARAM_OK | analysis_mode: str = positional
2025-09-29 16:50:45 | INFO     | log_parameter_passing | ✅ PARAM_OK | market_specialization: str = Crypto
2025-09-29 16:50:45 | INFO     | log_parameter_passing | ✅ PARAM_OK | chart_images_count: int = 1
2025-09-29 16:50:45 | INFO     | log_parameter_passing | ✅ PARAM_OK | user_query: str = Positional trading analysis for Crypto market | User tier: free - Provide comprehensive analysis wit...
2025-09-29 16:50:45 | INFO     | log_parameter_passing | 🔧 PARAMETER_PASSING | Component: WORKFLOW_VERIFY
2025-09-29 16:50:45 | INFO     | log_parameter_passing | ✅ PARAM_OK | instance_progress_broadcaster: bool = True
2025-09-29 16:50:45 | INFO     | log_parameter_passing | ✅ PARAM_OK | instance_session_id: str = async_98d0b880defc
2025-09-29 16:50:45 | INFO     | log_parameter_passing | ✅ PARAM_OK | instance_progress_broadcaster_type: str = ProgressBroadcaster
2025-09-29 16:50:45 | INFO     | log_sse_event        | 📡 SSE_EVENT | PROGRESS_BROADCAST_ATTEMPT - SUCCESS
2025-09-29 16:50:45 | DEBUG    | log_sse_event        |    📋 step: chart_analysis
2025-09-29 16:50:45 | DEBUG    | log_sse_event        |    📋 progress: 20
2025-09-29 16:50:45 | DEBUG    | log_sse_event        |    📋 message: 📊 Chart analysis completed
2025-09-29 16:50:45 | DEBUG    | log_sse_event        |    📋 details: {'node': 'chart_analysis'}
2025-09-29 16:50:45 | DEBUG    | log_sse_event        |    📋 session_id: async_98d0b880defc
2025-09-29 16:50:45 | INFO     | log_sse_event        | 📡 SSE_EVENT | PROGRESS_BROADCAST_SUCCESS - SUCCESS
2025-09-29 16:50:45 | DEBUG    | log_sse_event        |    📋 workflow_step: chart_analysis
2025-09-29 16:50:45 | DEBUG    | log_sse_event        |    📋 progress: 20
2025-09-29 16:50:45 | DEBUG    | log_sse_event        |    📋 message: 📊 Chart analysis completed
2025-09-29 16:50:45 | DEBUG    | log_sse_event        |    📋 session_id: async_98d0b880defc
