2025-09-29 15:08:26 | INFO     | log_session_start    | ================================================================================
2025-09-29 15:08:26 | INFO     | log_session_start    | 🚀 PROGRESS ANALYSIS SESSION STARTED
2025-09-29 15:08:26 | INFO     | log_session_start    | 📋 Session ID: async_a36ec6ebb42e
2025-09-29 15:08:26 | INFO     | log_session_start    | 📁 Log File: Agent_Trading\backend\logs\progress_bar\progress_analysis_20250929_150825_async_a36ec6ebb42e.log
2025-09-29 15:08:26 | INFO     | log_session_start    | ⏰ Start Time: 2025-09-29T15:08:25.993858
2025-09-29 15:08:26 | INFO     | log_session_start    | ================================================================================
2025-09-29 15:08:26 | INFO     | log_parameter_passing | 🔧 PARAMETER_PASSING | Component: WORKFLOW_INIT
2025-09-29 15:08:26 | INFO     | log_parameter_passing | ✅ PARAM_OK | session_id: str = async_a36ec6ebb42e
2025-09-29 15:08:26 | INFO     | log_parameter_passing | ✅ PARAM_OK | progress_broadcaster: ProgressBroadcaster = <app.helpers.workflow_management.progress_tracker.ProgressBroadcaster object at 0x0000022A1A452090>
2025-09-29 15:08:26 | INFO     | log_parameter_passing | ✅ PARAM_OK | progress_broadcaster_type: str = ProgressBroadcaster
2025-09-29 15:08:26 | INFO     | log_parameter_passing | ✅ PARAM_OK | analysis_mode: str = positional
2025-09-29 15:08:26 | INFO     | log_parameter_passing | ✅ PARAM_OK | market_specialization: str = Crypto
2025-09-29 15:08:26 | INFO     | log_parameter_passing | ✅ PARAM_OK | chart_images_count: int = 1
2025-09-29 15:08:26 | INFO     | log_parameter_passing | ✅ PARAM_OK | user_query: str = Positional trading analysis for Crypto market | User tier: free - Provide comprehensive analysis wit...
2025-09-29 15:08:26 | INFO     | log_parameter_passing | 🔧 PARAMETER_PASSING | Component: WORKFLOW_VERIFY
2025-09-29 15:08:26 | INFO     | log_parameter_passing | ✅ PARAM_OK | instance_progress_broadcaster: bool = True
2025-09-29 15:08:26 | INFO     | log_parameter_passing | ✅ PARAM_OK | instance_session_id: str = async_a36ec6ebb42e
2025-09-29 15:08:26 | INFO     | log_parameter_passing | ✅ PARAM_OK | instance_progress_broadcaster_type: str = ProgressBroadcaster
2025-09-29 15:08:26 | INFO     | log_workflow_step    | 📊 WORKFLOW_STEP | chart_analysis (20%) - 🚀 Starting comprehensive AI analysis workflow...
2025-09-29 15:08:26 | DEBUG    | log_workflow_step    |    📋 step: chart_analysis
2025-09-29 15:08:26 | DEBUG    | log_workflow_step    |    📋 stage: start
2025-09-29 15:08:26 | INFO     | log_sse_event        | 📡 SSE_EVENT | PROGRESS_BROADCAST_ATTEMPT - SUCCESS
2025-09-29 15:08:26 | DEBUG    | log_sse_event        |    📋 step: chart_analysis
2025-09-29 15:08:26 | DEBUG    | log_sse_event        |    📋 progress: 20
2025-09-29 15:08:26 | DEBUG    | log_sse_event        |    📋 message: 🚀 Starting comprehensive AI analysis workflow...
2025-09-29 15:08:26 | DEBUG    | log_sse_event        |    📋 details: {'step': 'chart_analysis', 'stage': 'start'}
2025-09-29 15:08:26 | DEBUG    | log_sse_event        |    📋 session_id: async_a36ec6ebb42e
2025-09-29 15:08:26 | INFO     | log_sse_event        | 📡 SSE_EVENT | PROGRESS_BROADCAST_SUCCESS - SUCCESS
2025-09-29 15:08:26 | DEBUG    | log_sse_event        |    📋 workflow_step: chart_analysis
2025-09-29 15:08:26 | DEBUG    | log_sse_event        |    📋 progress: 20
2025-09-29 15:08:26 | DEBUG    | log_sse_event        |    📋 message: 🚀 Starting comprehensive AI analysis workflow...
2025-09-29 15:08:26 | DEBUG    | log_sse_event        |    📋 session_id: async_a36ec6ebb42e
2025-09-29 15:08:40 | INFO     | log_workflow_step    | 📊 WORKFLOW_STEP | symbol_detection (40%) - ✅ Symbol detected: BTCUSD (crypto market)
2025-09-29 15:08:40 | DEBUG    | log_workflow_step    |    📋 step: symbol_detection
2025-09-29 15:08:40 | DEBUG    | log_workflow_step    |    📋 symbol: BTCUSD
2025-09-29 15:08:40 | DEBUG    | log_workflow_step    |    📋 market: crypto
2025-09-29 15:08:40 | INFO     | log_sse_event        | 📡 SSE_EVENT | PROGRESS_BROADCAST_ATTEMPT - SUCCESS
2025-09-29 15:08:40 | DEBUG    | log_sse_event        |    📋 step: symbol_detection
2025-09-29 15:08:40 | DEBUG    | log_sse_event        |    📋 progress: 40
2025-09-29 15:08:40 | DEBUG    | log_sse_event        |    📋 message: ✅ Symbol detected: BTCUSD (crypto market)
2025-09-29 15:08:40 | DEBUG    | log_sse_event        |    📋 details: {'step': 'symbol_detection', 'symbol': 'BTCUSD', 'market': 'crypto'}
2025-09-29 15:08:40 | DEBUG    | log_sse_event        |    📋 session_id: async_a36ec6ebb42e
2025-09-29 15:08:40 | INFO     | log_sse_event        | 📡 SSE_EVENT | PROGRESS_BROADCAST_SUCCESS - SUCCESS
2025-09-29 15:08:40 | DEBUG    | log_sse_event        |    📋 workflow_step: symbol_detection
2025-09-29 15:08:40 | DEBUG    | log_sse_event        |    📋 progress: 40
2025-09-29 15:08:40 | DEBUG    | log_sse_event        |    📋 message: ✅ Symbol detected: BTCUSD (crypto market)
2025-09-29 15:08:40 | DEBUG    | log_sse_event        |    📋 session_id: async_a36ec6ebb42e
2025-09-29 15:08:40 | INFO     | log_workflow_step    | 📊 WORKFLOW_STEP | tool_execution (50%) - 🎯 Executing market data tools...
2025-09-29 15:08:40 | DEBUG    | log_workflow_step    |    📋 step: tool_execution
2025-09-29 15:08:40 | DEBUG    | log_workflow_step    |    📋 market: crypto
2025-09-29 15:08:40 | INFO     | log_sse_event        | 📡 SSE_EVENT | PROGRESS_BROADCAST_ATTEMPT - SUCCESS
2025-09-29 15:08:40 | DEBUG    | log_sse_event        |    📋 step: tool_execution
2025-09-29 15:08:40 | DEBUG    | log_sse_event        |    📋 progress: 50
2025-09-29 15:08:40 | DEBUG    | log_sse_event        |    📋 message: 🎯 Executing market data tools...
2025-09-29 15:08:40 | DEBUG    | log_sse_event        |    📋 details: {'step': 'tool_execution', 'market': 'crypto'}
2025-09-29 15:08:40 | DEBUG    | log_sse_event        |    📋 session_id: async_a36ec6ebb42e
2025-09-29 15:08:40 | INFO     | log_sse_event        | 📡 SSE_EVENT | PROGRESS_BROADCAST_SUCCESS - SUCCESS
2025-09-29 15:08:40 | DEBUG    | log_sse_event        |    📋 workflow_step: tool_execution
2025-09-29 15:08:40 | DEBUG    | log_sse_event        |    📋 progress: 50
2025-09-29 15:08:40 | DEBUG    | log_sse_event        |    📋 message: 🎯 Executing market data tools...
2025-09-29 15:08:40 | DEBUG    | log_sse_event        |    📋 session_id: async_a36ec6ebb42e
2025-09-29 15:08:45 | INFO     | log_workflow_step    | 📊 WORKFLOW_STEP | tool_summarization (60%) - 🎯 Summarizing market data with AI...
2025-09-29 15:08:45 | DEBUG    | log_workflow_step    |    📋 step: tool_summarization
2025-09-29 15:08:45 | INFO     | log_sse_event        | 📡 SSE_EVENT | PROGRESS_BROADCAST_ATTEMPT - SUCCESS
2025-09-29 15:08:45 | DEBUG    | log_sse_event        |    📋 step: tool_summarization
2025-09-29 15:08:45 | DEBUG    | log_sse_event        |    📋 progress: 60
2025-09-29 15:08:45 | DEBUG    | log_sse_event        |    📋 message: 🎯 Summarizing market data with AI...
2025-09-29 15:08:45 | DEBUG    | log_sse_event        |    📋 details: {'step': 'tool_summarization'}
2025-09-29 15:08:45 | DEBUG    | log_sse_event        |    📋 session_id: async_a36ec6ebb42e
2025-09-29 15:08:45 | INFO     | log_sse_event        | 📡 SSE_EVENT | PROGRESS_BROADCAST_SUCCESS - SUCCESS
2025-09-29 15:08:45 | DEBUG    | log_sse_event        |    📋 workflow_step: tool_summarization
2025-09-29 15:08:45 | DEBUG    | log_sse_event        |    📋 progress: 60
2025-09-29 15:08:45 | DEBUG    | log_sse_event        |    📋 message: 🎯 Summarizing market data with AI...
2025-09-29 15:08:45 | DEBUG    | log_sse_event        |    📋 session_id: async_a36ec6ebb42e
2025-09-29 15:08:58 | INFO     | log_workflow_step    | 📊 WORKFLOW_STEP | rag_integration (70%) - 💾 Integrating historical context and patterns...
2025-09-29 15:08:58 | DEBUG    | log_workflow_step    |    📋 step: rag_integration
2025-09-29 15:08:58 | INFO     | log_sse_event        | 📡 SSE_EVENT | PROGRESS_BROADCAST_ATTEMPT - SUCCESS
2025-09-29 15:08:58 | DEBUG    | log_sse_event        |    📋 step: rag_integration
2025-09-29 15:08:58 | DEBUG    | log_sse_event        |    📋 progress: 70
2025-09-29 15:08:58 | DEBUG    | log_sse_event        |    📋 message: 💾 Integrating historical context and patterns...
2025-09-29 15:08:58 | DEBUG    | log_sse_event        |    📋 details: {'step': 'rag_integration'}
2025-09-29 15:08:58 | DEBUG    | log_sse_event        |    📋 session_id: async_a36ec6ebb42e
2025-09-29 15:08:58 | INFO     | log_sse_event        | 📡 SSE_EVENT | PROGRESS_BROADCAST_SUCCESS - SUCCESS
2025-09-29 15:08:58 | DEBUG    | log_sse_event        |    📋 workflow_step: rag_integration
2025-09-29 15:08:58 | DEBUG    | log_sse_event        |    📋 progress: 70
2025-09-29 15:08:58 | DEBUG    | log_sse_event        |    📋 message: 💾 Integrating historical context and patterns...
2025-09-29 15:08:58 | DEBUG    | log_sse_event        |    📋 session_id: async_a36ec6ebb42e
2025-09-29 15:09:04 | INFO     | log_workflow_step    | 📊 WORKFLOW_STEP | final_analysis (80%) - 🎯 Generating final trading analysis...
2025-09-29 15:09:04 | DEBUG    | log_workflow_step    |    📋 step: final_analysis
2025-09-29 15:09:04 | INFO     | log_sse_event        | 📡 SSE_EVENT | PROGRESS_BROADCAST_ATTEMPT - SUCCESS
2025-09-29 15:09:04 | DEBUG    | log_sse_event        |    📋 step: final_analysis
2025-09-29 15:09:04 | DEBUG    | log_sse_event        |    📋 progress: 80
2025-09-29 15:09:04 | DEBUG    | log_sse_event        |    📋 message: 🎯 Generating final trading analysis...
2025-09-29 15:09:04 | DEBUG    | log_sse_event        |    📋 details: {'step': 'final_analysis'}
2025-09-29 15:09:04 | DEBUG    | log_sse_event        |    📋 session_id: async_a36ec6ebb42e
2025-09-29 15:09:04 | INFO     | log_sse_event        | 📡 SSE_EVENT | PROGRESS_BROADCAST_SUCCESS - SUCCESS
2025-09-29 15:09:04 | DEBUG    | log_sse_event        |    📋 workflow_step: final_analysis
2025-09-29 15:09:04 | DEBUG    | log_sse_event        |    📋 progress: 80
2025-09-29 15:09:04 | DEBUG    | log_sse_event        |    📋 message: 🎯 Generating final trading analysis...
2025-09-29 15:09:04 | DEBUG    | log_sse_event        |    📋 session_id: async_a36ec6ebb42e
2025-09-29 15:09:33 | INFO     | log_workflow_step    | 📊 WORKFLOW_STEP | memory_storage (90%) - 💾 Storing analysis for future reference...
2025-09-29 15:09:33 | DEBUG    | log_workflow_step    |    📋 step: memory_storage
2025-09-29 15:09:33 | INFO     | log_sse_event        | 📡 SSE_EVENT | PROGRESS_BROADCAST_ATTEMPT - SUCCESS
2025-09-29 15:09:33 | DEBUG    | log_sse_event        |    📋 step: memory_storage
2025-09-29 15:09:33 | DEBUG    | log_sse_event        |    📋 progress: 90
2025-09-29 15:09:33 | DEBUG    | log_sse_event        |    📋 message: 💾 Storing analysis for future reference...
2025-09-29 15:09:33 | DEBUG    | log_sse_event        |    📋 details: {'step': 'memory_storage'}
2025-09-29 15:09:33 | DEBUG    | log_sse_event        |    📋 session_id: async_a36ec6ebb42e
2025-09-29 15:09:33 | INFO     | log_sse_event        | 📡 SSE_EVENT | PROGRESS_BROADCAST_SUCCESS - SUCCESS
2025-09-29 15:09:33 | DEBUG    | log_sse_event        |    📋 workflow_step: memory_storage
2025-09-29 15:09:33 | DEBUG    | log_sse_event        |    📋 progress: 90
2025-09-29 15:09:33 | DEBUG    | log_sse_event        |    📋 message: 💾 Storing analysis for future reference...
2025-09-29 15:09:33 | DEBUG    | log_sse_event        |    📋 session_id: async_a36ec6ebb42e
2025-09-29 15:09:33 | INFO     | log_workflow_step    | 📊 WORKFLOW_STEP | complete (100%) - 🎉 Analysis completed successfully!
2025-09-29 15:09:33 | DEBUG    | log_workflow_step    |    📋 step: complete
2025-09-29 15:09:33 | DEBUG    | log_workflow_step    |    📋 workflow_status: complete
2025-09-29 15:09:33 | INFO     | log_sse_event        | 📡 SSE_EVENT | PROGRESS_BROADCAST_ATTEMPT - SUCCESS
2025-09-29 15:09:33 | DEBUG    | log_sse_event        |    📋 step: complete
2025-09-29 15:09:33 | DEBUG    | log_sse_event        |    📋 progress: 100
2025-09-29 15:09:33 | DEBUG    | log_sse_event        |    📋 message: 🎉 Analysis completed successfully!
2025-09-29 15:09:33 | DEBUG    | log_sse_event        |    📋 details: {'step': 'complete', 'workflow_status': 'complete'}
2025-09-29 15:09:33 | DEBUG    | log_sse_event        |    📋 session_id: async_a36ec6ebb42e
2025-09-29 15:09:33 | INFO     | log_sse_event        | 📡 SSE_EVENT | PROGRESS_BROADCAST_SUCCESS - SUCCESS
2025-09-29 15:09:33 | DEBUG    | log_sse_event        |    📋 workflow_step: complete
2025-09-29 15:09:33 | DEBUG    | log_sse_event        |    📋 progress: 100
2025-09-29 15:09:33 | DEBUG    | log_sse_event        |    📋 message: 🎉 Analysis completed successfully!
2025-09-29 15:09:33 | DEBUG    | log_sse_event        |    📋 session_id: async_a36ec6ebb42e
