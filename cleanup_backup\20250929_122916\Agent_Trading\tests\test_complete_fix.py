#!/usr/bin/env python3
"""
Complete test for both progress broadcasting and data display fixes.
"""

import asyncio
import requests
import json
import base64
import time
from datetime import datetime

# Test configuration
BASE_URL = "http://localhost:8000"
EMAIL = "<EMAIL>"
PASSWORD = "Bunnych@1627"

async def test_complete_fixes():
    """Test both progress broadcasting and data display fixes."""
    print("🧪 TESTING COMPLETE FIXES")
    print("=" * 60)
    
    # 1. Login
    print("1️⃣ Logging in...")
    login_response = requests.post(f"{BASE_URL}/api/v1/auth/login", json={
        "email": EMAIL,
        "password": PASSWORD
    })
    
    if login_response.status_code != 200:
        print(f"❌ Login failed: {login_response.status_code}")
        print(login_response.text)
        return False
    
    token = login_response.json()["access_token"]
    headers = {"Authorization": f"Bearer {token}"}
    print("✅ Login successful")
    
    # 2. Start analysis
    print("\n2️⃣ Starting analysis...")
    
    # Load test image
    try:
        with open("Agent_Trading/Testing_images/Screenshot 2025-08-03 190956.png", "rb") as f:
            test_image_b64 = base64.b64encode(f.read()).decode('utf-8')
    except FileNotFoundError:
        print("❌ Test image not found")
        return False
    
    analysis_request = {
        "images_base64": [test_image_b64],
        "analysis_type": "Positional",
        "market_specialization": "Crypto",
        "preferred_model": "gemini-2.5-flash"
    }
    
    start_response = requests.post(
        f"{BASE_URL}/api/v1/trading/async/start",
        json=analysis_request,
        headers=headers
    )
    
    if start_response.status_code != 200:
        print(f"❌ Analysis start failed: {start_response.status_code}")
        print(start_response.text)
        return False
    
    session_id = start_response.json()["session_id"]
    print(f"✅ Analysis started with session: {session_id}")
    
    # 3. Monitor progress and completion
    print("\n3️⃣ Monitoring progress...")
    progress_steps = []
    
    for i in range(120):  # Monitor for up to 2 minutes
        try:
            result_response = requests.get(
                f"{BASE_URL}/api/v1/trading/async/result/{session_id}",
                headers=headers
            )
            
            if result_response.status_code == 200:
                result_data = result_response.json()
                
                if result_data.get("status") == "completed":
                    print(f"🎉 Analysis completed in {i} seconds!")
                    
                    # Check data structure
                    print("\n4️⃣ Checking data structure...")
                    data = result_data.get('data', {})
                    print(f"📊 Top-level data keys: {list(data.keys())}")

                    # The actual data is nested in data.data
                    actual_data = data.get('data', {})
                    print(f"📊 Actual data keys: {list(actual_data.keys())}")

                    # Check for detected_symbol and market_type in the correct location
                    detected_symbol = actual_data.get('detected_symbol')
                    market_type = actual_data.get('market_type')
                    success = actual_data.get('success')
                    trading_signals = actual_data.get('trading_signals', {})
                    analysis_summary = trading_signals.get('analysis_summary') if isinstance(trading_signals, dict) else None
                    trade_ideas = trading_signals.get('trade_ideas', []) if isinstance(trading_signals, dict) else []
                    
                    print(f"   ✅ success: {success}")
                    print(f"   🎯 detected_symbol: {detected_symbol}")
                    print(f"   🏪 market_type: {market_type}")
                    print(f"   📝 analysis_summary: {analysis_summary[:100] if analysis_summary else None}...")
                    print(f"   💡 trade_ideas count: {len(trade_ideas) if isinstance(trade_ideas, list) else 'not list'}")
                    
                    # Check if data fix is successful
                    data_fix_success = (
                        detected_symbol and detected_symbol != "Unknown Symbol" and
                        market_type and market_type != "Unknown Market" and
                        success and
                        analysis_summary and
                        isinstance(trade_ideas, list) and len(trade_ideas) > 0
                    )
                    
                    if data_fix_success:
                        print("✅ DATA FIX SUCCESSFUL!")
                    else:
                        print("❌ Data fix still needed")
                        print(f"   Raw data preview: {str(data)[:300]}...")
                    
                    break
                elif result_data.get("status") == "processing":
                    print(f"⏳ Processing... ({i+1}/120)")
                else:
                    print(f"❓ Status: {result_data.get('status')}")
            
            time.sleep(1)
            
        except Exception as e:
            print(f"❌ Error: {e}")
            break
    
    # 4. Check progress logs
    print("\n5️⃣ Checking progress logs...")
    try:
        with open("Agent_Trading/backend/logs/progress_debug.log", "r", encoding='utf-8', errors='ignore') as f:
            recent_logs = f.readlines()[-30:]  # Last 30 lines
            
        progress_found = False
        session_found = False
        has_progress_broadcaster = False
        intermediate_steps = []
        
        for line in recent_logs:
            if session_id in line:
                session_found = True
                print(f"   📝 {line.strip()}")
                
                # Check for progress steps
                if "WORKFLOW_STEP" in line:
                    progress_found = True
                    if "progress" in line.lower():
                        # Extract progress percentage
                        try:
                            if "40%" in line:
                                intermediate_steps.append("40%")
                            elif "50%" in line:
                                intermediate_steps.append("50%")
                            elif "65%" in line:
                                intermediate_steps.append("65%")
                            elif "80%" in line:
                                intermediate_steps.append("80%")
                            elif "95%" in line:
                                intermediate_steps.append("95%")
                            elif "100%" in line:
                                intermediate_steps.append("100%")
                        except:
                            pass
                
                # Check for progress broadcaster
                if "'has_progress_broadcaster': True" in line:
                    has_progress_broadcaster = True
        
        print(f"\n📊 Progress Analysis:")
        print(f"   ✅ Session found in logs: {session_found}")
        print(f"   ✅ Progress broadcaster working: {has_progress_broadcaster}")
        print(f"   ✅ Progress steps found: {progress_found}")
        print(f"   📈 Intermediate steps detected: {intermediate_steps}")
        
        # Check if progress fix is successful
        progress_fix_success = (
            session_found and 
            has_progress_broadcaster and 
            len(intermediate_steps) >= 3  # Should have multiple intermediate steps
        )
        
        if progress_fix_success:
            print("✅ PROGRESS FIX SUCCESSFUL!")
        else:
            print("❌ Progress fix still needed")
            
    except Exception as e:
        print(f"❌ Error reading logs: {e}")
    
    print("\n" + "=" * 60)
    print("🏁 TEST COMPLETED")
    
    # Final summary
    if 'data_fix_success' in locals() and 'progress_fix_success' in locals():
        if data_fix_success and progress_fix_success:
            print("🎉 ALL FIXES SUCCESSFUL!")
            return True
        else:
            print("❌ Some fixes still need work")
            return False
    else:
        print("❌ Could not complete all tests")
        return False

if __name__ == "__main__":
    asyncio.run(test_complete_fixes())
