#!/usr/bin/env python3
"""
Real-time SSE Test
==================

This script tests if SSE events are truly streaming in real-time
by sending progress updates with deliberate delays.
"""

import asyncio
import aiohttp
import json
import time
from datetime import datetime

# Your credentials
EMAIL = "<EMAIL>"
PASSWORD = "Bunnych@1627"
BASE_URL = "http://localhost:8000"

async def test_realtime_sse():
    """Test real-time SSE streaming with timed updates"""
    print("🚀 Testing Real-time SSE Streaming")
    print("=" * 50)
    
    # Step 1: Login
    print("1️⃣ Authenticating...")
    async with aiohttp.ClientSession() as session:
        login_data = {"email": EMAIL, "password": PASSWORD}
        
        async with session.post(f"{BASE_URL}/api/v1/auth/login", json=login_data) as response:
            if response.status == 200:
                result = await response.json()
                token = result["access_token"]
                print(f"✅ Authenticated as {result['user']['email']}")
            else:
                print(f"❌ Authentication failed: {response.status}")
                return
    
    # Step 2: Create a test session and manually send progress updates
    test_session_id = f"realtime_test_{int(time.time())}"
    print(f"\n2️⃣ Testing with session: {test_session_id}")
    
    # Step 3: Start SSE monitoring in background
    print("\n3️⃣ Starting SSE monitoring...")
    
    async def monitor_sse():
        """Monitor SSE events and log timing"""
        sse_url = f"{BASE_URL}/api/v1/progress/stream/{test_session_id}?token={token}"
        
        async with aiohttp.ClientSession() as session:
            try:
                async with session.get(sse_url, headers={"Accept": "text/event-stream"}) as response:
                    if response.status != 200:
                        print(f"❌ SSE connection failed: {response.status}")
                        return
                    
                    print("📡 SSE connection established")
                    event_count = 0
                    start_time = time.time()
                    
                    async for line in response.content:
                        line = line.decode('utf-8').strip()
                        
                        if line.startswith('data: '):
                            data_str = line[6:]
                            
                            try:
                                data = json.loads(data_str)
                                event_count += 1
                                current_time = time.time()
                                elapsed = current_time - start_time
                                
                                timestamp = datetime.now().strftime("%H:%M:%S.%f")[:-3]
                                
                                if data.get('type') == 'progress_update':
                                    update = data.get('data', {})
                                    step = update.get('step', 'unknown')
                                    progress = update.get('progress', 0)
                                    
                                    print(f"[{timestamp}] 📈 Event #{event_count} (+{elapsed:.2f}s): {step} ({progress}%)")
                                    
                                elif data.get('type') == 'connected':
                                    print(f"[{timestamp}] 🔗 Connected to session")
                                    
                                elif data.get('type') == 'complete':
                                    print(f"[{timestamp}] 🎉 Analysis complete")
                                    break
                                    
                                else:
                                    print(f"[{timestamp}] 📨 Event #{event_count}: {data.get('type', 'unknown')}")
                                    
                            except json.JSONDecodeError:
                                print(f"⚠️ Failed to parse: {data_str}")
                                
            except Exception as e:
                print(f"❌ SSE monitoring error: {e}")
    
    # Step 4: Send manual progress updates with delays
    async def send_manual_updates():
        """Send progress updates manually with delays to test real-time streaming"""
        await asyncio.sleep(2)  # Wait for SSE to connect
        
        print("\n4️⃣ Sending manual progress updates with 3-second delays...")
        
        # Import the progress broadcaster
        import sys
        import os
        sys.path.append(os.path.join(os.path.dirname(__file__), 'Agent_Trading', 'backend'))
        
        try:
            # Import from the exact same module path as the SSE endpoint
            import sys
            import os
            backend_path = os.path.join(os.path.dirname(__file__), 'Agent_Trading', 'backend')
            if backend_path not in sys.path:
                sys.path.insert(0, backend_path)

            from app.helpers.workflow_management.progress_tracker import get_progress_broadcaster, WorkflowStep, ProgressUpdate
            from datetime import datetime

            # Get the SAME global broadcaster instance
            broadcaster = get_progress_broadcaster()
            print(f"🔍 Using broadcaster instance ID: {id(broadcaster)}")
            
            # Test steps with deliberate delays
            test_steps = [
                (WorkflowStep.CHART_ANALYSIS, 20, "🔍 Starting chart analysis..."),
                (WorkflowStep.SYMBOL_DETECTION, 30, "🎯 Detecting trading symbol..."),
                (WorkflowStep.TOOL_EXECUTION, 50, "⚙️ Executing market tools..."),
                (WorkflowStep.TOOL_SUMMARIZATION, 60, "📊 Summarizing data..."),
                (WorkflowStep.RAG_INTEGRATION, 70, "🧠 Integrating context..."),
                (WorkflowStep.FINAL_ANALYSIS, 80, "📈 Generating analysis..."),
                (WorkflowStep.MEMORY_STORAGE, 90, "💾 Storing results..."),
                (WorkflowStep.COMPLETE, 100, "🎉 Analysis complete!")
            ]
            
            for i, (step, progress, message) in enumerate(test_steps):
                print(f"📤 Sending update {i+1}/8: {step.value} ({progress}%)")
                
                update = ProgressUpdate(
                    step=step,
                    progress=progress,
                    message=message,
                    timestamp=datetime.now().isoformat(),
                    details={"manual_test": True, "step_number": i+1}
                )
                
                await broadcaster.broadcast_update(test_session_id, update)
                
                if i < len(test_steps) - 1:  # Don't wait after the last update
                    print(f"⏳ Waiting 3 seconds before next update...")
                    await asyncio.sleep(3)  # 3-second delay between updates
                    
        except Exception as e:
            print(f"❌ Failed to send manual updates: {e}")
            import traceback
            traceback.print_exc()
    
    # Run both tasks concurrently
    try:
        await asyncio.gather(
            monitor_sse(),
            send_manual_updates()
        )
    except Exception as e:
        print(f"❌ Test failed: {e}")
    
    print("\n" + "=" * 50)
    print("📊 REAL-TIME SSE TEST COMPLETE")
    print("=" * 50)
    print("🔍 Check the timing between 'Sending update' and 'Event received'")
    print("✅ If events appear immediately after sending: SSE is real-time")
    print("❌ If events appear all at once: SSE is buffered")

if __name__ == "__main__":
    asyncio.run(test_realtime_sse())
