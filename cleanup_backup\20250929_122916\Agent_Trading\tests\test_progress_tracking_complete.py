#!/usr/bin/env python3
"""
Test script to verify complete progress tracking system
Tests all 8 workflow steps and completion handling
"""

import asyncio
import aiohttp
import json
import time
from pathlib import Path

# Test configuration
BACKEND_URL = "http://localhost:8000"
TEST_IMAGE_PATH = Path("test_chart.jpg")

async def test_complete_progress_tracking():
    """Test the complete progress tracking system end-to-end"""
    print("🧪 Testing Complete Progress Tracking System")
    print("=" * 60)

    # Step 1: Create a test chart image if it doesn't exist
    if not TEST_IMAGE_PATH.exists():
        print("📸 Creating test chart image...")
        # Create a simple test image (1x1 pixel)
        from PIL import Image
        img = Image.new('RGB', (100, 100), color='blue')
        img.save(TEST_IMAGE_PATH)
        print(f"✅ Created test image: {TEST_IMAGE_PATH}")

    async with aiohttp.ClientSession() as session:
        # Step 2: Start analysis
        print("\n🚀 Starting analysis...")

        with open(TEST_IMAGE_PATH, 'rb') as f:
            form_data = aiohttp.FormData()
            form_data.add_field('chart_images', f, filename='test_chart.jpg', content_type='image/jpeg')
            form_data.add_field('analysis_type', 'Positional')
            form_data.add_field('market_specialization', 'Crypto')
            form_data.add_field('preferred_model', 'gemini-2.5-pro')

            async with session.post(f"{BACKEND_URL}/api/v1/trading/analyze-async", data=form_data) as response:
                if response.status != 200:
                    print(f"❌ Failed to start analysis: {response.status}")
                    return

                result = await response.json()
                session_id = result.get('session_id')
                print(f"✅ Analysis started with session: {session_id}")

        # Step 3: Monitor progress via SSE
        print("\n📡 Monitoring progress via SSE...")
        progress_updates = []

        try:
            async with session.get(f"{BACKEND_URL}/api/v1/progress/stream/{session_id}") as sse_response:
                if sse_response.status != 200:
                    print(f"❌ Failed to connect to SSE: {sse_response.status}")
                    return

                print("✅ Connected to SSE stream")

                async for line in sse_response.content:
                    line = line.decode('utf-8').strip()

                    if line.startswith('data: '):
                        try:
                            data = json.loads(line[6:])  # Remove 'data: ' prefix

                            if data.get('type') == 'progress_update':
                                update = data.get('data', {})
                                step = update.get('step')
                                progress = update.get('progress')
                                message = update.get('message')

                                progress_updates.append({
                                    'step': step,
                                    'progress': progress,
                                    'message': message,
                                    'timestamp': time.time()
                                })

                                print(f"📈 Progress: {step} ({progress}%) - {message}")

                            elif data.get('type') == 'complete':
                                print("🎉 Analysis completed!")
                                break

                        except json.JSONDecodeError:
                            continue

                    elif line.startswith('event: '):
                        continue  # Skip event lines

                    elif line == '':
                        continue  # Skip empty lines

        except Exception as e:
            print(f"⚠️ SSE connection error: {e}")

        # Step 4: Analyze progress updates
        print("\n📊 Progress Analysis:")
        print("=" * 40)

        expected_steps = [
            'chart_analysis',
            'symbol_detection',
            'tool_execution',
            'tool_summarization',
            'rag_integration',
            'final_analysis',
            'memory_storage',
            'complete'
        ]

        received_steps = [update['step'] for update in progress_updates]

        print(f"Expected steps: {len(expected_steps)}")
        print(f"Received steps: {len(received_steps)}")
        print(f"Steps received: {received_steps}")

        # Check for missing steps
        missing_steps = [step for step in expected_steps if step not in received_steps]
        if missing_steps:
            print(f"❌ Missing steps: {missing_steps}")
        else:
            print("✅ All expected steps received!")

        # Check progress sequence
        progress_values = [update['progress'] for update in progress_updates]
        is_increasing = all(progress_values[i] <= progress_values[i+1] for i in range(len(progress_values)-1))

        if is_increasing:
            print("✅ Progress values are increasing correctly")
        else:
            print(f"❌ Progress values not increasing: {progress_values}")

        # Step 5: Fetch final results
        print("\n🎯 Fetching final results...")

        async with session.get(f"{BACKEND_URL}/api/v1/trading/async-result/{session_id}") as response:
            if response.status == 200:
                result = await response.json()
                if result.get('success') and result.get('status') == 'completed':
                    print("✅ Final results retrieved successfully!")
                    print(f"   - Analysis data: {bool(result.get('data'))}")
                    print(f"   - Execution time: {result.get('metadata', {}).get('execution_time', 'N/A')}s")
                else:
                    print(f"❌ Analysis not completed: {result.get('status')}")
            else:
                print(f"❌ Failed to fetch results: {response.status}")

        print("\n🏁 Test completed!")

if __name__ == "__main__":
    asyncio.run(test_complete_progress_tracking())