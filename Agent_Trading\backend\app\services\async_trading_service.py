"""
Async Trading Analysis Service
============================

This module implements async trading analysis to avoid frontend timeouts.
Instead of blocking HTTP requests, analysis runs in background tasks while
progress is streamed via Server-Sent Events.
"""

import asyncio
import time
import uuid
from typing import Dict, Any, List
from fastapi import BackgroundTasks
from sqlalchemy.ext.asyncio import AsyncSession

from app.models.user import User as DBUser
from app.api.validators import AnalyzeRequestValidator
from app.services.trading_service import TradingAnalysisService
from app.helpers.workflow_management.progress_tracker import WorkflowProgressTracker, ProgressBroadcaster, WorkflowStep
from app.core.logging import get_logger
from app.core.debug_logger import backend_debug, progress_debug, result_debug, langgraph_debug
from app.core.database import get_async_session
from app.core.progress_bar_logger import get_progress_bar_logger

logger = get_logger("async_trading_service")

class AsyncTradingAnalysisService:
    """Service for handling async trading analysis tasks."""

    def __init__(self):
        self.active_tasks: Dict[str, asyncio.Task] = {}
        self.results_cache: Dict[str, Dict[str, Any]] = {}
        self.pending_requests: Dict[str, Dict[str, Any]] = {}  # Store requests waiting for SSE connection
    
    async def prepare_analysis_task(
        self,
        session_id: str,
        request: AnalyzeRequestValidator,
        user: DBUser,
        progress_tracker: WorkflowProgressTracker,
        progress_broadcaster: ProgressBroadcaster
    ) -> None:
        """Prepare analysis task but don't start until SSE connection is established."""

        # Store the request for later execution
        self.pending_requests[session_id] = {
            "request": request,
            "user": user,
            "progress_tracker": progress_tracker,
            "progress_broadcaster": progress_broadcaster
        }

        logger.info(f"Prepared analysis task for session {session_id} - waiting for SSE connection")

    async def start_analysis_task(
        self,
        session_id: str,
        request: AnalyzeRequestValidator,
        user: DBUser,
        progress_tracker: WorkflowProgressTracker,
        progress_broadcaster: ProgressBroadcaster
    ) -> None:
        """Start async analysis task in background."""

        task = asyncio.create_task(
            self._run_analysis_background(
                session_id, request, user, progress_tracker, progress_broadcaster
            )
        )

        self.active_tasks[session_id] = task
        logger.info(f"Started async analysis task for session {session_id}")

    async def start_pending_analysis(self, session_id: str) -> bool:
        """Start analysis for a session that was waiting for SSE connection."""

        if session_id not in self.pending_requests:
            logger.warning(f"No pending request found for session {session_id}")
            return False

        pending = self.pending_requests.pop(session_id)

        await self.start_analysis_task(
            session_id=session_id,
            request=pending["request"],
            user=pending["user"],
            progress_tracker=pending["progress_tracker"],
            progress_broadcaster=pending["progress_broadcaster"]
        )

        logger.info(f"Started pending analysis for session {session_id}")
        return True
    
    async def _run_analysis_background(
        self,
        session_id: str,
        request: AnalyzeRequestValidator,
        user: DBUser,
        progress_tracker: WorkflowProgressTracker,
        progress_broadcaster: ProgressBroadcaster
    ) -> None:
        """Run the actual analysis in background."""

        start_time = time.time()

        # 🔧 NEW: Initialize progress bar logger and log parameters
        progress_bar_logger = get_progress_bar_logger(session_id)

        # Log all input parameters
        input_parameters = {
            "session_id": session_id,
            "user_id": str(user.id),
            "user_email": user.email,
            "user_tier": user.tier,
            "analysis_type": request.analysis_type,
            "market_specialization": request.market_specialization,
            "preferred_model": request.preferred_model,
            "image_count": len(request.images_base64),
            "progress_tracker": progress_tracker is not None,
            "progress_tracker_type": type(progress_tracker).__name__ if progress_tracker else "None",
            "progress_broadcaster": progress_broadcaster is not None,
            "progress_broadcaster_type": type(progress_broadcaster).__name__ if progress_broadcaster else "None"
        }
        progress_bar_logger.log_parameter_passing("ASYNC_SERVICE_START", input_parameters)

        try:
            logger.info(f"🚀 Starting background analysis for session {session_id}")

            # 🔧 FIX: Wait for SSE connection before sending any progress updates
            # This prevents the race condition where updates are buffered before client connects
            logger.info(f"⏳ Waiting for SSE connection for session {session_id}")

            # Wait up to 10 seconds for SSE connection
            max_wait_time = 10.0
            wait_interval = 0.1
            total_waited = 0.0

            while total_waited < max_wait_time:
                if session_id in progress_broadcaster.clients and len(progress_broadcaster.clients[session_id]) > 0:
                    logger.info(f"✅ SSE connection confirmed for session {session_id} after {total_waited:.1f}s")
                    break
                await asyncio.sleep(wait_interval)
                total_waited += wait_interval
            else:
                logger.warning(f"⚠️ SSE connection timeout for session {session_id} after {max_wait_time}s - proceeding anyway")

            # Update progress: Starting analysis
            update = progress_tracker.update_progress(
                session_id,
                WorkflowStep.CHART_UPLOAD,
                "📊 Processing uploaded chart images...",
                details={"user_id": str(user.id), "analysis_type": request.analysis_type, "image_count": len(request.images_base64)}
            )
            await progress_broadcaster.broadcast_update(session_id, update)
            
            # Get database session for background task
            async for db in get_async_session():
                # Initialize trading service
                trading_service = TradingAnalysisService(
                    db=db
                )
                
                # Convert base64 images to bytes
                chart_images_bytes: List[bytes] = []
                for i, img_b64 in enumerate(request.images_base64):
                    import base64
                    image_bytes = base64.b64decode(img_b64)
                    chart_images_bytes.append(image_bytes)
                    
                    # Progress update for image processing
                    update = progress_tracker.update_progress(
                        session_id,
                        WorkflowStep.CHART_UPLOAD,
                        f"🖼️ Processed chart image {i+1}/{len(request.images_base64)} ({len(image_bytes)//1024}KB)",
                        details={"image_size": len(image_bytes), "images_processed": i+1}
                    )
                    await progress_broadcaster.broadcast_update(session_id, update)
                
                # Add artificial delay for better UX and to allow SSE connection to establish
                import asyncio
                await asyncio.sleep(1.0)  # Delay to allow SSE client to connect and receive buffered updates
                
                # 🔧 REMOVED: Early symbol_detection progress - workflow handles this internally
                
                # Enhanced LangGraph flow logging
                workflow_id = f"workflow_{session_id}"
                langgraph_debug.langgraph_flow_start(session_id, workflow_id, request.preferred_model or "gpt-4o")

                # 🔧 NEW: Log parameters before calling trading service
                trading_service_params = {
                    "user_id": str(user.id),
                    "chart_images_count": len(chart_images_bytes),
                    "analysis_type": request.analysis_type,
                    "market_specialization": request.market_specialization,
                    "preferred_model": request.preferred_model,
                    "session_id": session_id,
                    "progress_tracker": progress_tracker is not None,
                    "progress_broadcaster": progress_broadcaster is not None,
                    "progress_tracker_id": id(progress_tracker) if progress_tracker else None,
                    "progress_broadcaster_id": id(progress_broadcaster) if progress_broadcaster else None
                }
                progress_bar_logger.log_parameter_passing("TRADING_SERVICE_CALL", trading_service_params)

                # Run the actual analysis
                start_time = time.time()
                result = await trading_service.perform_analysis(
                    user=user,
                    chart_images=chart_images_bytes,
                    analysis_type=request.analysis_type,
                    market_specialization=request.market_specialization,
                    preferred_model=request.preferred_model,
                    timeframes=request.timeframes,
                    ticker_hint=request.ticker_hint,
                    context_hint=request.context_hint,
                    session_id=session_id,
                    progress_tracker=progress_tracker,
                    progress_broadcaster=progress_broadcaster
                )
                
                execution_time = time.time() - start_time
                
                if result["success"]:
                    # Prepare result data
                    result_data = {
                        "success": True,
                        "data": result["data"],
                        "metadata": {
                            "execution_time": execution_time,
                            "user_id": str(user.id),
                            "analysis_type": request.analysis_type,
                            "market_specialization": request.market_specialization,
                            "image_count": len(request.images_base64),
                            "model_used": result.get("model_used", request.preferred_model)
                        },
                        "session_id": session_id
                    }

                    # Cache the successful result in async service
                    self.results_cache[session_id] = result_data

                    # Enhanced LangGraph flow completion logging
                    langgraph_debug.langgraph_flow_complete(session_id, execution_time, True)

                    # Enhanced debug logging with file output
                    data_size = len(str(result_data).encode('utf-8'))
                    result_debug.result_stored(session_id, list(result_data.keys()), data_size)
                    result_debug.data_structure(session_id, "STORED_RESULT", result_data)

                    # Also store in progress tracker for backward compatibility
                    progress_tracker.store_result(session_id, result_data)

                    # Final progress update
                    update = progress_tracker.update_progress(
                        session_id,
                        WorkflowStep.COMPLETE,
                        "🎉 Analysis completed successfully!",
                        details={
                            "execution_time": execution_time,
                            "result_available": True
                        }
                    )
                    await progress_broadcaster.broadcast_update(session_id, update)

                    # 🔧 CRITICAL FIX: Mark session as complete AFTER result is stored and completion signal sent
                    progress_broadcaster.mark_session_complete(session_id)

                    logger.info(
                        f"✅ Background analysis completed successfully",
                        session_id=session_id,
                        execution_time=execution_time,
                        user_id=str(user.id)
                    )
                    
                else:
                    # Prepare error result data
                    error_msg = result.get("error", "Analysis failed")
                    error_data = {
                        "success": False,
                        "error": error_msg,
                        "session_id": session_id
                    }

                    # Cache the error result in async service
                    self.results_cache[session_id] = error_data

                    # Also store in progress tracker for backward compatibility
                    progress_tracker.store_result(session_id, error_data)

                    # Error progress update
                    update = progress_tracker.update_progress(
                        session_id,
                        WorkflowStep.COMPLETE,
                        f"❌ Analysis failed: {error_msg}",
                        details={"error": True}
                    )
                    await progress_broadcaster.broadcast_update(session_id, update)

                    # 🔧 CRITICAL FIX: Mark session as complete even for errors
                    progress_broadcaster.mark_session_complete(session_id)

                    logger.error(
                        f"❌ Background analysis failed",
                        session_id=session_id,
                        error=error_msg,
                        user_id=str(user.id)
                    )
                
                # Break after processing with one database session
                break
        
        except Exception as e:
            execution_time = time.time() - start_time
            error_msg = f"Unexpected error: {str(e)}"
            
            # Cache the exception
            self.results_cache[session_id] = {
                "success": False,
                "error": error_msg,
                "session_id": session_id
            }
            
            # Error progress update
            update = progress_tracker.update_progress(
                session_id,
                WorkflowStep.COMPLETE,
                f"💥 Critical error: {error_msg}",
                details={"error": True, "exception": str(e)}
            )
            await progress_broadcaster.broadcast_update(session_id, update)

            # 🔧 CRITICAL FIX: Mark session as complete even for critical errors
            progress_broadcaster.mark_session_complete(session_id)
            
            logger.error(
                f"💥 Background analysis crashed",
                session_id=session_id,
                error=str(e),
                execution_time=execution_time,
                user_id=str(user.id)
            )
        
        finally:
            # Cleanup task reference
            if session_id in self.active_tasks:
                del self.active_tasks[session_id]
    
    def get_analysis_result(self, session_id: str) -> Dict[str, Any] | None:
        """Get cached analysis result for a session."""
        result = self.results_cache.get(session_id)

        # Clean debug logging
        if result:
            result_debug.result_requested(session_id, True, list(result.keys()))
            result_debug.data_structure(session_id, "RETRIEVED_RESULT", result)
        else:
            result_debug.result_requested(session_id, False)

        return result
    
    def cleanup_session(self, session_id: str) -> None:
        """Clean up session data and cancel running tasks."""
        
        # Cancel running task if exists
        if session_id in self.active_tasks:
            task = self.active_tasks[session_id]
            if not task.done():
                task.cancel()
            del self.active_tasks[session_id]
        
        # Remove cached result
        if session_id in self.results_cache:
            del self.results_cache[session_id]
        
        logger.info(f"🧹 Cleaned up session: {session_id}")
    
    def get_active_sessions(self) -> List[str]:
        """Get list of currently active analysis sessions."""
        return list(self.active_tasks.keys())

# Global service instance
async_trading_service = AsyncTradingAnalysisService()
