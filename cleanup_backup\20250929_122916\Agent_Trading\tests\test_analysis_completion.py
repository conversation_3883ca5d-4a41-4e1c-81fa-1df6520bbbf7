#!/usr/bin/env python3
"""
Test Analysis Completion and Results Storage
============================================

This script tests if the analysis completes properly and stores results
that can be retrieved by the frontend.
"""

import asyncio
import aiohttp
import json
import base64
import time
from datetime import datetime

# Your credentials
EMAIL = "<EMAIL>"
PASSWORD = "Bunnych@1627"
BASE_URL = "http://localhost:8000"

async def test_analysis_completion():
    """Test complete analysis flow and result retrieval"""
    print("🧪 Testing Analysis Completion and Results Storage")
    print("=" * 60)
    
    # Step 1: Login
    print("1️⃣ Authenticating...")
    async with aiohttp.ClientSession() as session:
        login_data = {"email": EMAIL, "password": PASSWORD}
        
        async with session.post(f"{BASE_URL}/api/v1/auth/login", json=login_data) as response:
            if response.status == 200:
                result = await response.json()
                token = result["access_token"]
                print("✅ Authentication successful")
            else:
                error = await response.text()
                print(f"❌ Authentication failed: {response.status} - {error}")
                return False
    
    # Step 2: Prepare test image
    print("\n2️⃣ Preparing test image...")
    
    # Create a simple test image (1x1 pixel PNG)
    test_image_data = base64.b64encode(
        b'\x89PNG\r\n\x1a\n\x00\x00\x00\rIHDR\x00\x00\x00\x01\x00\x00\x00\x01\x08\x02\x00\x00\x00\x90wS\xde\x00\x00\x00\tpHYs\x00\x00\x0b\x13\x00\x00\x0b\x13\x01\x00\x9a\x9c\x18\x00\x00\x00\nIDATx\x9cc\xf8\x00\x00\x00\x01\x00\x01\x00\x00\x00\x00IEND\xaeB`\x82'
    ).decode('utf-8')
    
    analysis_data = {
        "images_base64": [test_image_data],
        "analysis_type": "positional",
        "market_specialization": "Crypto",
        "preferred_model": "gemini-2.5-flash",
        "timeframes": ["1h"],
        "ticker_hint": "BTC",
        "context_hint": "Test analysis"
    }
    
    print("✅ Test image prepared")
    
    # Step 3: Start analysis
    print("\n3️⃣ Starting analysis...")
    
    headers = {"Authorization": f"Bearer {token}"}
    session_id = None
    
    async with aiohttp.ClientSession() as session:
        async with session.post(f"{BASE_URL}/api/v1/trading/async/start", 
                               headers=headers, json=analysis_data) as response:
            if response.status == 200:
                result = await response.json()
                session_id = result["session_id"]
                print(f"✅ Analysis started with session: {session_id}")
            else:
                error = await response.text()
                print(f"❌ Analysis failed: {response.status} - {error}")
                return False
    
    # Step 4: Wait for analysis to complete
    print(f"\n4️⃣ Waiting for analysis to complete...")
    
    max_wait_time = 120  # 2 minutes
    start_time = time.time()
    
    while time.time() - start_time < max_wait_time:
        async with aiohttp.ClientSession() as session:
            async with session.get(f"{BASE_URL}/api/v1/trading/async/result/{session_id}", 
                                  headers=headers) as response:
                if response.status == 200:
                    result = await response.json()
                    
                    if result["status"] == "completed":
                        print("✅ Analysis completed successfully!")
                        
                        # Check if we have data
                        if result.get("data"):
                            print(f"✅ Analysis data received: {len(str(result['data']))} characters")
                            
                            # Check for key fields
                            data = result["data"]
                            if isinstance(data, dict):
                                print(f"📊 Data structure keys: {list(data.keys())}")
                                
                                # Check for expected fields
                                expected_fields = ["detected_symbol", "market_type", "trading_signals", "analysis"]
                                found_fields = [field for field in expected_fields if field in data]
                                print(f"✅ Found expected fields: {found_fields}")
                                
                                if len(found_fields) > 0:
                                    print("🎉 Analysis results are properly structured!")
                                    return True
                                else:
                                    print("⚠️ Analysis completed but missing expected fields")
                                    print(f"📋 Available fields: {list(data.keys()) if isinstance(data, dict) else 'Not a dict'}")
                            else:
                                print(f"⚠️ Analysis data is not a dict: {type(data)}")
                        else:
                            print("❌ Analysis completed but no data returned")
                            return False
                    
                    elif result["status"] == "failed":
                        print(f"❌ Analysis failed: {result.get('error', 'Unknown error')}")
                        return False
                    
                    elif result["status"] == "running":
                        elapsed = time.time() - start_time
                        print(f"⏳ Analysis still running... ({elapsed:.1f}s elapsed)")
                    
                    else:
                        print(f"❓ Unknown status: {result['status']}")
                
                else:
                    error = await response.text()
                    print(f"❌ Failed to check result: {response.status} - {error}")
        
        await asyncio.sleep(2)  # Check every 2 seconds
    
    print(f"⏰ Analysis timed out after {max_wait_time} seconds")
    return False

if __name__ == "__main__":
    try:
        result = asyncio.run(test_analysis_completion())
        if result:
            print("\n✅ Analysis completion test passed!")
        else:
            print("\n❌ Analysis completion test failed!")
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
