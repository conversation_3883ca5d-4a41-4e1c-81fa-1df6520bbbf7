#!/usr/bin/env python3
"""
Cleanup Duplicate Logs
======================

This script removes duplicate and legacy log files, keeping only the unified logging structure.
"""

import os
import shutil
from pathlib import Path
from datetime import datetime

def cleanup_duplicate_logs():
    """Clean up duplicate and legacy log files."""
    print("🧹 CLEANING UP DUPLICATE LOGS")
    print("=" * 50)
    
    log_dir = Path("Agent_Trading/backend/logs")
    
    if not log_dir.exists():
        print("❌ Logs directory not found")
        return False
    
    # Files to remove (legacy/duplicate logs)
    files_to_remove = [
        "api_debug.log",
        "backend_debug.log", 
        "langgraph_debug.log",
        "llm_interactions.jsonl",
        "progress_debug.log",
        "result_debug.log",
        "tool_calls.jsonl"
    ]
    
    # Workflow files to move to archive
    workflow_files = list(log_dir.glob("workflow_*.json"))
    
    removed_count = 0
    moved_count = 0
    
    print("🗑️  Removing duplicate log files...")
    for file_name in files_to_remove:
        file_path = log_dir / file_name
        if file_path.exists():
            try:
                file_path.unlink()
                print(f"   ✅ Removed: {file_name}")
                removed_count += 1
            except Exception as e:
                print(f"   ❌ Failed to remove {file_name}: {e}")
    
    print(f"\n📦 Moving workflow files to archive...")
    archive_dir = log_dir / "archive"
    archive_dir.mkdir(exist_ok=True)
    
    for workflow_file in workflow_files:
        try:
            destination = archive_dir / workflow_file.name
            if not destination.exists():
                shutil.move(str(workflow_file), str(destination))
                print(f"   ✅ Moved: {workflow_file.name}")
                moved_count += 1
            else:
                # File already exists in archive, remove the duplicate
                workflow_file.unlink()
                print(f"   🗑️  Removed duplicate: {workflow_file.name}")
                removed_count += 1
        except Exception as e:
            print(f"   ❌ Failed to move {workflow_file.name}: {e}")
    
    # Remove empty progress_bar directory if it exists
    progress_bar_dir = log_dir / "progress_bar"
    if progress_bar_dir.exists() and not any(progress_bar_dir.iterdir()):
        try:
            progress_bar_dir.rmdir()
            print(f"   ✅ Removed empty directory: progress_bar")
            removed_count += 1
        except Exception as e:
            print(f"   ❌ Failed to remove progress_bar directory: {e}")
    
    print(f"\n📊 Cleanup Summary:")
    print(f"   Files removed: {removed_count}")
    print(f"   Files moved to archive: {moved_count}")
    
    # Show current unified logging structure
    print(f"\n📁 Current Unified Logging Structure:")
    unified_dirs = ["application", "analysis", "progress", "api", "errors", "archive"]
    
    for dir_name in unified_dirs:
        dir_path = log_dir / dir_name
        if dir_path.exists():
            file_count = len(list(dir_path.rglob("*.log"))) + len(list(dir_path.rglob("*.json"))) + len(list(dir_path.rglob("*.jsonl")))
            print(f"   ✅ {dir_name}/ ({file_count} files)")
        else:
            print(f"   ❌ {dir_name}/ (missing)")
    
    print(f"\n🎉 Log cleanup completed!")
    print(f"💡 The unified logging system is now clean and organized.")
    
    return True

def show_current_logs():
    """Show current log files in the unified structure."""
    print("\n📋 CURRENT LOG FILES:")
    print("=" * 50)
    
    log_dir = Path("Agent_Trading/backend/logs")
    
    if not log_dir.exists():
        print("❌ Logs directory not found")
        return
    
    # Show files in each unified directory
    unified_dirs = ["application", "analysis", "progress", "api", "errors"]
    
    for dir_name in unified_dirs:
        dir_path = log_dir / dir_name
        if dir_path.exists():
            print(f"\n📁 {dir_name}/")
            
            # Show log files
            log_files = list(dir_path.rglob("*.log"))
            json_files = list(dir_path.rglob("*.json"))
            jsonl_files = list(dir_path.rglob("*.jsonl"))
            
            all_files = log_files + json_files + jsonl_files
            
            if all_files:
                for file in sorted(all_files):
                    rel_path = file.relative_to(dir_path)
                    file_size = file.stat().st_size
                    size_str = f"{file_size:,} bytes" if file_size < 1024 else f"{file_size/1024:.1f} KB"
                    print(f"   📄 {rel_path} ({size_str})")
            else:
                print(f"   (empty)")
    
    # Show session logs specifically
    session_dir = log_dir / "progress" / "sessions"
    if session_dir.exists():
        session_files = list(session_dir.glob("*.log"))
        if session_files:
            print(f"\n📁 progress/sessions/ ({len(session_files)} session logs)")
            # Show only the most recent 5 session logs
            recent_sessions = sorted(session_files, key=lambda x: x.stat().st_mtime, reverse=True)[:5]
            for session_file in recent_sessions:
                file_size = session_file.stat().st_size
                size_str = f"{file_size:,} bytes" if file_size < 1024 else f"{file_size/1024:.1f} KB"
                print(f"   📄 {session_file.name} ({size_str})")
            
            if len(session_files) > 5:
                print(f"   ... and {len(session_files) - 5} more session logs")

def main():
    """Main function."""
    print("🧹 LOG CLEANUP UTILITY")
    print(f"Start time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"Working directory: {os.getcwd()}")
    
    # Show current state
    show_current_logs()
    
    # Perform cleanup
    success = cleanup_duplicate_logs()
    
    # Show final state
    if success:
        show_current_logs()
    
    return success

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
