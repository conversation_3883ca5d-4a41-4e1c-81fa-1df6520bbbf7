{"workflow_id": "trading_analysis_1759068149", "start_time": "2025-09-28T19:32:29.338043", "context": {"analysis_mode": "positional", "market_specialization": "Crypto", "chart_count": 1}, "steps": [{"step_number": 1, "step_name": "Chart Analysis", "step_type": "llm_call", "timestamp": "2025-09-28T19:32:29.338043", "execution_time": null, "status": "success", "error": null, "input_summary": "Chart image for symbol detection"}, {"step_number": 2, "step_name": "Chart Analysis", "step_type": "llm_call", "timestamp": "2025-09-28T19:33:00.454484", "execution_time": 31.116441249847412, "status": "success", "error": null, "output_summary": "Dict with keys: ['detected_symbol', 'market_type', 'timeframe', 'current_price', 'trend_direction']..."}, {"step_number": 3, "step_name": "Step 2: Tool Selection", "step_type": "api_calls", "timestamp": "2025-09-28T19:33:00.454484", "execution_time": null, "status": "success", "error": null, "input_summary": "Market: crypto, Symbol: BTCUSD"}, {"step_number": 4, "step_name": "Step 2: Tool Selection", "step_type": "api_calls", "timestamp": "2025-09-28T19:33:05.485773", "execution_time": 5.031289339065552, "status": "success", "error": null, "output_summary": "Dict with keys: ['success', 'tool_results', 'tool_usage_log', 'execution_time']"}, {"step_number": 5, "step_name": "Step 3: Flash Summarization", "step_type": "llm_call", "timestamp": "2025-09-28T19:33:05.485773", "execution_time": null, "status": "success", "error": null, "input_summary": "Tool results for summarization"}, {"step_number": 6, "step_name": "Step 3: Flash Summarization", "step_type": "llm_call", "timestamp": "2025-09-28T19:33:40.148959", "execution_time": 34.663185596466064, "status": "success", "error": null, "output_summary": "Dict with keys: ['success', 'summarized_tools', 'tool_count', 'comprehensive_summary']"}, {"step_number": 7, "step_name": "Step 4: Final Analysis", "step_type": "llm_call", "timestamp": "2025-09-28T19:33:47.587156", "execution_time": null, "status": "success", "error": null, "input_summary": "Chart + Tool summaries + RAG tool available"}, {"step_number": 8, "step_name": "Final Analysis", "step_type": "llm_call", "timestamp": "2025-09-28T19:34:21.514287", "execution_time": 33.92652082443237, "status": "success", "error": null, "output_summary": "Dict with keys: ['success', 'analysis', 'analysis_notes', 'trading_signals', 'detected_symbol']..."}], "llm_calls": [], "summary": {"total_steps": 8, "total_llm_calls": 0, "total_tokens": 0, "total_cost": 0.0, "execution_time": 112.18568992614746}, "end_time": "2025-09-28T19:34:21.523733", "status": "success", "error": null}