2025-09-29 17:27:24 | INFO     | log_session_start    | ================================================================================
2025-09-29 17:27:24 | INFO     | log_session_start    | 🚀 PROGRESS ANALYSIS SESSION STARTED
2025-09-29 17:27:24 | INFO     | log_session_start    | 📋 Session ID: async_1ae36eaac94f
2025-09-29 17:27:24 | INFO     | log_session_start    | 📁 Log File: Agent_Trading\backend\logs\progress_bar\progress_analysis_20250929_172724_async_1ae36eaac94f.log
2025-09-29 17:27:24 | INFO     | log_session_start    | ⏰ Start Time: 2025-09-29T17:27:24.707757
2025-09-29 17:27:24 | INFO     | log_session_start    | ================================================================================
2025-09-29 17:27:24 | INFO     | log_parameter_passing | 🔧 PARAMETER_PASSING | Component: WORKFLOW_INIT
2025-09-29 17:27:24 | INFO     | log_parameter_passing | ✅ PARAM_OK | session_id: str = async_1ae36eaac94f
2025-09-29 17:27:24 | INFO     | log_parameter_passing | ✅ PARAM_OK | progress_broadcaster: ProgressBroadcaster = <app.helpers.workflow_management.progress_tracker.ProgressBroadcaster object at 0x00000187B58D2C90>
2025-09-29 17:27:24 | INFO     | log_parameter_passing | ✅ PARAM_OK | progress_broadcaster_type: str = ProgressBroadcaster
2025-09-29 17:27:24 | INFO     | log_parameter_passing | ✅ PARAM_OK | analysis_mode: str = positional
2025-09-29 17:27:24 | INFO     | log_parameter_passing | ✅ PARAM_OK | market_specialization: str = Crypto
2025-09-29 17:27:24 | INFO     | log_parameter_passing | ✅ PARAM_OK | chart_images_count: int = 1
2025-09-29 17:27:24 | INFO     | log_parameter_passing | ✅ PARAM_OK | user_query: str = Positional trading analysis for Crypto market | User tier: free - Provide comprehensive analysis wit...
2025-09-29 17:27:24 | INFO     | log_parameter_passing | 🔧 PARAMETER_PASSING | Component: WORKFLOW_VERIFY
2025-09-29 17:27:24 | INFO     | log_parameter_passing | ✅ PARAM_OK | instance_progress_broadcaster: bool = True
2025-09-29 17:27:24 | INFO     | log_parameter_passing | ✅ PARAM_OK | instance_session_id: str = async_1ae36eaac94f
2025-09-29 17:27:24 | INFO     | log_parameter_passing | ✅ PARAM_OK | instance_progress_broadcaster_type: str = ProgressBroadcaster
