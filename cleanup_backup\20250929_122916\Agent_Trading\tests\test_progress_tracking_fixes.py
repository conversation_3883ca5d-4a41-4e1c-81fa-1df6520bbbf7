#!/usr/bin/env python3
"""
Test Progress Tracking Fixes
============================

This script tests all the progress tracking fixes to ensure:
1. All 8 workflow steps are broadcast in correct order
2. No duplicate progress updates
3. Memory storage comes before completion
4. Results are properly displayed after completion
5. Frontend receives all progress updates correctly
"""

import asyncio
import requests
import json
import time
import base64
from datetime import datetime
import threading
from typing import Dict, List

# Configuration
BASE_URL = "http://localhost:8000"
FRONTEND_URL = "http://localhost:3000"

class ProgressTrackingTester:
    def __init__(self):
        self.auth_token = None
        self.session_id = None
        self.progress_updates = []
        self.sse_connected = False
        self.sse_thread = None
        
    def authenticate(self) -> bool:
        """Authenticate with test user"""
        try:
            response = requests.post(f"{BASE_URL}/api/v1/auth/login", json={
                "email": "<EMAIL>",
                "password": "test123"
            })
            
            if response.status_code == 200:
                self.auth_token = response.json()["access_token"]
                print("✅ Authentication successful")
                return True
            else:
                print(f"❌ Authentication failed: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ Authentication error: {e}")
            return False
    
    def start_sse_monitoring(self, session_id: str):
        """Start monitoring SSE progress updates"""
        def monitor_sse():
            try:
                import sseclient
                headers = {
                    "Authorization": f"Bearer {self.auth_token}",
                    "Accept": "text/event-stream",
                    "Cache-Control": "no-cache"
                }
                
                url = f"{BASE_URL}/api/v1/progress/stream/{session_id}"
                response = requests.get(url, headers=headers, stream=True)
                
                if response.status_code == 200:
                    self.sse_connected = True
                    print("✅ SSE connection established")
                    
                    client = sseclient.SSEClient(response)
                    for event in client.events():
                        if event.data:
                            try:
                                data = json.loads(event.data)
                                self.progress_updates.append({
                                    "timestamp": datetime.now().isoformat(),
                                    "data": data
                                })
                                
                                if data.get("type") == "progress_update":
                                    progress_data = data.get("data", {})
                                    step = progress_data.get("step", "unknown")
                                    progress = progress_data.get("progress", 0)
                                    message = progress_data.get("message", "")
                                    
                                    print(f"📈 Progress Update: {step} ({progress}%) - {message}")
                                    
                                elif data.get("type") == "complete":
                                    print("🎉 Analysis completed via SSE")
                                    break
                                    
                            except json.JSONDecodeError:
                                print(f"⚠️ Invalid SSE data: {event.data}")
                else:
                    print(f"❌ SSE connection failed: {response.status_code}")
                    
            except Exception as e:
                print(f"❌ SSE monitoring error: {e}")
        
        self.sse_thread = threading.Thread(target=monitor_sse, daemon=True)
        self.sse_thread.start()
        time.sleep(2)  # Allow connection to establish
    
    def start_analysis(self) -> str:
        """Start analysis and return session ID"""
        try:
            # Load test image
            with open("Agent_Trading/Testing_images/Screenshot 2025-08-03 190956.png", "rb") as f:
                image_data = f.read()
                image_b64 = base64.b64encode(image_data).decode('utf-8')
            
            request_data = {
                "images_base64": [image_b64],
                "analysis_type": "Positional",
                "market_specialization": "Crypto",
                "preferred_model": "gemini-2.5-flash"
            }
            
            headers = {"Authorization": f"Bearer {self.auth_token}"}
            response = requests.post(
                f"{BASE_URL}/api/v1/trading/async/start",
                json=request_data,
                headers=headers
            )
            
            if response.status_code == 200:
                session_id = response.json()["session_id"]
                print(f"✅ Analysis started with session: {session_id}")
                return session_id
            else:
                print(f"❌ Analysis start failed: {response.status_code}")
                print(response.text)
                return None
                
        except Exception as e:
            print(f"❌ Analysis start error: {e}")
            return None
    
    def wait_for_completion(self, timeout: int = 300) -> bool:
        """Wait for analysis completion"""
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            # Check if we received completion via SSE
            for update in self.progress_updates:
                if update["data"].get("type") == "complete":
                    return True
            
            time.sleep(5)
        
        print(f"⚠️ Analysis did not complete within {timeout} seconds")
        return False
    
    def get_results(self, session_id: str) -> Dict:
        """Get analysis results"""
        try:
            headers = {"Authorization": f"Bearer {self.auth_token}"}
            response = requests.get(
                f"{BASE_URL}/api/v1/trading/async/result/{session_id}",
                headers=headers
            )
            
            if response.status_code == 200:
                return response.json()
            else:
                print(f"❌ Results fetch failed: {response.status_code}")
                return None
                
        except Exception as e:
            print(f"❌ Results fetch error: {e}")
            return None
    
    def analyze_progress_updates(self):
        """Analyze the collected progress updates"""
        print("\n" + "="*60)
        print("📊 PROGRESS TRACKING ANALYSIS")
        print("="*60)
        
        # Expected steps in order
        expected_steps = [
            "chart_analysis",
            "symbol_detection", 
            "tool_execution",
            "tool_summarization",
            "rag_integration",
            "final_analysis",
            "memory_storage",
            "complete"
        ]
        
        # Extract progress updates
        progress_updates = []
        for update in self.progress_updates:
            if update["data"].get("type") == "progress_update":
                progress_data = update["data"].get("data", {})
                progress_updates.append({
                    "step": progress_data.get("step"),
                    "progress": progress_data.get("progress"),
                    "message": progress_data.get("message"),
                    "timestamp": update["timestamp"]
                })
        
        print(f"📈 Total progress updates received: {len(progress_updates)}")
        print("\n🔍 Progress Update Sequence:")
        
        for i, update in enumerate(progress_updates, 1):
            step = update["step"]
            progress = update["progress"]
            message = update["message"]
            print(f"  {i}. {step} ({progress}%) - {message}")
        
        # Check for missing steps
        received_steps = [update["step"] for update in progress_updates]
        missing_steps = [step for step in expected_steps if step not in received_steps]
        
        if missing_steps:
            print(f"\n❌ Missing steps: {missing_steps}")
        else:
            print(f"\n✅ All expected steps received")
        
        # Check step order
        step_order_correct = True
        for i, expected_step in enumerate(expected_steps):
            if expected_step in received_steps:
                actual_index = received_steps.index(expected_step)
                expected_index = i
                if actual_index != expected_index:
                    step_order_correct = False
                    print(f"❌ Step order issue: {expected_step} at position {actual_index}, expected {expected_index}")
        
        if step_order_correct:
            print("✅ Step order is correct")
        
        # Check for duplicates
        step_counts = {}
        for step in received_steps:
            step_counts[step] = step_counts.get(step, 0) + 1
        
        duplicates = {step: count for step, count in step_counts.items() if count > 1}
        if duplicates:
            print(f"❌ Duplicate steps found: {duplicates}")
        else:
            print("✅ No duplicate steps")
        
        return {
            "total_updates": len(progress_updates),
            "missing_steps": missing_steps,
            "step_order_correct": step_order_correct,
            "duplicates": duplicates,
            "all_steps_received": len(missing_steps) == 0
        }
    
    def run_test(self):
        """Run the complete test"""
        print("🚀 Starting Progress Tracking Test")
        print("="*60)
        
        # Step 1: Authenticate
        if not self.authenticate():
            return False
        
        # Step 2: Start analysis
        session_id = self.start_analysis()
        if not session_id:
            return False
        
        # Step 3: Start SSE monitoring
        self.start_sse_monitoring(session_id)
        
        # Step 4: Wait for completion
        print("\n⏳ Waiting for analysis completion...")
        completed = self.wait_for_completion()
        
        # Step 5: Get results
        if completed:
            print("\n📊 Fetching results...")
            results = self.get_results(session_id)
            if results:
                print("✅ Results fetched successfully")
                print(f"🔍 Analysis success: {results.get('success', False)}")
                print(f"🔍 Detected symbol: {results.get('data', {}).get('detected_symbol', 'N/A')}")
            else:
                print("❌ Failed to fetch results")
        
        # Step 6: Analyze progress tracking
        analysis = self.analyze_progress_updates()
        
        # Final summary
        print("\n" + "="*60)
        print("📋 TEST SUMMARY")
        print("="*60)
        
        if analysis["all_steps_received"] and analysis["step_order_correct"] and not analysis["duplicates"]:
            print("🎉 ALL TESTS PASSED!")
            print("✅ Progress tracking is working correctly")
        else:
            print("❌ SOME TESTS FAILED")
            if not analysis["all_steps_received"]:
                print(f"  - Missing steps: {analysis['missing_steps']}")
            if not analysis["step_order_correct"]:
                print("  - Step order is incorrect")
            if analysis["duplicates"]:
                print(f"  - Duplicate steps: {analysis['duplicates']}")
        
        return analysis["all_steps_received"] and analysis["step_order_correct"] and not analysis["duplicates"]

if __name__ == "__main__":
    tester = ProgressTrackingTester()
    success = tester.run_test()
    exit(0 if success else 1)
