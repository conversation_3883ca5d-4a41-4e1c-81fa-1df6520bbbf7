"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/hooks/useRealTimeProgress.ts":
/*!******************************************!*\
  !*** ./src/hooks/useRealTimeProgress.ts ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WORKFLOW_STEPS: () => (/* binding */ WORKFLOW_STEPS),\n/* harmony export */   useRealTimeProgress: () => (/* binding */ useRealTimeProgress)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/**\r\n * Real-time progress tracking hook for LangGraph AI analysis workflow\r\n * Uses Server-Sent Events ONLY for live updates (no polling fallback)\r\n */ \n// ✅ ALIGNED: Frontend mapping now matches actual LangGraph workflow execution\nconst WORKFLOW_STEPS = {\n    CHART_ANALYSIS: {\n        name: 'Chart Analysis',\n        progress: 20,\n        message: '📊 Starting comprehensive AI analysis workflow...'\n    },\n    SYMBOL_DETECTION: {\n        name: 'Symbol Detection',\n        progress: 40,\n        message: '✅ Symbol detected in chart'\n    },\n    TOOL_EXECUTION: {\n        name: 'Data Collection',\n        progress: 50,\n        message: '🛠️ Collecting data from tools...'\n    },\n    TOOL_SUMMARIZATION: {\n        name: 'Data Processing',\n        progress: 60,\n        message: '📋 Processing and summarizing data...'\n    },\n    RAG_INTEGRATION: {\n        name: 'RAG Integration',\n        progress: 70,\n        message: '🧠 Integrating historical context and patterns...'\n    },\n    FINAL_ANALYSIS: {\n        name: 'Final Analysis',\n        progress: 80,\n        message: '🎯 Generating final trading analysis...'\n    },\n    MEMORY_STORAGE: {\n        name: 'Database Storage',\n        progress: 90,\n        message: '💾 Saving analysis...'\n    },\n    COMPLETE: {\n        name: 'Complete',\n        progress: 100,\n        message: '🎉 Analysis complete!'\n    }\n};\nfunction useRealTimeProgress() {\n    const [state, setState] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({\n        sessionId: null,\n        currentStep: 'chart_analysis',\n        progress: 0,\n        message: 'Preparing for analysis...',\n        isConnected: false,\n        isComplete: false,\n        error: null,\n        updates: []\n    });\n    // 🔧 CRITICAL FIX: Progress smoothing for rapid updates\n    const [progressQueue, setProgressQueue] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [isProcessingQueue, setIsProcessingQueue] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    // Process progress queue with smooth animations\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useRealTimeProgress.useEffect\": ()=>{\n            if (progressQueue.length > 0 && !isProcessingQueue) {\n                setIsProcessingQueue(true);\n                const processNext = {\n                    \"useRealTimeProgress.useEffect.processNext\": ()=>{\n                        setProgressQueue({\n                            \"useRealTimeProgress.useEffect.processNext\": (queue)=>{\n                                if (queue.length === 0) {\n                                    setIsProcessingQueue(false);\n                                    return queue;\n                                }\n                                const [nextUpdate, ...remaining] = queue;\n                                // Apply the update\n                                setState({\n                                    \"useRealTimeProgress.useEffect.processNext\": (prev)=>({\n                                            ...prev,\n                                            currentStep: nextUpdate.step,\n                                            progress: nextUpdate.progress,\n                                            message: nextUpdate.message\n                                        })\n                                }[\"useRealTimeProgress.useEffect.processNext\"]);\n                                // 🔧 IMPROVED: Dynamic delay based on queue length and progress difference\n                                let delay = 400 // Base delay reduced from 800ms to 400ms\n                                ;\n                                if (remaining.length > 0) {\n                                    const nextProgress = remaining[0].progress;\n                                    const currentProgress = nextUpdate.progress;\n                                    const progressDiff = nextProgress - currentProgress;\n                                    // Shorter delay for small progress jumps, longer for big jumps\n                                    if (progressDiff <= 10) {\n                                        delay = 300; // Fast updates for small increments\n                                    } else if (progressDiff <= 20) {\n                                        delay = 500; // Medium delay for medium increments\n                                    } else {\n                                        delay = 700; // Longer delay for big jumps\n                                    }\n                                    // If queue is getting long, speed up processing\n                                    if (remaining.length > 3) {\n                                        delay = Math.max(200, delay * 0.6); // Speed up but not too fast\n                                    }\n                                    console.log(\"\\uD83D\\uDCC8 FRONTEND_DEBUG: Processing queue - current: \".concat(currentProgress, \"%, next: \").concat(nextProgress, \"%, delay: \").concat(delay, \"ms, queue length: \").concat(remaining.length));\n                                    setTimeout(processNext, delay);\n                                } else {\n                                    setIsProcessingQueue(false);\n                                }\n                                return remaining;\n                            }\n                        }[\"useRealTimeProgress.useEffect.processNext\"]);\n                    }\n                }[\"useRealTimeProgress.useEffect.processNext\"];\n                processNext();\n            }\n        }\n    }[\"useRealTimeProgress.useEffect\"], [\n        progressQueue,\n        isProcessingQueue\n    ]);\n    const eventSourceRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    // Check if analysis is already complete\n    const checkAnalysisStatus = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useRealTimeProgress.useCallback[checkAnalysisStatus]\": async (sessionId)=>{\n            try {\n                const token = localStorage.getItem('access_token');\n                const baseURL = \"http://localhost:8000\" || 0;\n                const response = await fetch(\"\".concat(baseURL, \"/api/v1/async-trading/status/\").concat(sessionId), {\n                    headers: {\n                        'Authorization': \"Bearer \".concat(token),\n                        'Content-Type': 'application/json'\n                    }\n                });\n                if (response.ok) {\n                    const data = await response.json();\n                    if (data.status === 'completed') {\n                        setState({\n                            \"useRealTimeProgress.useCallback[checkAnalysisStatus]\": (prev)=>({\n                                    ...prev,\n                                    isComplete: true,\n                                    progress: 100,\n                                    currentStep: 'complete',\n                                    message: 'Analysis completed successfully!'\n                                })\n                        }[\"useRealTimeProgress.useCallback[checkAnalysisStatus]\"]);\n                        return true;\n                    }\n                }\n            } catch (error) {\n                console.error('Failed to check analysis status:', error);\n            }\n            return false;\n        }\n    }[\"useRealTimeProgress.useCallback[checkAnalysisStatus]\"], []);\n    const connectToProgress = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useRealTimeProgress.useCallback[connectToProgress]\": async (sessionId)=>{\n            // Close existing SSE connection\n            if (eventSourceRef.current) {\n                eventSourceRef.current.close();\n            }\n            // 🔧 CRITICAL FIX: Update state with the provided session ID IMMEDIATELY\n            // This ensures the UI shows the correct session ID right away\n            console.log('🔧 FRONTEND_DEBUG: Setting session ID immediately:', sessionId);\n            setState({\n                \"useRealTimeProgress.useCallback[connectToProgress]\": (prev)=>({\n                        ...prev,\n                        sessionId,\n                        error: null,\n                        currentStep: 'chart_analysis',\n                        progress: 0,\n                        message: 'Connecting to progress stream...',\n                        isConnected: false,\n                        isComplete: false\n                    })\n            }[\"useRealTimeProgress.useCallback[connectToProgress]\"]);\n            // First check if analysis is already complete\n            const isComplete = await checkAnalysisStatus(sessionId);\n            if (isComplete) {\n                return;\n            }\n            const token = localStorage.getItem('access_token');\n            const baseURL = \"http://localhost:8000\" || 0;\n            if (!token) {\n                console.error('❌ No access token found for SSE connection');\n                setState({\n                    \"useRealTimeProgress.useCallback[connectToProgress]\": (prev)=>({\n                            ...prev,\n                            error: 'Authentication required for progress tracking'\n                        })\n                }[\"useRealTimeProgress.useCallback[connectToProgress]\"]);\n                return;\n            }\n            // Try SSE first - Fixed URL construction and added better error handling\n            const sseUrl = \"\".concat(baseURL, \"/api/v1/progress/stream/\").concat(sessionId, \"?token=\").concat(encodeURIComponent(token));\n            console.log('🔗 FRONTEND_DEBUG: Attempting SSE connection');\n            console.log('🔗 FRONTEND_DEBUG: SSE URL:', sseUrl);\n            console.log('🔗 FRONTEND_DEBUG: Session ID:', sessionId);\n            console.log('🔗 FRONTEND_DEBUG: Token present:', !!token);\n            console.log('🔗 FRONTEND_DEBUG: Token length:', token === null || token === void 0 ? void 0 : token.length);\n            console.log('🔗 FRONTEND_DEBUG: Base URL:', baseURL);\n            // Create EventSource with proper error handling\n            let eventSource;\n            try {\n                eventSource = new EventSource(sseUrl);\n                console.log('✅ FRONTEND_DEBUG: EventSource created successfully');\n            } catch (error) {\n                console.error('❌ FRONTEND_DEBUG: Failed to create EventSource:', error);\n                setState({\n                    \"useRealTimeProgress.useCallback[connectToProgress]\": (prev)=>({\n                            ...prev,\n                            error: \"Failed to create SSE connection: \".concat(error)\n                        })\n                }[\"useRealTimeProgress.useCallback[connectToProgress]\"]);\n                return;\n            }\n            eventSourceRef.current = eventSource;\n            let sseConnected = false;\n            // SSE connection timeout (no polling fallback)\n            const sseTimeout = setTimeout({\n                \"useRealTimeProgress.useCallback[connectToProgress].sseTimeout\": ()=>{\n                    if (!sseConnected) {\n                        console.log('⚠️ SSE connection timeout - no polling fallback');\n                        eventSource.close();\n                        setState({\n                            \"useRealTimeProgress.useCallback[connectToProgress].sseTimeout\": (prev)=>({\n                                    ...prev,\n                                    isConnected: false,\n                                    error: 'SSE connection timeout - check backend connection'\n                                })\n                        }[\"useRealTimeProgress.useCallback[connectToProgress].sseTimeout\"]);\n                    }\n                }\n            }[\"useRealTimeProgress.useCallback[connectToProgress].sseTimeout\"], 10000) // Increased timeout to 10 seconds\n            ;\n            eventSource.onopen = ({\n                \"useRealTimeProgress.useCallback[connectToProgress]\": ()=>{\n                    console.log('✅ FRONTEND_DEBUG: SSE connection established');\n                    console.log('✅ FRONTEND_DEBUG: EventSource readyState:', eventSource.readyState);\n                    console.log('✅ FRONTEND_DEBUG: EventSource URL:', eventSource.url);\n                    sseConnected = true;\n                    clearTimeout(sseTimeout);\n                    setState({\n                        \"useRealTimeProgress.useCallback[connectToProgress]\": (prev)=>({\n                                ...prev,\n                                isConnected: true,\n                                error: null,\n                                message: 'Connected to progress stream'\n                            })\n                    }[\"useRealTimeProgress.useCallback[connectToProgress]\"]);\n                }\n            })[\"useRealTimeProgress.useCallback[connectToProgress]\"];\n            eventSource.onmessage = ({\n                \"useRealTimeProgress.useCallback[connectToProgress]\": (event)=>{\n                    try {\n                        console.log('📨 FRONTEND_DEBUG: Raw SSE message received:', event.data);\n                        const data = JSON.parse(event.data);\n                        console.log('📨 FRONTEND_DEBUG: Parsed SSE data:', data);\n                        console.log('📨 FRONTEND_DEBUG: Event type:', data.type);\n                        switch(data.type){\n                            case 'connected':\n                                console.log('✅ FRONTEND_DEBUG: Progress stream connected for session:', data.session_id);\n                                break;\n                            case 'progress_update':\n                                const update = data.data;\n                                console.log('📈 FRONTEND_DEBUG: SSE Progress update:', update);\n                                // Map backend step to frontend display\n                                const stepInfo = getStepInfo(update.step);\n                                console.log('📈 FRONTEND_DEBUG: Step info mapped:', stepInfo);\n                                // 🔧 CRITICAL DEBUG: Log the exact state update\n                                console.log('📈 FRONTEND_DEBUG: About to update state with:', {\n                                    currentStep: update.step,\n                                    progress: stepInfo.progress,\n                                    message: stepInfo.message,\n                                    backendProgress: update.progress,\n                                    frontendProgress: stepInfo.progress\n                                });\n                                // 🔧 CRITICAL FIX: Use progress queue for smooth animations\n                                setState({\n                                    \"useRealTimeProgress.useCallback[connectToProgress]\": (prev)=>{\n                                        // Only queue if this is actually a new step or higher progress\n                                        const isNewStep = update.step !== prev.currentStep;\n                                        const isHigherProgress = stepInfo.progress > prev.progress;\n                                        if (isNewStep || isHigherProgress) {\n                                            console.log('📈 FRONTEND_DEBUG: Queueing progress update:', stepInfo.progress, 'step:', update.step);\n                                            // Add to progress queue for smooth processing\n                                            setProgressQueue({\n                                                \"useRealTimeProgress.useCallback[connectToProgress]\": (queue)=>[\n                                                        ...queue,\n                                                        {\n                                                            step: update.step,\n                                                            progress: stepInfo.progress,\n                                                            message: stepInfo.message\n                                                        }\n                                                    ]\n                                            }[\"useRealTimeProgress.useCallback[connectToProgress]\"]);\n                                            // Update the updates array immediately\n                                            return {\n                                                ...prev,\n                                                updates: [\n                                                    ...prev.updates,\n                                                    update\n                                                ]\n                                            };\n                                        } else {\n                                            console.log('📈 FRONTEND_DEBUG: Skipping duplicate/lower progress update:', stepInfo.progress, 'current:', prev.progress);\n                                            return prev;\n                                        }\n                                    }\n                                }[\"useRealTimeProgress.useCallback[connectToProgress]\"]);\n                                break;\n                            case 'complete':\n                                console.log('🎉 SSE Analysis complete!');\n                                setState({\n                                    \"useRealTimeProgress.useCallback[connectToProgress]\": (prev)=>({\n                                            ...prev,\n                                            isComplete: true,\n                                            progress: 100,\n                                            currentStep: 'complete',\n                                            message: 'Analysis completed successfully!'\n                                        })\n                                }[\"useRealTimeProgress.useCallback[connectToProgress]\"]);\n                                eventSource.close();\n                                break;\n                            case 'ping':\n                                break;\n                            case 'error':\n                                console.error('❌ SSE Progress stream error:', data.error);\n                                setState({\n                                    \"useRealTimeProgress.useCallback[connectToProgress]\": (prev)=>({\n                                            ...prev,\n                                            error: data.error,\n                                            isConnected: false\n                                        })\n                                }[\"useRealTimeProgress.useCallback[connectToProgress]\"]);\n                                eventSource.close();\n                                break;\n                        }\n                    } catch (error) {\n                        console.error('Failed to parse SSE progress update:', error);\n                    }\n                }\n            })[\"useRealTimeProgress.useCallback[connectToProgress]\"];\n            eventSource.onerror = ({\n                \"useRealTimeProgress.useCallback[connectToProgress]\": (error)=>{\n                    console.error('❌ FRONTEND_DEBUG: SSE EventSource error:', error);\n                    console.error('❌ FRONTEND_DEBUG: EventSource readyState:', eventSource.readyState);\n                    console.error('❌ FRONTEND_DEBUG: EventSource URL:', sseUrl);\n                    clearTimeout(sseTimeout);\n                    setState({\n                        \"useRealTimeProgress.useCallback[connectToProgress]\": (prev)=>({\n                                ...prev,\n                                isConnected: false,\n                                error: 'SSE connection failed - no polling fallback as requested'\n                            })\n                    }[\"useRealTimeProgress.useCallback[connectToProgress]\"]);\n                    eventSource.close();\n                }\n            })[\"useRealTimeProgress.useCallback[connectToProgress]\"];\n        }\n    }[\"useRealTimeProgress.useCallback[connectToProgress]\"], []);\n    // Helper function to map backend steps to frontend display\n    const getStepInfo = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useRealTimeProgress.useCallback[getStepInfo]\": (step)=>{\n            switch(step){\n                case 'chart_analysis':\n                    return WORKFLOW_STEPS.CHART_ANALYSIS;\n                case 'symbol_detection':\n                    return WORKFLOW_STEPS.SYMBOL_DETECTION;\n                case 'tool_execution':\n                    return WORKFLOW_STEPS.TOOL_EXECUTION;\n                case 'tool_summarization':\n                    return WORKFLOW_STEPS.TOOL_SUMMARIZATION;\n                case 'rag_integration':\n                    return WORKFLOW_STEPS.RAG_INTEGRATION;\n                case 'final_analysis':\n                    return WORKFLOW_STEPS.FINAL_ANALYSIS;\n                case 'memory_storage':\n                    return WORKFLOW_STEPS.MEMORY_STORAGE;\n                case 'complete':\n                    return WORKFLOW_STEPS.COMPLETE;\n                default:\n                    return {\n                        name: 'Processing',\n                        progress: 50,\n                        message: 'Processing...'\n                    };\n            }\n        }\n    }[\"useRealTimeProgress.useCallback[getStepInfo]\"], []);\n    // Polling removed - SSE only implementation\n    const startProgressTracking = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useRealTimeProgress.useCallback[startProgressTracking]\": async (backendSessionId)=>{\n            if (backendSessionId) {\n                // Use backend session ID directly\n                await connectToProgress(backendSessionId);\n                return backendSessionId;\n            } else {\n                // Fallback: generate frontend session ID if backend doesn't provide one\n                const sessionId = \"frontend_\".concat(Date.now(), \"_\").concat(Math.random().toString(36).substring(2, 11));\n                setState({\n                    \"useRealTimeProgress.useCallback[startProgressTracking]\": (prev)=>({\n                            ...prev,\n                            sessionId,\n                            error: null\n                        })\n                }[\"useRealTimeProgress.useCallback[startProgressTracking]\"]);\n                await connectToProgress(sessionId);\n                console.log('📊 Generated fallback progress session:', sessionId);\n                return sessionId;\n            }\n        }\n    }[\"useRealTimeProgress.useCallback[startProgressTracking]\"], [\n        connectToProgress\n    ]);\n    const cleanup = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useRealTimeProgress.useCallback[cleanup]\": async (sessionId)=>{\n            // Close EventSource connection\n            if (eventSourceRef.current) {\n                eventSourceRef.current.close();\n                eventSourceRef.current = null;\n            }\n            // No polling to clear - SSE only\n            console.log('📊 Progress session cleanup completed:', sessionId || state.sessionId);\n            // Reset state\n            setState({\n                sessionId: null,\n                currentStep: 'chart_analysis',\n                progress: 0,\n                message: 'Preparing for analysis...',\n                isConnected: false,\n                isComplete: false,\n                error: null,\n                updates: []\n            });\n        }\n    }[\"useRealTimeProgress.useCallback[cleanup]\"], [\n        state.sessionId\n    ]);\n    // Cleanup on unmount\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useRealTimeProgress.useEffect\": ()=>{\n            return ({\n                \"useRealTimeProgress.useEffect\": ()=>{\n                    if (eventSourceRef.current) {\n                        eventSourceRef.current.close();\n                    }\n                }\n            })[\"useRealTimeProgress.useEffect\"];\n        }\n    }[\"useRealTimeProgress.useEffect\"], []);\n    return {\n        ...state,\n        startProgressTracking,\n        connectToProgress,\n        cleanup,\n        WORKFLOW_STEPS\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useRealTimeProgress.ts\n"));

/***/ })

});