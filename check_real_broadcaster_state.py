#!/usr/bin/env python3
"""
Check Real Broadcaster State
===========================

This script checks the actual singleton ProgressBroadcaster state
that's being used by the running application.
"""

import sys
import os
import json
import asyncio
from pathlib import Path
from datetime import datetime

# Add the backend directory to the Python path
backend_dir = Path(__file__).parent / "Agent_Trading" / "backend"
sys.path.insert(0, str(backend_dir))

async def check_real_broadcaster_state():
    """Check the actual singleton ProgressBroadcaster state."""
    print("🔍 CHECKING REAL BROADCASTER STATE")
    print("=" * 50)
    
    try:
        # Import the singleton getter function (not the class directly)
        from app.helpers.workflow_management.progress_tracker import get_progress_broadcaster, WorkflowStep, ProgressUpdate
        
        # Get the actual singleton instance that the application uses
        broadcaster = get_progress_broadcaster()
        
        print(f"📊 Real Broadcaster ID: {id(broadcaster)}")
        print(f"📊 Active sessions: {list(broadcaster.clients.keys())}")
        print(f"📊 Buffered sessions: {list(broadcaster.buffered_updates.keys())}")
        
        # Check buffered updates for each session
        total_buffered = 0
        for session_id, updates in broadcaster.buffered_updates.items():
            print(f"\n📦 Session {session_id}:")
            print(f"   Buffered updates: {len(updates)}")
            print(f"   Active clients: {len(broadcaster.clients.get(session_id, []))}")
            total_buffered += len(updates)
            
            if updates:
                print("   Recent updates:")
                for i, update in enumerate(updates[-5:]):  # Show last 5 updates
                    step = update.get('step', 'unknown')
                    progress = update.get('progress', 0)
                    message = update.get('message', 'No message')[:50]
                    timestamp = update.get('timestamp', 'No timestamp')
                    print(f"     {i+1}. {step} ({progress}%) - {message} [{timestamp}]")
        
        print(f"\n📊 SUMMARY:")
        print(f"   Total sessions with buffered updates: {len(broadcaster.buffered_updates)}")
        print(f"   Total buffered updates: {total_buffered}")
        print(f"   Active client sessions: {len(broadcaster.clients)}")
        
        # Test if we can add a client to an existing session with buffered updates
        if broadcaster.buffered_updates:
            test_session = list(broadcaster.buffered_updates.keys())[0]
            buffered_count = len(broadcaster.buffered_updates[test_session])
            
            print(f"\n🧪 TESTING REPLAY ON EXISTING SESSION: {test_session}")
            print(f"   Session has {buffered_count} buffered updates")
            
            # Add a client to this session
            client_queue = await broadcaster.add_client(test_session)
            
            # Try to receive buffered updates
            received_updates = []
            try:
                # Try to get multiple updates with timeout
                for i in range(min(buffered_count, 5)):  # Try to get up to 5 updates
                    update = await asyncio.wait_for(client_queue.get(), timeout=0.5)
                    received_updates.append(update)
                    print(f"   ✅ Received buffered update {i+1}: {update.get('step')} ({update.get('progress')}%)")
            except asyncio.TimeoutError:
                print(f"   ⏰ Timeout after receiving {len(received_updates)} updates")
            
            if received_updates:
                print(f"   🎉 SUCCESS: Received {len(received_updates)} buffered updates!")
                return True
            else:
                print(f"   ❌ FAILED: No buffered updates received")
                return False
        else:
            print(f"\n⚠️  No sessions with buffered updates to test")
            return True  # Not a failure, just no data to test
            
    except Exception as e:
        print(f"❌ Error checking real broadcaster state: {e}")
        import traceback
        traceback.print_exc()
        return False

def analyze_session_timing():
    """Analyze the timing between session creation and client connection."""
    print(f"\n⏰ ANALYZING SESSION TIMING")
    print("=" * 50)
    
    # Check progress debug log for timing patterns
    progress_log = Path("Agent_Trading/backend/logs/progress_debug.log")
    if progress_log.exists():
        try:
            with open(progress_log, 'r', encoding='utf-8', errors='ignore') as f:
                lines = f.readlines()
            
            # Look for session lifecycle events
            session_events = {}
            
            for line in lines:
                if "SESSION_LIFECYCLE" in line and "workflow_start" in line:
                    # Extract session ID and timestamp
                    if "Session:" in line:
                        parts = line.split("Session:")
                        if len(parts) > 1:
                            session_id = parts[1].split("|")[0].strip()
                            timestamp = line.split("]")[0].split("[")[1]
                            session_events[session_id] = {"workflow_start": timestamp}
                
                elif "SSE_CONNECTION" in line:
                    # Extract session ID and timestamp
                    if "Session:" in line:
                        parts = line.split("Session:")
                        if len(parts) > 1:
                            session_id = parts[1].split("|")[0].strip()
                            timestamp = line.split("]")[0].split("[")[1]
                            if session_id in session_events:
                                session_events[session_id]["sse_connection"] = timestamp
            
            print(f"📊 Found {len(session_events)} sessions with timing data:")
            
            for session_id, events in session_events.items():
                print(f"\n📱 Session: {session_id}")
                if "workflow_start" in events:
                    print(f"   Workflow started: {events['workflow_start']}")
                if "sse_connection" in events:
                    print(f"   SSE connected:    {events['sse_connection']}")
                    
                    # Calculate timing gap
                    if "workflow_start" in events:
                        # Simple time comparison (this is approximate)
                        start_time = events["workflow_start"]
                        connect_time = events["sse_connection"]
                        print(f"   ⏱️  Connection delay: SSE connected after workflow started")
                        
                        # This explains why buffered updates are needed!
                        print(f"   🔍 This session would need buffered update replay")
                else:
                    print(f"   ❌ No SSE connection found")
                    print(f"   🔍 This session lost all progress updates")
        
        except Exception as e:
            print(f"❌ Error analyzing timing: {e}")

async def main():
    """Main function."""
    print("🔍 REAL BROADCASTER STATE CHECK")
    print(f"Start time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"Working directory: {os.getcwd()}")
    
    # Check the real broadcaster state
    broadcaster_ok = await check_real_broadcaster_state()
    
    # Analyze timing patterns
    analyze_session_timing()
    
    print(f"\n{'='*50}")
    print("📊 REAL STATE SUMMARY:")
    print(f"   Real Broadcaster: {'✅ HAS BUFFERED DATA' if broadcaster_ok else '❌ NO BUFFERED DATA'}")
    
    return broadcaster_ok

if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
