#!/usr/bin/env python3
"""
Debug Login Issue
=================

This script helps debug the login 500 error by testing
the authentication flow step by step.
"""

import sys
import os
import asyncio
import traceback
from pathlib import Path

# Add the backend directory to the Python path
backend_path = Path(__file__).parent / "Agent_Trading" / "backend"
sys.path.insert(0, str(backend_path))

async def test_database_connection():
    """Test if database connection is working."""
    print("🔍 Testing database connection...")
    
    try:
        from app.core.database import get_db
        from app.services.user_service import UserService
        
        # Get database session
        async for db in get_db():
            print("✅ Database connection established")
            
            # Test user service
            user_service = UserService(db)
            print("✅ User service created")
            
            # Try to get user by email
            test_email = "<EMAIL>"
            user = await user_service.get_user_by_email(test_email)
            
            if user:
                print(f"✅ User found: {user.email}")
                print(f"   - ID: {user.id}")
                print(f"   - Full Name: {user.full_name}")
                print(f"   - Active: {user.is_active}")
                print(f"   - Roles: {user.roles}")
                print(f"   - Tier: {user.tier}")
            else:
                print(f"❌ User not found: {test_email}")
                
                # List all users to see what's in the database
                print("\n🔍 Listing all users in database:")
                try:
                    from sqlalchemy import select
                    from app.models.user import User
                    
                    result = await db.execute(select(User))
                    all_users = result.scalars().all()
                    
                    if all_users:
                        for user in all_users:
                            print(f"   - {user.email} (ID: {user.id}, Active: {user.is_active})")
                    else:
                        print("   No users found in database")
                        
                except Exception as e:
                    print(f"   Error listing users: {e}")
            
            break  # Exit the async generator
            
        return True
        
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        print(f"   Traceback: {traceback.format_exc()}")
        return False

async def test_authentication():
    """Test the authentication process."""
    print("\n🔍 Testing authentication process...")
    
    try:
        from app.core.database import get_db
        from app.services.user_service import UserService
        
        test_email = "<EMAIL>"
        test_password = "Bunnych@1627"
        
        async for db in get_db():
            user_service = UserService(db)
            
            print(f"🔐 Attempting to authenticate: {test_email}")
            
            # Test authentication
            authenticated_user = await user_service.authenticate_user(test_email, test_password)
            
            if authenticated_user:
                print(f"✅ Authentication successful!")
                print(f"   - User: {authenticated_user.email}")
                print(f"   - ID: {authenticated_user.id}")
                return True
            else:
                print(f"❌ Authentication failed")
                
                # Check if user exists
                user = await user_service.get_user_by_email(test_email)
                if user:
                    print(f"   - User exists but password verification failed")
                    print(f"   - User active: {user.is_active}")
                else:
                    print(f"   - User does not exist")
                
                return False
            
            break
            
    except Exception as e:
        print(f"❌ Authentication test failed: {e}")
        print(f"   Traceback: {traceback.format_exc()}")
        return False

async def test_token_creation():
    """Test JWT token creation."""
    print("\n🔍 Testing JWT token creation...")
    
    try:
        from app.core.security import security_manager
        from app.models.security import User as SecurityUser
        from datetime import datetime
        
        # Create a test user object
        test_user = SecurityUser(
            id="test-id",
            email="<EMAIL>",
            full_name="Test User",
            roles=["user"],
            tier="free",
            is_active=True,
            created_at=datetime.utcnow()
        )
        
        # Test token creation
        access_token = security_manager.create_access_token(test_user)
        refresh_token = security_manager.create_refresh_token(test_user)
        
        print(f"✅ Token creation successful!")
        print(f"   - Access token length: {len(access_token)}")
        print(f"   - Refresh token length: {len(refresh_token)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Token creation failed: {e}")
        print(f"   Traceback: {traceback.format_exc()}")
        return False

async def test_async_workflow_import():
    """Test if the async workflow changes are causing import issues."""
    print("\n🔍 Testing async workflow imports...")
    
    try:
        # Test importing the modified files
        from app.services.enhanced_langgraph_service import EnhancedLangGraphService
        print("✅ Enhanced LangGraph service import successful")
        
        from app.helpers.workflow_management.complete_langgraph_workflow import CompleteLangGraphWorkflow
        print("✅ Complete LangGraph workflow import successful")
        
        # Test creating instances
        service = EnhancedLangGraphService()
        print("✅ Enhanced LangGraph service instance created")
        
        return True
        
    except Exception as e:
        print(f"❌ Async workflow import failed: {e}")
        print(f"   Traceback: {traceback.format_exc()}")
        return False

async def main():
    """Main debug function."""
    print("🐛 DEBUGGING LOGIN 500 ERROR")
    print("=" * 50)
    
    # Test each component
    tests = [
        ("Database Connection", test_database_connection),
        ("Authentication Process", test_authentication),
        ("JWT Token Creation", test_token_creation),
        ("Async Workflow Imports", test_async_workflow_import),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = await test_func()
            results[test_name] = result
        except Exception as e:
            print(f"❌ {test_name} crashed: {e}")
            results[test_name] = False
    
    # Summary
    print(f"\n{'='*50}")
    print("🔍 DEBUG SUMMARY:")
    
    all_passed = True
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   {test_name}: {status}")
        if not result:
            all_passed = False
    
    if all_passed:
        print(f"\n🎉 All tests passed! The login issue might be elsewhere.")
    else:
        print(f"\n❌ Some tests failed. This indicates the source of the login issue.")
    
    return all_passed

if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
