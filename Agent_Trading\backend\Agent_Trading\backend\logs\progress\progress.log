2025-09-29 13:39:28 | INFO     | unified.progress     | [async_4ca491b4062c] PROGRESS | chart_upload (5%) - Analysis request received, starting background processing... | client_count=0
2025-09-29 13:39:28 | INFO     | unified.progress     | [async_4ca491b4062c] PROGRESS | chart_upload (5%) - 📊 Processing uploaded chart images... | client_count=0
2025-09-29 13:39:28 | INFO     | unified.progress     | [async_4ca491b4062c] PROGRESS | chart_upload (5%) - 🖼️ Processed chart image 1/1 (75KB) | client_count=0
2025-09-29 13:39:28 | INFO     | unified.progress     | [async_4ca491b4062c] PROGRESS | replay_buffered (0%) - Replayed 3 buffered updates to new client | buffered_count=3
2025-09-29 13:39:29 | INFO     | unified.progress     | [async_4ca491b4062c] PROGRESS | chart_analysis (20%) - 🚀 Starting comprehensive AI analysis workflow... | client_count=1
2025-09-29 13:39:40 | INFO     | unified.progress     | [async_4ca491b4062c] PROGRESS | symbol_detection (40%) - ✅ Symbol detected: BTCUSD (crypto market) | client_count=1
2025-09-29 13:39:40 | INFO     | unified.progress     | [async_4ca491b4062c] PROGRESS | tool_execution (50%) - 🎯 Executing market data tools... | client_count=1
2025-09-29 13:39:45 | INFO     | unified.progress     | [async_4ca491b4062c] PROGRESS | tool_summarization (60%) - 🎯 Summarizing market data with AI... | client_count=1
2025-09-29 13:39:58 | INFO     | unified.progress     | [async_4ca491b4062c] PROGRESS | rag_integration (70%) - 💾 Integrating historical context and patterns... | client_count=1
2025-09-29 13:40:05 | INFO     | unified.progress     | [async_4ca491b4062c] PROGRESS | final_analysis (80%) - 🎯 Generating final trading analysis... | client_count=1
2025-09-29 13:40:29 | INFO     | unified.progress     | [async_4ca491b4062c] PROGRESS | memory_storage (90%) - 💾 Storing analysis for future reference... | client_count=1
2025-09-29 13:40:29 | INFO     | unified.progress     | [async_4ca491b4062c] PROGRESS | complete (100%) - 🎉 Analysis completed successfully! | client_count=1
2025-09-29 13:40:29 | INFO     | unified.progress     | [async_4ca491b4062c] PROGRESS | complete (100%) - ✅ AI analysis workflow completed successfully! | client_count=1
2025-09-29 13:40:29 | INFO     | unified.progress     | [async_4ca491b4062c] PROGRESS | complete (100%) - ✅ AI analysis workflow completed successfully! | client_count=1
2025-09-29 13:40:30 | INFO     | unified.progress     | [async_4ca491b4062c] PROGRESS | complete (100%) - 🎉 Analysis completed successfully! | client_count=0
2025-09-29 15:08:24 | INFO     | unified.progress     | [async_a36ec6ebb42e] PROGRESS | chart_upload (5%) - Analysis request received, starting background processing... | client_count=0
2025-09-29 15:08:24 | INFO     | unified.progress     | [async_a36ec6ebb42e] PROGRESS | chart_upload (5%) - 📊 Processing uploaded chart images... | client_count=0
2025-09-29 15:08:24 | INFO     | unified.progress     | [async_a36ec6ebb42e] PROGRESS | chart_upload (5%) - 🖼️ Processed chart image 1/1 (75KB) | client_count=0
2025-09-29 15:08:24 | INFO     | unified.progress     | [async_a36ec6ebb42e] PROGRESS | replay_buffered (0%) - Replayed 3 buffered updates to new client | buffered_count=3
2025-09-29 15:08:26 | INFO     | unified.progress     | [async_a36ec6ebb42e] PROGRESS | chart_analysis (20%) - 🚀 Starting comprehensive AI analysis workflow... | client_count=1
2025-09-29 15:08:40 | INFO     | unified.progress     | [async_a36ec6ebb42e] PROGRESS | symbol_detection (40%) - ✅ Symbol detected: BTCUSD (crypto market) | client_count=1
2025-09-29 15:08:40 | INFO     | unified.progress     | [async_a36ec6ebb42e] PROGRESS | tool_execution (50%) - 🎯 Executing market data tools... | client_count=1
2025-09-29 15:08:45 | INFO     | unified.progress     | [async_a36ec6ebb42e] PROGRESS | tool_summarization (60%) - 🎯 Summarizing market data with AI... | client_count=1
2025-09-29 15:08:58 | INFO     | unified.progress     | [async_a36ec6ebb42e] PROGRESS | rag_integration (70%) - 💾 Integrating historical context and patterns... | client_count=1
2025-09-29 15:09:04 | INFO     | unified.progress     | [async_a36ec6ebb42e] PROGRESS | final_analysis (80%) - 🎯 Generating final trading analysis... | client_count=1
2025-09-29 15:09:33 | INFO     | unified.progress     | [async_a36ec6ebb42e] PROGRESS | memory_storage (90%) - 💾 Storing analysis for future reference... | client_count=1
2025-09-29 15:09:33 | INFO     | unified.progress     | [async_a36ec6ebb42e] PROGRESS | complete (100%) - 🎉 Analysis completed successfully! | client_count=1
2025-09-29 15:09:33 | INFO     | unified.progress     | [async_a36ec6ebb42e] PROGRESS | complete (100%) - ✅ AI analysis workflow completed successfully! | client_count=1
2025-09-29 15:09:33 | INFO     | unified.progress     | [async_a36ec6ebb42e] PROGRESS | complete (100%) - ✅ AI analysis workflow completed successfully! | client_count=1
2025-09-29 15:09:34 | INFO     | unified.progress     | [async_a36ec6ebb42e] PROGRESS | complete (100%) - 🎉 Analysis completed successfully! | client_count=0
2025-09-29 15:43:08 | INFO     | unified.progress     | [async_11c262c36dbb] PROGRESS | chart_upload (5%) - Analysis request received, starting background processing... | client_count=0
2025-09-29 15:43:08 | INFO     | unified.progress     | [async_11c262c36dbb] PROGRESS | chart_upload (5%) - 📊 Processing uploaded chart images... | client_count=0
2025-09-29 15:43:08 | INFO     | unified.progress     | [async_11c262c36dbb] PROGRESS | chart_upload (5%) - 🖼️ Processed chart image 1/1 (0KB) | client_count=0
2025-09-29 15:43:10 | INFO     | unified.progress     | [async_11c262c36dbb] PROGRESS | chart_analysis (20%) - 🚀 Starting comprehensive AI analysis workflow... | client_count=0
2025-09-29 15:43:10 | INFO     | unified.progress     | [async_11c262c36dbb] PROGRESS | replay_buffered (0%) - Replayed 4 buffered updates to new client | buffered_count=4
2025-09-29 15:43:13 | INFO     | unified.progress     | [async_11c262c36dbb] PROGRESS | symbol_detection (40%) - ✅ Symbol detected: N/A - No chart image provided for analysis (N/A - No chart image provided for analysis market) | client_count=1
2025-09-29 15:43:13 | INFO     | unified.progress     | [async_11c262c36dbb] PROGRESS | tool_execution (50%) - 🎯 Executing market data tools... | client_count=1
2025-09-29 15:43:17 | INFO     | unified.progress     | [async_11c262c36dbb] PROGRESS | tool_summarization (60%) - 🎯 Summarizing market data with AI... | client_count=1
2025-09-29 15:43:30 | INFO     | unified.progress     | [async_11c262c36dbb] PROGRESS | rag_integration (70%) - 💾 Integrating historical context and patterns... | client_count=1
2025-09-29 15:43:39 | INFO     | unified.progress     | [async_11c262c36dbb] PROGRESS | final_analysis (80%) - 🎯 Generating final trading analysis... | client_count=1
2025-09-29 15:44:10 | INFO     | unified.progress     | [async_11c262c36dbb] PROGRESS | memory_storage (90%) - 💾 Storing analysis for future reference... | client_count=1
2025-09-29 15:44:10 | INFO     | unified.progress     | [async_11c262c36dbb] PROGRESS | complete (100%) - 🎉 Analysis completed successfully! | client_count=1
2025-09-29 15:44:10 | INFO     | unified.progress     | [async_11c262c36dbb] PROGRESS | memory_storage (90%) - 💾 Memory storage completed | client_count=0
2025-09-29 15:44:10 | INFO     | unified.progress     | [async_11c262c36dbb] PROGRESS | complete (100%) - ✅ AI analysis workflow completed successfully! | client_count=0
2025-09-29 15:44:11 | INFO     | unified.progress     | [async_11c262c36dbb] PROGRESS | complete (100%) - ✅ AI analysis workflow completed successfully! | client_count=0
2025-09-29 15:44:11 | INFO     | unified.progress     | [async_11c262c36dbb] PROGRESS | complete (100%) - 🎉 Analysis completed successfully! | client_count=0
2025-09-29 15:57:16 | INFO     | unified.progress     | [async_0a3a571ab098] PROGRESS | chart_upload (5%) - Analysis request received, starting background processing... | client_count=0
2025-09-29 15:57:16 | INFO     | unified.progress     | [async_0a3a571ab098] PROGRESS | chart_upload (5%) - 📊 Processing uploaded chart images... | client_count=0
2025-09-29 15:57:16 | INFO     | unified.progress     | [async_0a3a571ab098] PROGRESS | chart_upload (5%) - 🖼️ Processed chart image 1/1 (3KB) | client_count=0
2025-09-29 15:57:17 | INFO     | unified.progress     | [async_0a3a571ab098] PROGRESS | chart_analysis (20%) - 📊 Chart analysis completed | client_count=0
2025-09-29 15:57:18 | INFO     | unified.progress     | [async_0a3a571ab098] PROGRESS | replay_buffered (0%) - Replayed 4 buffered updates to new client | buffered_count=4
2025-09-29 15:57:47 | INFO     | unified.progress     | [async_0a3a571ab098] PROGRESS | symbol_detection (40%) - 🔍 Symbol detection completed | client_count=1
2025-09-29 15:57:47 | INFO     | unified.progress     | [async_0a3a571ab098] PROGRESS | tool_execution (50%) - 🛠️ Data collection completed | client_count=1
2025-09-29 15:57:47 | INFO     | unified.progress     | [async_0a3a571ab098] PROGRESS | tool_summarization (60%) - 📋 Data processing completed | client_count=1
2025-09-29 15:57:47 | INFO     | unified.progress     | [async_0a3a571ab098] PROGRESS | rag_integration (70%) - 🧠 RAG integration completed | client_count=1
2025-09-29 15:57:51 | INFO     | unified.progress     | [async_0a3a571ab098] PROGRESS | final_analysis (80%) - 🎯 AI analysis completed | client_count=1
2025-09-29 15:57:51 | INFO     | unified.progress     | [async_0a3a571ab098] PROGRESS | memory_storage (90%) - 💾 Storing analysis for future reference... | client_count=1
2025-09-29 15:57:51 | INFO     | unified.progress     | [async_0a3a571ab098] PROGRESS | complete (100%) - 🎉 Analysis completed successfully! | client_count=1
2025-09-29 15:57:52 | INFO     | unified.progress     | [async_0a3a571ab098] PROGRESS | memory_storage (90%) - 💾 Memory storage completed | client_count=0
2025-09-29 15:57:52 | INFO     | unified.progress     | [async_0a3a571ab098] PROGRESS | complete (100%) - ✅ AI analysis workflow completed successfully! | client_count=0
2025-09-29 15:57:52 | INFO     | unified.progress     | [async_0a3a571ab098] PROGRESS | complete (100%) - ✅ AI analysis workflow completed successfully! | client_count=0
2025-09-29 15:58:01 | INFO     | unified.progress     | [async_0a3a571ab098] PROGRESS | complete (100%) - 🎉 Analysis completed successfully! | client_count=0
2025-09-29 15:58:48 | INFO     | unified.progress     | [async_56ff7d1c2fe1] PROGRESS | chart_upload (5%) - Analysis request received, starting background processing... | client_count=0
2025-09-29 15:58:48 | INFO     | unified.progress     | [async_56ff7d1c2fe1] PROGRESS | chart_upload (5%) - 📊 Processing uploaded chart images... | client_count=0
2025-09-29 15:58:48 | INFO     | unified.progress     | [async_56ff7d1c2fe1] PROGRESS | chart_upload (5%) - 🖼️ Processed chart image 1/1 (75KB) | client_count=0
2025-09-29 15:58:48 | INFO     | unified.progress     | [async_56ff7d1c2fe1] PROGRESS | replay_buffered (0%) - Replayed 3 buffered updates to new client | buffered_count=3
2025-09-29 15:58:49 | INFO     | unified.progress     | [async_56ff7d1c2fe1] PROGRESS | chart_analysis (20%) - 📊 Chart analysis completed | client_count=1
2025-09-29 15:59:04 | INFO     | unified.progress     | [async_56ff7d1c2fe1] PROGRESS | symbol_detection (40%) - 🔍 Symbol detection completed | client_count=1
2025-09-29 15:59:04 | INFO     | unified.progress     | [async_56ff7d1c2fe1] PROGRESS | tool_execution (50%) - 🛠️ Data collection completed | client_count=1
2025-09-29 15:59:04 | INFO     | unified.progress     | [async_56ff7d1c2fe1] PROGRESS | tool_summarization (60%) - 📋 Data processing completed | client_count=1
2025-09-29 15:59:04 | INFO     | unified.progress     | [async_56ff7d1c2fe1] PROGRESS | rag_integration (70%) - 🧠 RAG integration completed | client_count=1
2025-09-29 15:59:04 | INFO     | unified.progress     | [async_56ff7d1c2fe1] PROGRESS | final_analysis (80%) - 🎯 AI analysis completed | client_count=1
2025-09-29 15:59:04 | INFO     | unified.progress     | [async_56ff7d1c2fe1] PROGRESS | memory_storage (90%) - 💾 Storing analysis for future reference... | client_count=1
2025-09-29 15:59:04 | INFO     | unified.progress     | [async_56ff7d1c2fe1] PROGRESS | complete (100%) - 🎉 Analysis completed successfully! | client_count=1
2025-09-29 15:59:04 | INFO     | unified.progress     | [async_56ff7d1c2fe1] PROGRESS | memory_storage (90%) - 💾 Memory storage completed | client_count=0
2025-09-29 15:59:04 | INFO     | unified.progress     | [async_56ff7d1c2fe1] PROGRESS | complete (100%) - ✅ AI analysis workflow completed successfully! | client_count=0
2025-09-29 15:59:04 | INFO     | unified.progress     | [async_56ff7d1c2fe1] PROGRESS | complete (100%) - ✅ AI analysis workflow completed successfully! | client_count=0
2025-09-29 15:59:04 | INFO     | unified.progress     | [async_56ff7d1c2fe1] PROGRESS | complete (100%) - 🎉 Analysis completed successfully! | client_count=0
2025-09-29 16:11:25 | INFO     | unified.progress     | [async_c50e7081c22c] PROGRESS | chart_upload (5%) - Analysis request received, starting background processing... | client_count=0
2025-09-29 16:11:25 | INFO     | unified.progress     | [async_c50e7081c22c] PROGRESS | chart_upload (5%) - 📊 Processing uploaded chart images... | client_count=0
2025-09-29 16:11:25 | INFO     | unified.progress     | [async_c50e7081c22c] PROGRESS | chart_upload (5%) - 🖼️ Processed chart image 1/1 (75KB) | client_count=0
2025-09-29 16:11:25 | INFO     | unified.progress     | [async_c50e7081c22c] PROGRESS | replay_buffered (0%) - Replayed 3 buffered updates to new client | buffered_count=3
2025-09-29 16:11:26 | INFO     | unified.progress     | [async_c50e7081c22c] PROGRESS | chart_analysis (20%) - 📊 Chart analysis completed | client_count=1
2025-09-29 16:11:37 | INFO     | unified.progress     | [async_c50e7081c22c] PROGRESS | symbol_detection (40%) - 🔍 Symbol detection completed | client_count=1
2025-09-29 16:11:37 | INFO     | unified.progress     | [async_c50e7081c22c] PROGRESS | tool_execution (50%) - 🛠️ Data collection completed | client_count=1
2025-09-29 16:11:37 | INFO     | unified.progress     | [async_c50e7081c22c] PROGRESS | tool_summarization (60%) - 📋 Data processing completed | client_count=1
2025-09-29 16:11:37 | INFO     | unified.progress     | [async_c50e7081c22c] PROGRESS | rag_integration (70%) - 🧠 RAG integration completed | client_count=1
2025-09-29 16:11:37 | INFO     | unified.progress     | [async_c50e7081c22c] PROGRESS | final_analysis (80%) - 🎯 AI analysis completed | client_count=1
2025-09-29 16:11:37 | INFO     | unified.progress     | [async_c50e7081c22c] PROGRESS | memory_storage (90%) - 💾 Storing analysis for future reference... | client_count=1
2025-09-29 16:11:37 | INFO     | unified.progress     | [async_c50e7081c22c] PROGRESS | complete (100%) - 🎉 Analysis completed successfully! | client_count=1
2025-09-29 16:11:37 | INFO     | unified.progress     | [async_c50e7081c22c] PROGRESS | memory_storage (90%) - 💾 Memory storage completed | client_count=0
2025-09-29 16:11:37 | INFO     | unified.progress     | [async_c50e7081c22c] PROGRESS | complete (100%) - ✅ AI analysis workflow completed successfully! | client_count=0
2025-09-29 16:11:37 | INFO     | unified.progress     | [async_c50e7081c22c] PROGRESS | complete (100%) - ✅ AI analysis workflow completed successfully! | client_count=0
2025-09-29 16:11:38 | INFO     | unified.progress     | [async_c50e7081c22c] PROGRESS | complete (100%) - 🎉 Analysis completed successfully! | client_count=0
2025-09-29 16:29:37 | INFO     | unified.progress     | [async_4f169b5f15ab] PROGRESS | chart_upload (5%) - Analysis request received, starting background processing... | client_count=0
2025-09-29 16:29:37 | INFO     | unified.progress     | [async_4f169b5f15ab] PROGRESS | chart_upload (5%) - 📊 Processing uploaded chart images... | client_count=0
2025-09-29 16:29:37 | INFO     | unified.progress     | [async_4f169b5f15ab] PROGRESS | chart_upload (5%) - 🖼️ Processed chart image 1/1 (75KB) | client_count=0
2025-09-29 16:29:37 | INFO     | unified.progress     | [async_4f169b5f15ab] PROGRESS | replay_buffered (0%) - Replayed 3 buffered updates to new client | buffered_count=3
2025-09-29 16:29:39 | INFO     | unified.progress     | [async_4f169b5f15ab] PROGRESS | chart_analysis (20%) - 📊 Chart analysis completed | client_count=1
2025-09-29 16:29:55 | INFO     | unified.progress     | [async_4f169b5f15ab] PROGRESS | symbol_detection (40%) - 🔍 Symbol detection completed | client_count=1
2025-09-29 16:29:55 | INFO     | unified.progress     | [async_4f169b5f15ab] PROGRESS | tool_execution (50%) - 🛠️ Data collection completed | client_count=1
2025-09-29 16:29:55 | INFO     | unified.progress     | [async_4f169b5f15ab] PROGRESS | tool_summarization (60%) - 📋 Data processing completed | client_count=1
2025-09-29 16:29:55 | INFO     | unified.progress     | [async_4f169b5f15ab] PROGRESS | rag_integration (70%) - 🧠 RAG integration completed | client_count=1
2025-09-29 16:29:55 | INFO     | unified.progress     | [async_4f169b5f15ab] PROGRESS | final_analysis (80%) - 🎯 AI analysis completed | client_count=1
2025-09-29 16:29:55 | INFO     | unified.progress     | [async_4f169b5f15ab] PROGRESS | memory_storage (90%) - 💾 Storing analysis for future reference... | client_count=1
2025-09-29 16:29:55 | INFO     | unified.progress     | [async_4f169b5f15ab] PROGRESS | complete (100%) - 🎉 Analysis completed successfully! | client_count=1
2025-09-29 16:29:56 | INFO     | unified.progress     | [async_4f169b5f15ab] PROGRESS | memory_storage (90%) - 💾 Memory storage completed | client_count=0
2025-09-29 16:29:56 | INFO     | unified.progress     | [async_4f169b5f15ab] PROGRESS | complete (100%) - ✅ AI analysis workflow completed successfully! | client_count=0
2025-09-29 16:29:56 | INFO     | unified.progress     | [async_4f169b5f15ab] PROGRESS | complete (100%) - ✅ AI analysis workflow completed successfully! | client_count=0
2025-09-29 16:30:06 | INFO     | unified.progress     | [async_4f169b5f15ab] PROGRESS | complete (100%) - 🎉 Analysis completed successfully! | client_count=0
2025-09-29 16:50:42 | INFO     | unified.progress     | [async_98d0b880defc] PROGRESS | chart_upload (5%) - Analysis request received, starting background processing... | client_count=0
2025-09-29 16:50:43 | INFO     | unified.progress     | [async_98d0b880defc] PROGRESS | chart_upload (5%) - 📊 Processing uploaded chart images... | client_count=0
2025-09-29 16:50:43 | INFO     | unified.progress     | [async_98d0b880defc] PROGRESS | chart_upload (5%) - 🖼️ Processed chart image 1/1 (75KB) | client_count=0
2025-09-29 16:50:43 | INFO     | unified.progress     | [async_98d0b880defc] PROGRESS | replay_buffered (0%) - Replayed 3 buffered updates to new client | buffered_count=3
2025-09-29 16:50:45 | INFO     | unified.progress     | [async_98d0b880defc] PROGRESS | chart_analysis (20%) - 📊 Chart analysis completed | client_count=1
2025-09-29 16:50:57 | INFO     | unified.progress     | [async_98d0b880defc] PROGRESS | symbol_detection (40%) - 🔍 Symbol detection completed | client_count=1
2025-09-29 16:50:57 | INFO     | unified.progress     | [async_98d0b880defc] PROGRESS | tool_execution (50%) - 🛠️ Data collection completed | client_count=1
2025-09-29 16:50:58 | INFO     | unified.progress     | [async_98d0b880defc] PROGRESS | tool_summarization (60%) - 📋 Data processing completed | client_count=1
2025-09-29 16:50:59 | INFO     | unified.progress     | [async_98d0b880defc] PROGRESS | rag_integration (70%) - 🧠 RAG integration completed | client_count=1
2025-09-29 16:51:00 | INFO     | unified.progress     | [async_98d0b880defc] PROGRESS | final_analysis (80%) - 🎯 AI analysis completed | client_count=1
2025-09-29 16:51:01 | INFO     | unified.progress     | [async_98d0b880defc] PROGRESS | memory_storage (90%) - 💾 Storing analysis for future reference... | client_count=1
2025-09-29 16:51:02 | INFO     | unified.progress     | [async_98d0b880defc] PROGRESS | complete (100%) - 🎉 Analysis completed successfully! | client_count=1
2025-09-29 16:51:03 | INFO     | unified.progress     | [async_98d0b880defc] PROGRESS | memory_storage (90%) - 💾 Memory storage completed | client_count=0
2025-09-29 16:51:03 | INFO     | unified.progress     | [async_98d0b880defc] PROGRESS | complete (100%) - ✅ AI analysis workflow completed successfully! | client_count=0
2025-09-29 16:51:03 | INFO     | unified.progress     | [async_98d0b880defc] PROGRESS | complete (100%) - ✅ AI analysis workflow completed successfully! | client_count=0
