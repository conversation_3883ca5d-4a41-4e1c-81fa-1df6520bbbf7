#!/usr/bin/env python3
"""
Test Logging System
==================

This script tests if the unified logging system is working correctly
and creating the proper log files.
"""

import sys
import os
from pathlib import Path
from datetime import datetime

# Add the backend directory to the Python path
backend_dir = Path(__file__).parent / "Agent_Trading" / "backend"
sys.path.insert(0, str(backend_dir))

def test_unified_logging():
    """Test the unified logging system."""
    print("🧪 TESTING UNIFIED LOGGING SYSTEM")
    print("=" * 50)
    
    try:
        # Import the unified logging system
        from app.core.unified_logging import setup_unified_logging, get_unified_logger
        
        print("✅ Successfully imported unified logging")
        
        # Initialize the logging system
        print("🔧 Initializing unified logging...")
        unified_logger = setup_unified_logging()
        print("✅ Unified logging initialized")
        
        # Test different types of logging
        test_session_id = f"test_{int(datetime.now().timestamp())}"
        
        print(f"📝 Testing with session ID: {test_session_id}")
        
        # Test application logging
        print("1️⃣ Testing application logging...")
        unified_logger.log_application("info", "Test application log message", test_param="test_value")
        
        # Test progress logging
        print("2️⃣ Testing progress logging...")
        unified_logger.log_progress(
            session_id=test_session_id,
            step="test_step",
            progress=50,
            message="Test progress message",
            test_data="test_value"
        )
        
        # Test analysis logging
        print("3️⃣ Testing analysis logging...")
        unified_logger.log_analysis("info", "Test analysis log message", analysis_type="test")
        
        # Test API logging
        print("4️⃣ Testing API logging...")
        unified_logger.log_api(method="GET", path="/test", status_code=200, execution_time=0.123)
        
        # Test error logging
        print("5️⃣ Testing error logging...")
        test_error = Exception("Test error message")
        unified_logger.log_error(test_error, {"test_context": "test"})
        
        print("✅ All logging tests completed")
        
        # Check if log files were created
        print("\n📁 Checking log files...")
        log_base_dir = Path("Agent_Trading/backend/logs")
        
        expected_dirs = ["application", "analysis", "progress", "api", "errors"]
        created_files = []
        
        for dir_name in expected_dirs:
            dir_path = log_base_dir / dir_name
            if dir_path.exists():
                print(f"✅ Directory exists: {dir_name}")
                # List files in directory
                files = list(dir_path.rglob("*.log"))
                if files:
                    for file in files:
                        print(f"   📄 {file.relative_to(log_base_dir)}")
                        created_files.append(file)
                else:
                    print(f"   ⚠️  No log files in {dir_name}")
            else:
                print(f"❌ Directory missing: {dir_name}")
        
        # Check session-specific logs
        session_dir = log_base_dir / "progress" / "sessions"
        if session_dir.exists():
            session_files = list(session_dir.glob(f"*{test_session_id}*.log"))
            if session_files:
                print(f"✅ Session log created: {session_files[0].name}")
                created_files.append(session_files[0])
            else:
                print(f"❌ No session log found for {test_session_id}")
        else:
            print("❌ Session directory doesn't exist")
        
        print(f"\n📊 Summary: {len(created_files)} log files created")
        
        if len(created_files) >= 3:
            print("🎉 LOGGING SYSTEM TEST PASSED!")
            return True
        else:
            print("⚠️  LOGGING SYSTEM TEST PARTIALLY FAILED")
            print("Some log files were not created as expected")
            return False
            
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("Make sure you're running from the correct directory")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def test_progress_tracker():
    """Test the progress tracker logging."""
    print("\n🔧 TESTING PROGRESS TRACKER LOGGING")
    print("=" * 50)
    
    try:
        from app.helpers.workflow_management.progress_tracker import ProgressBroadcaster, WorkflowStep, ProgressUpdate
        
        print("✅ Successfully imported progress tracker")
        
        # Create a progress broadcaster
        broadcaster = ProgressBroadcaster()
        print("✅ ProgressBroadcaster created")
        
        # Create a test progress update
        test_session_id = f"progress_test_{int(datetime.now().timestamp())}"
        
        progress_update = ProgressUpdate(
            step=WorkflowStep.CHART_UPLOAD,
            progress=25,
            message="Test progress update",
            timestamp=datetime.now()
        )
        
        print(f"📝 Testing progress broadcast for session: {test_session_id}")
        
        # Test broadcasting (this should create logs)
        import asyncio
        
        async def test_broadcast():
            await broadcaster.broadcast_update(test_session_id, progress_update)
            print("✅ Progress update broadcasted")
        
        # Run the async test
        asyncio.run(test_broadcast())
        
        # Check if progress logs were created
        log_base_dir = Path("Agent_Trading/backend/logs")
        session_dir = log_base_dir / "progress" / "sessions"
        
        if session_dir.exists():
            session_files = list(session_dir.glob(f"*{test_session_id}*.log"))
            if session_files:
                print(f"✅ Progress session log created: {session_files[0].name}")
                
                # Read the log content
                with open(session_files[0], 'r') as f:
                    content = f.read()
                    if "PROGRESS" in content and "Test progress update" in content:
                        print("✅ Progress log contains expected content")
                        return True
                    else:
                        print("⚠️  Progress log doesn't contain expected content")
                        print(f"Content: {content}")
                        return False
            else:
                print(f"❌ No progress session log found for {test_session_id}")
                return False
        else:
            print("❌ Progress session directory doesn't exist")
            return False
            
    except Exception as e:
        print(f"❌ Progress tracker test error: {e}")
        return False

def main():
    """Main test function."""
    print("🧪 COMPREHENSIVE LOGGING SYSTEM TEST")
    print(f"Start time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"Working directory: {os.getcwd()}")
    
    # Test 1: Unified logging system
    logging_success = test_unified_logging()
    
    # Test 2: Progress tracker logging
    progress_success = test_progress_tracker()
    
    print(f"\n{'='*50}")
    print("📊 FINAL RESULTS:")
    print(f"   Unified Logging: {'✅ PASS' if logging_success else '❌ FAIL'}")
    print(f"   Progress Tracking: {'✅ PASS' if progress_success else '❌ FAIL'}")
    
    overall_success = logging_success and progress_success
    
    if overall_success:
        print("🎉 ALL LOGGING TESTS PASSED!")
        print("The logging system is working correctly.")
    else:
        print("⚠️  SOME LOGGING TESTS FAILED")
        print("The logging system needs attention.")
    
    return overall_success

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
