2025-09-27 01:04:42 | INFO     | [debug_RESULT] | 
============================================================
2025-09-27 01:04:42 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-27 01:04:42 | INFO     | [debug_RESULT] | ============================================================
2025-09-27 01:04:42 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_6294690c2d15
2025-09-27 01:04:42 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_6294690c2d15']
2025-09-27 01:04:42 | INFO     | [debug_RESULT] | ℹ️ Cached results: []
2025-09-27 01:04:42 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_6294690c2d15]
2025-09-27 01:04:42 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-27 01:04:45 | INFO     | [debug_RESULT] | 
============================================================
2025-09-27 01:04:45 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-27 01:04:45 | INFO     | [debug_RESULT] | ============================================================
2025-09-27 01:04:45 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_6294690c2d15
2025-09-27 01:04:45 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_6294690c2d15']
2025-09-27 01:04:45 | INFO     | [debug_RESULT] | ℹ️ Cached results: []
2025-09-27 01:04:45 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_6294690c2d15]
2025-09-27 01:04:45 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-27 01:04:51 | INFO     | [debug_RESULT] | 
============================================================
2025-09-27 01:04:51 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-27 01:04:51 | INFO     | [debug_RESULT] | ============================================================
2025-09-27 01:04:51 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_6294690c2d15
2025-09-27 01:04:51 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_6294690c2d15']
2025-09-27 01:04:51 | INFO     | [debug_RESULT] | ℹ️ Cached results: []
2025-09-27 01:04:51 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_6294690c2d15]
2025-09-27 01:04:51 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-27 01:04:55 | INFO     | [debug_RESULT] | 
============================================================
2025-09-27 01:04:55 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-27 01:04:55 | INFO     | [debug_RESULT] | ============================================================
2025-09-27 01:04:55 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_6294690c2d15
2025-09-27 01:04:55 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_6294690c2d15']
2025-09-27 01:04:55 | INFO     | [debug_RESULT] | ℹ️ Cached results: []
2025-09-27 01:04:55 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_6294690c2d15]
2025-09-27 01:04:55 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-27 01:04:58 | INFO     | [debug_RESULT] | 
============================================================
2025-09-27 01:04:58 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-27 01:04:58 | INFO     | [debug_RESULT] | ============================================================
2025-09-27 01:04:58 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_6294690c2d15
2025-09-27 01:04:58 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_6294690c2d15']
2025-09-27 01:04:58 | INFO     | [debug_RESULT] | ℹ️ Cached results: []
2025-09-27 01:04:58 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_6294690c2d15]
2025-09-27 01:04:58 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-27 01:05:01 | INFO     | [debug_RESULT] | 
============================================================
2025-09-27 01:05:01 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-27 01:05:01 | INFO     | [debug_RESULT] | ============================================================
2025-09-27 01:05:01 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_6294690c2d15
2025-09-27 01:05:01 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_6294690c2d15']
2025-09-27 01:05:01 | INFO     | [debug_RESULT] | ℹ️ Cached results: []
2025-09-27 01:05:01 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_6294690c2d15]
2025-09-27 01:05:01 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-27 01:05:04 | INFO     | [debug_RESULT] | 
============================================================
2025-09-27 01:05:04 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-27 01:05:04 | INFO     | [debug_RESULT] | ============================================================
2025-09-27 01:05:04 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_6294690c2d15
2025-09-27 01:05:04 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_6294690c2d15']
2025-09-27 01:05:04 | INFO     | [debug_RESULT] | ℹ️ Cached results: []
2025-09-27 01:05:04 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_6294690c2d15]
2025-09-27 01:05:04 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-27 01:05:07 | INFO     | [debug_RESULT] | 
============================================================
2025-09-27 01:05:07 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-27 01:05:07 | INFO     | [debug_RESULT] | ============================================================
2025-09-27 01:05:07 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_6294690c2d15
2025-09-27 01:05:07 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_6294690c2d15']
2025-09-27 01:05:07 | INFO     | [debug_RESULT] | ℹ️ Cached results: []
2025-09-27 01:05:07 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_6294690c2d15]
2025-09-27 01:05:07 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-27 01:05:10 | INFO     | [debug_RESULT] | 
============================================================
2025-09-27 01:05:10 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-27 01:05:10 | INFO     | [debug_RESULT] | ============================================================
2025-09-27 01:05:10 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_6294690c2d15
2025-09-27 01:05:10 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_6294690c2d15']
2025-09-27 01:05:10 | INFO     | [debug_RESULT] | ℹ️ Cached results: []
2025-09-27 01:05:10 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_6294690c2d15]
2025-09-27 01:05:10 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-27 01:05:13 | INFO     | [debug_RESULT] | 
============================================================
2025-09-27 01:05:13 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-27 01:05:13 | INFO     | [debug_RESULT] | ============================================================
2025-09-27 01:05:13 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_6294690c2d15
2025-09-27 01:05:13 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_6294690c2d15']
2025-09-27 01:05:13 | INFO     | [debug_RESULT] | ℹ️ Cached results: []
2025-09-27 01:05:13 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_6294690c2d15]
2025-09-27 01:05:13 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-27 01:05:18 | INFO     | [debug_RESULT] | 
============================================================
2025-09-27 01:05:18 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-27 01:05:18 | INFO     | [debug_RESULT] | ============================================================
2025-09-27 01:05:18 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_6294690c2d15
2025-09-27 01:05:18 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_6294690c2d15']
2025-09-27 01:05:18 | INFO     | [debug_RESULT] | ℹ️ Cached results: []
2025-09-27 01:05:18 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_6294690c2d15]
2025-09-27 01:05:18 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-27 01:05:22 | INFO     | [debug_RESULT] | 
============================================================
2025-09-27 01:05:22 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-27 01:05:22 | INFO     | [debug_RESULT] | ============================================================
2025-09-27 01:05:22 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_6294690c2d15
2025-09-27 01:05:22 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_6294690c2d15']
2025-09-27 01:05:22 | INFO     | [debug_RESULT] | ℹ️ Cached results: []
2025-09-27 01:05:22 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_6294690c2d15]
2025-09-27 01:05:22 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-27 01:05:25 | INFO     | [debug_RESULT] | 
============================================================
2025-09-27 01:05:25 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-27 01:05:25 | INFO     | [debug_RESULT] | ============================================================
2025-09-27 01:05:25 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_6294690c2d15
2025-09-27 01:05:25 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_6294690c2d15']
2025-09-27 01:05:25 | INFO     | [debug_RESULT] | ℹ️ Cached results: []
2025-09-27 01:05:25 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_6294690c2d15]
2025-09-27 01:05:25 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-27 01:05:28 | INFO     | [debug_RESULT] | 
============================================================
2025-09-27 01:05:28 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-27 01:05:28 | INFO     | [debug_RESULT] | ============================================================
2025-09-27 01:05:28 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_6294690c2d15
2025-09-27 01:05:28 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_6294690c2d15']
2025-09-27 01:05:28 | INFO     | [debug_RESULT] | ℹ️ Cached results: []
2025-09-27 01:05:28 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_6294690c2d15]
2025-09-27 01:05:28 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-27 01:05:31 | INFO     | [debug_RESULT] | 
============================================================
2025-09-27 01:05:31 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-27 01:05:31 | INFO     | [debug_RESULT] | ============================================================
2025-09-27 01:05:31 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_6294690c2d15
2025-09-27 01:05:31 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_6294690c2d15']
2025-09-27 01:05:31 | INFO     | [debug_RESULT] | ℹ️ Cached results: []
2025-09-27 01:05:31 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_6294690c2d15]
2025-09-27 01:05:31 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-27 01:05:34 | INFO     | [debug_RESULT] | 
============================================================
2025-09-27 01:05:34 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-27 01:05:34 | INFO     | [debug_RESULT] | ============================================================
2025-09-27 01:05:34 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_6294690c2d15
2025-09-27 01:05:34 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_6294690c2d15']
2025-09-27 01:05:34 | INFO     | [debug_RESULT] | ℹ️ Cached results: []
2025-09-27 01:05:34 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_6294690c2d15]
2025-09-27 01:05:34 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-27 01:05:37 | INFO     | [debug_RESULT] | 
============================================================
2025-09-27 01:05:37 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-27 01:05:37 | INFO     | [debug_RESULT] | ============================================================
2025-09-27 01:05:37 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_6294690c2d15
2025-09-27 01:05:37 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_6294690c2d15']
2025-09-27 01:05:37 | INFO     | [debug_RESULT] | ℹ️ Cached results: []
2025-09-27 01:05:37 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_6294690c2d15]
2025-09-27 01:05:37 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-27 01:05:40 | INFO     | [debug_RESULT] | 
============================================================
2025-09-27 01:05:40 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-27 01:05:40 | INFO     | [debug_RESULT] | ============================================================
2025-09-27 01:05:40 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_6294690c2d15
2025-09-27 01:05:40 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_6294690c2d15']
2025-09-27 01:05:40 | INFO     | [debug_RESULT] | ℹ️ Cached results: []
2025-09-27 01:05:40 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_6294690c2d15]
2025-09-27 01:05:40 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-27 01:05:43 | INFO     | [debug_RESULT] | 
============================================================
2025-09-27 01:05:43 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-27 01:05:43 | INFO     | [debug_RESULT] | ============================================================
2025-09-27 01:05:43 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_6294690c2d15
2025-09-27 01:05:43 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_6294690c2d15']
2025-09-27 01:05:43 | INFO     | [debug_RESULT] | ℹ️ Cached results: []
2025-09-27 01:05:43 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_6294690c2d15]
2025-09-27 01:05:43 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-27 01:05:48 | INFO     | [debug_RESULT] | 
============================================================
2025-09-27 01:05:48 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-27 01:05:48 | INFO     | [debug_RESULT] | ============================================================
2025-09-27 01:05:48 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_6294690c2d15
2025-09-27 01:05:48 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_6294690c2d15']
2025-09-27 01:05:48 | INFO     | [debug_RESULT] | ℹ️ Cached results: []
2025-09-27 01:05:48 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_6294690c2d15]
2025-09-27 01:05:48 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-27 01:05:52 | INFO     | [debug_RESULT] | 
============================================================
2025-09-27 01:05:52 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-27 01:05:52 | INFO     | [debug_RESULT] | ============================================================
2025-09-27 01:05:52 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_6294690c2d15
2025-09-27 01:05:52 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_6294690c2d15']
2025-09-27 01:05:52 | INFO     | [debug_RESULT] | ℹ️ Cached results: []
2025-09-27 01:05:52 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_6294690c2d15]
2025-09-27 01:05:52 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-27 01:05:55 | INFO     | [debug_RESULT] | 
============================================================
2025-09-27 01:05:55 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-27 01:05:55 | INFO     | [debug_RESULT] | ============================================================
2025-09-27 01:05:55 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_6294690c2d15
2025-09-27 01:05:55 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_6294690c2d15']
2025-09-27 01:05:55 | INFO     | [debug_RESULT] | ℹ️ Cached results: []
2025-09-27 01:05:55 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_6294690c2d15]
2025-09-27 01:05:55 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-27 01:05:58 | INFO     | [debug_RESULT] | 
============================================================
2025-09-27 01:05:58 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-27 01:05:58 | INFO     | [debug_RESULT] | ============================================================
2025-09-27 01:05:58 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_6294690c2d15
2025-09-27 01:05:58 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_6294690c2d15']
2025-09-27 01:05:58 | INFO     | [debug_RESULT] | ℹ️ Cached results: []
2025-09-27 01:05:58 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_6294690c2d15]
2025-09-27 01:05:58 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-27 01:06:02 | INFO     | [debug_RESULT] | 
============================================================
2025-09-27 01:06:02 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-27 01:06:02 | INFO     | [debug_RESULT] | ============================================================
2025-09-27 01:06:02 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_6294690c2d15
2025-09-27 01:06:02 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_6294690c2d15']
2025-09-27 01:06:02 | INFO     | [debug_RESULT] | ℹ️ Cached results: []
2025-09-27 01:06:02 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_6294690c2d15]
2025-09-27 01:06:02 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-27 01:06:04 | INFO     | [debug_RESULT] | 💾 RESULT STORED [async_6294690c2d15]
2025-09-27 01:06:04 | INFO     | [debug_RESULT] |    └─ Keys: ['success', 'data', 'metadata', 'session_id']
2025-09-27 01:06:04 | INFO     | [debug_RESULT] |    └─ Data Size: 14862 bytes
2025-09-27 01:06:04 | INFO     | [debug_RESULT] |    └─ Storage Time: 2025-09-27T01:06:04.827430
2025-09-27 01:06:04 | INFO     | [debug_RESULT] | 🔍 DATA STRUCTURE [async_6294690c2d15] STORED_RESULT: ['success', 'data', 'metadata', 'session_id']
2025-09-27 01:06:04 | INFO     | [debug_RESULT] |     └─ success: bool
2025-09-27 01:06:04 | INFO     | [debug_RESULT] |     └─ data: dict with keys ['success', 'analysis_id', 'data', 'execution_time', 'metadata']
2025-09-27 01:06:04 | INFO     | [debug_RESULT] |     └─ metadata: dict with keys ['execution_time', 'user_id', 'analysis_type', 'market_specialization', 'image_count', 'model_used']
2025-09-27 01:06:04 | INFO     | [debug_RESULT] |     └─ session_id: str
2025-09-27 01:06:05 | INFO     | [debug_RESULT] | 
============================================================
2025-09-27 01:06:05 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-27 01:06:05 | INFO     | [debug_RESULT] | ============================================================
2025-09-27 01:06:05 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_6294690c2d15
2025-09-27 01:06:05 | INFO     | [debug_RESULT] | ℹ️ Active tasks: []
2025-09-27 01:06:05 | INFO     | [debug_RESULT] | ℹ️ Cached results: ['async_6294690c2d15']
2025-09-27 01:06:05 | INFO     | [debug_RESULT] | ✅ RESULT FOUND [async_6294690c2d15]: keys=['success', 'data', 'metadata', 'session_id']
2025-09-27 01:06:05 | INFO     | [debug_RESULT] | 🔍 DATA STRUCTURE [async_6294690c2d15] RETRIEVED_RESULT: ['success', 'data', 'metadata', 'session_id']
2025-09-27 01:06:05 | INFO     | [debug_RESULT] |     └─ success: bool
2025-09-27 01:06:05 | INFO     | [debug_RESULT] |     └─ data: dict with keys ['success', 'analysis_id', 'data', 'execution_time', 'metadata']
2025-09-27 01:06:05 | INFO     | [debug_RESULT] |     └─ metadata: dict with keys ['execution_time', 'user_id', 'analysis_type', 'market_specialization', 'image_count', 'model_used']
2025-09-27 01:06:05 | INFO     | [debug_RESULT] |     └─ session_id: str
2025-09-27 01:06:05 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=completed, has_data=True
2025-09-27 01:06:05 | INFO     | [debug_RESULT] | ℹ️ Response data keys: ['success', 'analysis_id', 'data', 'execution_time', 'metadata']
2025-09-27 01:19:01 | INFO     | [debug_RESULT] | 
============================================================
2025-09-27 01:19:01 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-27 01:19:01 | INFO     | [debug_RESULT] | ============================================================
2025-09-27 01:19:01 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_51c29cc92f71
2025-09-27 01:19:01 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_51c29cc92f71']
2025-09-27 01:19:01 | INFO     | [debug_RESULT] | ℹ️ Cached results: ['async_6294690c2d15']
2025-09-27 01:19:01 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_51c29cc92f71]
2025-09-27 01:19:01 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-27 01:19:04 | INFO     | [debug_RESULT] | 
============================================================
2025-09-27 01:19:04 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-27 01:19:04 | INFO     | [debug_RESULT] | ============================================================
2025-09-27 01:19:04 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_51c29cc92f71
2025-09-27 01:19:04 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_51c29cc92f71']
2025-09-27 01:19:04 | INFO     | [debug_RESULT] | ℹ️ Cached results: ['async_6294690c2d15']
2025-09-27 01:19:04 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_51c29cc92f71]
2025-09-27 01:19:04 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-27 01:19:07 | INFO     | [debug_RESULT] | 
============================================================
2025-09-27 01:19:07 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-27 01:19:07 | INFO     | [debug_RESULT] | ============================================================
2025-09-27 01:19:07 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_51c29cc92f71
2025-09-27 01:19:07 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_51c29cc92f71']
2025-09-27 01:19:07 | INFO     | [debug_RESULT] | ℹ️ Cached results: ['async_6294690c2d15']
2025-09-27 01:19:07 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_51c29cc92f71]
2025-09-27 01:19:07 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-27 01:19:11 | INFO     | [debug_RESULT] | 
============================================================
2025-09-27 01:19:11 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-27 01:19:11 | INFO     | [debug_RESULT] | ============================================================
2025-09-27 01:19:11 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_51c29cc92f71
2025-09-27 01:19:11 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_51c29cc92f71']
2025-09-27 01:19:11 | INFO     | [debug_RESULT] | ℹ️ Cached results: ['async_6294690c2d15']
2025-09-27 01:19:11 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_51c29cc92f71]
2025-09-27 01:19:11 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-27 01:19:14 | INFO     | [debug_RESULT] | 
============================================================
2025-09-27 01:19:14 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-27 01:19:14 | INFO     | [debug_RESULT] | ============================================================
2025-09-27 01:19:14 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_51c29cc92f71
2025-09-27 01:19:14 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_51c29cc92f71']
2025-09-27 01:19:14 | INFO     | [debug_RESULT] | ℹ️ Cached results: ['async_6294690c2d15']
2025-09-27 01:19:14 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_51c29cc92f71]
2025-09-27 01:19:14 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-27 01:19:26 | INFO     | [debug_RESULT] | 
============================================================
2025-09-27 01:19:26 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-27 01:19:26 | INFO     | [debug_RESULT] | ============================================================
2025-09-27 01:19:26 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_51c29cc92f71
2025-09-27 01:19:26 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_51c29cc92f71']
2025-09-27 01:19:26 | INFO     | [debug_RESULT] | ℹ️ Cached results: ['async_6294690c2d15']
2025-09-27 01:19:26 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_51c29cc92f71]
2025-09-27 01:19:26 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-27 01:19:29 | INFO     | [debug_RESULT] | 
============================================================
2025-09-27 01:19:29 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-27 01:19:29 | INFO     | [debug_RESULT] | ============================================================
2025-09-27 01:19:29 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_51c29cc92f71
2025-09-27 01:19:29 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_51c29cc92f71']
2025-09-27 01:19:29 | INFO     | [debug_RESULT] | ℹ️ Cached results: ['async_6294690c2d15']
2025-09-27 01:19:29 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_51c29cc92f71]
2025-09-27 01:19:29 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-27 01:19:33 | INFO     | [debug_RESULT] | 
============================================================
2025-09-27 01:19:33 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-27 01:19:33 | INFO     | [debug_RESULT] | ============================================================
2025-09-27 01:19:33 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_51c29cc92f71
2025-09-27 01:19:33 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_51c29cc92f71']
2025-09-27 01:19:33 | INFO     | [debug_RESULT] | ℹ️ Cached results: ['async_6294690c2d15']
2025-09-27 01:19:33 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_51c29cc92f71]
2025-09-27 01:19:33 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-27 01:19:36 | INFO     | [debug_RESULT] | 
============================================================
2025-09-27 01:19:36 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-27 01:19:36 | INFO     | [debug_RESULT] | ============================================================
2025-09-27 01:19:36 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_51c29cc92f71
2025-09-27 01:19:36 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_51c29cc92f71']
2025-09-27 01:19:36 | INFO     | [debug_RESULT] | ℹ️ Cached results: ['async_6294690c2d15']
2025-09-27 01:19:36 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_51c29cc92f71]
2025-09-27 01:19:36 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-27 01:19:39 | INFO     | [debug_RESULT] | 
============================================================
2025-09-27 01:19:39 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-27 01:19:39 | INFO     | [debug_RESULT] | ============================================================
2025-09-27 01:19:39 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_51c29cc92f71
2025-09-27 01:19:39 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_51c29cc92f71']
2025-09-27 01:19:39 | INFO     | [debug_RESULT] | ℹ️ Cached results: ['async_6294690c2d15']
2025-09-27 01:19:39 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_51c29cc92f71]
2025-09-27 01:19:39 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-27 01:19:42 | INFO     | [debug_RESULT] | 
============================================================
2025-09-27 01:19:42 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-27 01:19:42 | INFO     | [debug_RESULT] | ============================================================
2025-09-27 01:19:42 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_51c29cc92f71
2025-09-27 01:19:42 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_51c29cc92f71']
2025-09-27 01:19:42 | INFO     | [debug_RESULT] | ℹ️ Cached results: ['async_6294690c2d15']
2025-09-27 01:19:42 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_51c29cc92f71]
2025-09-27 01:19:42 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-27 01:19:48 | INFO     | [debug_RESULT] | 
============================================================
2025-09-27 01:19:48 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-27 01:19:48 | INFO     | [debug_RESULT] | ============================================================
2025-09-27 01:19:48 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_51c29cc92f71
2025-09-27 01:19:48 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_51c29cc92f71']
2025-09-27 01:19:48 | INFO     | [debug_RESULT] | ℹ️ Cached results: ['async_6294690c2d15']
2025-09-27 01:19:48 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_51c29cc92f71]
2025-09-27 01:19:48 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-27 01:19:54 | INFO     | [debug_RESULT] | 
============================================================
2025-09-27 01:19:54 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-27 01:19:54 | INFO     | [debug_RESULT] | ============================================================
2025-09-27 01:19:54 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_51c29cc92f71
2025-09-27 01:19:54 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_51c29cc92f71']
2025-09-27 01:19:54 | INFO     | [debug_RESULT] | ℹ️ Cached results: ['async_6294690c2d15']
2025-09-27 01:19:54 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_51c29cc92f71]
2025-09-27 01:19:54 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-27 01:19:57 | INFO     | [debug_RESULT] | 
============================================================
2025-09-27 01:19:57 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-27 01:19:57 | INFO     | [debug_RESULT] | ============================================================
2025-09-27 01:19:57 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_51c29cc92f71
2025-09-27 01:19:57 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_51c29cc92f71']
2025-09-27 01:19:57 | INFO     | [debug_RESULT] | ℹ️ Cached results: ['async_6294690c2d15']
2025-09-27 01:19:57 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_51c29cc92f71]
2025-09-27 01:19:57 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-27 01:20:02 | INFO     | [debug_RESULT] | 
============================================================
2025-09-27 01:20:02 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-27 01:20:02 | INFO     | [debug_RESULT] | ============================================================
2025-09-27 01:20:02 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_51c29cc92f71
2025-09-27 01:20:02 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_51c29cc92f71']
2025-09-27 01:20:02 | INFO     | [debug_RESULT] | ℹ️ Cached results: ['async_6294690c2d15']
2025-09-27 01:20:02 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_51c29cc92f71]
2025-09-27 01:20:02 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-27 01:20:05 | INFO     | [debug_RESULT] | 
============================================================
2025-09-27 01:20:05 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-27 01:20:05 | INFO     | [debug_RESULT] | ============================================================
2025-09-27 01:20:05 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_51c29cc92f71
2025-09-27 01:20:05 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_51c29cc92f71']
2025-09-27 01:20:05 | INFO     | [debug_RESULT] | ℹ️ Cached results: ['async_6294690c2d15']
2025-09-27 01:20:05 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_51c29cc92f71]
2025-09-27 01:20:05 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-27 01:20:08 | INFO     | [debug_RESULT] | 
============================================================
2025-09-27 01:20:08 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-27 01:20:08 | INFO     | [debug_RESULT] | ============================================================
2025-09-27 01:20:08 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_51c29cc92f71
2025-09-27 01:20:08 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_51c29cc92f71']
2025-09-27 01:20:08 | INFO     | [debug_RESULT] | ℹ️ Cached results: ['async_6294690c2d15']
2025-09-27 01:20:08 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_51c29cc92f71]
2025-09-27 01:20:08 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-27 01:20:11 | INFO     | [debug_RESULT] | 
============================================================
2025-09-27 01:20:11 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-27 01:20:11 | INFO     | [debug_RESULT] | ============================================================
2025-09-27 01:20:11 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_51c29cc92f71
2025-09-27 01:20:11 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_51c29cc92f71']
2025-09-27 01:20:11 | INFO     | [debug_RESULT] | ℹ️ Cached results: ['async_6294690c2d15']
2025-09-27 01:20:11 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_51c29cc92f71]
2025-09-27 01:20:11 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-27 01:20:14 | INFO     | [debug_RESULT] | 
============================================================
2025-09-27 01:20:14 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-27 01:20:14 | INFO     | [debug_RESULT] | ============================================================
2025-09-27 01:20:14 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_51c29cc92f71
2025-09-27 01:20:14 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_51c29cc92f71']
2025-09-27 01:20:14 | INFO     | [debug_RESULT] | ℹ️ Cached results: ['async_6294690c2d15']
2025-09-27 01:20:14 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_51c29cc92f71]
2025-09-27 01:20:14 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-27 01:20:18 | INFO     | [debug_RESULT] | 
============================================================
2025-09-27 01:20:18 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-27 01:20:18 | INFO     | [debug_RESULT] | ============================================================
2025-09-27 01:20:18 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_51c29cc92f71
2025-09-27 01:20:18 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_51c29cc92f71']
2025-09-27 01:20:18 | INFO     | [debug_RESULT] | ℹ️ Cached results: ['async_6294690c2d15']
2025-09-27 01:20:18 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_51c29cc92f71]
2025-09-27 01:20:18 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-27 01:20:21 | INFO     | [debug_RESULT] | 
============================================================
2025-09-27 01:20:21 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-27 01:20:21 | INFO     | [debug_RESULT] | ============================================================
2025-09-27 01:20:21 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_51c29cc92f71
2025-09-27 01:20:21 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_51c29cc92f71']
2025-09-27 01:20:21 | INFO     | [debug_RESULT] | ℹ️ Cached results: ['async_6294690c2d15']
2025-09-27 01:20:21 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_51c29cc92f71]
2025-09-27 01:20:21 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-27 01:20:24 | INFO     | [debug_RESULT] | 
============================================================
2025-09-27 01:20:24 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-27 01:20:24 | INFO     | [debug_RESULT] | ============================================================
2025-09-27 01:20:24 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_51c29cc92f71
2025-09-27 01:20:24 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_51c29cc92f71']
2025-09-27 01:20:24 | INFO     | [debug_RESULT] | ℹ️ Cached results: ['async_6294690c2d15']
2025-09-27 01:20:24 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_51c29cc92f71]
2025-09-27 01:20:24 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-27 01:20:27 | INFO     | [debug_RESULT] | 
============================================================
2025-09-27 01:20:27 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-27 01:20:27 | INFO     | [debug_RESULT] | ============================================================
2025-09-27 01:20:27 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_51c29cc92f71
2025-09-27 01:20:27 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_51c29cc92f71']
2025-09-27 01:20:27 | INFO     | [debug_RESULT] | ℹ️ Cached results: ['async_6294690c2d15']
2025-09-27 01:20:27 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_51c29cc92f71]
2025-09-27 01:20:27 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-27 01:20:30 | INFO     | [debug_RESULT] | 
============================================================
2025-09-27 01:20:30 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-27 01:20:30 | INFO     | [debug_RESULT] | ============================================================
2025-09-27 01:20:30 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_51c29cc92f71
2025-09-27 01:20:30 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_51c29cc92f71']
2025-09-27 01:20:30 | INFO     | [debug_RESULT] | ℹ️ Cached results: ['async_6294690c2d15']
2025-09-27 01:20:30 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_51c29cc92f71]
2025-09-27 01:20:30 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-27 01:20:32 | INFO     | [debug_RESULT] | 💾 RESULT STORED [async_51c29cc92f71]
2025-09-27 01:20:32 | INFO     | [debug_RESULT] |    └─ Keys: ['success', 'data', 'metadata', 'session_id']
2025-09-27 01:20:32 | INFO     | [debug_RESULT] |    └─ Data Size: 15407 bytes
2025-09-27 01:20:32 | INFO     | [debug_RESULT] |    └─ Storage Time: 2025-09-27T01:20:32.230443
2025-09-27 01:20:32 | INFO     | [debug_RESULT] | 🔍 DATA STRUCTURE [async_51c29cc92f71] STORED_RESULT: ['success', 'data', 'metadata', 'session_id']
2025-09-27 01:20:32 | INFO     | [debug_RESULT] |     └─ success: bool
2025-09-27 01:20:32 | INFO     | [debug_RESULT] |     └─ data: dict with keys ['success', 'analysis_id', 'data', 'execution_time', 'metadata']
2025-09-27 01:20:32 | INFO     | [debug_RESULT] |     └─ metadata: dict with keys ['execution_time', 'user_id', 'analysis_type', 'market_specialization', 'image_count', 'model_used']
2025-09-27 01:20:32 | INFO     | [debug_RESULT] |     └─ session_id: str
2025-09-27 01:20:33 | INFO     | [debug_RESULT] | 
============================================================
2025-09-27 01:20:33 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-27 01:20:33 | INFO     | [debug_RESULT] | ============================================================
2025-09-27 01:20:33 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_51c29cc92f71
2025-09-27 01:20:33 | INFO     | [debug_RESULT] | ℹ️ Active tasks: []
2025-09-27 01:20:33 | INFO     | [debug_RESULT] | ℹ️ Cached results: ['async_6294690c2d15', 'async_51c29cc92f71']
2025-09-27 01:20:33 | INFO     | [debug_RESULT] | ✅ RESULT FOUND [async_51c29cc92f71]: keys=['success', 'data', 'metadata', 'session_id']
2025-09-27 01:20:33 | INFO     | [debug_RESULT] | 🔍 DATA STRUCTURE [async_51c29cc92f71] RETRIEVED_RESULT: ['success', 'data', 'metadata', 'session_id']
2025-09-27 01:20:33 | INFO     | [debug_RESULT] |     └─ success: bool
2025-09-27 01:20:33 | INFO     | [debug_RESULT] |     └─ data: dict with keys ['success', 'analysis_id', 'data', 'execution_time', 'metadata']
2025-09-27 01:20:33 | INFO     | [debug_RESULT] |     └─ metadata: dict with keys ['execution_time', 'user_id', 'analysis_type', 'market_specialization', 'image_count', 'model_used']
2025-09-27 01:20:33 | INFO     | [debug_RESULT] |     └─ session_id: str
2025-09-27 01:20:33 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=completed, has_data=True
2025-09-27 01:20:33 | INFO     | [debug_RESULT] | ℹ️ Response data keys: ['success', 'analysis_id', 'data', 'execution_time', 'metadata']
2025-09-27 01:34:37 | INFO     | [debug_RESULT] | 
============================================================
2025-09-27 01:34:37 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-27 01:34:37 | INFO     | [debug_RESULT] | ============================================================
2025-09-27 01:34:37 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_ae42a7a505c8
2025-09-27 01:34:37 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_ae42a7a505c8']
2025-09-27 01:34:37 | INFO     | [debug_RESULT] | ℹ️ Cached results: ['async_6294690c2d15', 'async_51c29cc92f71']
2025-09-27 01:34:37 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_ae42a7a505c8]
2025-09-27 01:34:37 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-27 01:34:40 | INFO     | [debug_RESULT] | 
============================================================
2025-09-27 01:34:40 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-27 01:34:40 | INFO     | [debug_RESULT] | ============================================================
2025-09-27 01:34:40 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_ae42a7a505c8
2025-09-27 01:34:40 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_ae42a7a505c8']
2025-09-27 01:34:40 | INFO     | [debug_RESULT] | ℹ️ Cached results: ['async_6294690c2d15', 'async_51c29cc92f71']
2025-09-27 01:34:40 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_ae42a7a505c8]
2025-09-27 01:34:40 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-27 01:34:44 | INFO     | [debug_RESULT] | 
============================================================
2025-09-27 01:34:44 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-27 01:34:44 | INFO     | [debug_RESULT] | ============================================================
2025-09-27 01:34:44 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_ae42a7a505c8
2025-09-27 01:34:44 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_ae42a7a505c8']
2025-09-27 01:34:44 | INFO     | [debug_RESULT] | ℹ️ Cached results: ['async_6294690c2d15', 'async_51c29cc92f71']
2025-09-27 01:34:44 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_ae42a7a505c8]
2025-09-27 01:34:44 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-27 01:34:48 | INFO     | [debug_RESULT] | 
============================================================
2025-09-27 01:34:48 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-27 01:34:48 | INFO     | [debug_RESULT] | ============================================================
2025-09-27 01:34:48 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_ae42a7a505c8
2025-09-27 01:34:48 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_ae42a7a505c8']
2025-09-27 01:34:48 | INFO     | [debug_RESULT] | ℹ️ Cached results: ['async_6294690c2d15', 'async_51c29cc92f71']
2025-09-27 01:34:48 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_ae42a7a505c8]
2025-09-27 01:34:48 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-27 01:34:53 | INFO     | [debug_RESULT] | 
============================================================
2025-09-27 01:34:53 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-27 01:34:53 | INFO     | [debug_RESULT] | ============================================================
2025-09-27 01:34:53 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_ae42a7a505c8
2025-09-27 01:34:53 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_ae42a7a505c8']
2025-09-27 01:34:53 | INFO     | [debug_RESULT] | ℹ️ Cached results: ['async_6294690c2d15', 'async_51c29cc92f71']
2025-09-27 01:34:53 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_ae42a7a505c8]
2025-09-27 01:34:53 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-27 01:34:59 | INFO     | [debug_RESULT] | 
============================================================
2025-09-27 01:34:59 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-27 01:34:59 | INFO     | [debug_RESULT] | ============================================================
2025-09-27 01:34:59 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_ae42a7a505c8
2025-09-27 01:34:59 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_ae42a7a505c8']
2025-09-27 01:34:59 | INFO     | [debug_RESULT] | ℹ️ Cached results: ['async_6294690c2d15', 'async_51c29cc92f71']
2025-09-27 01:34:59 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_ae42a7a505c8]
2025-09-27 01:34:59 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-27 01:35:03 | INFO     | [debug_RESULT] | 
============================================================
2025-09-27 01:35:03 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-27 01:35:03 | INFO     | [debug_RESULT] | ============================================================
2025-09-27 01:35:03 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_ae42a7a505c8
2025-09-27 01:35:03 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_ae42a7a505c8']
2025-09-27 01:35:03 | INFO     | [debug_RESULT] | ℹ️ Cached results: ['async_6294690c2d15', 'async_51c29cc92f71']
2025-09-27 01:35:03 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_ae42a7a505c8]
2025-09-27 01:35:03 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-27 01:35:07 | INFO     | [debug_RESULT] | 
============================================================
2025-09-27 01:35:07 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-27 01:35:07 | INFO     | [debug_RESULT] | ============================================================
2025-09-27 01:35:07 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_ae42a7a505c8
2025-09-27 01:35:07 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_ae42a7a505c8']
2025-09-27 01:35:07 | INFO     | [debug_RESULT] | ℹ️ Cached results: ['async_6294690c2d15', 'async_51c29cc92f71']
2025-09-27 01:35:07 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_ae42a7a505c8]
2025-09-27 01:35:07 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-27 01:35:11 | INFO     | [debug_RESULT] | 
============================================================
2025-09-27 01:35:11 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-27 01:35:11 | INFO     | [debug_RESULT] | ============================================================
2025-09-27 01:35:11 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_ae42a7a505c8
2025-09-27 01:35:11 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_ae42a7a505c8']
2025-09-27 01:35:11 | INFO     | [debug_RESULT] | ℹ️ Cached results: ['async_6294690c2d15', 'async_51c29cc92f71']
2025-09-27 01:35:11 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_ae42a7a505c8]
2025-09-27 01:35:11 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-27 01:35:15 | INFO     | [debug_RESULT] | 
============================================================
2025-09-27 01:35:15 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-27 01:35:15 | INFO     | [debug_RESULT] | ============================================================
2025-09-27 01:35:15 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_ae42a7a505c8
2025-09-27 01:35:15 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_ae42a7a505c8']
2025-09-27 01:35:15 | INFO     | [debug_RESULT] | ℹ️ Cached results: ['async_6294690c2d15', 'async_51c29cc92f71']
2025-09-27 01:35:15 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_ae42a7a505c8]
2025-09-27 01:35:15 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-27 01:35:19 | INFO     | [debug_RESULT] | 
============================================================
2025-09-27 01:35:19 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-27 01:35:19 | INFO     | [debug_RESULT] | ============================================================
2025-09-27 01:35:19 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_ae42a7a505c8
2025-09-27 01:35:19 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_ae42a7a505c8']
2025-09-27 01:35:19 | INFO     | [debug_RESULT] | ℹ️ Cached results: ['async_6294690c2d15', 'async_51c29cc92f71']
2025-09-27 01:35:19 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_ae42a7a505c8]
2025-09-27 01:35:19 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-27 01:35:26 | INFO     | [debug_RESULT] | 
============================================================
2025-09-27 01:35:26 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-27 01:35:26 | INFO     | [debug_RESULT] | ============================================================
2025-09-27 01:35:26 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_ae42a7a505c8
2025-09-27 01:35:26 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_ae42a7a505c8']
2025-09-27 01:35:26 | INFO     | [debug_RESULT] | ℹ️ Cached results: ['async_6294690c2d15', 'async_51c29cc92f71']
2025-09-27 01:35:26 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_ae42a7a505c8]
2025-09-27 01:35:26 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-27 01:35:30 | INFO     | [debug_RESULT] | 
============================================================
2025-09-27 01:35:30 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-27 01:35:30 | INFO     | [debug_RESULT] | ============================================================
2025-09-27 01:35:30 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_ae42a7a505c8
2025-09-27 01:35:30 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_ae42a7a505c8']
2025-09-27 01:35:30 | INFO     | [debug_RESULT] | ℹ️ Cached results: ['async_6294690c2d15', 'async_51c29cc92f71']
2025-09-27 01:35:30 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_ae42a7a505c8]
2025-09-27 01:35:30 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-27 01:35:34 | INFO     | [debug_RESULT] | 
============================================================
2025-09-27 01:35:34 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-27 01:35:34 | INFO     | [debug_RESULT] | ============================================================
2025-09-27 01:35:34 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_ae42a7a505c8
2025-09-27 01:35:34 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_ae42a7a505c8']
2025-09-27 01:35:34 | INFO     | [debug_RESULT] | ℹ️ Cached results: ['async_6294690c2d15', 'async_51c29cc92f71']
2025-09-27 01:35:34 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_ae42a7a505c8]
2025-09-27 01:35:34 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-27 01:35:38 | INFO     | [debug_RESULT] | 
============================================================
2025-09-27 01:35:38 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-27 01:35:38 | INFO     | [debug_RESULT] | ============================================================
2025-09-27 01:35:38 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_ae42a7a505c8
2025-09-27 01:35:38 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_ae42a7a505c8']
2025-09-27 01:35:38 | INFO     | [debug_RESULT] | ℹ️ Cached results: ['async_6294690c2d15', 'async_51c29cc92f71']
2025-09-27 01:35:38 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_ae42a7a505c8]
2025-09-27 01:35:38 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-27 01:35:42 | INFO     | [debug_RESULT] | 
============================================================
2025-09-27 01:35:42 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-27 01:35:42 | INFO     | [debug_RESULT] | ============================================================
2025-09-27 01:35:42 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_ae42a7a505c8
2025-09-27 01:35:42 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_ae42a7a505c8']
2025-09-27 01:35:42 | INFO     | [debug_RESULT] | ℹ️ Cached results: ['async_6294690c2d15', 'async_51c29cc92f71']
2025-09-27 01:35:42 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_ae42a7a505c8]
2025-09-27 01:35:42 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-27 01:35:46 | INFO     | [debug_RESULT] | 
============================================================
2025-09-27 01:35:46 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-27 01:35:46 | INFO     | [debug_RESULT] | ============================================================
2025-09-27 01:35:46 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_ae42a7a505c8
2025-09-27 01:35:46 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_ae42a7a505c8']
2025-09-27 01:35:46 | INFO     | [debug_RESULT] | ℹ️ Cached results: ['async_6294690c2d15', 'async_51c29cc92f71']
2025-09-27 01:35:46 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_ae42a7a505c8]
2025-09-27 01:35:46 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-27 01:35:50 | INFO     | [debug_RESULT] | 
============================================================
2025-09-27 01:35:50 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-27 01:35:50 | INFO     | [debug_RESULT] | ============================================================
2025-09-27 01:35:50 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_ae42a7a505c8
2025-09-27 01:35:50 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_ae42a7a505c8']
2025-09-27 01:35:50 | INFO     | [debug_RESULT] | ℹ️ Cached results: ['async_6294690c2d15', 'async_51c29cc92f71']
2025-09-27 01:35:50 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_ae42a7a505c8]
2025-09-27 01:35:50 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-27 01:35:56 | INFO     | [debug_RESULT] | 
============================================================
2025-09-27 01:35:56 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-27 01:35:56 | INFO     | [debug_RESULT] | ============================================================
2025-09-27 01:35:56 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_ae42a7a505c8
2025-09-27 01:35:56 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_ae42a7a505c8']
2025-09-27 01:35:56 | INFO     | [debug_RESULT] | ℹ️ Cached results: ['async_6294690c2d15', 'async_51c29cc92f71']
2025-09-27 01:35:56 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_ae42a7a505c8]
2025-09-27 01:35:56 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-27 01:35:56 | INFO     | [debug_RESULT] | 💾 RESULT STORED [async_ae42a7a505c8]
2025-09-27 01:35:56 | INFO     | [debug_RESULT] |    └─ Keys: ['success', 'data', 'metadata', 'session_id']
2025-09-27 01:35:56 | INFO     | [debug_RESULT] |    └─ Data Size: 9269 bytes
2025-09-27 01:35:56 | INFO     | [debug_RESULT] |    └─ Storage Time: 2025-09-27T01:35:56.713829
2025-09-27 01:35:56 | INFO     | [debug_RESULT] | 🔍 DATA STRUCTURE [async_ae42a7a505c8] STORED_RESULT: ['success', 'data', 'metadata', 'session_id']
2025-09-27 01:35:56 | INFO     | [debug_RESULT] |     └─ success: bool
2025-09-27 01:35:56 | INFO     | [debug_RESULT] |     └─ data: dict with keys ['success', 'analysis_id', 'data', 'execution_time', 'metadata']
2025-09-27 01:35:56 | INFO     | [debug_RESULT] |     └─ metadata: dict with keys ['execution_time', 'user_id', 'analysis_type', 'market_specialization', 'image_count', 'model_used']
2025-09-27 01:35:56 | INFO     | [debug_RESULT] |     └─ session_id: str
2025-09-27 01:36:00 | INFO     | [debug_RESULT] | 
============================================================
2025-09-27 01:36:00 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-27 01:36:00 | INFO     | [debug_RESULT] | ============================================================
2025-09-27 01:36:00 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_ae42a7a505c8
2025-09-27 01:36:00 | INFO     | [debug_RESULT] | ℹ️ Active tasks: []
2025-09-27 01:36:00 | INFO     | [debug_RESULT] | ℹ️ Cached results: ['async_6294690c2d15', 'async_51c29cc92f71', 'async_ae42a7a505c8']
2025-09-27 01:36:00 | INFO     | [debug_RESULT] | ✅ RESULT FOUND [async_ae42a7a505c8]: keys=['success', 'data', 'metadata', 'session_id']
2025-09-27 01:36:00 | INFO     | [debug_RESULT] | 🔍 DATA STRUCTURE [async_ae42a7a505c8] RETRIEVED_RESULT: ['success', 'data', 'metadata', 'session_id']
2025-09-27 01:36:00 | INFO     | [debug_RESULT] |     └─ success: bool
2025-09-27 01:36:00 | INFO     | [debug_RESULT] |     └─ data: dict with keys ['success', 'analysis_id', 'data', 'execution_time', 'metadata']
2025-09-27 01:36:00 | INFO     | [debug_RESULT] |     └─ metadata: dict with keys ['execution_time', 'user_id', 'analysis_type', 'market_specialization', 'image_count', 'model_used']
2025-09-27 01:36:00 | INFO     | [debug_RESULT] |     └─ session_id: str
2025-09-27 01:36:00 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=completed, has_data=True
2025-09-27 01:36:00 | INFO     | [debug_RESULT] | ℹ️ Response data keys: ['success', 'analysis_id', 'data', 'execution_time', 'metadata']
2025-09-27 03:10:52 | INFO     | [debug_RESULT] | 
============================================================
2025-09-27 03:10:52 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-27 03:10:52 | INFO     | [debug_RESULT] | ============================================================
2025-09-27 03:10:52 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_ee98e548f25a
2025-09-27 03:10:52 | INFO     | [debug_RESULT] | ℹ️ Active tasks: []
2025-09-27 03:10:52 | INFO     | [debug_RESULT] | ℹ️ Cached results: ['async_6294690c2d15', 'async_51c29cc92f71', 'async_ae42a7a505c8', 'async_ee98e548f25a']
2025-09-27 03:10:52 | INFO     | [debug_RESULT] | ✅ RESULT FOUND [async_ee98e548f25a]: keys=['success', 'error', 'session_id']
2025-09-27 03:10:52 | INFO     | [debug_RESULT] | 🔍 DATA STRUCTURE [async_ee98e548f25a] RETRIEVED_RESULT: ['success', 'error', 'session_id']
2025-09-27 03:10:52 | INFO     | [debug_RESULT] |     └─ success: bool
2025-09-27 03:10:52 | INFO     | [debug_RESULT] |     └─ error: str
2025-09-27 03:10:52 | INFO     | [debug_RESULT] |     └─ session_id: str
2025-09-27 03:10:52 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=False, status=failed, has_data=False
2025-09-27 03:26:14 | INFO     | [debug_RESULT] | 
============================================================
2025-09-27 03:26:14 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-27 03:26:14 | INFO     | [debug_RESULT] | ============================================================
2025-09-27 03:26:14 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_f4c7b192d5e7
2025-09-27 03:26:14 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_f4c7b192d5e7']
2025-09-27 03:26:14 | INFO     | [debug_RESULT] | ℹ️ Cached results: ['async_6294690c2d15', 'async_51c29cc92f71', 'async_ae42a7a505c8', 'async_ee98e548f25a']
2025-09-27 03:26:14 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_f4c7b192d5e7]
2025-09-27 03:26:14 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-27 03:26:19 | INFO     | [debug_RESULT] | 
============================================================
2025-09-27 03:26:19 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-27 03:26:19 | INFO     | [debug_RESULT] | ============================================================
2025-09-27 03:26:19 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_f4c7b192d5e7
2025-09-27 03:26:19 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_f4c7b192d5e7']
2025-09-27 03:26:19 | INFO     | [debug_RESULT] | ℹ️ Cached results: ['async_6294690c2d15', 'async_51c29cc92f71', 'async_ae42a7a505c8', 'async_ee98e548f25a']
2025-09-27 03:26:20 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_f4c7b192d5e7]
2025-09-27 03:26:20 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-27 03:26:25 | INFO     | [debug_RESULT] | 
============================================================
2025-09-27 03:26:25 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-27 03:26:25 | INFO     | [debug_RESULT] | ============================================================
2025-09-27 03:26:25 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_f4c7b192d5e7
2025-09-27 03:26:25 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_f4c7b192d5e7']
2025-09-27 03:26:25 | INFO     | [debug_RESULT] | ℹ️ Cached results: ['async_6294690c2d15', 'async_51c29cc92f71', 'async_ae42a7a505c8', 'async_ee98e548f25a']
2025-09-27 03:26:25 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_f4c7b192d5e7]
2025-09-27 03:26:25 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-27 03:26:31 | INFO     | [debug_RESULT] | 
============================================================
2025-09-27 03:26:31 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-27 03:26:31 | INFO     | [debug_RESULT] | ============================================================
2025-09-27 03:26:31 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_f4c7b192d5e7
2025-09-27 03:26:31 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_f4c7b192d5e7']
2025-09-27 03:26:31 | INFO     | [debug_RESULT] | ℹ️ Cached results: ['async_6294690c2d15', 'async_51c29cc92f71', 'async_ae42a7a505c8', 'async_ee98e548f25a']
2025-09-27 03:26:31 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_f4c7b192d5e7]
2025-09-27 03:26:31 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-27 03:26:43 | INFO     | [debug_RESULT] | 
============================================================
2025-09-27 03:26:43 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-27 03:26:43 | INFO     | [debug_RESULT] | ============================================================
2025-09-27 03:26:43 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_f4c7b192d5e7
2025-09-27 03:26:43 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_f4c7b192d5e7']
2025-09-27 03:26:43 | INFO     | [debug_RESULT] | ℹ️ Cached results: ['async_6294690c2d15', 'async_51c29cc92f71', 'async_ae42a7a505c8', 'async_ee98e548f25a']
2025-09-27 03:26:43 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_f4c7b192d5e7]
2025-09-27 03:26:43 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-27 03:26:48 | INFO     | [debug_RESULT] | 
============================================================
2025-09-27 03:26:48 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-27 03:26:48 | INFO     | [debug_RESULT] | ============================================================
2025-09-27 03:26:48 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_f4c7b192d5e7
2025-09-27 03:26:48 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_f4c7b192d5e7']
2025-09-27 03:26:48 | INFO     | [debug_RESULT] | ℹ️ Cached results: ['async_6294690c2d15', 'async_51c29cc92f71', 'async_ae42a7a505c8', 'async_ee98e548f25a']
2025-09-27 03:26:49 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_f4c7b192d5e7]
2025-09-27 03:26:49 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-27 03:26:54 | INFO     | [debug_RESULT] | 
============================================================
2025-09-27 03:26:54 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-27 03:26:54 | INFO     | [debug_RESULT] | ============================================================
2025-09-27 03:26:54 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_f4c7b192d5e7
2025-09-27 03:26:54 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_f4c7b192d5e7']
2025-09-27 03:26:54 | INFO     | [debug_RESULT] | ℹ️ Cached results: ['async_6294690c2d15', 'async_51c29cc92f71', 'async_ae42a7a505c8', 'async_ee98e548f25a']
2025-09-27 03:26:54 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_f4c7b192d5e7]
2025-09-27 03:26:54 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-27 03:26:59 | INFO     | [debug_RESULT] | 
============================================================
2025-09-27 03:26:59 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-27 03:26:59 | INFO     | [debug_RESULT] | ============================================================
2025-09-27 03:26:59 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_f4c7b192d5e7
2025-09-27 03:26:59 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_f4c7b192d5e7']
2025-09-27 03:26:59 | INFO     | [debug_RESULT] | ℹ️ Cached results: ['async_6294690c2d15', 'async_51c29cc92f71', 'async_ae42a7a505c8', 'async_ee98e548f25a']
2025-09-27 03:26:59 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_f4c7b192d5e7]
2025-09-27 03:26:59 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-27 03:27:04 | INFO     | [debug_RESULT] | 
============================================================
2025-09-27 03:27:04 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-27 03:27:04 | INFO     | [debug_RESULT] | ============================================================
2025-09-27 03:27:04 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_f4c7b192d5e7
2025-09-27 03:27:04 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_f4c7b192d5e7']
2025-09-27 03:27:04 | INFO     | [debug_RESULT] | ℹ️ Cached results: ['async_6294690c2d15', 'async_51c29cc92f71', 'async_ae42a7a505c8', 'async_ee98e548f25a']
2025-09-27 03:27:04 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_f4c7b192d5e7]
2025-09-27 03:27:04 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-27 03:27:12 | INFO     | [debug_RESULT] | 
============================================================
2025-09-27 03:27:12 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-27 03:27:12 | INFO     | [debug_RESULT] | ============================================================
2025-09-27 03:27:12 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_f4c7b192d5e7
2025-09-27 03:27:12 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_f4c7b192d5e7']
2025-09-27 03:27:12 | INFO     | [debug_RESULT] | ℹ️ Cached results: ['async_6294690c2d15', 'async_51c29cc92f71', 'async_ae42a7a505c8', 'async_ee98e548f25a']
2025-09-27 03:27:12 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_f4c7b192d5e7]
2025-09-27 03:27:12 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-27 03:27:17 | INFO     | [debug_RESULT] | 
============================================================
2025-09-27 03:27:17 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-27 03:27:17 | INFO     | [debug_RESULT] | ============================================================
2025-09-27 03:27:17 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_f4c7b192d5e7
2025-09-27 03:27:17 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_f4c7b192d5e7']
2025-09-27 03:27:17 | INFO     | [debug_RESULT] | ℹ️ Cached results: ['async_6294690c2d15', 'async_51c29cc92f71', 'async_ae42a7a505c8', 'async_ee98e548f25a']
2025-09-27 03:27:17 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_f4c7b192d5e7]
2025-09-27 03:27:17 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-27 03:27:22 | INFO     | [debug_RESULT] | 
============================================================
2025-09-27 03:27:22 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-27 03:27:22 | INFO     | [debug_RESULT] | ============================================================
2025-09-27 03:27:22 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_f4c7b192d5e7
2025-09-27 03:27:22 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_f4c7b192d5e7']
2025-09-27 03:27:22 | INFO     | [debug_RESULT] | ℹ️ Cached results: ['async_6294690c2d15', 'async_51c29cc92f71', 'async_ae42a7a505c8', 'async_ee98e548f25a']
2025-09-27 03:27:22 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_f4c7b192d5e7]
2025-09-27 03:27:22 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-27 03:27:28 | INFO     | [debug_RESULT] | 💾 RESULT STORED [async_f4c7b192d5e7]
2025-09-27 03:27:28 | INFO     | [debug_RESULT] |    └─ Keys: ['success', 'data', 'metadata', 'session_id']
2025-09-27 03:27:28 | INFO     | [debug_RESULT] |    └─ Data Size: 9032 bytes
2025-09-27 03:27:28 | INFO     | [debug_RESULT] |    └─ Storage Time: 2025-09-27T03:27:28.892724
2025-09-27 03:27:28 | INFO     | [debug_RESULT] | 🔍 DATA STRUCTURE [async_f4c7b192d5e7] STORED_RESULT: ['success', 'data', 'metadata', 'session_id']
2025-09-27 03:27:28 | INFO     | [debug_RESULT] |     └─ success: bool
2025-09-27 03:27:28 | INFO     | [debug_RESULT] |     └─ data: dict with keys ['success', 'analysis_id', 'data', 'execution_time', 'metadata']
2025-09-27 03:27:28 | INFO     | [debug_RESULT] |     └─ metadata: dict with keys ['execution_time', 'user_id', 'analysis_type', 'market_specialization', 'image_count', 'model_used']
2025-09-27 03:27:28 | INFO     | [debug_RESULT] |     └─ session_id: str
2025-09-27 03:27:29 | INFO     | [debug_RESULT] | 
============================================================
2025-09-27 03:27:29 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-27 03:27:29 | INFO     | [debug_RESULT] | ============================================================
2025-09-27 03:27:29 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_f4c7b192d5e7
2025-09-27 03:27:29 | INFO     | [debug_RESULT] | ℹ️ Active tasks: []
2025-09-27 03:27:29 | INFO     | [debug_RESULT] | ℹ️ Cached results: ['async_6294690c2d15', 'async_51c29cc92f71', 'async_ae42a7a505c8', 'async_ee98e548f25a', 'async_f4c7b192d5e7']
2025-09-27 03:27:29 | INFO     | [debug_RESULT] | ✅ RESULT FOUND [async_f4c7b192d5e7]: keys=['success', 'data', 'metadata', 'session_id']
2025-09-27 03:27:29 | INFO     | [debug_RESULT] | 🔍 DATA STRUCTURE [async_f4c7b192d5e7] RETRIEVED_RESULT: ['success', 'data', 'metadata', 'session_id']
2025-09-27 03:27:29 | INFO     | [debug_RESULT] |     └─ success: bool
2025-09-27 03:27:29 | INFO     | [debug_RESULT] |     └─ data: dict with keys ['success', 'analysis_id', 'data', 'execution_time', 'metadata']
2025-09-27 03:27:29 | INFO     | [debug_RESULT] |     └─ metadata: dict with keys ['execution_time', 'user_id', 'analysis_type', 'market_specialization', 'image_count', 'model_used']
2025-09-27 03:27:29 | INFO     | [debug_RESULT] |     └─ session_id: str
2025-09-27 03:27:29 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=completed, has_data=True
2025-09-27 03:27:29 | INFO     | [debug_RESULT] | ℹ️ Response data keys: ['success', 'analysis_id', 'data', 'execution_time', 'metadata']
2025-09-28 12:58:00 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 12:58:00 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 12:58:00 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 12:58:00 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_dad3879e2fb0
2025-09-28 12:58:00 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_dad3879e2fb0']
2025-09-28 12:58:00 | INFO     | [debug_RESULT] | ℹ️ Cached results: ['async_6294690c2d15', 'async_51c29cc92f71', 'async_ae42a7a505c8', 'async_ee98e548f25a', 'async_f4c7b192d5e7']
2025-09-28 12:58:00 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_dad3879e2fb0]
2025-09-28 12:58:00 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-28 12:58:06 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 12:58:06 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 12:58:06 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 12:58:06 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_dad3879e2fb0
2025-09-28 12:58:06 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_dad3879e2fb0']
2025-09-28 12:58:06 | INFO     | [debug_RESULT] | ℹ️ Cached results: ['async_6294690c2d15', 'async_51c29cc92f71', 'async_ae42a7a505c8', 'async_ee98e548f25a', 'async_f4c7b192d5e7']
2025-09-28 12:58:06 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_dad3879e2fb0]
2025-09-28 12:58:06 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-28 12:58:11 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 12:58:11 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 12:58:11 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 12:58:11 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_dad3879e2fb0
2025-09-28 12:58:11 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_dad3879e2fb0']
2025-09-28 12:58:11 | INFO     | [debug_RESULT] | ℹ️ Cached results: ['async_6294690c2d15', 'async_51c29cc92f71', 'async_ae42a7a505c8', 'async_ee98e548f25a', 'async_f4c7b192d5e7']
2025-09-28 12:58:11 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_dad3879e2fb0]
2025-09-28 12:58:11 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-28 12:58:17 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 12:58:17 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 12:58:17 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 12:58:17 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_dad3879e2fb0
2025-09-28 12:58:17 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_dad3879e2fb0']
2025-09-28 12:58:17 | INFO     | [debug_RESULT] | ℹ️ Cached results: ['async_6294690c2d15', 'async_51c29cc92f71', 'async_ae42a7a505c8', 'async_ee98e548f25a', 'async_f4c7b192d5e7']
2025-09-28 12:58:17 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_dad3879e2fb0]
2025-09-28 12:58:17 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-28 12:58:24 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 12:58:24 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 12:58:24 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 12:58:24 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_dad3879e2fb0
2025-09-28 12:58:24 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_dad3879e2fb0']
2025-09-28 12:58:24 | INFO     | [debug_RESULT] | ℹ️ Cached results: ['async_6294690c2d15', 'async_51c29cc92f71', 'async_ae42a7a505c8', 'async_ee98e548f25a', 'async_f4c7b192d5e7']
2025-09-28 12:58:24 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_dad3879e2fb0]
2025-09-28 12:58:24 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-28 12:58:29 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 12:58:29 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 12:58:29 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 12:58:29 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_dad3879e2fb0
2025-09-28 12:58:29 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_dad3879e2fb0']
2025-09-28 12:58:29 | INFO     | [debug_RESULT] | ℹ️ Cached results: ['async_6294690c2d15', 'async_51c29cc92f71', 'async_ae42a7a505c8', 'async_ee98e548f25a', 'async_f4c7b192d5e7']
2025-09-28 12:58:29 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_dad3879e2fb0]
2025-09-28 12:58:29 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-28 12:58:35 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 12:58:35 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 12:58:35 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 12:58:35 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_dad3879e2fb0
2025-09-28 12:58:35 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_dad3879e2fb0']
2025-09-28 12:58:35 | INFO     | [debug_RESULT] | ℹ️ Cached results: ['async_6294690c2d15', 'async_51c29cc92f71', 'async_ae42a7a505c8', 'async_ee98e548f25a', 'async_f4c7b192d5e7']
2025-09-28 12:58:35 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_dad3879e2fb0]
2025-09-28 12:58:35 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-28 12:58:40 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 12:58:40 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 12:58:40 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 12:58:40 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_dad3879e2fb0
2025-09-28 12:58:40 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_dad3879e2fb0']
2025-09-28 12:58:40 | INFO     | [debug_RESULT] | ℹ️ Cached results: ['async_6294690c2d15', 'async_51c29cc92f71', 'async_ae42a7a505c8', 'async_ee98e548f25a', 'async_f4c7b192d5e7']
2025-09-28 12:58:40 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_dad3879e2fb0]
2025-09-28 12:58:40 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-28 12:58:45 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 12:58:45 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 12:58:45 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 12:58:45 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_dad3879e2fb0
2025-09-28 12:58:45 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_dad3879e2fb0']
2025-09-28 12:58:45 | INFO     | [debug_RESULT] | ℹ️ Cached results: ['async_6294690c2d15', 'async_51c29cc92f71', 'async_ae42a7a505c8', 'async_ee98e548f25a', 'async_f4c7b192d5e7']
2025-09-28 12:58:45 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_dad3879e2fb0]
2025-09-28 12:58:45 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-28 12:58:51 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 12:58:51 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 12:58:51 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 12:58:51 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_dad3879e2fb0
2025-09-28 12:58:51 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_dad3879e2fb0']
2025-09-28 12:58:51 | INFO     | [debug_RESULT] | ℹ️ Cached results: ['async_6294690c2d15', 'async_51c29cc92f71', 'async_ae42a7a505c8', 'async_ee98e548f25a', 'async_f4c7b192d5e7']
2025-09-28 12:58:51 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_dad3879e2fb0]
2025-09-28 12:58:51 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-28 12:58:59 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 12:58:59 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 12:58:59 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 12:58:59 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_dad3879e2fb0
2025-09-28 12:58:59 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_dad3879e2fb0']
2025-09-28 12:58:59 | INFO     | [debug_RESULT] | ℹ️ Cached results: ['async_6294690c2d15', 'async_51c29cc92f71', 'async_ae42a7a505c8', 'async_ee98e548f25a', 'async_f4c7b192d5e7']
2025-09-28 12:58:59 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_dad3879e2fb0]
2025-09-28 12:58:59 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-28 12:59:04 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 12:59:04 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 12:59:04 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 12:59:04 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_dad3879e2fb0
2025-09-28 12:59:04 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_dad3879e2fb0']
2025-09-28 12:59:04 | INFO     | [debug_RESULT] | ℹ️ Cached results: ['async_6294690c2d15', 'async_51c29cc92f71', 'async_ae42a7a505c8', 'async_ee98e548f25a', 'async_f4c7b192d5e7']
2025-09-28 12:59:04 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_dad3879e2fb0]
2025-09-28 12:59:04 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-28 12:59:10 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 12:59:10 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 12:59:10 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 12:59:10 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_dad3879e2fb0
2025-09-28 12:59:10 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_dad3879e2fb0']
2025-09-28 12:59:10 | INFO     | [debug_RESULT] | ℹ️ Cached results: ['async_6294690c2d15', 'async_51c29cc92f71', 'async_ae42a7a505c8', 'async_ee98e548f25a', 'async_f4c7b192d5e7']
2025-09-28 12:59:10 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_dad3879e2fb0]
2025-09-28 12:59:10 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-28 12:59:15 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 12:59:15 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 12:59:15 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 12:59:15 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_dad3879e2fb0
2025-09-28 12:59:15 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_dad3879e2fb0']
2025-09-28 12:59:15 | INFO     | [debug_RESULT] | ℹ️ Cached results: ['async_6294690c2d15', 'async_51c29cc92f71', 'async_ae42a7a505c8', 'async_ee98e548f25a', 'async_f4c7b192d5e7']
2025-09-28 12:59:15 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_dad3879e2fb0]
2025-09-28 12:59:15 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-28 12:59:17 | INFO     | [debug_RESULT] | 💾 RESULT STORED [async_dad3879e2fb0]
2025-09-28 12:59:17 | INFO     | [debug_RESULT] |    └─ Keys: ['success', 'data', 'metadata', 'session_id']
2025-09-28 12:59:17 | INFO     | [debug_RESULT] |    └─ Data Size: 8640 bytes
2025-09-28 12:59:17 | INFO     | [debug_RESULT] |    └─ Storage Time: 2025-09-28T12:59:17.692239
2025-09-28 12:59:17 | INFO     | [debug_RESULT] | 🔍 DATA STRUCTURE [async_dad3879e2fb0] STORED_RESULT: ['success', 'data', 'metadata', 'session_id']
2025-09-28 12:59:17 | INFO     | [debug_RESULT] |     └─ success: bool
2025-09-28 12:59:17 | INFO     | [debug_RESULT] |     └─ data: dict with keys ['success', 'analysis_id', 'data', 'execution_time', 'metadata']
2025-09-28 12:59:17 | INFO     | [debug_RESULT] |     └─ metadata: dict with keys ['execution_time', 'user_id', 'analysis_type', 'market_specialization', 'image_count', 'model_used']
2025-09-28 12:59:17 | INFO     | [debug_RESULT] |     └─ session_id: str
2025-09-28 12:59:21 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 12:59:21 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 12:59:21 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 12:59:21 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_dad3879e2fb0
2025-09-28 12:59:21 | INFO     | [debug_RESULT] | ℹ️ Active tasks: []
2025-09-28 12:59:21 | INFO     | [debug_RESULT] | ℹ️ Cached results: ['async_6294690c2d15', 'async_51c29cc92f71', 'async_ae42a7a505c8', 'async_ee98e548f25a', 'async_f4c7b192d5e7', 'async_dad3879e2fb0']
2025-09-28 12:59:21 | INFO     | [debug_RESULT] | ✅ RESULT FOUND [async_dad3879e2fb0]: keys=['success', 'data', 'metadata', 'session_id']
2025-09-28 12:59:21 | INFO     | [debug_RESULT] | 🔍 DATA STRUCTURE [async_dad3879e2fb0] RETRIEVED_RESULT: ['success', 'data', 'metadata', 'session_id']
2025-09-28 12:59:21 | INFO     | [debug_RESULT] |     └─ success: bool
2025-09-28 12:59:21 | INFO     | [debug_RESULT] |     └─ data: dict with keys ['success', 'analysis_id', 'data', 'execution_time', 'metadata']
2025-09-28 12:59:21 | INFO     | [debug_RESULT] |     └─ metadata: dict with keys ['execution_time', 'user_id', 'analysis_type', 'market_specialization', 'image_count', 'model_used']
2025-09-28 12:59:21 | INFO     | [debug_RESULT] |     └─ session_id: str
2025-09-28 12:59:21 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=completed, has_data=True
2025-09-28 12:59:21 | INFO     | [debug_RESULT] | ℹ️ Response data keys: ['success', 'analysis_id', 'data', 'execution_time', 'metadata']
2025-09-28 13:29:12 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 13:29:12 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 13:29:12 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 13:29:12 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_82d95fa09413
2025-09-28 13:29:12 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_82d95fa09413']
2025-09-28 13:29:12 | INFO     | [debug_RESULT] | ℹ️ Cached results: ['async_6294690c2d15', 'async_51c29cc92f71', 'async_ae42a7a505c8', 'async_ee98e548f25a', 'async_f4c7b192d5e7', 'async_dad3879e2fb0']
2025-09-28 13:29:12 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_82d95fa09413]
2025-09-28 13:29:12 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-28 13:29:17 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 13:29:17 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 13:29:17 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 13:29:17 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_82d95fa09413
2025-09-28 13:29:17 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_82d95fa09413']
2025-09-28 13:29:17 | INFO     | [debug_RESULT] | ℹ️ Cached results: ['async_6294690c2d15', 'async_51c29cc92f71', 'async_ae42a7a505c8', 'async_ee98e548f25a', 'async_f4c7b192d5e7', 'async_dad3879e2fb0']
2025-09-28 13:29:17 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_82d95fa09413]
2025-09-28 13:29:17 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-28 13:29:23 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 13:29:23 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 13:29:23 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 13:29:23 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_82d95fa09413
2025-09-28 13:29:23 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_82d95fa09413']
2025-09-28 13:29:23 | INFO     | [debug_RESULT] | ℹ️ Cached results: ['async_6294690c2d15', 'async_51c29cc92f71', 'async_ae42a7a505c8', 'async_ee98e548f25a', 'async_f4c7b192d5e7', 'async_dad3879e2fb0']
2025-09-28 13:29:23 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_82d95fa09413]
2025-09-28 13:29:23 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-28 13:29:28 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 13:29:28 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 13:29:28 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 13:29:28 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_82d95fa09413
2025-09-28 13:29:28 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_82d95fa09413']
2025-09-28 13:29:28 | INFO     | [debug_RESULT] | ℹ️ Cached results: ['async_6294690c2d15', 'async_51c29cc92f71', 'async_ae42a7a505c8', 'async_ee98e548f25a', 'async_f4c7b192d5e7', 'async_dad3879e2fb0']
2025-09-28 13:29:28 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_82d95fa09413]
2025-09-28 13:29:28 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-28 13:29:34 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 13:29:34 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 13:29:34 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 13:29:34 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_82d95fa09413
2025-09-28 13:29:34 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_82d95fa09413']
2025-09-28 13:29:34 | INFO     | [debug_RESULT] | ℹ️ Cached results: ['async_6294690c2d15', 'async_51c29cc92f71', 'async_ae42a7a505c8', 'async_ee98e548f25a', 'async_f4c7b192d5e7', 'async_dad3879e2fb0']
2025-09-28 13:29:34 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_82d95fa09413]
2025-09-28 13:29:34 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-28 13:29:39 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 13:29:39 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 13:29:39 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 13:29:39 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_82d95fa09413
2025-09-28 13:29:39 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_82d95fa09413']
2025-09-28 13:29:39 | INFO     | [debug_RESULT] | ℹ️ Cached results: ['async_6294690c2d15', 'async_51c29cc92f71', 'async_ae42a7a505c8', 'async_ee98e548f25a', 'async_f4c7b192d5e7', 'async_dad3879e2fb0']
2025-09-28 13:29:39 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_82d95fa09413]
2025-09-28 13:29:39 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-28 13:29:44 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 13:29:44 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 13:29:44 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 13:29:44 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_82d95fa09413
2025-09-28 13:29:44 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_82d95fa09413']
2025-09-28 13:29:44 | INFO     | [debug_RESULT] | ℹ️ Cached results: ['async_6294690c2d15', 'async_51c29cc92f71', 'async_ae42a7a505c8', 'async_ee98e548f25a', 'async_f4c7b192d5e7', 'async_dad3879e2fb0']
2025-09-28 13:29:44 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_82d95fa09413]
2025-09-28 13:29:44 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-28 13:29:50 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 13:29:50 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 13:29:50 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 13:29:50 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_82d95fa09413
2025-09-28 13:29:50 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_82d95fa09413']
2025-09-28 13:29:50 | INFO     | [debug_RESULT] | ℹ️ Cached results: ['async_6294690c2d15', 'async_51c29cc92f71', 'async_ae42a7a505c8', 'async_ee98e548f25a', 'async_f4c7b192d5e7', 'async_dad3879e2fb0']
2025-09-28 13:29:50 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_82d95fa09413]
2025-09-28 13:29:50 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-28 13:29:55 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 13:29:55 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 13:29:55 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 13:29:55 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_82d95fa09413
2025-09-28 13:29:55 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_82d95fa09413']
2025-09-28 13:29:55 | INFO     | [debug_RESULT] | ℹ️ Cached results: ['async_6294690c2d15', 'async_51c29cc92f71', 'async_ae42a7a505c8', 'async_ee98e548f25a', 'async_f4c7b192d5e7', 'async_dad3879e2fb0']
2025-09-28 13:29:55 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_82d95fa09413]
2025-09-28 13:29:55 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-28 13:30:01 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 13:30:01 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 13:30:01 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 13:30:01 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_82d95fa09413
2025-09-28 13:30:01 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_82d95fa09413']
2025-09-28 13:30:01 | INFO     | [debug_RESULT] | ℹ️ Cached results: ['async_6294690c2d15', 'async_51c29cc92f71', 'async_ae42a7a505c8', 'async_ee98e548f25a', 'async_f4c7b192d5e7', 'async_dad3879e2fb0']
2025-09-28 13:30:01 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_82d95fa09413]
2025-09-28 13:30:01 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-28 13:30:03 | INFO     | [debug_RESULT] | 💾 RESULT STORED [async_82d95fa09413]
2025-09-28 13:30:03 | INFO     | [debug_RESULT] |    └─ Keys: ['success', 'data', 'metadata', 'session_id']
2025-09-28 13:30:03 | INFO     | [debug_RESULT] |    └─ Data Size: 15693 bytes
2025-09-28 13:30:03 | INFO     | [debug_RESULT] |    └─ Storage Time: 2025-09-28T13:30:03.302038
2025-09-28 13:30:03 | INFO     | [debug_RESULT] | 🔍 DATA STRUCTURE [async_82d95fa09413] STORED_RESULT: ['success', 'data', 'metadata', 'session_id']
2025-09-28 13:30:03 | INFO     | [debug_RESULT] |     └─ success: bool
2025-09-28 13:30:03 | INFO     | [debug_RESULT] |     └─ data: dict with keys ['success', 'analysis_id', 'data', 'execution_time', 'metadata']
2025-09-28 13:30:03 | INFO     | [debug_RESULT] |     └─ metadata: dict with keys ['execution_time', 'user_id', 'analysis_type', 'market_specialization', 'image_count', 'model_used']
2025-09-28 13:30:03 | INFO     | [debug_RESULT] |     └─ session_id: str
2025-09-28 13:30:06 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 13:30:06 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 13:30:06 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 13:30:06 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_82d95fa09413
2025-09-28 13:30:06 | INFO     | [debug_RESULT] | ℹ️ Active tasks: []
2025-09-28 13:30:06 | INFO     | [debug_RESULT] | ℹ️ Cached results: ['async_6294690c2d15', 'async_51c29cc92f71', 'async_ae42a7a505c8', 'async_ee98e548f25a', 'async_f4c7b192d5e7', 'async_dad3879e2fb0', 'async_82d95fa09413']
2025-09-28 13:30:06 | INFO     | [debug_RESULT] | ✅ RESULT FOUND [async_82d95fa09413]: keys=['success', 'data', 'metadata', 'session_id']
2025-09-28 13:30:06 | INFO     | [debug_RESULT] | 🔍 DATA STRUCTURE [async_82d95fa09413] RETRIEVED_RESULT: ['success', 'data', 'metadata', 'session_id']
2025-09-28 13:30:06 | INFO     | [debug_RESULT] |     └─ success: bool
2025-09-28 13:30:06 | INFO     | [debug_RESULT] |     └─ data: dict with keys ['success', 'analysis_id', 'data', 'execution_time', 'metadata']
2025-09-28 13:30:06 | INFO     | [debug_RESULT] |     └─ metadata: dict with keys ['execution_time', 'user_id', 'analysis_type', 'market_specialization', 'image_count', 'model_used']
2025-09-28 13:30:06 | INFO     | [debug_RESULT] |     └─ session_id: str
2025-09-28 13:30:06 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=completed, has_data=True
2025-09-28 13:30:06 | INFO     | [debug_RESULT] | ℹ️ Response data keys: ['success', 'analysis_id', 'data', 'execution_time', 'metadata']
2025-09-28 15:34:57 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 15:34:57 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 15:34:57 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 15:34:57 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_f2b8222a1bff
2025-09-28 15:34:57 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_f2b8222a1bff']
2025-09-28 15:34:57 | INFO     | [debug_RESULT] | ℹ️ Cached results: []
2025-09-28 15:34:57 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_f2b8222a1bff]
2025-09-28 15:34:57 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-28 15:35:02 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 15:35:02 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 15:35:02 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 15:35:02 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_f2b8222a1bff
2025-09-28 15:35:02 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_f2b8222a1bff']
2025-09-28 15:35:02 | INFO     | [debug_RESULT] | ℹ️ Cached results: []
2025-09-28 15:35:02 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_f2b8222a1bff]
2025-09-28 15:35:02 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-28 15:35:08 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 15:35:08 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 15:35:08 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 15:35:08 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_f2b8222a1bff
2025-09-28 15:35:08 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_f2b8222a1bff']
2025-09-28 15:35:08 | INFO     | [debug_RESULT] | ℹ️ Cached results: []
2025-09-28 15:35:08 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_f2b8222a1bff]
2025-09-28 15:35:08 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-28 15:35:15 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 15:35:15 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 15:35:15 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 15:35:15 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_f2b8222a1bff
2025-09-28 15:35:15 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_f2b8222a1bff']
2025-09-28 15:35:15 | INFO     | [debug_RESULT] | ℹ️ Cached results: []
2025-09-28 15:35:15 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_f2b8222a1bff]
2025-09-28 15:35:15 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-28 15:35:20 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 15:35:20 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 15:35:20 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 15:35:20 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_f2b8222a1bff
2025-09-28 15:35:20 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_f2b8222a1bff']
2025-09-28 15:35:20 | INFO     | [debug_RESULT] | ℹ️ Cached results: []
2025-09-28 15:35:20 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_f2b8222a1bff]
2025-09-28 15:35:20 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-28 15:35:27 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 15:35:27 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 15:35:27 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 15:35:27 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_f2b8222a1bff
2025-09-28 15:35:27 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_f2b8222a1bff']
2025-09-28 15:35:27 | INFO     | [debug_RESULT] | ℹ️ Cached results: []
2025-09-28 15:35:27 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_f2b8222a1bff]
2025-09-28 15:35:27 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-28 15:35:32 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 15:35:32 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 15:35:32 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 15:35:32 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_f2b8222a1bff
2025-09-28 15:35:32 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_f2b8222a1bff']
2025-09-28 15:35:32 | INFO     | [debug_RESULT] | ℹ️ Cached results: []
2025-09-28 15:35:32 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_f2b8222a1bff]
2025-09-28 15:35:32 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-28 15:35:38 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 15:35:38 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 15:35:38 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 15:35:38 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_f2b8222a1bff
2025-09-28 15:35:38 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_f2b8222a1bff']
2025-09-28 15:35:38 | INFO     | [debug_RESULT] | ℹ️ Cached results: []
2025-09-28 15:35:38 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_f2b8222a1bff]
2025-09-28 15:35:38 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-28 15:35:46 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 15:35:46 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 15:35:46 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 15:35:46 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_f2b8222a1bff
2025-09-28 15:35:46 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_f2b8222a1bff']
2025-09-28 15:35:46 | INFO     | [debug_RESULT] | ℹ️ Cached results: []
2025-09-28 15:35:46 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_f2b8222a1bff]
2025-09-28 15:35:46 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-28 15:35:51 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 15:35:51 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 15:35:51 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 15:35:51 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_f2b8222a1bff
2025-09-28 15:35:51 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_f2b8222a1bff']
2025-09-28 15:35:51 | INFO     | [debug_RESULT] | ℹ️ Cached results: []
2025-09-28 15:35:51 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_f2b8222a1bff]
2025-09-28 15:35:51 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-28 15:35:57 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 15:35:57 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 15:35:57 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 15:35:57 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_f2b8222a1bff
2025-09-28 15:35:57 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_f2b8222a1bff']
2025-09-28 15:35:57 | INFO     | [debug_RESULT] | ℹ️ Cached results: []
2025-09-28 15:35:57 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_f2b8222a1bff]
2025-09-28 15:35:57 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-28 15:36:03 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 15:36:03 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 15:36:03 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 15:36:03 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_f2b8222a1bff
2025-09-28 15:36:03 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_f2b8222a1bff']
2025-09-28 15:36:03 | INFO     | [debug_RESULT] | ℹ️ Cached results: []
2025-09-28 15:36:03 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_f2b8222a1bff]
2025-09-28 15:36:03 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-28 15:36:09 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 15:36:09 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 15:36:09 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 15:36:09 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_f2b8222a1bff
2025-09-28 15:36:09 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_f2b8222a1bff']
2025-09-28 15:36:09 | INFO     | [debug_RESULT] | ℹ️ Cached results: []
2025-09-28 15:36:09 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_f2b8222a1bff]
2025-09-28 15:36:09 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-28 15:36:16 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 15:36:16 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 15:36:16 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 15:36:16 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_f2b8222a1bff
2025-09-28 15:36:16 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_f2b8222a1bff']
2025-09-28 15:36:16 | INFO     | [debug_RESULT] | ℹ️ Cached results: []
2025-09-28 15:36:16 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_f2b8222a1bff]
2025-09-28 15:36:16 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-28 15:36:22 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 15:36:22 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 15:36:22 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 15:36:22 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_f2b8222a1bff
2025-09-28 15:36:22 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_f2b8222a1bff']
2025-09-28 15:36:22 | INFO     | [debug_RESULT] | ℹ️ Cached results: []
2025-09-28 15:36:22 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_f2b8222a1bff]
2025-09-28 15:36:22 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-28 15:36:28 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 15:36:28 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 15:36:28 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 15:36:28 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_f2b8222a1bff
2025-09-28 15:36:28 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_f2b8222a1bff']
2025-09-28 15:36:28 | INFO     | [debug_RESULT] | ℹ️ Cached results: []
2025-09-28 15:36:28 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_f2b8222a1bff]
2025-09-28 15:36:28 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-28 15:36:34 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 15:36:34 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 15:36:34 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 15:36:34 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_f2b8222a1bff
2025-09-28 15:36:34 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_f2b8222a1bff']
2025-09-28 15:36:34 | INFO     | [debug_RESULT] | ℹ️ Cached results: []
2025-09-28 15:36:34 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_f2b8222a1bff]
2025-09-28 15:36:34 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-28 15:36:40 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 15:36:40 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 15:36:40 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 15:36:40 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_f2b8222a1bff
2025-09-28 15:36:40 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_f2b8222a1bff']
2025-09-28 15:36:40 | INFO     | [debug_RESULT] | ℹ️ Cached results: []
2025-09-28 15:36:40 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_f2b8222a1bff]
2025-09-28 15:36:40 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-28 15:36:47 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 15:36:47 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 15:36:47 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 15:36:47 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_f2b8222a1bff
2025-09-28 15:36:47 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_f2b8222a1bff']
2025-09-28 15:36:47 | INFO     | [debug_RESULT] | ℹ️ Cached results: []
2025-09-28 15:36:47 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_f2b8222a1bff]
2025-09-28 15:36:47 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-28 15:36:52 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 15:36:52 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 15:36:52 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 15:36:53 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_f2b8222a1bff
2025-09-28 15:36:53 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_f2b8222a1bff']
2025-09-28 15:36:53 | INFO     | [debug_RESULT] | ℹ️ Cached results: []
2025-09-28 15:36:53 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_f2b8222a1bff]
2025-09-28 15:36:53 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-28 15:36:58 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 15:36:58 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 15:36:58 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 15:36:58 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_f2b8222a1bff
2025-09-28 15:36:58 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_f2b8222a1bff']
2025-09-28 15:36:58 | INFO     | [debug_RESULT] | ℹ️ Cached results: []
2025-09-28 15:36:58 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_f2b8222a1bff]
2025-09-28 15:36:58 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-28 15:37:00 | INFO     | [debug_RESULT] | 💾 RESULT STORED [async_f2b8222a1bff]
2025-09-28 15:37:00 | INFO     | [debug_RESULT] |    └─ Keys: ['success', 'data', 'metadata', 'session_id']
2025-09-28 15:37:00 | INFO     | [debug_RESULT] |    └─ Data Size: 13427 bytes
2025-09-28 15:37:00 | INFO     | [debug_RESULT] |    └─ Storage Time: 2025-09-28T15:37:00.344939
2025-09-28 15:37:00 | INFO     | [debug_RESULT] | 🔍 DATA STRUCTURE [async_f2b8222a1bff] STORED_RESULT: ['success', 'data', 'metadata', 'session_id']
2025-09-28 15:37:00 | INFO     | [debug_RESULT] |     └─ success: bool
2025-09-28 15:37:00 | INFO     | [debug_RESULT] |     └─ data: dict with keys ['success', 'analysis_id', 'data', 'execution_time', 'metadata']
2025-09-28 15:37:00 | INFO     | [debug_RESULT] |     └─ metadata: dict with keys ['execution_time', 'user_id', 'analysis_type', 'market_specialization', 'image_count', 'model_used']
2025-09-28 15:37:00 | INFO     | [debug_RESULT] |     └─ session_id: str
2025-09-28 15:37:04 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 15:37:04 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 15:37:04 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 15:37:04 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_f2b8222a1bff
2025-09-28 15:37:04 | INFO     | [debug_RESULT] | ℹ️ Active tasks: []
2025-09-28 15:37:04 | INFO     | [debug_RESULT] | ℹ️ Cached results: ['async_f2b8222a1bff']
2025-09-28 15:37:04 | INFO     | [debug_RESULT] | ✅ RESULT FOUND [async_f2b8222a1bff]: keys=['success', 'data', 'metadata', 'session_id']
2025-09-28 15:37:04 | INFO     | [debug_RESULT] | 🔍 DATA STRUCTURE [async_f2b8222a1bff] RETRIEVED_RESULT: ['success', 'data', 'metadata', 'session_id']
2025-09-28 15:37:04 | INFO     | [debug_RESULT] |     └─ success: bool
2025-09-28 15:37:04 | INFO     | [debug_RESULT] |     └─ data: dict with keys ['success', 'analysis_id', 'data', 'execution_time', 'metadata']
2025-09-28 15:37:04 | INFO     | [debug_RESULT] |     └─ metadata: dict with keys ['execution_time', 'user_id', 'analysis_type', 'market_specialization', 'image_count', 'model_used']
2025-09-28 15:37:04 | INFO     | [debug_RESULT] |     └─ session_id: str
2025-09-28 15:37:04 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=completed, has_data=True
2025-09-28 15:37:04 | INFO     | [debug_RESULT] | ℹ️ Response data keys: ['success', 'analysis_id', 'data', 'execution_time', 'metadata']
2025-09-28 16:00:32 | INFO     | [debug_RESULT] | 💾 RESULT STORED [async_f4f25e11ad6b]
2025-09-28 16:00:32 | INFO     | [debug_RESULT] |    └─ Keys: ['success', 'data', 'metadata', 'session_id']
2025-09-28 16:00:32 | INFO     | [debug_RESULT] |    └─ Data Size: 16551 bytes
2025-09-28 16:00:32 | INFO     | [debug_RESULT] |    └─ Storage Time: 2025-09-28T16:00:32.497948
2025-09-28 16:00:32 | INFO     | [debug_RESULT] | 🔍 DATA STRUCTURE [async_f4f25e11ad6b] STORED_RESULT: ['success', 'data', 'metadata', 'session_id']
2025-09-28 16:00:32 | INFO     | [debug_RESULT] |     └─ success: bool
2025-09-28 16:00:32 | INFO     | [debug_RESULT] |     └─ data: dict with keys ['success', 'analysis_id', 'data', 'execution_time', 'metadata']
2025-09-28 16:00:32 | INFO     | [debug_RESULT] |     └─ metadata: dict with keys ['execution_time', 'user_id', 'analysis_type', 'market_specialization', 'image_count', 'model_used']
2025-09-28 16:00:32 | INFO     | [debug_RESULT] |     └─ session_id: str
2025-09-28 16:18:50 | INFO     | [debug_RESULT] | 💾 RESULT STORED [async_1492829efc32]
2025-09-28 16:18:50 | INFO     | [debug_RESULT] |    └─ Keys: ['success', 'data', 'metadata', 'session_id']
2025-09-28 16:18:50 | INFO     | [debug_RESULT] |    └─ Data Size: 16816 bytes
2025-09-28 16:18:50 | INFO     | [debug_RESULT] |    └─ Storage Time: 2025-09-28T16:18:50.708312
2025-09-28 16:18:50 | INFO     | [debug_RESULT] | 🔍 DATA STRUCTURE [async_1492829efc32] STORED_RESULT: ['success', 'data', 'metadata', 'session_id']
2025-09-28 16:18:50 | INFO     | [debug_RESULT] |     └─ success: bool
2025-09-28 16:18:50 | INFO     | [debug_RESULT] |     └─ data: dict with keys ['success', 'analysis_id', 'data', 'execution_time', 'metadata']
2025-09-28 16:18:50 | INFO     | [debug_RESULT] |     └─ metadata: dict with keys ['execution_time', 'user_id', 'analysis_type', 'market_specialization', 'image_count', 'model_used']
2025-09-28 16:18:50 | INFO     | [debug_RESULT] |     └─ session_id: str
2025-09-28 16:27:07 | INFO     | [debug_RESULT] | 💾 RESULT STORED [async_b561bcdda83b]
2025-09-28 16:27:07 | INFO     | [debug_RESULT] |    └─ Keys: ['success', 'data', 'metadata', 'session_id']
2025-09-28 16:27:07 | INFO     | [debug_RESULT] |    └─ Data Size: 9737 bytes
2025-09-28 16:27:07 | INFO     | [debug_RESULT] |    └─ Storage Time: 2025-09-28T16:27:07.388623
2025-09-28 16:27:07 | INFO     | [debug_RESULT] | 🔍 DATA STRUCTURE [async_b561bcdda83b] STORED_RESULT: ['success', 'data', 'metadata', 'session_id']
2025-09-28 16:27:07 | INFO     | [debug_RESULT] |     └─ success: bool
2025-09-28 16:27:07 | INFO     | [debug_RESULT] |     └─ data: dict with keys ['success', 'analysis_id', 'data', 'execution_time', 'metadata']
2025-09-28 16:27:07 | INFO     | [debug_RESULT] |     └─ metadata: dict with keys ['execution_time', 'user_id', 'analysis_type', 'market_specialization', 'image_count', 'model_used']
2025-09-28 16:27:07 | INFO     | [debug_RESULT] |     └─ session_id: str
2025-09-28 16:39:23 | INFO     | [debug_RESULT] | 💾 RESULT STORED [async_e4476a6af45b]
2025-09-28 16:39:23 | INFO     | [debug_RESULT] |    └─ Keys: ['success', 'data', 'metadata', 'session_id']
2025-09-28 16:39:23 | INFO     | [debug_RESULT] |    └─ Data Size: 15516 bytes
2025-09-28 16:39:23 | INFO     | [debug_RESULT] |    └─ Storage Time: 2025-09-28T16:39:23.831401
2025-09-28 16:39:23 | INFO     | [debug_RESULT] | 🔍 DATA STRUCTURE [async_e4476a6af45b] STORED_RESULT: ['success', 'data', 'metadata', 'session_id']
2025-09-28 16:39:23 | INFO     | [debug_RESULT] |     └─ success: bool
2025-09-28 16:39:23 | INFO     | [debug_RESULT] |     └─ data: dict with keys ['success', 'analysis_id', 'data', 'execution_time', 'metadata']
2025-09-28 16:39:23 | INFO     | [debug_RESULT] |     └─ metadata: dict with keys ['execution_time', 'user_id', 'analysis_type', 'market_specialization', 'image_count', 'model_used']
2025-09-28 16:39:23 | INFO     | [debug_RESULT] |     └─ session_id: str
2025-09-28 17:00:29 | INFO     | [debug_RESULT] | 💾 RESULT STORED [async_bca5e0661a81]
2025-09-28 17:00:29 | INFO     | [debug_RESULT] |    └─ Keys: ['success', 'data', 'metadata', 'session_id']
2025-09-28 17:00:29 | INFO     | [debug_RESULT] |    └─ Data Size: 13558 bytes
2025-09-28 17:00:29 | INFO     | [debug_RESULT] |    └─ Storage Time: 2025-09-28T17:00:29.131816
2025-09-28 17:00:29 | INFO     | [debug_RESULT] | 🔍 DATA STRUCTURE [async_bca5e0661a81] STORED_RESULT: ['success', 'data', 'metadata', 'session_id']
2025-09-28 17:00:29 | INFO     | [debug_RESULT] |     └─ success: bool
2025-09-28 17:00:29 | INFO     | [debug_RESULT] |     └─ data: dict with keys ['success', 'analysis_id', 'data', 'execution_time', 'metadata']
2025-09-28 17:00:29 | INFO     | [debug_RESULT] |     └─ metadata: dict with keys ['execution_time', 'user_id', 'analysis_type', 'market_specialization', 'image_count', 'model_used']
2025-09-28 17:00:29 | INFO     | [debug_RESULT] |     └─ session_id: str
2025-09-28 17:24:49 | INFO     | [debug_RESULT] | 💾 RESULT STORED [async_a4648a305b64]
2025-09-28 17:24:49 | INFO     | [debug_RESULT] |    └─ Keys: ['success', 'data', 'metadata', 'session_id']
2025-09-28 17:24:49 | INFO     | [debug_RESULT] |    └─ Data Size: 14972 bytes
2025-09-28 17:24:49 | INFO     | [debug_RESULT] |    └─ Storage Time: 2025-09-28T17:24:49.177288
2025-09-28 17:24:49 | INFO     | [debug_RESULT] | 🔍 DATA STRUCTURE [async_a4648a305b64] STORED_RESULT: ['success', 'data', 'metadata', 'session_id']
2025-09-28 17:24:49 | INFO     | [debug_RESULT] |     └─ success: bool
2025-09-28 17:24:49 | INFO     | [debug_RESULT] |     └─ data: dict with keys ['success', 'analysis_id', 'data', 'execution_time', 'metadata']
2025-09-28 17:24:49 | INFO     | [debug_RESULT] |     └─ metadata: dict with keys ['execution_time', 'user_id', 'analysis_type', 'market_specialization', 'image_count', 'model_used']
2025-09-28 17:24:49 | INFO     | [debug_RESULT] |     └─ session_id: str
2025-09-28 19:19:29 | INFO     | [debug_RESULT] | 💾 RESULT STORED [async_c79237becd64]
2025-09-28 19:19:29 | INFO     | [debug_RESULT] |    └─ Keys: ['success', 'data', 'metadata', 'session_id']
2025-09-28 19:19:29 | INFO     | [debug_RESULT] |    └─ Data Size: 14880 bytes
2025-09-28 19:19:29 | INFO     | [debug_RESULT] |    └─ Storage Time: 2025-09-28T19:19:29.301690
2025-09-28 19:19:29 | INFO     | [debug_RESULT] | 🔍 DATA STRUCTURE [async_c79237becd64] STORED_RESULT: ['success', 'data', 'metadata', 'session_id']
2025-09-28 19:19:29 | INFO     | [debug_RESULT] |     └─ success: bool
2025-09-28 19:19:29 | INFO     | [debug_RESULT] |     └─ data: dict with keys ['success', 'analysis_id', 'data', 'execution_time', 'metadata']
2025-09-28 19:19:29 | INFO     | [debug_RESULT] |     └─ metadata: dict with keys ['execution_time', 'user_id', 'analysis_type', 'market_specialization', 'image_count', 'model_used']
2025-09-28 19:19:29 | INFO     | [debug_RESULT] |     └─ session_id: str
2025-09-28 19:19:29 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 19:19:29 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 19:19:29 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 19:19:29 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_c79237becd64
2025-09-28 19:19:29 | INFO     | [debug_RESULT] | ℹ️ Active tasks: []
2025-09-28 19:19:29 | INFO     | [debug_RESULT] | ℹ️ Cached results: ['async_c79237becd64']
2025-09-28 19:19:29 | INFO     | [debug_RESULT] | ✅ RESULT FOUND [async_c79237becd64]: keys=['success', 'data', 'metadata', 'session_id']
2025-09-28 19:19:29 | INFO     | [debug_RESULT] | 🔍 DATA STRUCTURE [async_c79237becd64] RETRIEVED_RESULT: ['success', 'data', 'metadata', 'session_id']
2025-09-28 19:19:29 | INFO     | [debug_RESULT] |     └─ success: bool
2025-09-28 19:19:29 | INFO     | [debug_RESULT] |     └─ data: dict with keys ['success', 'analysis_id', 'data', 'execution_time', 'metadata']
2025-09-28 19:19:29 | INFO     | [debug_RESULT] |     └─ metadata: dict with keys ['execution_time', 'user_id', 'analysis_type', 'market_specialization', 'image_count', 'model_used']
2025-09-28 19:19:29 | INFO     | [debug_RESULT] |     └─ session_id: str
2025-09-28 19:19:29 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=completed, has_data=True
2025-09-28 19:19:29 | INFO     | [debug_RESULT] | ℹ️ Response data keys: ['success', 'analysis_id', 'data', 'execution_time', 'metadata']
2025-09-28 19:48:18 | INFO     | [debug_RESULT] | 💾 RESULT STORED [async_d4edc02a7fcf]
2025-09-28 19:48:18 | INFO     | [debug_RESULT] |    └─ Keys: ['success', 'data', 'metadata', 'session_id']
2025-09-28 19:48:18 | INFO     | [debug_RESULT] |    └─ Data Size: 13853 bytes
2025-09-28 19:48:18 | INFO     | [debug_RESULT] |    └─ Storage Time: 2025-09-28T19:48:18.391413
2025-09-28 19:48:18 | INFO     | [debug_RESULT] | 🔍 DATA STRUCTURE [async_d4edc02a7fcf] STORED_RESULT: ['success', 'data', 'metadata', 'session_id']
2025-09-28 19:48:18 | INFO     | [debug_RESULT] |     └─ success: bool
2025-09-28 19:48:18 | INFO     | [debug_RESULT] |     └─ data: dict with keys ['success', 'analysis_id', 'data', 'execution_time', 'metadata']
2025-09-28 19:48:18 | INFO     | [debug_RESULT] |     └─ metadata: dict with keys ['execution_time', 'user_id', 'analysis_type', 'market_specialization', 'image_count', 'model_used']
2025-09-28 19:48:18 | INFO     | [debug_RESULT] |     └─ session_id: str
2025-09-28 19:48:18 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 19:48:18 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 19:48:18 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 19:48:18 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_d4edc02a7fcf
2025-09-28 19:48:18 | INFO     | [debug_RESULT] | ℹ️ Active tasks: []
2025-09-28 19:48:18 | INFO     | [debug_RESULT] | ℹ️ Cached results: ['async_d4edc02a7fcf']
2025-09-28 19:48:18 | INFO     | [debug_RESULT] | ✅ RESULT FOUND [async_d4edc02a7fcf]: keys=['success', 'data', 'metadata', 'session_id']
2025-09-28 19:48:18 | INFO     | [debug_RESULT] | 🔍 DATA STRUCTURE [async_d4edc02a7fcf] RETRIEVED_RESULT: ['success', 'data', 'metadata', 'session_id']
2025-09-28 19:48:18 | INFO     | [debug_RESULT] |     └─ success: bool
2025-09-28 19:48:18 | INFO     | [debug_RESULT] |     └─ data: dict with keys ['success', 'analysis_id', 'data', 'execution_time', 'metadata']
2025-09-28 19:48:18 | INFO     | [debug_RESULT] |     └─ metadata: dict with keys ['execution_time', 'user_id', 'analysis_type', 'market_specialization', 'image_count', 'model_used']
2025-09-28 19:48:18 | INFO     | [debug_RESULT] |     └─ session_id: str
2025-09-28 19:48:18 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=completed, has_data=True
2025-09-28 19:48:18 | INFO     | [debug_RESULT] | ℹ️ Response data keys: ['success', 'analysis_id', 'data', 'execution_time', 'metadata']
2025-09-28 20:04:24 | INFO     | [debug_RESULT] | 💾 RESULT STORED [async_aef778b959ed]
2025-09-28 20:04:24 | INFO     | [debug_RESULT] |    └─ Keys: ['success', 'data', 'metadata', 'session_id']
2025-09-28 20:04:24 | INFO     | [debug_RESULT] |    └─ Data Size: 15746 bytes
2025-09-28 20:04:24 | INFO     | [debug_RESULT] |    └─ Storage Time: 2025-09-28T20:04:24.788366
2025-09-28 20:04:24 | INFO     | [debug_RESULT] | 🔍 DATA STRUCTURE [async_aef778b959ed] STORED_RESULT: ['success', 'data', 'metadata', 'session_id']
2025-09-28 20:04:24 | INFO     | [debug_RESULT] |     └─ success: bool
2025-09-28 20:04:24 | INFO     | [debug_RESULT] |     └─ data: dict with keys ['success', 'analysis_id', 'data', 'execution_time', 'metadata']
2025-09-28 20:04:24 | INFO     | [debug_RESULT] |     └─ metadata: dict with keys ['execution_time', 'user_id', 'analysis_type', 'market_specialization', 'image_count', 'model_used']
2025-09-28 20:04:24 | INFO     | [debug_RESULT] |     └─ session_id: str
2025-09-28 20:04:25 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 20:04:25 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 20:04:25 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 20:04:25 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_aef778b959ed
2025-09-28 20:04:25 | INFO     | [debug_RESULT] | ℹ️ Active tasks: []
2025-09-28 20:04:25 | INFO     | [debug_RESULT] | ℹ️ Cached results: ['async_aef778b959ed']
2025-09-28 20:04:25 | INFO     | [debug_RESULT] | ✅ RESULT FOUND [async_aef778b959ed]: keys=['success', 'data', 'metadata', 'session_id']
2025-09-28 20:04:25 | INFO     | [debug_RESULT] | 🔍 DATA STRUCTURE [async_aef778b959ed] RETRIEVED_RESULT: ['success', 'data', 'metadata', 'session_id']
2025-09-28 20:04:25 | INFO     | [debug_RESULT] |     └─ success: bool
2025-09-28 20:04:25 | INFO     | [debug_RESULT] |     └─ data: dict with keys ['success', 'analysis_id', 'data', 'execution_time', 'metadata']
2025-09-28 20:04:25 | INFO     | [debug_RESULT] |     └─ metadata: dict with keys ['execution_time', 'user_id', 'analysis_type', 'market_specialization', 'image_count', 'model_used']
2025-09-28 20:04:25 | INFO     | [debug_RESULT] |     └─ session_id: str
2025-09-28 20:04:25 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=completed, has_data=True
2025-09-28 20:04:25 | INFO     | [debug_RESULT] | ℹ️ Response data keys: ['success', 'analysis_id', 'data', 'execution_time', 'metadata']
2025-09-28 21:12:46 | INFO     | [debug_RESULT] | 💾 RESULT STORED [async_3ec2f3de7ec2]
2025-09-28 21:12:46 | INFO     | [debug_RESULT] |    └─ Keys: ['success', 'data', 'metadata', 'session_id']
2025-09-28 21:12:46 | INFO     | [debug_RESULT] |    └─ Data Size: 11061 bytes
2025-09-28 21:12:46 | INFO     | [debug_RESULT] |    └─ Storage Time: 2025-09-28T21:12:46.346956
2025-09-28 21:12:46 | INFO     | [debug_RESULT] | 🔍 DATA STRUCTURE [async_3ec2f3de7ec2] STORED_RESULT: ['success', 'data', 'metadata', 'session_id']
2025-09-28 21:12:46 | INFO     | [debug_RESULT] |     └─ success: bool
2025-09-28 21:12:46 | INFO     | [debug_RESULT] |     └─ data: dict with keys ['success', 'analysis_id', 'data', 'execution_time', 'metadata']
2025-09-28 21:12:46 | INFO     | [debug_RESULT] |     └─ metadata: dict with keys ['execution_time', 'user_id', 'analysis_type', 'market_specialization', 'image_count', 'model_used']
2025-09-28 21:12:46 | INFO     | [debug_RESULT] |     └─ session_id: str
2025-09-28 21:12:46 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 21:12:46 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 21:12:46 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 21:12:46 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_3ec2f3de7ec2
2025-09-28 21:12:46 | INFO     | [debug_RESULT] | ℹ️ Active tasks: []
2025-09-28 21:12:46 | INFO     | [debug_RESULT] | ℹ️ Cached results: ['async_3ec2f3de7ec2']
2025-09-28 21:12:46 | INFO     | [debug_RESULT] | ✅ RESULT FOUND [async_3ec2f3de7ec2]: keys=['success', 'data', 'metadata', 'session_id']
2025-09-28 21:12:46 | INFO     | [debug_RESULT] | 🔍 DATA STRUCTURE [async_3ec2f3de7ec2] RETRIEVED_RESULT: ['success', 'data', 'metadata', 'session_id']
2025-09-28 21:12:46 | INFO     | [debug_RESULT] |     └─ success: bool
2025-09-28 21:12:46 | INFO     | [debug_RESULT] |     └─ data: dict with keys ['success', 'analysis_id', 'data', 'execution_time', 'metadata']
2025-09-28 21:12:46 | INFO     | [debug_RESULT] |     └─ metadata: dict with keys ['execution_time', 'user_id', 'analysis_type', 'market_specialization', 'image_count', 'model_used']
2025-09-28 21:12:46 | INFO     | [debug_RESULT] |     └─ session_id: str
2025-09-28 21:12:46 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=completed, has_data=True
2025-09-28 21:12:46 | INFO     | [debug_RESULT] | ℹ️ Response data keys: ['success', 'analysis_id', 'data', 'execution_time', 'metadata']
2025-09-28 21:31:33 | INFO     | [debug_RESULT] | 💾 RESULT STORED [async_3768a28457c5]
2025-09-28 21:31:33 | INFO     | [debug_RESULT] |    └─ Keys: ['success', 'data', 'metadata', 'session_id']
2025-09-28 21:31:33 | INFO     | [debug_RESULT] |    └─ Data Size: 965 bytes
2025-09-28 21:31:33 | INFO     | [debug_RESULT] |    └─ Storage Time: 2025-09-28T21:31:33.677932
2025-09-28 21:31:33 | INFO     | [debug_RESULT] | 🔍 DATA STRUCTURE [async_3768a28457c5] STORED_RESULT: ['success', 'data', 'metadata', 'session_id']
2025-09-28 21:31:33 | INFO     | [debug_RESULT] |     └─ success: bool
2025-09-28 21:31:33 | INFO     | [debug_RESULT] |     └─ data: dict with keys ['success', 'analysis_id', 'data', 'execution_time', 'metadata']
2025-09-28 21:31:33 | INFO     | [debug_RESULT] |     └─ metadata: dict with keys ['execution_time', 'user_id', 'analysis_type', 'market_specialization', 'image_count', 'model_used']
2025-09-28 21:31:33 | INFO     | [debug_RESULT] |     └─ session_id: str
2025-09-28 21:31:41 | INFO     | [debug_RESULT] | 💾 RESULT STORED [async_cb3229beba46]
2025-09-28 21:31:41 | INFO     | [debug_RESULT] |    └─ Keys: ['success', 'data', 'metadata', 'session_id']
2025-09-28 21:31:41 | INFO     | [debug_RESULT] |    └─ Data Size: 966 bytes
2025-09-28 21:31:41 | INFO     | [debug_RESULT] |    └─ Storage Time: 2025-09-28T21:31:41.916148
2025-09-28 21:31:41 | INFO     | [debug_RESULT] | 🔍 DATA STRUCTURE [async_cb3229beba46] STORED_RESULT: ['success', 'data', 'metadata', 'session_id']
2025-09-28 21:31:41 | INFO     | [debug_RESULT] |     └─ success: bool
2025-09-28 21:31:41 | INFO     | [debug_RESULT] |     └─ data: dict with keys ['success', 'analysis_id', 'data', 'execution_time', 'metadata']
2025-09-28 21:31:41 | INFO     | [debug_RESULT] |     └─ metadata: dict with keys ['execution_time', 'user_id', 'analysis_type', 'market_specialization', 'image_count', 'model_used']
2025-09-28 21:31:41 | INFO     | [debug_RESULT] |     └─ session_id: str
2025-09-28 21:31:57 | INFO     | [debug_RESULT] | 💾 RESULT STORED [async_3dd8dad43ead]
2025-09-28 21:31:57 | INFO     | [debug_RESULT] |    └─ Keys: ['success', 'data', 'metadata', 'session_id']
2025-09-28 21:31:57 | INFO     | [debug_RESULT] |    └─ Data Size: 965 bytes
2025-09-28 21:31:57 | INFO     | [debug_RESULT] |    └─ Storage Time: 2025-09-28T21:31:57.154537
2025-09-28 21:31:57 | INFO     | [debug_RESULT] | 🔍 DATA STRUCTURE [async_3dd8dad43ead] STORED_RESULT: ['success', 'data', 'metadata', 'session_id']
2025-09-28 21:31:57 | INFO     | [debug_RESULT] |     └─ success: bool
2025-09-28 21:31:57 | INFO     | [debug_RESULT] |     └─ data: dict with keys ['success', 'analysis_id', 'data', 'execution_time', 'metadata']
2025-09-28 21:31:57 | INFO     | [debug_RESULT] |     └─ metadata: dict with keys ['execution_time', 'user_id', 'analysis_type', 'market_specialization', 'image_count', 'model_used']
2025-09-28 21:31:57 | INFO     | [debug_RESULT] |     └─ session_id: str
2025-09-28 21:42:21 | INFO     | [debug_RESULT] | 💾 RESULT STORED [async_7c841c9f417c]
2025-09-28 21:42:21 | INFO     | [debug_RESULT] |    └─ Keys: ['success', 'data', 'metadata', 'session_id']
2025-09-28 21:42:21 | INFO     | [debug_RESULT] |    └─ Data Size: 964 bytes
2025-09-28 21:42:21 | INFO     | [debug_RESULT] |    └─ Storage Time: 2025-09-28T21:42:21.501017
2025-09-28 21:42:21 | INFO     | [debug_RESULT] | 🔍 DATA STRUCTURE [async_7c841c9f417c] STORED_RESULT: ['success', 'data', 'metadata', 'session_id']
2025-09-28 21:42:21 | INFO     | [debug_RESULT] |     └─ success: bool
2025-09-28 21:42:21 | INFO     | [debug_RESULT] |     └─ data: dict with keys ['success', 'analysis_id', 'data', 'execution_time', 'metadata']
2025-09-28 21:42:21 | INFO     | [debug_RESULT] |     └─ metadata: dict with keys ['execution_time', 'user_id', 'analysis_type', 'market_specialization', 'image_count', 'model_used']
2025-09-28 21:42:21 | INFO     | [debug_RESULT] |     └─ session_id: str
2025-09-28 21:42:21 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 21:42:21 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 21:42:21 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 21:42:21 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_7c841c9f417c
2025-09-28 21:42:21 | INFO     | [debug_RESULT] | ℹ️ Active tasks: []
2025-09-28 21:42:21 | INFO     | [debug_RESULT] | ℹ️ Cached results: ['async_7c841c9f417c']
2025-09-28 21:42:21 | INFO     | [debug_RESULT] | ✅ RESULT FOUND [async_7c841c9f417c]: keys=['success', 'data', 'metadata', 'session_id']
2025-09-28 21:42:21 | INFO     | [debug_RESULT] | 🔍 DATA STRUCTURE [async_7c841c9f417c] RETRIEVED_RESULT: ['success', 'data', 'metadata', 'session_id']
2025-09-28 21:42:21 | INFO     | [debug_RESULT] |     └─ success: bool
2025-09-28 21:42:21 | INFO     | [debug_RESULT] |     └─ data: dict with keys ['success', 'analysis_id', 'data', 'execution_time', 'metadata']
2025-09-28 21:42:21 | INFO     | [debug_RESULT] |     └─ metadata: dict with keys ['execution_time', 'user_id', 'analysis_type', 'market_specialization', 'image_count', 'model_used']
2025-09-28 21:42:21 | INFO     | [debug_RESULT] |     └─ session_id: str
2025-09-28 21:42:21 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=completed, has_data=True
2025-09-28 21:42:21 | INFO     | [debug_RESULT] | ℹ️ Response data keys: ['success', 'analysis_id', 'data', 'execution_time', 'metadata']
2025-09-28 21:56:15 | INFO     | [debug_RESULT] | 💾 RESULT STORED [async_aab194f6fcdf]
2025-09-28 21:56:15 | INFO     | [debug_RESULT] |    └─ Keys: ['success', 'data', 'metadata', 'session_id']
2025-09-28 21:56:15 | INFO     | [debug_RESULT] |    └─ Data Size: 965 bytes
2025-09-28 21:56:15 | INFO     | [debug_RESULT] |    └─ Storage Time: 2025-09-28T21:56:15.345082
2025-09-28 21:56:15 | INFO     | [debug_RESULT] | 🔍 DATA STRUCTURE [async_aab194f6fcdf] STORED_RESULT: ['success', 'data', 'metadata', 'session_id']
2025-09-28 21:56:15 | INFO     | [debug_RESULT] |     └─ success: bool
2025-09-28 21:56:15 | INFO     | [debug_RESULT] |     └─ data: dict with keys ['success', 'analysis_id', 'data', 'execution_time', 'metadata']
2025-09-28 21:56:15 | INFO     | [debug_RESULT] |     └─ metadata: dict with keys ['execution_time', 'user_id', 'analysis_type', 'market_specialization', 'image_count', 'model_used']
2025-09-28 21:56:15 | INFO     | [debug_RESULT] |     └─ session_id: str
2025-09-28 21:56:15 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 21:56:15 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 21:56:15 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 21:56:15 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_aab194f6fcdf
2025-09-28 21:56:15 | INFO     | [debug_RESULT] | ℹ️ Active tasks: []
2025-09-28 21:56:15 | INFO     | [debug_RESULT] | ℹ️ Cached results: ['async_aab194f6fcdf']
2025-09-28 21:56:15 | INFO     | [debug_RESULT] | ✅ RESULT FOUND [async_aab194f6fcdf]: keys=['success', 'data', 'metadata', 'session_id']
2025-09-28 21:56:15 | INFO     | [debug_RESULT] | 🔍 DATA STRUCTURE [async_aab194f6fcdf] RETRIEVED_RESULT: ['success', 'data', 'metadata', 'session_id']
2025-09-28 21:56:15 | INFO     | [debug_RESULT] |     └─ success: bool
2025-09-28 21:56:15 | INFO     | [debug_RESULT] |     └─ data: dict with keys ['success', 'analysis_id', 'data', 'execution_time', 'metadata']
2025-09-28 21:56:15 | INFO     | [debug_RESULT] |     └─ metadata: dict with keys ['execution_time', 'user_id', 'analysis_type', 'market_specialization', 'image_count', 'model_used']
2025-09-28 21:56:15 | INFO     | [debug_RESULT] |     └─ session_id: str
2025-09-28 21:56:15 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=completed, has_data=True
2025-09-28 21:56:15 | INFO     | [debug_RESULT] | ℹ️ Response data keys: ['success', 'analysis_id', 'data', 'execution_time', 'metadata']
2025-09-28 22:02:57 | INFO     | [debug_RESULT] | 💾 RESULT STORED [async_1d395ae01004]
2025-09-28 22:02:57 | INFO     | [debug_RESULT] |    └─ Keys: ['success', 'data', 'metadata', 'session_id']
2025-09-28 22:02:57 | INFO     | [debug_RESULT] |    └─ Data Size: 966 bytes
2025-09-28 22:02:57 | INFO     | [debug_RESULT] |    └─ Storage Time: 2025-09-28T22:02:57.562820
2025-09-28 22:02:57 | INFO     | [debug_RESULT] | 🔍 DATA STRUCTURE [async_1d395ae01004] STORED_RESULT: ['success', 'data', 'metadata', 'session_id']
2025-09-28 22:02:57 | INFO     | [debug_RESULT] |     └─ success: bool
2025-09-28 22:02:57 | INFO     | [debug_RESULT] |     └─ data: dict with keys ['success', 'analysis_id', 'data', 'execution_time', 'metadata']
2025-09-28 22:02:57 | INFO     | [debug_RESULT] |     └─ metadata: dict with keys ['execution_time', 'user_id', 'analysis_type', 'market_specialization', 'image_count', 'model_used']
2025-09-28 22:02:57 | INFO     | [debug_RESULT] |     └─ session_id: str
2025-09-28 22:02:57 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 22:02:57 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 22:02:57 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 22:02:57 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_1d395ae01004
2025-09-28 22:02:57 | INFO     | [debug_RESULT] | ℹ️ Active tasks: []
2025-09-28 22:02:57 | INFO     | [debug_RESULT] | ℹ️ Cached results: ['async_1d395ae01004']
2025-09-28 22:02:57 | INFO     | [debug_RESULT] | ✅ RESULT FOUND [async_1d395ae01004]: keys=['success', 'data', 'metadata', 'session_id']
2025-09-28 22:02:57 | INFO     | [debug_RESULT] | 🔍 DATA STRUCTURE [async_1d395ae01004] RETRIEVED_RESULT: ['success', 'data', 'metadata', 'session_id']
2025-09-28 22:02:57 | INFO     | [debug_RESULT] |     └─ success: bool
2025-09-28 22:02:57 | INFO     | [debug_RESULT] |     └─ data: dict with keys ['success', 'analysis_id', 'data', 'execution_time', 'metadata']
2025-09-28 22:02:57 | INFO     | [debug_RESULT] |     └─ metadata: dict with keys ['execution_time', 'user_id', 'analysis_type', 'market_specialization', 'image_count', 'model_used']
2025-09-28 22:02:57 | INFO     | [debug_RESULT] |     └─ session_id: str
2025-09-28 22:02:57 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=completed, has_data=True
2025-09-28 22:02:57 | INFO     | [debug_RESULT] | ℹ️ Response data keys: ['success', 'analysis_id', 'data', 'execution_time', 'metadata']
2025-09-28 22:03:33 | INFO     | [debug_RESULT] | 💾 RESULT STORED [async_4ba1b92a41b1]
2025-09-28 22:03:33 | INFO     | [debug_RESULT] |    └─ Keys: ['success', 'data', 'metadata', 'session_id']
2025-09-28 22:03:33 | INFO     | [debug_RESULT] |    └─ Data Size: 967 bytes
2025-09-28 22:03:33 | INFO     | [debug_RESULT] |    └─ Storage Time: 2025-09-28T22:03:33.123181
2025-09-28 22:03:33 | INFO     | [debug_RESULT] | 🔍 DATA STRUCTURE [async_4ba1b92a41b1] STORED_RESULT: ['success', 'data', 'metadata', 'session_id']
2025-09-28 22:03:33 | INFO     | [debug_RESULT] |     └─ success: bool
2025-09-28 22:03:33 | INFO     | [debug_RESULT] |     └─ data: dict with keys ['success', 'analysis_id', 'data', 'execution_time', 'metadata']
2025-09-28 22:03:33 | INFO     | [debug_RESULT] |     └─ metadata: dict with keys ['execution_time', 'user_id', 'analysis_type', 'market_specialization', 'image_count', 'model_used']
2025-09-28 22:03:33 | INFO     | [debug_RESULT] |     └─ session_id: str
2025-09-28 22:03:33 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 22:03:33 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 22:03:33 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 22:03:33 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_4ba1b92a41b1
2025-09-28 22:03:33 | INFO     | [debug_RESULT] | ℹ️ Active tasks: []
2025-09-28 22:03:33 | INFO     | [debug_RESULT] | ℹ️ Cached results: ['async_1d395ae01004', 'async_4ba1b92a41b1']
2025-09-28 22:03:33 | INFO     | [debug_RESULT] | ✅ RESULT FOUND [async_4ba1b92a41b1]: keys=['success', 'data', 'metadata', 'session_id']
2025-09-28 22:03:33 | INFO     | [debug_RESULT] | 🔍 DATA STRUCTURE [async_4ba1b92a41b1] RETRIEVED_RESULT: ['success', 'data', 'metadata', 'session_id']
2025-09-28 22:03:33 | INFO     | [debug_RESULT] |     └─ success: bool
2025-09-28 22:03:33 | INFO     | [debug_RESULT] |     └─ data: dict with keys ['success', 'analysis_id', 'data', 'execution_time', 'metadata']
2025-09-28 22:03:33 | INFO     | [debug_RESULT] |     └─ metadata: dict with keys ['execution_time', 'user_id', 'analysis_type', 'market_specialization', 'image_count', 'model_used']
2025-09-28 22:03:33 | INFO     | [debug_RESULT] |     └─ session_id: str
2025-09-28 22:03:33 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=completed, has_data=True
2025-09-28 22:03:33 | INFO     | [debug_RESULT] | ℹ️ Response data keys: ['success', 'analysis_id', 'data', 'execution_time', 'metadata']
2025-09-28 22:13:57 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 22:13:57 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 22:13:57 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 22:13:57 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_b73030fe3cb4
2025-09-28 22:13:57 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_b73030fe3cb4']
2025-09-28 22:13:57 | INFO     | [debug_RESULT] | ℹ️ Cached results: []
2025-09-28 22:13:57 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_b73030fe3cb4]
2025-09-28 22:13:57 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-28 22:14:00 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 22:14:00 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 22:14:00 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 22:14:00 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_b73030fe3cb4
2025-09-28 22:14:00 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_b73030fe3cb4']
2025-09-28 22:14:00 | INFO     | [debug_RESULT] | ℹ️ Cached results: []
2025-09-28 22:14:00 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_b73030fe3cb4]
2025-09-28 22:14:00 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-28 22:14:03 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 22:14:03 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 22:14:03 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 22:14:03 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_b73030fe3cb4
2025-09-28 22:14:03 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_b73030fe3cb4']
2025-09-28 22:14:03 | INFO     | [debug_RESULT] | ℹ️ Cached results: []
2025-09-28 22:14:03 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_b73030fe3cb4]
2025-09-28 22:14:03 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-28 22:14:06 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 22:14:06 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 22:14:06 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 22:14:06 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_b73030fe3cb4
2025-09-28 22:14:06 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_b73030fe3cb4']
2025-09-28 22:14:06 | INFO     | [debug_RESULT] | ℹ️ Cached results: []
2025-09-28 22:14:06 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_b73030fe3cb4]
2025-09-28 22:14:06 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-28 22:14:09 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 22:14:09 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 22:14:09 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 22:14:09 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_b73030fe3cb4
2025-09-28 22:14:09 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_b73030fe3cb4']
2025-09-28 22:14:09 | INFO     | [debug_RESULT] | ℹ️ Cached results: []
2025-09-28 22:14:09 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_b73030fe3cb4]
2025-09-28 22:14:09 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-28 22:14:12 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 22:14:12 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 22:14:12 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 22:14:12 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_b73030fe3cb4
2025-09-28 22:14:12 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_b73030fe3cb4']
2025-09-28 22:14:12 | INFO     | [debug_RESULT] | ℹ️ Cached results: []
2025-09-28 22:14:12 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_b73030fe3cb4]
2025-09-28 22:14:12 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-28 22:14:15 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 22:14:15 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 22:14:15 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 22:14:15 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_b73030fe3cb4
2025-09-28 22:14:15 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_b73030fe3cb4']
2025-09-28 22:14:15 | INFO     | [debug_RESULT] | ℹ️ Cached results: []
2025-09-28 22:14:15 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_b73030fe3cb4]
2025-09-28 22:14:15 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-28 22:14:18 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 22:14:18 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 22:14:18 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 22:14:18 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_b73030fe3cb4
2025-09-28 22:14:18 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_b73030fe3cb4']
2025-09-28 22:14:18 | INFO     | [debug_RESULT] | ℹ️ Cached results: []
2025-09-28 22:14:18 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_b73030fe3cb4]
2025-09-28 22:14:18 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-28 22:14:21 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 22:14:21 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 22:14:21 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 22:14:21 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_b73030fe3cb4
2025-09-28 22:14:21 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_b73030fe3cb4']
2025-09-28 22:14:21 | INFO     | [debug_RESULT] | ℹ️ Cached results: []
2025-09-28 22:14:21 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_b73030fe3cb4]
2025-09-28 22:14:21 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-28 22:14:24 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 22:14:24 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 22:14:24 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 22:14:24 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_b73030fe3cb4
2025-09-28 22:14:24 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_b73030fe3cb4']
2025-09-28 22:14:24 | INFO     | [debug_RESULT] | ℹ️ Cached results: []
2025-09-28 22:14:24 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_b73030fe3cb4]
2025-09-28 22:14:24 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-28 22:14:27 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 22:14:27 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 22:14:27 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 22:14:27 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_b73030fe3cb4
2025-09-28 22:14:27 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_b73030fe3cb4']
2025-09-28 22:14:27 | INFO     | [debug_RESULT] | ℹ️ Cached results: []
2025-09-28 22:14:27 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_b73030fe3cb4]
2025-09-28 22:14:27 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-28 22:14:30 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 22:14:30 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 22:14:30 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 22:14:30 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_b73030fe3cb4
2025-09-28 22:14:30 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_b73030fe3cb4']
2025-09-28 22:14:30 | INFO     | [debug_RESULT] | ℹ️ Cached results: []
2025-09-28 22:14:30 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_b73030fe3cb4]
2025-09-28 22:14:30 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-28 22:14:33 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 22:14:33 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 22:14:33 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 22:14:33 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_b73030fe3cb4
2025-09-28 22:14:33 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_b73030fe3cb4']
2025-09-28 22:14:33 | INFO     | [debug_RESULT] | ℹ️ Cached results: []
2025-09-28 22:14:33 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_b73030fe3cb4]
2025-09-28 22:14:33 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-28 22:14:37 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 22:14:37 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 22:14:37 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 22:14:37 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_b73030fe3cb4
2025-09-28 22:14:37 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_b73030fe3cb4']
2025-09-28 22:14:37 | INFO     | [debug_RESULT] | ℹ️ Cached results: []
2025-09-28 22:14:37 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_b73030fe3cb4]
2025-09-28 22:14:37 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-28 22:14:40 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 22:14:40 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 22:14:40 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 22:14:40 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_b73030fe3cb4
2025-09-28 22:14:40 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_b73030fe3cb4']
2025-09-28 22:14:40 | INFO     | [debug_RESULT] | ℹ️ Cached results: []
2025-09-28 22:14:40 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_b73030fe3cb4]
2025-09-28 22:14:40 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-28 22:14:43 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 22:14:43 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 22:14:43 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 22:14:43 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_b73030fe3cb4
2025-09-28 22:14:43 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_b73030fe3cb4']
2025-09-28 22:14:43 | INFO     | [debug_RESULT] | ℹ️ Cached results: []
2025-09-28 22:14:43 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_b73030fe3cb4]
2025-09-28 22:14:43 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-28 22:14:46 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 22:14:46 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 22:14:46 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 22:14:46 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_b73030fe3cb4
2025-09-28 22:14:46 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_b73030fe3cb4']
2025-09-28 22:14:46 | INFO     | [debug_RESULT] | ℹ️ Cached results: []
2025-09-28 22:14:46 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_b73030fe3cb4]
2025-09-28 22:14:46 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-28 22:14:49 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 22:14:49 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 22:14:49 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 22:14:49 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_b73030fe3cb4
2025-09-28 22:14:49 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_b73030fe3cb4']
2025-09-28 22:14:49 | INFO     | [debug_RESULT] | ℹ️ Cached results: []
2025-09-28 22:14:49 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_b73030fe3cb4]
2025-09-28 22:14:49 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-28 22:14:52 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 22:14:52 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 22:14:52 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 22:14:52 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_b73030fe3cb4
2025-09-28 22:14:52 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_b73030fe3cb4']
2025-09-28 22:14:52 | INFO     | [debug_RESULT] | ℹ️ Cached results: []
2025-09-28 22:14:52 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_b73030fe3cb4]
2025-09-28 22:14:52 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-28 22:14:56 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 22:14:56 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 22:14:56 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 22:14:56 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_b73030fe3cb4
2025-09-28 22:14:56 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_b73030fe3cb4']
2025-09-28 22:14:56 | INFO     | [debug_RESULT] | ℹ️ Cached results: []
2025-09-28 22:14:56 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_b73030fe3cb4]
2025-09-28 22:14:56 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-28 22:14:59 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 22:14:59 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 22:14:59 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 22:14:59 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_b73030fe3cb4
2025-09-28 22:14:59 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_b73030fe3cb4']
2025-09-28 22:14:59 | INFO     | [debug_RESULT] | ℹ️ Cached results: []
2025-09-28 22:14:59 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_b73030fe3cb4]
2025-09-28 22:14:59 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-28 22:15:02 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 22:15:02 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 22:15:02 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 22:15:02 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_b73030fe3cb4
2025-09-28 22:15:02 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_b73030fe3cb4']
2025-09-28 22:15:02 | INFO     | [debug_RESULT] | ℹ️ Cached results: []
2025-09-28 22:15:02 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_b73030fe3cb4]
2025-09-28 22:15:02 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-28 22:15:05 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 22:15:05 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 22:15:05 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 22:15:05 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_b73030fe3cb4
2025-09-28 22:15:05 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_b73030fe3cb4']
2025-09-28 22:15:05 | INFO     | [debug_RESULT] | ℹ️ Cached results: []
2025-09-28 22:15:05 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_b73030fe3cb4]
2025-09-28 22:15:05 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-28 22:15:08 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 22:15:08 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 22:15:08 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 22:15:08 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_b73030fe3cb4
2025-09-28 22:15:08 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_b73030fe3cb4']
2025-09-28 22:15:08 | INFO     | [debug_RESULT] | ℹ️ Cached results: []
2025-09-28 22:15:08 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_b73030fe3cb4]
2025-09-28 22:15:08 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-28 22:15:11 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 22:15:11 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 22:15:11 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 22:15:11 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_b73030fe3cb4
2025-09-28 22:15:11 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_b73030fe3cb4']
2025-09-28 22:15:11 | INFO     | [debug_RESULT] | ℹ️ Cached results: []
2025-09-28 22:15:11 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_b73030fe3cb4]
2025-09-28 22:15:11 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-28 22:15:14 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 22:15:14 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 22:15:14 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 22:15:14 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_b73030fe3cb4
2025-09-28 22:15:14 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_b73030fe3cb4']
2025-09-28 22:15:14 | INFO     | [debug_RESULT] | ℹ️ Cached results: []
2025-09-28 22:15:14 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_b73030fe3cb4]
2025-09-28 22:15:14 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-28 22:15:17 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 22:15:17 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 22:15:17 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 22:15:17 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_b73030fe3cb4
2025-09-28 22:15:17 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_b73030fe3cb4']
2025-09-28 22:15:17 | INFO     | [debug_RESULT] | ℹ️ Cached results: []
2025-09-28 22:15:17 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_b73030fe3cb4]
2025-09-28 22:15:17 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-28 22:15:20 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 22:15:20 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 22:15:20 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 22:15:20 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_b73030fe3cb4
2025-09-28 22:15:20 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_b73030fe3cb4']
2025-09-28 22:15:20 | INFO     | [debug_RESULT] | ℹ️ Cached results: []
2025-09-28 22:15:20 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_b73030fe3cb4]
2025-09-28 22:15:20 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-28 22:15:23 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 22:15:23 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 22:15:23 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 22:15:23 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_b73030fe3cb4
2025-09-28 22:15:23 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_b73030fe3cb4']
2025-09-28 22:15:23 | INFO     | [debug_RESULT] | ℹ️ Cached results: []
2025-09-28 22:15:23 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_b73030fe3cb4]
2025-09-28 22:15:23 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-28 22:15:26 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 22:15:26 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 22:15:26 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 22:15:26 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_b73030fe3cb4
2025-09-28 22:15:26 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_b73030fe3cb4']
2025-09-28 22:15:26 | INFO     | [debug_RESULT] | ℹ️ Cached results: []
2025-09-28 22:15:26 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_b73030fe3cb4]
2025-09-28 22:15:26 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-28 22:15:29 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 22:15:29 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 22:15:29 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 22:15:29 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_b73030fe3cb4
2025-09-28 22:15:29 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_b73030fe3cb4']
2025-09-28 22:15:29 | INFO     | [debug_RESULT] | ℹ️ Cached results: []
2025-09-28 22:15:29 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_b73030fe3cb4]
2025-09-28 22:15:29 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-28 22:15:32 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 22:15:32 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 22:15:32 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 22:15:32 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_b73030fe3cb4
2025-09-28 22:15:32 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_b73030fe3cb4']
2025-09-28 22:15:32 | INFO     | [debug_RESULT] | ℹ️ Cached results: []
2025-09-28 22:15:32 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_b73030fe3cb4]
2025-09-28 22:15:32 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-28 22:15:35 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 22:15:35 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 22:15:35 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 22:15:35 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_b73030fe3cb4
2025-09-28 22:15:35 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_b73030fe3cb4']
2025-09-28 22:15:35 | INFO     | [debug_RESULT] | ℹ️ Cached results: []
2025-09-28 22:15:35 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_b73030fe3cb4]
2025-09-28 22:15:35 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-28 22:15:38 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 22:15:38 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 22:15:38 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 22:15:38 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_b73030fe3cb4
2025-09-28 22:15:38 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_b73030fe3cb4']
2025-09-28 22:15:38 | INFO     | [debug_RESULT] | ℹ️ Cached results: []
2025-09-28 22:15:38 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_b73030fe3cb4]
2025-09-28 22:15:38 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-28 22:15:42 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 22:15:42 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 22:15:42 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 22:15:42 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_b73030fe3cb4
2025-09-28 22:15:42 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_b73030fe3cb4']
2025-09-28 22:15:42 | INFO     | [debug_RESULT] | ℹ️ Cached results: []
2025-09-28 22:15:42 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_b73030fe3cb4]
2025-09-28 22:15:42 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-28 22:15:45 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 22:15:45 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 22:15:45 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 22:15:45 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_b73030fe3cb4
2025-09-28 22:15:45 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_b73030fe3cb4']
2025-09-28 22:15:45 | INFO     | [debug_RESULT] | ℹ️ Cached results: []
2025-09-28 22:15:45 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_b73030fe3cb4]
2025-09-28 22:15:45 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-28 22:15:48 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 22:15:48 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 22:15:48 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 22:15:48 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_b73030fe3cb4
2025-09-28 22:15:48 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_b73030fe3cb4']
2025-09-28 22:15:48 | INFO     | [debug_RESULT] | ℹ️ Cached results: []
2025-09-28 22:15:48 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_b73030fe3cb4]
2025-09-28 22:15:48 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-28 22:15:51 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 22:15:51 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 22:15:51 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 22:15:51 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_b73030fe3cb4
2025-09-28 22:15:51 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_b73030fe3cb4']
2025-09-28 22:15:51 | INFO     | [debug_RESULT] | ℹ️ Cached results: []
2025-09-28 22:15:51 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_b73030fe3cb4]
2025-09-28 22:15:51 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-28 22:15:54 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 22:15:54 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 22:15:54 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 22:15:54 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_b73030fe3cb4
2025-09-28 22:15:54 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_b73030fe3cb4']
2025-09-28 22:15:54 | INFO     | [debug_RESULT] | ℹ️ Cached results: []
2025-09-28 22:15:54 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_b73030fe3cb4]
2025-09-28 22:15:54 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-28 22:15:57 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 22:15:57 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 22:15:57 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 22:15:57 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_b73030fe3cb4
2025-09-28 22:15:57 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_b73030fe3cb4']
2025-09-28 22:15:57 | INFO     | [debug_RESULT] | ℹ️ Cached results: []
2025-09-28 22:15:57 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_b73030fe3cb4]
2025-09-28 22:15:57 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-28 22:16:00 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 22:16:00 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 22:16:00 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 22:16:00 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_b73030fe3cb4
2025-09-28 22:16:00 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_b73030fe3cb4']
2025-09-28 22:16:00 | INFO     | [debug_RESULT] | ℹ️ Cached results: []
2025-09-28 22:16:00 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_b73030fe3cb4]
2025-09-28 22:16:00 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-28 22:16:03 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 22:16:03 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 22:16:03 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 22:16:03 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_b73030fe3cb4
2025-09-28 22:16:03 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_b73030fe3cb4']
2025-09-28 22:16:03 | INFO     | [debug_RESULT] | ℹ️ Cached results: []
2025-09-28 22:16:03 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_b73030fe3cb4]
2025-09-28 22:16:03 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-28 22:16:06 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 22:16:06 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 22:16:06 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 22:16:06 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_b73030fe3cb4
2025-09-28 22:16:06 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_b73030fe3cb4']
2025-09-28 22:16:06 | INFO     | [debug_RESULT] | ℹ️ Cached results: []
2025-09-28 22:16:06 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_b73030fe3cb4]
2025-09-28 22:16:06 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-28 22:16:09 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 22:16:09 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 22:16:09 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 22:16:09 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_b73030fe3cb4
2025-09-28 22:16:09 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_b73030fe3cb4']
2025-09-28 22:16:09 | INFO     | [debug_RESULT] | ℹ️ Cached results: []
2025-09-28 22:16:09 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_b73030fe3cb4]
2025-09-28 22:16:09 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-28 22:16:12 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 22:16:12 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 22:16:12 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 22:16:12 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_b73030fe3cb4
2025-09-28 22:16:12 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_b73030fe3cb4']
2025-09-28 22:16:12 | INFO     | [debug_RESULT] | ℹ️ Cached results: []
2025-09-28 22:16:12 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_b73030fe3cb4]
2025-09-28 22:16:12 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-28 22:16:15 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 22:16:15 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 22:16:15 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 22:16:15 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_b73030fe3cb4
2025-09-28 22:16:15 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_b73030fe3cb4']
2025-09-28 22:16:15 | INFO     | [debug_RESULT] | ℹ️ Cached results: []
2025-09-28 22:16:15 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_b73030fe3cb4]
2025-09-28 22:16:15 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-28 22:16:18 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 22:16:18 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 22:16:18 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 22:16:18 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_b73030fe3cb4
2025-09-28 22:16:18 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_b73030fe3cb4']
2025-09-28 22:16:18 | INFO     | [debug_RESULT] | ℹ️ Cached results: []
2025-09-28 22:16:18 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_b73030fe3cb4]
2025-09-28 22:16:18 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-28 22:16:21 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 22:16:21 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 22:16:21 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 22:16:21 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_b73030fe3cb4
2025-09-28 22:16:21 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_b73030fe3cb4']
2025-09-28 22:16:21 | INFO     | [debug_RESULT] | ℹ️ Cached results: []
2025-09-28 22:16:21 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_b73030fe3cb4]
2025-09-28 22:16:21 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-28 22:16:24 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 22:16:24 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 22:16:24 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 22:16:24 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_b73030fe3cb4
2025-09-28 22:16:24 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_b73030fe3cb4']
2025-09-28 22:16:24 | INFO     | [debug_RESULT] | ℹ️ Cached results: []
2025-09-28 22:16:24 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_b73030fe3cb4]
2025-09-28 22:16:24 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-28 22:16:27 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 22:16:27 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 22:16:27 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 22:16:27 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_b73030fe3cb4
2025-09-28 22:16:27 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_b73030fe3cb4']
2025-09-28 22:16:27 | INFO     | [debug_RESULT] | ℹ️ Cached results: []
2025-09-28 22:16:27 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_b73030fe3cb4]
2025-09-28 22:16:27 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-28 22:16:30 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 22:16:30 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 22:16:30 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 22:16:30 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_b73030fe3cb4
2025-09-28 22:16:30 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_b73030fe3cb4']
2025-09-28 22:16:30 | INFO     | [debug_RESULT] | ℹ️ Cached results: []
2025-09-28 22:16:30 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_b73030fe3cb4]
2025-09-28 22:16:30 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-28 22:16:34 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 22:16:34 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 22:16:34 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 22:16:34 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_b73030fe3cb4
2025-09-28 22:16:34 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_b73030fe3cb4']
2025-09-28 22:16:34 | INFO     | [debug_RESULT] | ℹ️ Cached results: []
2025-09-28 22:16:34 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_b73030fe3cb4]
2025-09-28 22:16:34 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-28 22:16:37 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 22:16:37 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 22:16:37 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 22:16:37 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_b73030fe3cb4
2025-09-28 22:16:37 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_b73030fe3cb4']
2025-09-28 22:16:37 | INFO     | [debug_RESULT] | ℹ️ Cached results: []
2025-09-28 22:16:37 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_b73030fe3cb4]
2025-09-28 22:16:37 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-28 22:16:38 | INFO     | [debug_RESULT] | 💾 RESULT STORED [async_b73030fe3cb4]
2025-09-28 22:16:38 | INFO     | [debug_RESULT] |    └─ Keys: ['success', 'data', 'metadata', 'session_id']
2025-09-28 22:16:38 | INFO     | [debug_RESULT] |    └─ Data Size: 14264 bytes
2025-09-28 22:16:38 | INFO     | [debug_RESULT] |    └─ Storage Time: 2025-09-28T22:16:38.399229
2025-09-28 22:16:38 | INFO     | [debug_RESULT] | 🔍 DATA STRUCTURE [async_b73030fe3cb4] STORED_RESULT: ['success', 'data', 'metadata', 'session_id']
2025-09-28 22:16:38 | INFO     | [debug_RESULT] |     └─ success: bool
2025-09-28 22:16:38 | INFO     | [debug_RESULT] |     └─ data: dict with keys ['success', 'analysis_id', 'data', 'execution_time', 'metadata']
2025-09-28 22:16:38 | INFO     | [debug_RESULT] |     └─ metadata: dict with keys ['execution_time', 'user_id', 'analysis_type', 'market_specialization', 'image_count', 'model_used']
2025-09-28 22:16:38 | INFO     | [debug_RESULT] |     └─ session_id: str
2025-09-28 22:16:40 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 22:16:40 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 22:16:40 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 22:16:40 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_b73030fe3cb4
2025-09-28 22:16:40 | INFO     | [debug_RESULT] | ℹ️ Active tasks: []
2025-09-28 22:16:40 | INFO     | [debug_RESULT] | ℹ️ Cached results: ['async_b73030fe3cb4']
2025-09-28 22:16:40 | INFO     | [debug_RESULT] | ✅ RESULT FOUND [async_b73030fe3cb4]: keys=['success', 'data', 'metadata', 'session_id']
2025-09-28 22:16:40 | INFO     | [debug_RESULT] | 🔍 DATA STRUCTURE [async_b73030fe3cb4] RETRIEVED_RESULT: ['success', 'data', 'metadata', 'session_id']
2025-09-28 22:16:40 | INFO     | [debug_RESULT] |     └─ success: bool
2025-09-28 22:16:40 | INFO     | [debug_RESULT] |     └─ data: dict with keys ['success', 'analysis_id', 'data', 'execution_time', 'metadata']
2025-09-28 22:16:40 | INFO     | [debug_RESULT] |     └─ metadata: dict with keys ['execution_time', 'user_id', 'analysis_type', 'market_specialization', 'image_count', 'model_used']
2025-09-28 22:16:40 | INFO     | [debug_RESULT] |     └─ session_id: str
2025-09-28 22:16:40 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=completed, has_data=True
2025-09-28 22:16:40 | INFO     | [debug_RESULT] | ℹ️ Response data keys: ['success', 'analysis_id', 'data', 'execution_time', 'metadata']
2025-09-28 22:21:50 | INFO     | [debug_RESULT] | 💾 RESULT STORED [async_61ec654fbe9c]
2025-09-28 22:21:50 | INFO     | [debug_RESULT] |    └─ Keys: ['success', 'data', 'metadata', 'session_id']
2025-09-28 22:21:50 | INFO     | [debug_RESULT] |    └─ Data Size: 8276 bytes
2025-09-28 22:21:50 | INFO     | [debug_RESULT] |    └─ Storage Time: 2025-09-28T22:21:50.919569
2025-09-28 22:21:50 | INFO     | [debug_RESULT] | 🔍 DATA STRUCTURE [async_61ec654fbe9c] STORED_RESULT: ['success', 'data', 'metadata', 'session_id']
2025-09-28 22:21:50 | INFO     | [debug_RESULT] |     └─ success: bool
2025-09-28 22:21:50 | INFO     | [debug_RESULT] |     └─ data: dict with keys ['success', 'analysis_id', 'data', 'execution_time', 'metadata']
2025-09-28 22:21:50 | INFO     | [debug_RESULT] |     └─ metadata: dict with keys ['execution_time', 'user_id', 'analysis_type', 'market_specialization', 'image_count', 'model_used']
2025-09-28 22:21:50 | INFO     | [debug_RESULT] |     └─ session_id: str
2025-09-28 22:21:51 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 22:21:51 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 22:21:51 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 22:21:51 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_61ec654fbe9c
2025-09-28 22:21:51 | INFO     | [debug_RESULT] | ℹ️ Active tasks: []
2025-09-28 22:21:51 | INFO     | [debug_RESULT] | ℹ️ Cached results: ['async_b73030fe3cb4', 'async_61ec654fbe9c']
2025-09-28 22:21:51 | INFO     | [debug_RESULT] | ✅ RESULT FOUND [async_61ec654fbe9c]: keys=['success', 'data', 'metadata', 'session_id']
2025-09-28 22:21:51 | INFO     | [debug_RESULT] | 🔍 DATA STRUCTURE [async_61ec654fbe9c] RETRIEVED_RESULT: ['success', 'data', 'metadata', 'session_id']
2025-09-28 22:21:51 | INFO     | [debug_RESULT] |     └─ success: bool
2025-09-28 22:21:51 | INFO     | [debug_RESULT] |     └─ data: dict with keys ['success', 'analysis_id', 'data', 'execution_time', 'metadata']
2025-09-28 22:21:51 | INFO     | [debug_RESULT] |     └─ metadata: dict with keys ['execution_time', 'user_id', 'analysis_type', 'market_specialization', 'image_count', 'model_used']
2025-09-28 22:21:51 | INFO     | [debug_RESULT] |     └─ session_id: str
2025-09-28 22:21:51 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=completed, has_data=True
2025-09-28 22:21:51 | INFO     | [debug_RESULT] | ℹ️ Response data keys: ['success', 'analysis_id', 'data', 'execution_time', 'metadata']
2025-09-28 22:36:23 | INFO     | [debug_RESULT] | 💾 RESULT STORED [async_3ab985c5aaa2]
2025-09-28 22:36:23 | INFO     | [debug_RESULT] |    └─ Keys: ['success', 'data', 'metadata', 'session_id']
2025-09-28 22:36:23 | INFO     | [debug_RESULT] |    └─ Data Size: 9009 bytes
2025-09-28 22:36:23 | INFO     | [debug_RESULT] |    └─ Storage Time: 2025-09-28T22:36:23.962813
2025-09-28 22:36:23 | INFO     | [debug_RESULT] | 🔍 DATA STRUCTURE [async_3ab985c5aaa2] STORED_RESULT: ['success', 'data', 'metadata', 'session_id']
2025-09-28 22:36:23 | INFO     | [debug_RESULT] |     └─ success: bool
2025-09-28 22:36:23 | INFO     | [debug_RESULT] |     └─ data: dict with keys ['success', 'analysis_id', 'data', 'execution_time', 'metadata']
2025-09-28 22:36:23 | INFO     | [debug_RESULT] |     └─ metadata: dict with keys ['execution_time', 'user_id', 'analysis_type', 'market_specialization', 'image_count', 'model_used']
2025-09-28 22:36:23 | INFO     | [debug_RESULT] |     └─ session_id: str
2025-09-28 22:36:24 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 22:36:24 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 22:36:24 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 22:36:24 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_3ab985c5aaa2
2025-09-28 22:36:24 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_798b1b925ad3', 'async_c05001dcdca7']
2025-09-28 22:36:24 | INFO     | [debug_RESULT] | ℹ️ Cached results: ['async_3ab985c5aaa2']
2025-09-28 22:36:24 | INFO     | [debug_RESULT] | ✅ RESULT FOUND [async_3ab985c5aaa2]: keys=['success', 'data', 'metadata', 'session_id']
2025-09-28 22:36:24 | INFO     | [debug_RESULT] | 🔍 DATA STRUCTURE [async_3ab985c5aaa2] RETRIEVED_RESULT: ['success', 'data', 'metadata', 'session_id']
2025-09-28 22:36:24 | INFO     | [debug_RESULT] |     └─ success: bool
2025-09-28 22:36:24 | INFO     | [debug_RESULT] |     └─ data: dict with keys ['success', 'analysis_id', 'data', 'execution_time', 'metadata']
2025-09-28 22:36:24 | INFO     | [debug_RESULT] |     └─ metadata: dict with keys ['execution_time', 'user_id', 'analysis_type', 'market_specialization', 'image_count', 'model_used']
2025-09-28 22:36:24 | INFO     | [debug_RESULT] |     └─ session_id: str
2025-09-28 22:36:24 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=completed, has_data=True
2025-09-28 22:36:24 | INFO     | [debug_RESULT] | ℹ️ Response data keys: ['success', 'analysis_id', 'data', 'execution_time', 'metadata']
2025-09-28 22:36:26 | INFO     | [debug_RESULT] | 💾 RESULT STORED [async_798b1b925ad3]
2025-09-28 22:36:26 | INFO     | [debug_RESULT] |    └─ Keys: ['success', 'data', 'metadata', 'session_id']
2025-09-28 22:36:26 | INFO     | [debug_RESULT] |    └─ Data Size: 10817 bytes
2025-09-28 22:36:26 | INFO     | [debug_RESULT] |    └─ Storage Time: 2025-09-28T22:36:26.383153
2025-09-28 22:36:26 | INFO     | [debug_RESULT] | 🔍 DATA STRUCTURE [async_798b1b925ad3] STORED_RESULT: ['success', 'data', 'metadata', 'session_id']
2025-09-28 22:36:26 | INFO     | [debug_RESULT] |     └─ success: bool
2025-09-28 22:36:26 | INFO     | [debug_RESULT] |     └─ data: dict with keys ['success', 'analysis_id', 'data', 'execution_time', 'metadata']
2025-09-28 22:36:26 | INFO     | [debug_RESULT] |     └─ metadata: dict with keys ['execution_time', 'user_id', 'analysis_type', 'market_specialization', 'image_count', 'model_used']
2025-09-28 22:36:26 | INFO     | [debug_RESULT] |     └─ session_id: str
2025-09-28 22:36:29 | INFO     | [debug_RESULT] | 💾 RESULT STORED [async_c05001dcdca7]
2025-09-28 22:36:29 | INFO     | [debug_RESULT] |    └─ Keys: ['success', 'data', 'metadata', 'session_id']
2025-09-28 22:36:29 | INFO     | [debug_RESULT] |    └─ Data Size: 15525 bytes
2025-09-28 22:36:29 | INFO     | [debug_RESULT] |    └─ Storage Time: 2025-09-28T22:36:29.693450
2025-09-28 22:36:29 | INFO     | [debug_RESULT] | 🔍 DATA STRUCTURE [async_c05001dcdca7] STORED_RESULT: ['success', 'data', 'metadata', 'session_id']
2025-09-28 22:36:29 | INFO     | [debug_RESULT] |     └─ success: bool
2025-09-28 22:36:29 | INFO     | [debug_RESULT] |     └─ data: dict with keys ['success', 'analysis_id', 'data', 'execution_time', 'metadata']
2025-09-28 22:36:29 | INFO     | [debug_RESULT] |     └─ metadata: dict with keys ['execution_time', 'user_id', 'analysis_type', 'market_specialization', 'image_count', 'model_used']
2025-09-28 22:36:29 | INFO     | [debug_RESULT] |     └─ session_id: str
2025-09-28 22:48:15 | INFO     | [debug_RESULT] | 💾 RESULT STORED [async_eab55a3dfa35]
2025-09-28 22:48:15 | INFO     | [debug_RESULT] |    └─ Keys: ['success', 'data', 'metadata', 'session_id']
2025-09-28 22:48:15 | INFO     | [debug_RESULT] |    └─ Data Size: 18014 bytes
2025-09-28 22:48:15 | INFO     | [debug_RESULT] |    └─ Storage Time: 2025-09-28T22:48:15.620586
2025-09-28 22:48:15 | INFO     | [debug_RESULT] | 🔍 DATA STRUCTURE [async_eab55a3dfa35] STORED_RESULT: ['success', 'data', 'metadata', 'session_id']
2025-09-28 22:48:15 | INFO     | [debug_RESULT] |     └─ success: bool
2025-09-28 22:48:15 | INFO     | [debug_RESULT] |     └─ data: dict with keys ['success', 'analysis_id', 'data', 'execution_time', 'metadata']
2025-09-28 22:48:15 | INFO     | [debug_RESULT] |     └─ metadata: dict with keys ['execution_time', 'user_id', 'analysis_type', 'market_specialization', 'image_count', 'model_used']
2025-09-28 22:48:15 | INFO     | [debug_RESULT] |     └─ session_id: str
2025-09-28 22:48:16 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 22:48:16 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 22:48:16 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 22:48:16 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_eab55a3dfa35
2025-09-28 22:48:16 | INFO     | [debug_RESULT] | ℹ️ Active tasks: []
2025-09-28 22:48:16 | INFO     | [debug_RESULT] | ℹ️ Cached results: ['async_eab55a3dfa35']
2025-09-28 22:48:16 | INFO     | [debug_RESULT] | ✅ RESULT FOUND [async_eab55a3dfa35]: keys=['success', 'data', 'metadata', 'session_id']
2025-09-28 22:48:16 | INFO     | [debug_RESULT] | 🔍 DATA STRUCTURE [async_eab55a3dfa35] RETRIEVED_RESULT: ['success', 'data', 'metadata', 'session_id']
2025-09-28 22:48:16 | INFO     | [debug_RESULT] |     └─ success: bool
2025-09-28 22:48:16 | INFO     | [debug_RESULT] |     └─ data: dict with keys ['success', 'analysis_id', 'data', 'execution_time', 'metadata']
2025-09-28 22:48:16 | INFO     | [debug_RESULT] |     └─ metadata: dict with keys ['execution_time', 'user_id', 'analysis_type', 'market_specialization', 'image_count', 'model_used']
2025-09-28 22:48:16 | INFO     | [debug_RESULT] |     └─ session_id: str
2025-09-28 22:48:16 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=completed, has_data=True
2025-09-28 22:48:16 | INFO     | [debug_RESULT] | ℹ️ Response data keys: ['success', 'analysis_id', 'data', 'execution_time', 'metadata']
2025-09-28 23:04:31 | INFO     | [debug_RESULT] | 💾 RESULT STORED [async_b683e9c7448f]
2025-09-28 23:04:31 | INFO     | [debug_RESULT] |    └─ Keys: ['success', 'data', 'metadata', 'session_id']
2025-09-28 23:04:31 | INFO     | [debug_RESULT] |    └─ Data Size: 15125 bytes
2025-09-28 23:04:31 | INFO     | [debug_RESULT] |    └─ Storage Time: 2025-09-28T23:04:31.234876
2025-09-28 23:04:31 | INFO     | [debug_RESULT] | 🔍 DATA STRUCTURE [async_b683e9c7448f] STORED_RESULT: ['success', 'data', 'metadata', 'session_id']
2025-09-28 23:04:31 | INFO     | [debug_RESULT] |     └─ success: bool
2025-09-28 23:04:31 | INFO     | [debug_RESULT] |     └─ data: dict with keys ['success', 'analysis_id', 'data', 'execution_time', 'metadata']
2025-09-28 23:04:31 | INFO     | [debug_RESULT] |     └─ metadata: dict with keys ['execution_time', 'user_id', 'analysis_type', 'market_specialization', 'image_count', 'model_used']
2025-09-28 23:04:31 | INFO     | [debug_RESULT] |     └─ session_id: str
2025-09-28 23:04:37 | INFO     | [debug_RESULT] | 💾 RESULT STORED [async_1f9e22f900ce]
2025-09-28 23:04:37 | INFO     | [debug_RESULT] |    └─ Keys: ['success', 'data', 'metadata', 'session_id']
2025-09-28 23:04:37 | INFO     | [debug_RESULT] |    └─ Data Size: 8708 bytes
2025-09-28 23:04:37 | INFO     | [debug_RESULT] |    └─ Storage Time: 2025-09-28T23:04:37.100810
2025-09-28 23:04:37 | INFO     | [debug_RESULT] | 🔍 DATA STRUCTURE [async_1f9e22f900ce] STORED_RESULT: ['success', 'data', 'metadata', 'session_id']
2025-09-28 23:04:37 | INFO     | [debug_RESULT] |     └─ success: bool
2025-09-28 23:04:37 | INFO     | [debug_RESULT] |     └─ data: dict with keys ['success', 'analysis_id', 'data', 'execution_time', 'metadata']
2025-09-28 23:04:37 | INFO     | [debug_RESULT] |     └─ metadata: dict with keys ['execution_time', 'user_id', 'analysis_type', 'market_specialization', 'image_count', 'model_used']
2025-09-28 23:04:37 | INFO     | [debug_RESULT] |     └─ session_id: str
2025-09-28 23:07:33 | INFO     | [debug_RESULT] | 💾 RESULT STORED [async_e209775adeed]
2025-09-28 23:07:33 | INFO     | [debug_RESULT] |    └─ Keys: ['success', 'data', 'metadata', 'session_id']
2025-09-28 23:07:33 | INFO     | [debug_RESULT] |    └─ Data Size: 10833 bytes
2025-09-28 23:07:33 | INFO     | [debug_RESULT] |    └─ Storage Time: 2025-09-28T23:07:33.515419
2025-09-28 23:07:33 | INFO     | [debug_RESULT] | 🔍 DATA STRUCTURE [async_e209775adeed] STORED_RESULT: ['success', 'data', 'metadata', 'session_id']
2025-09-28 23:07:33 | INFO     | [debug_RESULT] |     └─ success: bool
2025-09-28 23:07:33 | INFO     | [debug_RESULT] |     └─ data: dict with keys ['success', 'analysis_id', 'data', 'execution_time', 'metadata']
2025-09-28 23:07:33 | INFO     | [debug_RESULT] |     └─ metadata: dict with keys ['execution_time', 'user_id', 'analysis_type', 'market_specialization', 'image_count', 'model_used']
2025-09-28 23:07:33 | INFO     | [debug_RESULT] |     └─ session_id: str
2025-09-28 23:15:34 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 23:15:34 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 23:15:34 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 23:15:34 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_ca359a0f58e5
2025-09-28 23:15:34 | INFO     | [debug_RESULT] | ℹ️ Active tasks: []
2025-09-28 23:15:34 | INFO     | [debug_RESULT] | ℹ️ Cached results: ['async_ca359a0f58e5']
2025-09-28 23:15:34 | INFO     | [debug_RESULT] | ✅ RESULT FOUND [async_ca359a0f58e5]: keys=['success', 'error', 'session_id']
2025-09-28 23:15:34 | INFO     | [debug_RESULT] | 🔍 DATA STRUCTURE [async_ca359a0f58e5] RETRIEVED_RESULT: ['success', 'error', 'session_id']
2025-09-28 23:15:34 | INFO     | [debug_RESULT] |     └─ success: bool
2025-09-28 23:15:34 | INFO     | [debug_RESULT] |     └─ error: str
2025-09-28 23:15:34 | INFO     | [debug_RESULT] |     └─ session_id: str
2025-09-28 23:15:34 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=False, status=failed, has_data=False
2025-09-28 23:15:40 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 23:15:40 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 23:15:40 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 23:15:40 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_ca359a0f58e5
2025-09-28 23:15:40 | INFO     | [debug_RESULT] | ℹ️ Active tasks: []
2025-09-28 23:15:40 | INFO     | [debug_RESULT] | ℹ️ Cached results: ['async_ca359a0f58e5']
2025-09-28 23:15:40 | INFO     | [debug_RESULT] | ✅ RESULT FOUND [async_ca359a0f58e5]: keys=['success', 'error', 'session_id']
2025-09-28 23:15:40 | INFO     | [debug_RESULT] | 🔍 DATA STRUCTURE [async_ca359a0f58e5] RETRIEVED_RESULT: ['success', 'error', 'session_id']
2025-09-28 23:15:40 | INFO     | [debug_RESULT] |     └─ success: bool
2025-09-28 23:15:40 | INFO     | [debug_RESULT] |     └─ error: str
2025-09-28 23:15:40 | INFO     | [debug_RESULT] |     └─ session_id: str
2025-09-28 23:15:40 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=False, status=failed, has_data=False
2025-09-28 23:15:45 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 23:15:45 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 23:15:45 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 23:15:45 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_ca359a0f58e5
2025-09-28 23:15:45 | INFO     | [debug_RESULT] | ℹ️ Active tasks: []
2025-09-28 23:15:45 | INFO     | [debug_RESULT] | ℹ️ Cached results: ['async_ca359a0f58e5']
2025-09-28 23:15:45 | INFO     | [debug_RESULT] | ✅ RESULT FOUND [async_ca359a0f58e5]: keys=['success', 'error', 'session_id']
2025-09-28 23:15:45 | INFO     | [debug_RESULT] | 🔍 DATA STRUCTURE [async_ca359a0f58e5] RETRIEVED_RESULT: ['success', 'error', 'session_id']
2025-09-28 23:15:45 | INFO     | [debug_RESULT] |     └─ success: bool
2025-09-28 23:15:45 | INFO     | [debug_RESULT] |     └─ error: str
2025-09-28 23:15:45 | INFO     | [debug_RESULT] |     └─ session_id: str
2025-09-28 23:15:45 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=False, status=failed, has_data=False
2025-09-28 23:15:50 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 23:15:50 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 23:15:50 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 23:15:50 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_ca359a0f58e5
2025-09-28 23:15:50 | INFO     | [debug_RESULT] | ℹ️ Active tasks: []
2025-09-28 23:15:50 | INFO     | [debug_RESULT] | ℹ️ Cached results: ['async_ca359a0f58e5']
2025-09-28 23:15:50 | INFO     | [debug_RESULT] | ✅ RESULT FOUND [async_ca359a0f58e5]: keys=['success', 'error', 'session_id']
2025-09-28 23:15:50 | INFO     | [debug_RESULT] | 🔍 DATA STRUCTURE [async_ca359a0f58e5] RETRIEVED_RESULT: ['success', 'error', 'session_id']
2025-09-28 23:15:50 | INFO     | [debug_RESULT] |     └─ success: bool
2025-09-28 23:15:50 | INFO     | [debug_RESULT] |     └─ error: str
2025-09-28 23:15:50 | INFO     | [debug_RESULT] |     └─ session_id: str
2025-09-28 23:15:50 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=False, status=failed, has_data=False
2025-09-28 23:15:55 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 23:15:55 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 23:15:55 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 23:15:55 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_ca359a0f58e5
2025-09-28 23:15:55 | INFO     | [debug_RESULT] | ℹ️ Active tasks: []
2025-09-28 23:15:55 | INFO     | [debug_RESULT] | ℹ️ Cached results: ['async_ca359a0f58e5']
2025-09-28 23:15:55 | INFO     | [debug_RESULT] | ✅ RESULT FOUND [async_ca359a0f58e5]: keys=['success', 'error', 'session_id']
2025-09-28 23:15:55 | INFO     | [debug_RESULT] | 🔍 DATA STRUCTURE [async_ca359a0f58e5] RETRIEVED_RESULT: ['success', 'error', 'session_id']
2025-09-28 23:15:55 | INFO     | [debug_RESULT] |     └─ success: bool
2025-09-28 23:15:55 | INFO     | [debug_RESULT] |     └─ error: str
2025-09-28 23:15:55 | INFO     | [debug_RESULT] |     └─ session_id: str
2025-09-28 23:15:55 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=False, status=failed, has_data=False
2025-09-28 23:16:00 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 23:16:00 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 23:16:00 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 23:16:00 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_ca359a0f58e5
2025-09-28 23:16:00 | INFO     | [debug_RESULT] | ℹ️ Active tasks: []
2025-09-28 23:16:00 | INFO     | [debug_RESULT] | ℹ️ Cached results: ['async_ca359a0f58e5']
2025-09-28 23:16:00 | INFO     | [debug_RESULT] | ✅ RESULT FOUND [async_ca359a0f58e5]: keys=['success', 'error', 'session_id']
2025-09-28 23:16:00 | INFO     | [debug_RESULT] | 🔍 DATA STRUCTURE [async_ca359a0f58e5] RETRIEVED_RESULT: ['success', 'error', 'session_id']
2025-09-28 23:16:00 | INFO     | [debug_RESULT] |     └─ success: bool
2025-09-28 23:16:00 | INFO     | [debug_RESULT] |     └─ error: str
2025-09-28 23:16:00 | INFO     | [debug_RESULT] |     └─ session_id: str
2025-09-28 23:16:00 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=False, status=failed, has_data=False
2025-09-28 23:16:08 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 23:16:08 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 23:16:08 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 23:16:08 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_ca359a0f58e5
2025-09-28 23:16:08 | INFO     | [debug_RESULT] | ℹ️ Active tasks: []
2025-09-28 23:16:08 | INFO     | [debug_RESULT] | ℹ️ Cached results: ['async_ca359a0f58e5']
2025-09-28 23:16:08 | INFO     | [debug_RESULT] | ✅ RESULT FOUND [async_ca359a0f58e5]: keys=['success', 'error', 'session_id']
2025-09-28 23:16:08 | INFO     | [debug_RESULT] | 🔍 DATA STRUCTURE [async_ca359a0f58e5] RETRIEVED_RESULT: ['success', 'error', 'session_id']
2025-09-28 23:16:08 | INFO     | [debug_RESULT] |     └─ success: bool
2025-09-28 23:16:08 | INFO     | [debug_RESULT] |     └─ error: str
2025-09-28 23:16:08 | INFO     | [debug_RESULT] |     └─ session_id: str
2025-09-28 23:16:08 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=False, status=failed, has_data=False
2025-09-28 23:16:13 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 23:16:13 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 23:16:13 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 23:16:13 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_ca359a0f58e5
2025-09-28 23:16:13 | INFO     | [debug_RESULT] | ℹ️ Active tasks: []
2025-09-28 23:16:13 | INFO     | [debug_RESULT] | ℹ️ Cached results: ['async_ca359a0f58e5']
2025-09-28 23:16:13 | INFO     | [debug_RESULT] | ✅ RESULT FOUND [async_ca359a0f58e5]: keys=['success', 'error', 'session_id']
2025-09-28 23:16:13 | INFO     | [debug_RESULT] | 🔍 DATA STRUCTURE [async_ca359a0f58e5] RETRIEVED_RESULT: ['success', 'error', 'session_id']
2025-09-28 23:16:13 | INFO     | [debug_RESULT] |     └─ success: bool
2025-09-28 23:16:13 | INFO     | [debug_RESULT] |     └─ error: str
2025-09-28 23:16:13 | INFO     | [debug_RESULT] |     └─ session_id: str
2025-09-28 23:16:13 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=False, status=failed, has_data=False
2025-09-28 23:16:18 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 23:16:18 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 23:16:18 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 23:16:18 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_ca359a0f58e5
2025-09-28 23:16:18 | INFO     | [debug_RESULT] | ℹ️ Active tasks: []
2025-09-28 23:16:18 | INFO     | [debug_RESULT] | ℹ️ Cached results: ['async_ca359a0f58e5']
2025-09-28 23:16:18 | INFO     | [debug_RESULT] | ✅ RESULT FOUND [async_ca359a0f58e5]: keys=['success', 'error', 'session_id']
2025-09-28 23:16:18 | INFO     | [debug_RESULT] | 🔍 DATA STRUCTURE [async_ca359a0f58e5] RETRIEVED_RESULT: ['success', 'error', 'session_id']
2025-09-28 23:16:18 | INFO     | [debug_RESULT] |     └─ success: bool
2025-09-28 23:16:18 | INFO     | [debug_RESULT] |     └─ error: str
2025-09-28 23:16:18 | INFO     | [debug_RESULT] |     └─ session_id: str
2025-09-28 23:16:18 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=False, status=failed, has_data=False
2025-09-28 23:16:23 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 23:16:23 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 23:16:23 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 23:16:23 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_ca359a0f58e5
2025-09-28 23:16:23 | INFO     | [debug_RESULT] | ℹ️ Active tasks: []
2025-09-28 23:16:23 | INFO     | [debug_RESULT] | ℹ️ Cached results: ['async_ca359a0f58e5']
2025-09-28 23:16:23 | INFO     | [debug_RESULT] | ✅ RESULT FOUND [async_ca359a0f58e5]: keys=['success', 'error', 'session_id']
2025-09-28 23:16:23 | INFO     | [debug_RESULT] | 🔍 DATA STRUCTURE [async_ca359a0f58e5] RETRIEVED_RESULT: ['success', 'error', 'session_id']
2025-09-28 23:16:23 | INFO     | [debug_RESULT] |     └─ success: bool
2025-09-28 23:16:23 | INFO     | [debug_RESULT] |     └─ error: str
2025-09-28 23:16:23 | INFO     | [debug_RESULT] |     └─ session_id: str
2025-09-28 23:16:23 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=False, status=failed, has_data=False
2025-09-28 23:17:02 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 23:17:02 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 23:17:02 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 23:17:02 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_786d224ce7f0
2025-09-28 23:17:02 | INFO     | [debug_RESULT] | ℹ️ Active tasks: []
2025-09-28 23:17:02 | INFO     | [debug_RESULT] | ℹ️ Cached results: ['async_ca359a0f58e5', 'async_786d224ce7f0']
2025-09-28 23:17:02 | INFO     | [debug_RESULT] | ✅ RESULT FOUND [async_786d224ce7f0]: keys=['success', 'error', 'session_id']
2025-09-28 23:17:02 | INFO     | [debug_RESULT] | 🔍 DATA STRUCTURE [async_786d224ce7f0] RETRIEVED_RESULT: ['success', 'error', 'session_id']
2025-09-28 23:17:02 | INFO     | [debug_RESULT] |     └─ success: bool
2025-09-28 23:17:02 | INFO     | [debug_RESULT] |     └─ error: str
2025-09-28 23:17:02 | INFO     | [debug_RESULT] |     └─ session_id: str
2025-09-28 23:17:02 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=False, status=failed, has_data=False
2025-09-28 23:21:43 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 23:21:43 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 23:21:43 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 23:21:43 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_786d224ce7f0
2025-09-28 23:21:43 | INFO     | [debug_RESULT] | ℹ️ Active tasks: []
2025-09-28 23:21:43 | INFO     | [debug_RESULT] | ℹ️ Cached results: []
2025-09-28 23:21:43 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_786d224ce7f0]
2025-09-28 23:21:43 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=False, status=not_found, has_data=False
2025-09-28 23:21:53 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 23:21:53 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 23:21:53 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 23:21:53 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_b7670f1b912f
2025-09-28 23:21:53 | INFO     | [debug_RESULT] | ℹ️ Active tasks: []
2025-09-28 23:21:53 | INFO     | [debug_RESULT] | ℹ️ Cached results: ['async_b7670f1b912f']
2025-09-28 23:21:53 | INFO     | [debug_RESULT] | ✅ RESULT FOUND [async_b7670f1b912f]: keys=['success', 'error', 'session_id']
2025-09-28 23:21:53 | INFO     | [debug_RESULT] | 🔍 DATA STRUCTURE [async_b7670f1b912f] RETRIEVED_RESULT: ['success', 'error', 'session_id']
2025-09-28 23:21:53 | INFO     | [debug_RESULT] |     └─ success: bool
2025-09-28 23:21:53 | INFO     | [debug_RESULT] |     └─ error: str
2025-09-28 23:21:53 | INFO     | [debug_RESULT] |     └─ session_id: str
2025-09-28 23:21:53 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=False, status=failed, has_data=False
2025-09-28 23:22:04 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 23:22:04 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 23:22:04 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 23:22:04 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_599271a9f171
2025-09-28 23:22:04 | INFO     | [debug_RESULT] | ℹ️ Active tasks: []
2025-09-28 23:22:04 | INFO     | [debug_RESULT] | ℹ️ Cached results: ['async_b7670f1b912f', 'async_599271a9f171']
2025-09-28 23:22:04 | INFO     | [debug_RESULT] | ✅ RESULT FOUND [async_599271a9f171]: keys=['success', 'error', 'session_id']
2025-09-28 23:22:04 | INFO     | [debug_RESULT] | 🔍 DATA STRUCTURE [async_599271a9f171] RETRIEVED_RESULT: ['success', 'error', 'session_id']
2025-09-28 23:22:04 | INFO     | [debug_RESULT] |     └─ success: bool
2025-09-28 23:22:04 | INFO     | [debug_RESULT] |     └─ error: str
2025-09-28 23:22:04 | INFO     | [debug_RESULT] |     └─ session_id: str
2025-09-28 23:22:04 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=False, status=failed, has_data=False
2025-09-28 23:22:10 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 23:22:10 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 23:22:10 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 23:22:10 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_599271a9f171
2025-09-28 23:22:10 | INFO     | [debug_RESULT] | ℹ️ Active tasks: []
2025-09-28 23:22:10 | INFO     | [debug_RESULT] | ℹ️ Cached results: ['async_b7670f1b912f', 'async_599271a9f171']
2025-09-28 23:22:10 | INFO     | [debug_RESULT] | ✅ RESULT FOUND [async_599271a9f171]: keys=['success', 'error', 'session_id']
2025-09-28 23:22:10 | INFO     | [debug_RESULT] | 🔍 DATA STRUCTURE [async_599271a9f171] RETRIEVED_RESULT: ['success', 'error', 'session_id']
2025-09-28 23:22:10 | INFO     | [debug_RESULT] |     └─ success: bool
2025-09-28 23:22:10 | INFO     | [debug_RESULT] |     └─ error: str
2025-09-28 23:22:10 | INFO     | [debug_RESULT] |     └─ session_id: str
2025-09-28 23:22:10 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=False, status=failed, has_data=False
2025-09-28 23:22:15 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 23:22:15 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 23:22:15 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 23:22:15 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_599271a9f171
2025-09-28 23:22:15 | INFO     | [debug_RESULT] | ℹ️ Active tasks: []
2025-09-28 23:22:15 | INFO     | [debug_RESULT] | ℹ️ Cached results: ['async_b7670f1b912f', 'async_599271a9f171']
2025-09-28 23:22:15 | INFO     | [debug_RESULT] | ✅ RESULT FOUND [async_599271a9f171]: keys=['success', 'error', 'session_id']
2025-09-28 23:22:15 | INFO     | [debug_RESULT] | 🔍 DATA STRUCTURE [async_599271a9f171] RETRIEVED_RESULT: ['success', 'error', 'session_id']
2025-09-28 23:22:15 | INFO     | [debug_RESULT] |     └─ success: bool
2025-09-28 23:22:15 | INFO     | [debug_RESULT] |     └─ error: str
2025-09-28 23:22:15 | INFO     | [debug_RESULT] |     └─ session_id: str
2025-09-28 23:22:15 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=False, status=failed, has_data=False
2025-09-28 23:22:20 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 23:22:20 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 23:22:20 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 23:22:20 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_599271a9f171
2025-09-28 23:22:20 | INFO     | [debug_RESULT] | ℹ️ Active tasks: []
2025-09-28 23:22:20 | INFO     | [debug_RESULT] | ℹ️ Cached results: ['async_b7670f1b912f', 'async_599271a9f171']
2025-09-28 23:22:20 | INFO     | [debug_RESULT] | ✅ RESULT FOUND [async_599271a9f171]: keys=['success', 'error', 'session_id']
2025-09-28 23:22:20 | INFO     | [debug_RESULT] | 🔍 DATA STRUCTURE [async_599271a9f171] RETRIEVED_RESULT: ['success', 'error', 'session_id']
2025-09-28 23:22:20 | INFO     | [debug_RESULT] |     └─ success: bool
2025-09-28 23:22:20 | INFO     | [debug_RESULT] |     └─ error: str
2025-09-28 23:22:20 | INFO     | [debug_RESULT] |     └─ session_id: str
2025-09-28 23:22:20 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=False, status=failed, has_data=False
2025-09-28 23:22:25 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 23:22:25 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 23:22:25 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 23:22:25 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_599271a9f171
2025-09-28 23:22:25 | INFO     | [debug_RESULT] | ℹ️ Active tasks: []
2025-09-28 23:22:25 | INFO     | [debug_RESULT] | ℹ️ Cached results: ['async_b7670f1b912f', 'async_599271a9f171']
2025-09-28 23:22:25 | INFO     | [debug_RESULT] | ✅ RESULT FOUND [async_599271a9f171]: keys=['success', 'error', 'session_id']
2025-09-28 23:22:25 | INFO     | [debug_RESULT] | 🔍 DATA STRUCTURE [async_599271a9f171] RETRIEVED_RESULT: ['success', 'error', 'session_id']
2025-09-28 23:22:25 | INFO     | [debug_RESULT] |     └─ success: bool
2025-09-28 23:22:25 | INFO     | [debug_RESULT] |     └─ error: str
2025-09-28 23:22:25 | INFO     | [debug_RESULT] |     └─ session_id: str
2025-09-28 23:22:25 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=False, status=failed, has_data=False
2025-09-28 23:22:30 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 23:22:30 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 23:22:30 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 23:22:30 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_599271a9f171
2025-09-28 23:22:30 | INFO     | [debug_RESULT] | ℹ️ Active tasks: []
2025-09-28 23:22:30 | INFO     | [debug_RESULT] | ℹ️ Cached results: ['async_b7670f1b912f', 'async_599271a9f171']
2025-09-28 23:22:30 | INFO     | [debug_RESULT] | ✅ RESULT FOUND [async_599271a9f171]: keys=['success', 'error', 'session_id']
2025-09-28 23:22:30 | INFO     | [debug_RESULT] | 🔍 DATA STRUCTURE [async_599271a9f171] RETRIEVED_RESULT: ['success', 'error', 'session_id']
2025-09-28 23:22:30 | INFO     | [debug_RESULT] |     └─ success: bool
2025-09-28 23:22:30 | INFO     | [debug_RESULT] |     └─ error: str
2025-09-28 23:22:30 | INFO     | [debug_RESULT] |     └─ session_id: str
2025-09-28 23:22:30 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=False, status=failed, has_data=False
2025-09-28 23:22:36 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 23:22:36 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 23:22:36 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 23:22:36 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_599271a9f171
2025-09-28 23:22:36 | INFO     | [debug_RESULT] | ℹ️ Active tasks: []
2025-09-28 23:22:36 | INFO     | [debug_RESULT] | ℹ️ Cached results: ['async_b7670f1b912f', 'async_599271a9f171']
2025-09-28 23:22:36 | INFO     | [debug_RESULT] | ✅ RESULT FOUND [async_599271a9f171]: keys=['success', 'error', 'session_id']
2025-09-28 23:22:36 | INFO     | [debug_RESULT] | 🔍 DATA STRUCTURE [async_599271a9f171] RETRIEVED_RESULT: ['success', 'error', 'session_id']
2025-09-28 23:22:36 | INFO     | [debug_RESULT] |     └─ success: bool
2025-09-28 23:22:36 | INFO     | [debug_RESULT] |     └─ error: str
2025-09-28 23:22:36 | INFO     | [debug_RESULT] |     └─ session_id: str
2025-09-28 23:22:36 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=False, status=failed, has_data=False
2025-09-28 23:22:41 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 23:22:41 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 23:22:41 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 23:22:41 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_599271a9f171
2025-09-28 23:22:41 | INFO     | [debug_RESULT] | ℹ️ Active tasks: []
2025-09-28 23:22:41 | INFO     | [debug_RESULT] | ℹ️ Cached results: ['async_b7670f1b912f', 'async_599271a9f171']
2025-09-28 23:22:41 | INFO     | [debug_RESULT] | ✅ RESULT FOUND [async_599271a9f171]: keys=['success', 'error', 'session_id']
2025-09-28 23:22:41 | INFO     | [debug_RESULT] | 🔍 DATA STRUCTURE [async_599271a9f171] RETRIEVED_RESULT: ['success', 'error', 'session_id']
2025-09-28 23:22:41 | INFO     | [debug_RESULT] |     └─ success: bool
2025-09-28 23:22:41 | INFO     | [debug_RESULT] |     └─ error: str
2025-09-28 23:22:41 | INFO     | [debug_RESULT] |     └─ session_id: str
2025-09-28 23:22:42 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=False, status=failed, has_data=False
2025-09-28 23:22:47 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 23:22:47 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 23:22:47 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 23:22:47 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_599271a9f171
2025-09-28 23:22:47 | INFO     | [debug_RESULT] | ℹ️ Active tasks: []
2025-09-28 23:22:47 | INFO     | [debug_RESULT] | ℹ️ Cached results: ['async_b7670f1b912f', 'async_599271a9f171']
2025-09-28 23:22:47 | INFO     | [debug_RESULT] | ✅ RESULT FOUND [async_599271a9f171]: keys=['success', 'error', 'session_id']
2025-09-28 23:22:47 | INFO     | [debug_RESULT] | 🔍 DATA STRUCTURE [async_599271a9f171] RETRIEVED_RESULT: ['success', 'error', 'session_id']
2025-09-28 23:22:47 | INFO     | [debug_RESULT] |     └─ success: bool
2025-09-28 23:22:47 | INFO     | [debug_RESULT] |     └─ error: str
2025-09-28 23:22:47 | INFO     | [debug_RESULT] |     └─ session_id: str
2025-09-28 23:22:47 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=False, status=failed, has_data=False
2025-09-28 23:22:52 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 23:22:52 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 23:22:52 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 23:22:52 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_599271a9f171
2025-09-28 23:22:52 | INFO     | [debug_RESULT] | ℹ️ Active tasks: []
2025-09-28 23:22:52 | INFO     | [debug_RESULT] | ℹ️ Cached results: ['async_b7670f1b912f', 'async_599271a9f171']
2025-09-28 23:22:52 | INFO     | [debug_RESULT] | ✅ RESULT FOUND [async_599271a9f171]: keys=['success', 'error', 'session_id']
2025-09-28 23:22:52 | INFO     | [debug_RESULT] | 🔍 DATA STRUCTURE [async_599271a9f171] RETRIEVED_RESULT: ['success', 'error', 'session_id']
2025-09-28 23:22:52 | INFO     | [debug_RESULT] |     └─ success: bool
2025-09-28 23:22:52 | INFO     | [debug_RESULT] |     └─ error: str
2025-09-28 23:22:52 | INFO     | [debug_RESULT] |     └─ session_id: str
2025-09-28 23:22:52 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=False, status=failed, has_data=False
2025-09-28 23:23:11 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 23:23:11 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 23:23:11 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 23:23:11 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_aaa8be1c6726
2025-09-28 23:23:11 | INFO     | [debug_RESULT] | ℹ️ Active tasks: []
2025-09-28 23:23:11 | INFO     | [debug_RESULT] | ℹ️ Cached results: ['async_b7670f1b912f', 'async_599271a9f171', 'async_aaa8be1c6726']
2025-09-28 23:23:11 | INFO     | [debug_RESULT] | ✅ RESULT FOUND [async_aaa8be1c6726]: keys=['success', 'error', 'session_id']
2025-09-28 23:23:11 | INFO     | [debug_RESULT] | 🔍 DATA STRUCTURE [async_aaa8be1c6726] RETRIEVED_RESULT: ['success', 'error', 'session_id']
2025-09-28 23:23:11 | INFO     | [debug_RESULT] |     └─ success: bool
2025-09-28 23:23:11 | INFO     | [debug_RESULT] |     └─ error: str
2025-09-28 23:23:11 | INFO     | [debug_RESULT] |     └─ session_id: str
2025-09-28 23:23:11 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=False, status=failed, has_data=False
2025-09-28 23:25:18 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 23:25:18 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 23:25:18 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 23:25:18 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_880d87551d8a
2025-09-28 23:25:18 | INFO     | [debug_RESULT] | ℹ️ Active tasks: []
2025-09-28 23:25:18 | INFO     | [debug_RESULT] | ℹ️ Cached results: ['async_b7670f1b912f', 'async_599271a9f171', 'async_aaa8be1c6726', 'async_880d87551d8a']
2025-09-28 23:25:18 | INFO     | [debug_RESULT] | ✅ RESULT FOUND [async_880d87551d8a]: keys=['success', 'error', 'session_id']
2025-09-28 23:25:18 | INFO     | [debug_RESULT] | 🔍 DATA STRUCTURE [async_880d87551d8a] RETRIEVED_RESULT: ['success', 'error', 'session_id']
2025-09-28 23:25:18 | INFO     | [debug_RESULT] |     └─ success: bool
2025-09-28 23:25:18 | INFO     | [debug_RESULT] |     └─ error: str
2025-09-28 23:25:18 | INFO     | [debug_RESULT] |     └─ session_id: str
2025-09-28 23:25:18 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=False, status=failed, has_data=False
2025-09-28 23:29:26 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 23:29:26 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 23:29:26 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 23:29:26 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_747c2ad7d6f8
2025-09-28 23:29:26 | INFO     | [debug_RESULT] | ℹ️ Active tasks: []
2025-09-28 23:29:26 | INFO     | [debug_RESULT] | ℹ️ Cached results: ['async_747c2ad7d6f8']
2025-09-28 23:29:26 | INFO     | [debug_RESULT] | ✅ RESULT FOUND [async_747c2ad7d6f8]: keys=['success', 'error', 'session_id']
2025-09-28 23:29:26 | INFO     | [debug_RESULT] | 🔍 DATA STRUCTURE [async_747c2ad7d6f8] RETRIEVED_RESULT: ['success', 'error', 'session_id']
2025-09-28 23:29:26 | INFO     | [debug_RESULT] |     └─ success: bool
2025-09-28 23:29:26 | INFO     | [debug_RESULT] |     └─ error: str
2025-09-28 23:29:26 | INFO     | [debug_RESULT] |     └─ session_id: str
2025-09-28 23:29:26 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=False, status=failed, has_data=False
2025-09-28 23:29:31 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 23:29:31 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 23:29:31 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 23:29:31 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_747c2ad7d6f8
2025-09-28 23:29:31 | INFO     | [debug_RESULT] | ℹ️ Active tasks: []
2025-09-28 23:29:31 | INFO     | [debug_RESULT] | ℹ️ Cached results: ['async_747c2ad7d6f8']
2025-09-28 23:29:31 | INFO     | [debug_RESULT] | ✅ RESULT FOUND [async_747c2ad7d6f8]: keys=['success', 'error', 'session_id']
2025-09-28 23:29:31 | INFO     | [debug_RESULT] | 🔍 DATA STRUCTURE [async_747c2ad7d6f8] RETRIEVED_RESULT: ['success', 'error', 'session_id']
2025-09-28 23:29:31 | INFO     | [debug_RESULT] |     └─ success: bool
2025-09-28 23:29:31 | INFO     | [debug_RESULT] |     └─ error: str
2025-09-28 23:29:31 | INFO     | [debug_RESULT] |     └─ session_id: str
2025-09-28 23:29:31 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=False, status=failed, has_data=False
2025-09-28 23:29:36 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 23:29:36 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 23:29:36 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 23:29:36 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_747c2ad7d6f8
2025-09-28 23:29:36 | INFO     | [debug_RESULT] | ℹ️ Active tasks: []
2025-09-28 23:29:36 | INFO     | [debug_RESULT] | ℹ️ Cached results: ['async_747c2ad7d6f8']
2025-09-28 23:29:36 | INFO     | [debug_RESULT] | ✅ RESULT FOUND [async_747c2ad7d6f8]: keys=['success', 'error', 'session_id']
2025-09-28 23:29:36 | INFO     | [debug_RESULT] | 🔍 DATA STRUCTURE [async_747c2ad7d6f8] RETRIEVED_RESULT: ['success', 'error', 'session_id']
2025-09-28 23:29:36 | INFO     | [debug_RESULT] |     └─ success: bool
2025-09-28 23:29:36 | INFO     | [debug_RESULT] |     └─ error: str
2025-09-28 23:29:36 | INFO     | [debug_RESULT] |     └─ session_id: str
2025-09-28 23:29:36 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=False, status=failed, has_data=False
2025-09-28 23:29:41 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 23:29:41 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 23:29:41 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 23:29:41 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_880d87551d8a
2025-09-28 23:29:41 | INFO     | [debug_RESULT] | ℹ️ Active tasks: []
2025-09-28 23:29:41 | INFO     | [debug_RESULT] | ℹ️ Cached results: ['async_747c2ad7d6f8']
2025-09-28 23:29:41 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_880d87551d8a]
2025-09-28 23:29:41 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=False, status=not_found, has_data=False
2025-09-28 23:29:41 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 23:29:41 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 23:29:41 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 23:29:41 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_92c32b2a2779
2025-09-28 23:29:41 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_92c32b2a2779']
2025-09-28 23:29:41 | INFO     | [debug_RESULT] | ℹ️ Cached results: ['async_747c2ad7d6f8']
2025-09-28 23:29:41 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_92c32b2a2779]
2025-09-28 23:29:41 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-28 23:29:41 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 23:29:41 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 23:29:41 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 23:29:41 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_747c2ad7d6f8
2025-09-28 23:29:41 | INFO     | [debug_RESULT] | ℹ️ Active tasks: []
2025-09-28 23:29:41 | INFO     | [debug_RESULT] | ℹ️ Cached results: ['async_747c2ad7d6f8', 'async_92c32b2a2779']
2025-09-28 23:29:41 | INFO     | [debug_RESULT] | ✅ RESULT FOUND [async_747c2ad7d6f8]: keys=['success', 'error', 'session_id']
2025-09-28 23:29:41 | INFO     | [debug_RESULT] | 🔍 DATA STRUCTURE [async_747c2ad7d6f8] RETRIEVED_RESULT: ['success', 'error', 'session_id']
2025-09-28 23:29:41 | INFO     | [debug_RESULT] |     └─ success: bool
2025-09-28 23:29:41 | INFO     | [debug_RESULT] |     └─ error: str
2025-09-28 23:29:41 | INFO     | [debug_RESULT] |     └─ session_id: str
2025-09-28 23:29:41 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=False, status=failed, has_data=False
2025-09-28 23:29:46 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 23:29:46 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 23:29:46 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 23:29:46 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_747c2ad7d6f8
2025-09-28 23:29:46 | INFO     | [debug_RESULT] | ℹ️ Active tasks: []
2025-09-28 23:29:46 | INFO     | [debug_RESULT] | ℹ️ Cached results: ['async_747c2ad7d6f8', 'async_92c32b2a2779']
2025-09-28 23:29:47 | INFO     | [debug_RESULT] | ✅ RESULT FOUND [async_747c2ad7d6f8]: keys=['success', 'error', 'session_id']
2025-09-28 23:29:47 | INFO     | [debug_RESULT] | 🔍 DATA STRUCTURE [async_747c2ad7d6f8] RETRIEVED_RESULT: ['success', 'error', 'session_id']
2025-09-28 23:29:47 | INFO     | [debug_RESULT] |     └─ success: bool
2025-09-28 23:29:47 | INFO     | [debug_RESULT] |     └─ error: str
2025-09-28 23:29:47 | INFO     | [debug_RESULT] |     └─ session_id: str
2025-09-28 23:29:47 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=False, status=failed, has_data=False
2025-09-28 23:29:52 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 23:29:52 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 23:29:52 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 23:29:52 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_747c2ad7d6f8
2025-09-28 23:29:52 | INFO     | [debug_RESULT] | ℹ️ Active tasks: []
2025-09-28 23:29:52 | INFO     | [debug_RESULT] | ℹ️ Cached results: ['async_747c2ad7d6f8', 'async_92c32b2a2779']
2025-09-28 23:29:52 | INFO     | [debug_RESULT] | ✅ RESULT FOUND [async_747c2ad7d6f8]: keys=['success', 'error', 'session_id']
2025-09-28 23:29:52 | INFO     | [debug_RESULT] | 🔍 DATA STRUCTURE [async_747c2ad7d6f8] RETRIEVED_RESULT: ['success', 'error', 'session_id']
2025-09-28 23:29:52 | INFO     | [debug_RESULT] |     └─ success: bool
2025-09-28 23:29:52 | INFO     | [debug_RESULT] |     └─ error: str
2025-09-28 23:29:52 | INFO     | [debug_RESULT] |     └─ session_id: str
2025-09-28 23:29:52 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=False, status=failed, has_data=False
2025-09-28 23:29:57 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 23:29:57 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 23:29:57 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 23:29:57 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_747c2ad7d6f8
2025-09-28 23:29:57 | INFO     | [debug_RESULT] | ℹ️ Active tasks: []
2025-09-28 23:29:57 | INFO     | [debug_RESULT] | ℹ️ Cached results: ['async_747c2ad7d6f8', 'async_92c32b2a2779']
2025-09-28 23:29:57 | INFO     | [debug_RESULT] | ✅ RESULT FOUND [async_747c2ad7d6f8]: keys=['success', 'error', 'session_id']
2025-09-28 23:29:57 | INFO     | [debug_RESULT] | 🔍 DATA STRUCTURE [async_747c2ad7d6f8] RETRIEVED_RESULT: ['success', 'error', 'session_id']
2025-09-28 23:29:57 | INFO     | [debug_RESULT] |     └─ success: bool
2025-09-28 23:29:57 | INFO     | [debug_RESULT] |     └─ error: str
2025-09-28 23:29:57 | INFO     | [debug_RESULT] |     └─ session_id: str
2025-09-28 23:29:57 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=False, status=failed, has_data=False
2025-09-28 23:30:03 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 23:30:03 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 23:30:03 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 23:30:03 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_747c2ad7d6f8
2025-09-28 23:30:03 | INFO     | [debug_RESULT] | ℹ️ Active tasks: []
2025-09-28 23:30:03 | INFO     | [debug_RESULT] | ℹ️ Cached results: ['async_747c2ad7d6f8', 'async_92c32b2a2779']
2025-09-28 23:30:03 | INFO     | [debug_RESULT] | ✅ RESULT FOUND [async_747c2ad7d6f8]: keys=['success', 'error', 'session_id']
2025-09-28 23:30:03 | INFO     | [debug_RESULT] | 🔍 DATA STRUCTURE [async_747c2ad7d6f8] RETRIEVED_RESULT: ['success', 'error', 'session_id']
2025-09-28 23:30:03 | INFO     | [debug_RESULT] |     └─ success: bool
2025-09-28 23:30:03 | INFO     | [debug_RESULT] |     └─ error: str
2025-09-28 23:30:03 | INFO     | [debug_RESULT] |     └─ session_id: str
2025-09-28 23:30:03 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=False, status=failed, has_data=False
2025-09-28 23:30:10 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 23:30:10 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 23:30:10 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 23:30:10 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_747c2ad7d6f8
2025-09-28 23:30:10 | INFO     | [debug_RESULT] | ℹ️ Active tasks: []
2025-09-28 23:30:10 | INFO     | [debug_RESULT] | ℹ️ Cached results: ['async_747c2ad7d6f8', 'async_92c32b2a2779']
2025-09-28 23:30:10 | INFO     | [debug_RESULT] | ✅ RESULT FOUND [async_747c2ad7d6f8]: keys=['success', 'error', 'session_id']
2025-09-28 23:30:10 | INFO     | [debug_RESULT] | 🔍 DATA STRUCTURE [async_747c2ad7d6f8] RETRIEVED_RESULT: ['success', 'error', 'session_id']
2025-09-28 23:30:10 | INFO     | [debug_RESULT] |     └─ success: bool
2025-09-28 23:30:10 | INFO     | [debug_RESULT] |     └─ error: str
2025-09-28 23:30:10 | INFO     | [debug_RESULT] |     └─ session_id: str
2025-09-28 23:30:10 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=False, status=failed, has_data=False
2025-09-28 23:30:15 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 23:30:15 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 23:30:15 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 23:30:15 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_747c2ad7d6f8
2025-09-28 23:30:15 | INFO     | [debug_RESULT] | ℹ️ Active tasks: []
2025-09-28 23:30:15 | INFO     | [debug_RESULT] | ℹ️ Cached results: ['async_747c2ad7d6f8', 'async_92c32b2a2779']
2025-09-28 23:30:15 | INFO     | [debug_RESULT] | ✅ RESULT FOUND [async_747c2ad7d6f8]: keys=['success', 'error', 'session_id']
2025-09-28 23:30:15 | INFO     | [debug_RESULT] | 🔍 DATA STRUCTURE [async_747c2ad7d6f8] RETRIEVED_RESULT: ['success', 'error', 'session_id']
2025-09-28 23:30:15 | INFO     | [debug_RESULT] |     └─ success: bool
2025-09-28 23:30:15 | INFO     | [debug_RESULT] |     └─ error: str
2025-09-28 23:30:15 | INFO     | [debug_RESULT] |     └─ session_id: str
2025-09-28 23:30:15 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=False, status=failed, has_data=False
2025-09-28 23:30:39 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 23:30:39 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 23:30:39 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 23:30:39 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_9aa2bd7ee7ff
2025-09-28 23:30:39 | INFO     | [debug_RESULT] | ℹ️ Active tasks: []
2025-09-28 23:30:39 | INFO     | [debug_RESULT] | ℹ️ Cached results: ['async_747c2ad7d6f8', 'async_92c32b2a2779', 'async_9aa2bd7ee7ff']
2025-09-28 23:30:39 | INFO     | [debug_RESULT] | ✅ RESULT FOUND [async_9aa2bd7ee7ff]: keys=['success', 'error', 'session_id']
2025-09-28 23:30:39 | INFO     | [debug_RESULT] | 🔍 DATA STRUCTURE [async_9aa2bd7ee7ff] RETRIEVED_RESULT: ['success', 'error', 'session_id']
2025-09-28 23:30:39 | INFO     | [debug_RESULT] |     └─ success: bool
2025-09-28 23:30:39 | INFO     | [debug_RESULT] |     └─ error: str
2025-09-28 23:30:39 | INFO     | [debug_RESULT] |     └─ session_id: str
2025-09-28 23:30:39 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=False, status=failed, has_data=False
2025-09-28 23:38:11 | INFO     | [debug_RESULT] | 💾 RESULT STORED [async_7dfdaa664c93]
2025-09-28 23:38:11 | INFO     | [debug_RESULT] |    └─ Keys: ['success', 'data', 'metadata', 'session_id']
2025-09-28 23:38:11 | INFO     | [debug_RESULT] |    └─ Data Size: 9084 bytes
2025-09-28 23:38:11 | INFO     | [debug_RESULT] |    └─ Storage Time: 2025-09-28T23:38:11.543708
2025-09-28 23:38:11 | INFO     | [debug_RESULT] | 🔍 DATA STRUCTURE [async_7dfdaa664c93] STORED_RESULT: ['success', 'data', 'metadata', 'session_id']
2025-09-28 23:38:11 | INFO     | [debug_RESULT] |     └─ success: bool
2025-09-28 23:38:11 | INFO     | [debug_RESULT] |     └─ data: dict with keys ['success', 'analysis_id', 'data', 'execution_time', 'metadata']
2025-09-28 23:38:11 | INFO     | [debug_RESULT] |     └─ metadata: dict with keys ['execution_time', 'user_id', 'analysis_type', 'market_specialization', 'image_count', 'model_used']
2025-09-28 23:38:11 | INFO     | [debug_RESULT] |     └─ session_id: str
2025-09-28 23:40:00 | INFO     | [debug_RESULT] | 💾 RESULT STORED [async_5dc6f0645c51]
2025-09-28 23:40:00 | INFO     | [debug_RESULT] |    └─ Keys: ['success', 'data', 'metadata', 'session_id']
2025-09-28 23:40:00 | INFO     | [debug_RESULT] |    └─ Data Size: 13879 bytes
2025-09-28 23:40:00 | INFO     | [debug_RESULT] |    └─ Storage Time: 2025-09-28T23:40:00.036611
2025-09-28 23:40:00 | INFO     | [debug_RESULT] | 🔍 DATA STRUCTURE [async_5dc6f0645c51] STORED_RESULT: ['success', 'data', 'metadata', 'session_id']
2025-09-28 23:40:00 | INFO     | [debug_RESULT] |     └─ success: bool
2025-09-28 23:40:00 | INFO     | [debug_RESULT] |     └─ data: dict with keys ['success', 'analysis_id', 'data', 'execution_time', 'metadata']
2025-09-28 23:40:00 | INFO     | [debug_RESULT] |     └─ metadata: dict with keys ['execution_time', 'user_id', 'analysis_type', 'market_specialization', 'image_count', 'model_used']
2025-09-28 23:40:00 | INFO     | [debug_RESULT] |     └─ session_id: str
2025-09-28 23:40:00 | INFO     | [debug_RESULT] | 
============================================================
2025-09-28 23:40:00 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-28 23:40:00 | INFO     | [debug_RESULT] | ============================================================
2025-09-28 23:40:00 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_5dc6f0645c51
2025-09-28 23:40:00 | INFO     | [debug_RESULT] | ℹ️ Active tasks: []
2025-09-28 23:40:00 | INFO     | [debug_RESULT] | ℹ️ Cached results: ['async_7dfdaa664c93', 'async_5dc6f0645c51']
2025-09-28 23:40:00 | INFO     | [debug_RESULT] | ✅ RESULT FOUND [async_5dc6f0645c51]: keys=['success', 'data', 'metadata', 'session_id']
2025-09-28 23:40:00 | INFO     | [debug_RESULT] | 🔍 DATA STRUCTURE [async_5dc6f0645c51] RETRIEVED_RESULT: ['success', 'data', 'metadata', 'session_id']
2025-09-28 23:40:00 | INFO     | [debug_RESULT] |     └─ success: bool
2025-09-28 23:40:00 | INFO     | [debug_RESULT] |     └─ data: dict with keys ['success', 'analysis_id', 'data', 'execution_time', 'metadata']
2025-09-28 23:40:00 | INFO     | [debug_RESULT] |     └─ metadata: dict with keys ['execution_time', 'user_id', 'analysis_type', 'market_specialization', 'image_count', 'model_used']
2025-09-28 23:40:00 | INFO     | [debug_RESULT] |     └─ session_id: str
2025-09-28 23:40:00 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=completed, has_data=True
2025-09-28 23:40:00 | INFO     | [debug_RESULT] | ℹ️ Response data keys: ['success', 'analysis_id', 'data', 'execution_time', 'metadata']
2025-09-29 00:30:41 | INFO     | [debug_RESULT] | 
============================================================
2025-09-29 00:30:41 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-29 00:30:41 | INFO     | [debug_RESULT] | ============================================================
2025-09-29 00:30:41 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_fec524a30428
2025-09-29 00:30:41 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_fec524a30428']
2025-09-29 00:30:41 | INFO     | [debug_RESULT] | ℹ️ Cached results: []
2025-09-29 00:30:41 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_fec524a30428]
2025-09-29 00:30:41 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-29 00:30:42 | INFO     | [debug_RESULT] | 💾 RESULT STORED [async_fec524a30428]
2025-09-29 00:30:42 | INFO     | [debug_RESULT] |    └─ Keys: ['success', 'data', 'metadata', 'session_id']
2025-09-29 00:30:42 | INFO     | [debug_RESULT] |    └─ Data Size: 8985 bytes
2025-09-29 00:30:42 | INFO     | [debug_RESULT] |    └─ Storage Time: 2025-09-29T00:30:42.015354
2025-09-29 00:30:42 | INFO     | [debug_RESULT] | 🔍 DATA STRUCTURE [async_fec524a30428] STORED_RESULT: ['success', 'data', 'metadata', 'session_id']
2025-09-29 00:30:42 | INFO     | [debug_RESULT] |     └─ success: bool
2025-09-29 00:30:42 | INFO     | [debug_RESULT] |     └─ data: dict with keys ['success', 'analysis_id', 'data', 'execution_time', 'metadata']
2025-09-29 00:30:42 | INFO     | [debug_RESULT] |     └─ metadata: dict with keys ['execution_time', 'user_id', 'analysis_type', 'market_specialization', 'image_count', 'model_used']
2025-09-29 00:30:42 | INFO     | [debug_RESULT] |     └─ session_id: str
2025-09-29 01:04:27 | INFO     | [debug_RESULT] | 
============================================================
2025-09-29 01:04:27 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-29 01:04:27 | INFO     | [debug_RESULT] | ============================================================
2025-09-29 01:04:27 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_afa577b8cf34
2025-09-29 01:04:27 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_afa577b8cf34']
2025-09-29 01:04:27 | INFO     | [debug_RESULT] | ℹ️ Cached results: []
2025-09-29 01:04:27 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_afa577b8cf34]
2025-09-29 01:04:27 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-29 01:04:27 | INFO     | [debug_RESULT] | 💾 RESULT STORED [async_afa577b8cf34]
2025-09-29 01:04:27 | INFO     | [debug_RESULT] |    └─ Keys: ['success', 'data', 'metadata', 'session_id']
2025-09-29 01:04:27 | INFO     | [debug_RESULT] |    └─ Data Size: 3158 bytes
2025-09-29 01:04:27 | INFO     | [debug_RESULT] |    └─ Storage Time: 2025-09-29T01:04:27.402965
2025-09-29 01:04:27 | INFO     | [debug_RESULT] | 🔍 DATA STRUCTURE [async_afa577b8cf34] STORED_RESULT: ['success', 'data', 'metadata', 'session_id']
2025-09-29 01:04:27 | INFO     | [debug_RESULT] |     └─ success: bool
2025-09-29 01:04:27 | INFO     | [debug_RESULT] |     └─ data: dict with keys ['success', 'analysis_id', 'data', 'execution_time', 'metadata']
2025-09-29 01:04:27 | INFO     | [debug_RESULT] |     └─ metadata: dict with keys ['execution_time', 'user_id', 'analysis_type', 'market_specialization', 'image_count', 'model_used']
2025-09-29 01:04:27 | INFO     | [debug_RESULT] |     └─ session_id: str
2025-09-29 01:15:11 | INFO     | [debug_RESULT] | 
============================================================
2025-09-29 01:15:11 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-29 01:15:11 | INFO     | [debug_RESULT] | ============================================================
2025-09-29 01:15:11 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_5db019865433
2025-09-29 01:15:11 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_5db019865433']
2025-09-29 01:15:11 | INFO     | [debug_RESULT] | ℹ️ Cached results: ['async_afa577b8cf34']
2025-09-29 01:15:11 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_5db019865433]
2025-09-29 01:15:11 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-29 01:15:12 | INFO     | [debug_RESULT] | 💾 RESULT STORED [async_5db019865433]
2025-09-29 01:15:12 | INFO     | [debug_RESULT] |    └─ Keys: ['success', 'data', 'metadata', 'session_id']
2025-09-29 01:15:12 | INFO     | [debug_RESULT] |    └─ Data Size: 14029 bytes
2025-09-29 01:15:12 | INFO     | [debug_RESULT] |    └─ Storage Time: 2025-09-29T01:15:12.062807
2025-09-29 01:15:12 | INFO     | [debug_RESULT] | 🔍 DATA STRUCTURE [async_5db019865433] STORED_RESULT: ['success', 'data', 'metadata', 'session_id']
2025-09-29 01:15:12 | INFO     | [debug_RESULT] |     └─ success: bool
2025-09-29 01:15:12 | INFO     | [debug_RESULT] |     └─ data: dict with keys ['success', 'analysis_id', 'data', 'execution_time', 'metadata']
2025-09-29 01:15:12 | INFO     | [debug_RESULT] |     └─ metadata: dict with keys ['execution_time', 'user_id', 'analysis_type', 'market_specialization', 'image_count', 'model_used']
2025-09-29 01:15:12 | INFO     | [debug_RESULT] |     └─ session_id: str
2025-09-29 01:28:57 | INFO     | [debug_RESULT] | 
============================================================
2025-09-29 01:28:57 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-29 01:28:57 | INFO     | [debug_RESULT] | ============================================================
2025-09-29 01:28:57 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_5db019865433
2025-09-29 01:28:57 | INFO     | [debug_RESULT] | ℹ️ Active tasks: []
2025-09-29 01:28:57 | INFO     | [debug_RESULT] | ℹ️ Cached results: []
2025-09-29 01:28:57 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_5db019865433]
2025-09-29 01:28:57 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=False, status=not_found, has_data=False
2025-09-29 01:34:38 | INFO     | [debug_RESULT] | 
============================================================
2025-09-29 01:34:38 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-29 01:34:38 | INFO     | [debug_RESULT] | ============================================================
2025-09-29 01:34:39 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_2dd9fc3e987b
2025-09-29 01:34:39 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_2dd9fc3e987b']
2025-09-29 01:34:39 | INFO     | [debug_RESULT] | ℹ️ Cached results: []
2025-09-29 01:34:39 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_2dd9fc3e987b]
2025-09-29 01:34:39 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-29 01:34:39 | INFO     | [debug_RESULT] | 💾 RESULT STORED [async_2dd9fc3e987b]
2025-09-29 01:34:39 | INFO     | [debug_RESULT] |    └─ Keys: ['success', 'data', 'metadata', 'session_id']
2025-09-29 01:34:39 | INFO     | [debug_RESULT] |    └─ Data Size: 16586 bytes
2025-09-29 01:34:39 | INFO     | [debug_RESULT] |    └─ Storage Time: 2025-09-29T01:34:39.538666
2025-09-29 01:34:39 | INFO     | [debug_RESULT] | 🔍 DATA STRUCTURE [async_2dd9fc3e987b] STORED_RESULT: ['success', 'data', 'metadata', 'session_id']
2025-09-29 01:34:39 | INFO     | [debug_RESULT] |     └─ success: bool
2025-09-29 01:34:39 | INFO     | [debug_RESULT] |     └─ data: dict with keys ['success', 'analysis_id', 'data', 'execution_time', 'metadata']
2025-09-29 01:34:39 | INFO     | [debug_RESULT] |     └─ metadata: dict with keys ['execution_time', 'user_id', 'analysis_type', 'market_specialization', 'image_count', 'model_used']
2025-09-29 01:34:39 | INFO     | [debug_RESULT] |     └─ session_id: str
2025-09-29 01:38:17 | INFO     | [debug_RESULT] | 
============================================================
2025-09-29 01:38:17 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-29 01:38:17 | INFO     | [debug_RESULT] | ============================================================
2025-09-29 01:38:17 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_2dd9fc3e987b
2025-09-29 01:38:17 | INFO     | [debug_RESULT] | ℹ️ Active tasks: []
2025-09-29 01:38:17 | INFO     | [debug_RESULT] | ℹ️ Cached results: ['async_2dd9fc3e987b']
2025-09-29 01:38:17 | INFO     | [debug_RESULT] | ✅ RESULT FOUND [async_2dd9fc3e987b]: keys=['success', 'data', 'metadata', 'session_id']
2025-09-29 01:38:17 | INFO     | [debug_RESULT] | 🔍 DATA STRUCTURE [async_2dd9fc3e987b] RETRIEVED_RESULT: ['success', 'data', 'metadata', 'session_id']
2025-09-29 01:38:17 | INFO     | [debug_RESULT] |     └─ success: bool
2025-09-29 01:38:17 | INFO     | [debug_RESULT] |     └─ data: dict with keys ['success', 'analysis_id', 'data', 'execution_time', 'metadata']
2025-09-29 01:38:17 | INFO     | [debug_RESULT] |     └─ metadata: dict with keys ['execution_time', 'user_id', 'analysis_type', 'market_specialization', 'image_count', 'model_used']
2025-09-29 01:38:17 | INFO     | [debug_RESULT] |     └─ session_id: str
2025-09-29 01:38:17 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=completed, has_data=True
2025-09-29 01:38:17 | INFO     | [debug_RESULT] | ℹ️ Response data keys: ['success', 'analysis_id', 'data', 'execution_time', 'metadata']
2025-09-29 02:00:36 | INFO     | [debug_RESULT] | 💾 RESULT STORED [async_40c14e819bf2]
2025-09-29 02:00:36 | INFO     | [debug_RESULT] |    └─ Keys: ['success', 'data', 'metadata', 'session_id']
2025-09-29 02:00:36 | INFO     | [debug_RESULT] |    └─ Data Size: 10032 bytes
2025-09-29 02:00:36 | INFO     | [debug_RESULT] |    └─ Storage Time: 2025-09-29T02:00:36.591404
2025-09-29 02:00:36 | INFO     | [debug_RESULT] | 🔍 DATA STRUCTURE [async_40c14e819bf2] STORED_RESULT: ['success', 'data', 'metadata', 'session_id']
2025-09-29 02:00:36 | INFO     | [debug_RESULT] |     └─ success: bool
2025-09-29 02:00:36 | INFO     | [debug_RESULT] |     └─ data: dict with keys ['success', 'analysis_id', 'data', 'execution_time', 'metadata']
2025-09-29 02:00:36 | INFO     | [debug_RESULT] |     └─ metadata: dict with keys ['execution_time', 'user_id', 'analysis_type', 'market_specialization', 'image_count', 'model_used']
2025-09-29 02:00:36 | INFO     | [debug_RESULT] |     └─ session_id: str
2025-09-29 02:08:57 | INFO     | [debug_RESULT] | 💾 RESULT STORED [async_cde76545b38a]
2025-09-29 02:08:57 | INFO     | [debug_RESULT] |    └─ Keys: ['success', 'data', 'metadata', 'session_id']
2025-09-29 02:08:57 | INFO     | [debug_RESULT] |    └─ Data Size: 6539 bytes
2025-09-29 02:08:57 | INFO     | [debug_RESULT] |    └─ Storage Time: 2025-09-29T02:08:57.857931
2025-09-29 02:08:57 | INFO     | [debug_RESULT] | 🔍 DATA STRUCTURE [async_cde76545b38a] STORED_RESULT: ['success', 'data', 'metadata', 'session_id']
2025-09-29 02:08:57 | INFO     | [debug_RESULT] |     └─ success: bool
2025-09-29 02:08:57 | INFO     | [debug_RESULT] |     └─ data: dict with keys ['success', 'analysis_id', 'data', 'execution_time', 'metadata']
2025-09-29 02:08:57 | INFO     | [debug_RESULT] |     └─ metadata: dict with keys ['execution_time', 'user_id', 'analysis_type', 'market_specialization', 'image_count', 'model_used']
2025-09-29 02:08:57 | INFO     | [debug_RESULT] |     └─ session_id: str
2025-09-29 02:09:03 | INFO     | [debug_RESULT] | 💾 RESULT STORED [async_328d05c6f330]
2025-09-29 02:09:03 | INFO     | [debug_RESULT] |    └─ Keys: ['success', 'data', 'metadata', 'session_id']
2025-09-29 02:09:03 | INFO     | [debug_RESULT] |    └─ Data Size: 7174 bytes
2025-09-29 02:09:03 | INFO     | [debug_RESULT] |    └─ Storage Time: 2025-09-29T02:09:03.265898
2025-09-29 02:09:03 | INFO     | [debug_RESULT] | 🔍 DATA STRUCTURE [async_328d05c6f330] STORED_RESULT: ['success', 'data', 'metadata', 'session_id']
2025-09-29 02:09:03 | INFO     | [debug_RESULT] |     └─ success: bool
2025-09-29 02:09:03 | INFO     | [debug_RESULT] |     └─ data: dict with keys ['success', 'analysis_id', 'data', 'execution_time', 'metadata']
2025-09-29 02:09:03 | INFO     | [debug_RESULT] |     └─ metadata: dict with keys ['execution_time', 'user_id', 'analysis_type', 'market_specialization', 'image_count', 'model_used']
2025-09-29 02:09:03 | INFO     | [debug_RESULT] |     └─ session_id: str
2025-09-29 02:13:25 | INFO     | [debug_RESULT] | 
============================================================
2025-09-29 02:13:25 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-29 02:13:25 | INFO     | [debug_RESULT] | ============================================================
2025-09-29 02:13:25 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_97b1923234b1
2025-09-29 02:13:25 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_97b1923234b1']
2025-09-29 02:13:25 | INFO     | [debug_RESULT] | ℹ️ Cached results: ['async_cde76545b38a', 'async_328d05c6f330']
2025-09-29 02:13:25 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_97b1923234b1]
2025-09-29 02:13:25 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-29 02:13:26 | INFO     | [debug_RESULT] | 💾 RESULT STORED [async_97b1923234b1]
2025-09-29 02:13:26 | INFO     | [debug_RESULT] |    └─ Keys: ['success', 'data', 'metadata', 'session_id']
2025-09-29 02:13:26 | INFO     | [debug_RESULT] |    └─ Data Size: 18063 bytes
2025-09-29 02:13:26 | INFO     | [debug_RESULT] |    └─ Storage Time: 2025-09-29T02:13:26.112341
2025-09-29 02:13:26 | INFO     | [debug_RESULT] | 🔍 DATA STRUCTURE [async_97b1923234b1] STORED_RESULT: ['success', 'data', 'metadata', 'session_id']
2025-09-29 02:13:26 | INFO     | [debug_RESULT] |     └─ success: bool
2025-09-29 02:13:26 | INFO     | [debug_RESULT] |     └─ data: dict with keys ['success', 'analysis_id', 'data', 'execution_time', 'metadata']
2025-09-29 02:13:26 | INFO     | [debug_RESULT] |     └─ metadata: dict with keys ['execution_time', 'user_id', 'analysis_type', 'market_specialization', 'image_count', 'model_used']
2025-09-29 02:13:26 | INFO     | [debug_RESULT] |     └─ session_id: str
2025-09-29 02:27:53 | INFO     | [debug_RESULT] | 💾 RESULT STORED [async_9b9b0fe6ef93]
2025-09-29 02:27:53 | INFO     | [debug_RESULT] |    └─ Keys: ['success', 'data', 'metadata', 'session_id']
2025-09-29 02:27:53 | INFO     | [debug_RESULT] |    └─ Data Size: 16581 bytes
2025-09-29 02:27:53 | INFO     | [debug_RESULT] |    └─ Storage Time: 2025-09-29T02:27:53.485478
2025-09-29 02:27:53 | INFO     | [debug_RESULT] | 🔍 DATA STRUCTURE [async_9b9b0fe6ef93] STORED_RESULT: ['success', 'data', 'metadata', 'session_id']
2025-09-29 02:27:53 | INFO     | [debug_RESULT] |     └─ success: bool
2025-09-29 02:27:53 | INFO     | [debug_RESULT] |     └─ data: dict with keys ['success', 'analysis_id', 'data', 'execution_time', 'metadata']
2025-09-29 02:27:53 | INFO     | [debug_RESULT] |     └─ metadata: dict with keys ['execution_time', 'user_id', 'analysis_type', 'market_specialization', 'image_count', 'model_used']
2025-09-29 02:27:53 | INFO     | [debug_RESULT] |     └─ session_id: str
2025-09-29 02:34:05 | INFO     | [debug_RESULT] | 
============================================================
2025-09-29 02:34:05 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-29 02:34:05 | INFO     | [debug_RESULT] | ============================================================
2025-09-29 02:34:05 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_a5ae2ef7f44e
2025-09-29 02:34:05 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_a5ae2ef7f44e']
2025-09-29 02:34:05 | INFO     | [debug_RESULT] | ℹ️ Cached results: ['async_9b9b0fe6ef93']
2025-09-29 02:34:05 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_a5ae2ef7f44e]
2025-09-29 02:34:05 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-29 02:34:05 | INFO     | [debug_RESULT] | 💾 RESULT STORED [async_a5ae2ef7f44e]
2025-09-29 02:34:05 | INFO     | [debug_RESULT] |    └─ Keys: ['success', 'data', 'metadata', 'session_id']
2025-09-29 02:34:05 | INFO     | [debug_RESULT] |    └─ Data Size: 15379 bytes
2025-09-29 02:34:05 | INFO     | [debug_RESULT] |    └─ Storage Time: 2025-09-29T02:34:05.705748
2025-09-29 02:34:05 | INFO     | [debug_RESULT] | 🔍 DATA STRUCTURE [async_a5ae2ef7f44e] STORED_RESULT: ['success', 'data', 'metadata', 'session_id']
2025-09-29 02:34:05 | INFO     | [debug_RESULT] |     └─ success: bool
2025-09-29 02:34:05 | INFO     | [debug_RESULT] |     └─ data: dict with keys ['success', 'analysis_id', 'data', 'execution_time', 'metadata']
2025-09-29 02:34:05 | INFO     | [debug_RESULT] |     └─ metadata: dict with keys ['execution_time', 'user_id', 'analysis_type', 'market_specialization', 'image_count', 'model_used']
2025-09-29 02:34:05 | INFO     | [debug_RESULT] |     └─ session_id: str
2025-09-29 02:47:36 | INFO     | [debug_RESULT] | 
============================================================
2025-09-29 02:47:36 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-29 02:47:36 | INFO     | [debug_RESULT] | ============================================================
2025-09-29 02:47:36 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_4c3c5aea615e
2025-09-29 02:47:36 | INFO     | [debug_RESULT] | ℹ️ Active tasks: []
2025-09-29 02:47:36 | INFO     | [debug_RESULT] | ℹ️ Cached results: ['async_39487be8168b', 'async_4c3c5aea615e']
2025-09-29 02:47:36 | INFO     | [debug_RESULT] | ✅ RESULT FOUND [async_4c3c5aea615e]: keys=['success', 'error', 'session_id']
2025-09-29 02:47:36 | INFO     | [debug_RESULT] | 🔍 DATA STRUCTURE [async_4c3c5aea615e] RETRIEVED_RESULT: ['success', 'error', 'session_id']
2025-09-29 02:47:36 | INFO     | [debug_RESULT] |     └─ success: bool
2025-09-29 02:47:36 | INFO     | [debug_RESULT] |     └─ error: str
2025-09-29 02:47:36 | INFO     | [debug_RESULT] |     └─ session_id: str
2025-09-29 02:47:36 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=False, status=failed, has_data=False
2025-09-29 03:03:54 | INFO     | [debug_RESULT] | 
============================================================
2025-09-29 03:03:54 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-29 03:03:54 | INFO     | [debug_RESULT] | ============================================================
2025-09-29 03:03:54 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_db49593a6419
2025-09-29 03:03:54 | INFO     | [debug_RESULT] | ℹ️ Active tasks: []
2025-09-29 03:03:54 | INFO     | [debug_RESULT] | ℹ️ Cached results: ['async_84b5ccec7f7b', 'async_db49593a6419']
2025-09-29 03:03:54 | INFO     | [debug_RESULT] | ✅ RESULT FOUND [async_db49593a6419]: keys=['success', 'error', 'session_id']
2025-09-29 03:03:54 | INFO     | [debug_RESULT] | 🔍 DATA STRUCTURE [async_db49593a6419] RETRIEVED_RESULT: ['success', 'error', 'session_id']
2025-09-29 03:03:54 | INFO     | [debug_RESULT] |     └─ success: bool
2025-09-29 03:03:54 | INFO     | [debug_RESULT] |     └─ error: str
2025-09-29 03:03:54 | INFO     | [debug_RESULT] |     └─ session_id: str
2025-09-29 03:03:54 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=False, status=failed, has_data=False
2025-09-29 09:02:53 | INFO     | [debug_RESULT] | 
============================================================
2025-09-29 09:02:53 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-29 09:02:53 | INFO     | [debug_RESULT] | ============================================================
2025-09-29 09:02:53 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_f2da9bb1f2db
2025-09-29 09:02:53 | INFO     | [debug_RESULT] | ℹ️ Active tasks: []
2025-09-29 09:02:53 | INFO     | [debug_RESULT] | ℹ️ Cached results: ['async_f2da9bb1f2db']
2025-09-29 09:02:53 | INFO     | [debug_RESULT] | ✅ RESULT FOUND [async_f2da9bb1f2db]: keys=['success', 'error', 'session_id']
2025-09-29 09:02:53 | INFO     | [debug_RESULT] | 🔍 DATA STRUCTURE [async_f2da9bb1f2db] RETRIEVED_RESULT: ['success', 'error', 'session_id']
2025-09-29 09:02:53 | INFO     | [debug_RESULT] |     └─ success: bool
2025-09-29 09:02:53 | INFO     | [debug_RESULT] |     └─ error: str
2025-09-29 09:02:53 | INFO     | [debug_RESULT] |     └─ session_id: str
2025-09-29 09:02:53 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=False, status=failed, has_data=False
2025-09-29 09:42:35 | INFO     | [debug_RESULT] | 
============================================================
2025-09-29 09:42:35 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-29 09:42:35 | INFO     | [debug_RESULT] | ============================================================
2025-09-29 09:42:35 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_a3d6ccc67456
2025-09-29 09:42:35 | INFO     | [debug_RESULT] | ℹ️ Active tasks: []
2025-09-29 09:42:35 | INFO     | [debug_RESULT] | ℹ️ Cached results: ['async_a3d6ccc67456', 'async_36c1a608c13e']
2025-09-29 09:42:35 | INFO     | [debug_RESULT] | ✅ RESULT FOUND [async_a3d6ccc67456]: keys=['success', 'error', 'session_id']
2025-09-29 09:42:35 | INFO     | [debug_RESULT] | 🔍 DATA STRUCTURE [async_a3d6ccc67456] RETRIEVED_RESULT: ['success', 'error', 'session_id']
2025-09-29 09:42:35 | INFO     | [debug_RESULT] |     └─ success: bool
2025-09-29 09:42:35 | INFO     | [debug_RESULT] |     └─ error: str
2025-09-29 09:42:35 | INFO     | [debug_RESULT] |     └─ session_id: str
2025-09-29 09:42:35 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=False, status=failed, has_data=False
2025-09-29 10:19:54 | INFO     | [debug_RESULT] | 
============================================================
2025-09-29 10:19:54 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-29 10:19:54 | INFO     | [debug_RESULT] | ============================================================
2025-09-29 10:19:54 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_6952a8bae988
2025-09-29 10:19:54 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_6952a8bae988']
2025-09-29 10:19:54 | INFO     | [debug_RESULT] | ℹ️ Cached results: []
2025-09-29 10:19:54 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_6952a8bae988]
2025-09-29 10:19:54 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-29 10:19:55 | INFO     | [debug_RESULT] | 💾 RESULT STORED [async_6952a8bae988]
2025-09-29 10:19:55 | INFO     | [debug_RESULT] |    └─ Keys: ['success', 'data', 'metadata', 'session_id']
2025-09-29 10:19:55 | INFO     | [debug_RESULT] |    └─ Data Size: 14204 bytes
2025-09-29 10:19:55 | INFO     | [debug_RESULT] |    └─ Storage Time: 2025-09-29T10:19:55.161491
2025-09-29 10:19:55 | INFO     | [debug_RESULT] | 🔍 DATA STRUCTURE [async_6952a8bae988] STORED_RESULT: ['success', 'data', 'metadata', 'session_id']
2025-09-29 10:19:55 | INFO     | [debug_RESULT] |     └─ success: bool
2025-09-29 10:19:55 | INFO     | [debug_RESULT] |     └─ data: dict with keys ['success', 'analysis_id', 'data', 'execution_time', 'metadata']
2025-09-29 10:19:55 | INFO     | [debug_RESULT] |     └─ metadata: dict with keys ['execution_time', 'user_id', 'analysis_type', 'market_specialization', 'image_count', 'model_used']
2025-09-29 10:19:55 | INFO     | [debug_RESULT] |     └─ session_id: str
2025-09-29 12:37:30 | INFO     | [debug_RESULT] | 
============================================================
2025-09-29 12:37:30 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-29 12:37:30 | INFO     | [debug_RESULT] | ============================================================
2025-09-29 12:37:30 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_c166a9acfe64
2025-09-29 12:37:30 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_c166a9acfe64']
2025-09-29 12:37:30 | INFO     | [debug_RESULT] | ℹ️ Cached results: []
2025-09-29 12:37:30 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_c166a9acfe64]
2025-09-29 12:37:30 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-29 12:37:31 | INFO     | [debug_RESULT] | 💾 RESULT STORED [async_c166a9acfe64]
2025-09-29 12:37:31 | INFO     | [debug_RESULT] |    └─ Keys: ['success', 'data', 'metadata', 'session_id']
2025-09-29 12:37:31 | INFO     | [debug_RESULT] |    └─ Data Size: 12578 bytes
2025-09-29 12:37:31 | INFO     | [debug_RESULT] |    └─ Storage Time: 2025-09-29T12:37:31.126803
2025-09-29 12:37:31 | INFO     | [debug_RESULT] | 🔍 DATA STRUCTURE [async_c166a9acfe64] STORED_RESULT: ['success', 'data', 'metadata', 'session_id']
2025-09-29 12:37:31 | INFO     | [debug_RESULT] |     └─ success: bool
2025-09-29 12:37:31 | INFO     | [debug_RESULT] |     └─ data: dict with keys ['success', 'analysis_id', 'data', 'execution_time', 'metadata']
2025-09-29 12:37:31 | INFO     | [debug_RESULT] |     └─ metadata: dict with keys ['execution_time', 'user_id', 'analysis_type', 'market_specialization', 'image_count', 'model_used']
2025-09-29 12:37:31 | INFO     | [debug_RESULT] |     └─ session_id: str
2025-09-29 13:40:30 | INFO     | [debug_RESULT] | 
============================================================
2025-09-29 13:40:30 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-29 13:40:30 | INFO     | [debug_RESULT] | ============================================================
2025-09-29 13:40:30 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_4ca491b4062c
2025-09-29 13:40:30 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_4ca491b4062c']
2025-09-29 13:40:30 | INFO     | [debug_RESULT] | ℹ️ Cached results: []
2025-09-29 13:40:30 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_4ca491b4062c]
2025-09-29 13:40:30 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-29 13:40:30 | INFO     | [debug_RESULT] | 💾 RESULT STORED [async_4ca491b4062c]
2025-09-29 13:40:30 | INFO     | [debug_RESULT] |    └─ Keys: ['success', 'data', 'metadata', 'session_id']
2025-09-29 13:40:30 | INFO     | [debug_RESULT] |    └─ Data Size: 13604 bytes
2025-09-29 13:40:30 | INFO     | [debug_RESULT] |    └─ Storage Time: 2025-09-29T13:40:30.318999
2025-09-29 13:40:30 | INFO     | [debug_RESULT] | 🔍 DATA STRUCTURE [async_4ca491b4062c] STORED_RESULT: ['success', 'data', 'metadata', 'session_id']
2025-09-29 13:40:30 | INFO     | [debug_RESULT] |     └─ success: bool
2025-09-29 13:40:30 | INFO     | [debug_RESULT] |     └─ data: dict with keys ['success', 'analysis_id', 'data', 'execution_time', 'metadata']
2025-09-29 13:40:30 | INFO     | [debug_RESULT] |     └─ metadata: dict with keys ['execution_time', 'user_id', 'analysis_type', 'market_specialization', 'image_count', 'model_used']
2025-09-29 13:40:30 | INFO     | [debug_RESULT] |     └─ session_id: str
2025-09-29 15:09:34 | INFO     | [debug_RESULT] | 💾 RESULT STORED [async_a36ec6ebb42e]
2025-09-29 15:09:34 | INFO     | [debug_RESULT] |    └─ Keys: ['success', 'data', 'metadata', 'session_id']
2025-09-29 15:09:34 | INFO     | [debug_RESULT] |    └─ Data Size: 16148 bytes
2025-09-29 15:09:34 | INFO     | [debug_RESULT] |    └─ Storage Time: 2025-09-29T15:09:34.236746
2025-09-29 15:09:34 | INFO     | [debug_RESULT] | 🔍 DATA STRUCTURE [async_a36ec6ebb42e] STORED_RESULT: ['success', 'data', 'metadata', 'session_id']
2025-09-29 15:09:34 | INFO     | [debug_RESULT] |     └─ success: bool
2025-09-29 15:09:34 | INFO     | [debug_RESULT] |     └─ data: dict with keys ['success', 'analysis_id', 'data', 'execution_time', 'metadata']
2025-09-29 15:09:34 | INFO     | [debug_RESULT] |     └─ metadata: dict with keys ['execution_time', 'user_id', 'analysis_type', 'market_specialization', 'image_count', 'model_used']
2025-09-29 15:09:34 | INFO     | [debug_RESULT] |     └─ session_id: str
2025-09-29 15:09:34 | INFO     | [debug_RESULT] | 
============================================================
2025-09-29 15:09:34 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-29 15:09:34 | INFO     | [debug_RESULT] | ============================================================
2025-09-29 15:09:34 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_a36ec6ebb42e
2025-09-29 15:09:34 | INFO     | [debug_RESULT] | ℹ️ Active tasks: []
2025-09-29 15:09:34 | INFO     | [debug_RESULT] | ℹ️ Cached results: ['async_a36ec6ebb42e']
2025-09-29 15:09:34 | INFO     | [debug_RESULT] | ✅ RESULT FOUND [async_a36ec6ebb42e]: keys=['success', 'data', 'metadata', 'session_id']
2025-09-29 15:09:34 | INFO     | [debug_RESULT] | 🔍 DATA STRUCTURE [async_a36ec6ebb42e] RETRIEVED_RESULT: ['success', 'data', 'metadata', 'session_id']
2025-09-29 15:09:34 | INFO     | [debug_RESULT] |     └─ success: bool
2025-09-29 15:09:34 | INFO     | [debug_RESULT] |     └─ data: dict with keys ['success', 'analysis_id', 'data', 'execution_time', 'metadata']
2025-09-29 15:09:34 | INFO     | [debug_RESULT] |     └─ metadata: dict with keys ['execution_time', 'user_id', 'analysis_type', 'market_specialization', 'image_count', 'model_used']
2025-09-29 15:09:34 | INFO     | [debug_RESULT] |     └─ session_id: str
2025-09-29 15:09:34 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=completed, has_data=True
2025-09-29 15:09:34 | INFO     | [debug_RESULT] | ℹ️ Response data keys: ['success', 'analysis_id', 'data', 'execution_time', 'metadata']
2025-09-29 15:44:11 | INFO     | [debug_RESULT] | 💾 RESULT STORED [async_11c262c36dbb]
2025-09-29 15:44:11 | INFO     | [debug_RESULT] |    └─ Keys: ['success', 'data', 'metadata', 'session_id']
2025-09-29 15:44:11 | INFO     | [debug_RESULT] |    └─ Data Size: 1786 bytes
2025-09-29 15:44:11 | INFO     | [debug_RESULT] |    └─ Storage Time: 2025-09-29T15:44:11.840826
2025-09-29 15:44:11 | INFO     | [debug_RESULT] | 🔍 DATA STRUCTURE [async_11c262c36dbb] STORED_RESULT: ['success', 'data', 'metadata', 'session_id']
2025-09-29 15:44:11 | INFO     | [debug_RESULT] |     └─ success: bool
2025-09-29 15:44:11 | INFO     | [debug_RESULT] |     └─ data: dict with keys ['success', 'analysis_id', 'data', 'execution_time', 'metadata']
2025-09-29 15:44:11 | INFO     | [debug_RESULT] |     └─ metadata: dict with keys ['execution_time', 'user_id', 'analysis_type', 'market_specialization', 'image_count', 'model_used']
2025-09-29 15:44:11 | INFO     | [debug_RESULT] |     └─ session_id: str
2025-09-29 15:58:01 | INFO     | [debug_RESULT] | 💾 RESULT STORED [async_0a3a571ab098]
2025-09-29 15:58:01 | INFO     | [debug_RESULT] |    └─ Keys: ['success', 'data', 'metadata', 'session_id']
2025-09-29 15:58:01 | INFO     | [debug_RESULT] |    └─ Data Size: 1783 bytes
2025-09-29 15:58:01 | INFO     | [debug_RESULT] |    └─ Storage Time: 2025-09-29T15:58:01.615134
2025-09-29 15:58:01 | INFO     | [debug_RESULT] | 🔍 DATA STRUCTURE [async_0a3a571ab098] STORED_RESULT: ['success', 'data', 'metadata', 'session_id']
2025-09-29 15:58:01 | INFO     | [debug_RESULT] |     └─ success: bool
2025-09-29 15:58:01 | INFO     | [debug_RESULT] |     └─ data: dict with keys ['success', 'analysis_id', 'data', 'execution_time', 'metadata']
2025-09-29 15:58:01 | INFO     | [debug_RESULT] |     └─ metadata: dict with keys ['execution_time', 'user_id', 'analysis_type', 'market_specialization', 'image_count', 'model_used']
2025-09-29 15:58:01 | INFO     | [debug_RESULT] |     └─ session_id: str
2025-09-29 15:59:04 | INFO     | [debug_RESULT] | 
============================================================
2025-09-29 15:59:04 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-29 15:59:04 | INFO     | [debug_RESULT] | ============================================================
2025-09-29 15:59:04 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_56ff7d1c2fe1
2025-09-29 15:59:04 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_56ff7d1c2fe1']
2025-09-29 15:59:04 | INFO     | [debug_RESULT] | ℹ️ Cached results: ['async_0a3a571ab098']
2025-09-29 15:59:04 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_56ff7d1c2fe1]
2025-09-29 15:59:04 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-29 15:59:04 | INFO     | [debug_RESULT] | 💾 RESULT STORED [async_56ff7d1c2fe1]
2025-09-29 15:59:04 | INFO     | [debug_RESULT] |    └─ Keys: ['success', 'data', 'metadata', 'session_id']
2025-09-29 15:59:04 | INFO     | [debug_RESULT] |    └─ Data Size: 1784 bytes
2025-09-29 15:59:04 | INFO     | [debug_RESULT] |    └─ Storage Time: 2025-09-29T15:59:04.772129
2025-09-29 15:59:04 | INFO     | [debug_RESULT] | 🔍 DATA STRUCTURE [async_56ff7d1c2fe1] STORED_RESULT: ['success', 'data', 'metadata', 'session_id']
2025-09-29 15:59:04 | INFO     | [debug_RESULT] |     └─ success: bool
2025-09-29 15:59:04 | INFO     | [debug_RESULT] |     └─ data: dict with keys ['success', 'analysis_id', 'data', 'execution_time', 'metadata']
2025-09-29 15:59:04 | INFO     | [debug_RESULT] |     └─ metadata: dict with keys ['execution_time', 'user_id', 'analysis_type', 'market_specialization', 'image_count', 'model_used']
2025-09-29 15:59:04 | INFO     | [debug_RESULT] |     └─ session_id: str
2025-09-29 16:11:37 | INFO     | [debug_RESULT] | 
============================================================
2025-09-29 16:11:37 | INFO     | [debug_RESULT] | 🎯 RESULT ENDPOINT REQUEST
2025-09-29 16:11:37 | INFO     | [debug_RESULT] | ============================================================
2025-09-29 16:11:37 | INFO     | [debug_RESULT] | ℹ️ Getting result for session: async_c50e7081c22c
2025-09-29 16:11:37 | INFO     | [debug_RESULT] | ℹ️ Active tasks: ['async_c50e7081c22c']
2025-09-29 16:11:37 | INFO     | [debug_RESULT] | ℹ️ Cached results: ['async_0a3a571ab098', 'async_56ff7d1c2fe1']
2025-09-29 16:11:37 | INFO     | [debug_RESULT] | ❌ RESULT NOT FOUND [async_c50e7081c22c]
2025-09-29 16:11:37 | INFO     | [debug_RESULT] | ℹ️ Endpoint returning: success=True, status=running, has_data=False
2025-09-29 16:11:38 | INFO     | [debug_RESULT] | 💾 RESULT STORED [async_c50e7081c22c]
2025-09-29 16:11:38 | INFO     | [debug_RESULT] |    └─ Keys: ['success', 'data', 'metadata', 'session_id']
2025-09-29 16:11:38 | INFO     | [debug_RESULT] |    └─ Data Size: 1785 bytes
2025-09-29 16:11:38 | INFO     | [debug_RESULT] |    └─ Storage Time: 2025-09-29T16:11:38.747520
2025-09-29 16:11:38 | INFO     | [debug_RESULT] | 🔍 DATA STRUCTURE [async_c50e7081c22c] STORED_RESULT: ['success', 'data', 'metadata', 'session_id']
2025-09-29 16:11:38 | INFO     | [debug_RESULT] |     └─ success: bool
2025-09-29 16:11:38 | INFO     | [debug_RESULT] |     └─ data: dict with keys ['success', 'analysis_id', 'data', 'execution_time', 'metadata']
2025-09-29 16:11:38 | INFO     | [debug_RESULT] |     └─ metadata: dict with keys ['execution_time', 'user_id', 'analysis_type', 'market_specialization', 'image_count', 'model_used']
2025-09-29 16:11:38 | INFO     | [debug_RESULT] |     └─ session_id: str
