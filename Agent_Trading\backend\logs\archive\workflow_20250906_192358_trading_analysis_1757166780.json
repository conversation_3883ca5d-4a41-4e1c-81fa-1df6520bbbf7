{"workflow_id": "trading_analysis_1757166780", "start_time": "2025-09-06T19:23:00.959086", "context": {"analysis_mode": "positional", "market_specialization": "crypto", "chart_count": 1}, "steps": [{"step_number": 1, "step_name": "Chart Analysis", "step_type": "llm_call", "timestamp": "2025-09-06T19:23:00.959431", "execution_time": null, "status": "success", "error": null, "input_summary": "Chart image for symbol detection"}, {"step_number": 2, "step_name": "Chart Analysis", "step_type": "llm_call", "timestamp": "2025-09-06T19:23:11.535264", "execution_time": 10.575398921966553, "status": "success", "error": null, "output_summary": "Dict with keys: ['detected_symbol', 'market_type', 'timeframe', 'all_timeframes', 'current_price']..."}, {"step_number": 3, "step_name": "Step 2: Tool Selection", "step_type": "api_calls", "timestamp": "2025-09-06T19:23:11.535859", "execution_time": null, "status": "success", "error": null, "input_summary": "Market: crypto, Symbol: BTCUSD"}, {"step_number": 4, "step_name": "Step 2: Tool Selection", "step_type": "api_calls", "timestamp": "2025-09-06T19:23:19.202202", "execution_time": 7.666016578674316, "status": "success", "error": null, "output_summary": "Dict with keys: ['success', 'tool_results', 'tool_usage_log', 'execution_time']"}, {"step_number": 5, "step_name": "Step 3: Flash Summarization", "step_type": "llm_call", "timestamp": "2025-09-06T19:23:19.202632", "execution_time": null, "status": "success", "error": null, "input_summary": "Tool results for summarization"}, {"step_number": 6, "step_name": "Step 3: Flash Summarization", "step_type": "llm_call", "timestamp": "2025-09-06T19:23:31.561406", "execution_time": 12.358415842056274, "status": "success", "error": null, "output_summary": "Dict with keys: ['success', 'summarized_tools', 'tool_count']"}, {"step_number": 7, "step_name": "Step 4: Final Analysis", "step_type": "llm_call", "timestamp": "2025-09-06T19:23:31.723664", "execution_time": null, "status": "success", "error": null, "input_summary": "Chart + Tool summaries + RAG tool available"}, {"step_number": 8, "step_name": "Final Analysis", "step_type": "llm_call", "timestamp": "2025-09-06T19:23:58.681959", "execution_time": 26.95795226097107, "status": "success", "error": null, "output_summary": "Dict with keys: ['success', 'analysis', 'analysis_notes', 'trading_signals', 'detected_symbol']..."}], "llm_calls": [], "summary": {"total_steps": 8, "total_llm_calls": 0, "total_tokens": 0, "total_cost": 0.0, "execution_time": 57.74280881881714}, "end_time": "2025-09-06T19:23:58.701930", "status": "success", "error": null}