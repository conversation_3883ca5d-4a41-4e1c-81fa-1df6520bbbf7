[2025-09-29 12:35:34] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔥 BROADCASTING update for session async_c166a9acfe64 | Data: {'step': 'chart_upload', 'progress': 5, 'message': 'Analysis request received, starting background processing...', 'has_clients': False, 'client_count': 0, 'broadcaster_id': 2361389686224, 'all_sessions': []}
[2025-09-29 12:35:35] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | No clients found for session async_c166a9acfe64 - update buffered (total: 1) | Data: {}
[2025-09-29 12:35:35] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔥 BROADCASTING update for session async_c166a9acfe64 | Data: {'step': 'chart_upload', 'progress': 5, 'message': '📊 Processing uploaded chart images...', 'has_clients': False, 'client_count': 0, 'broadcaster_id': 2361389686224, 'all_sessions': []}
[2025-09-29 12:35:35] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | No clients found for session async_c166a9acfe64 - update buffered (total: 2) | Data: {}
[2025-09-29 12:35:35] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔥 BROADCASTING update for session async_c166a9acfe64 | Data: {'step': 'chart_upload', 'progress': 5, 'message': '🖼️ Processed chart image 1/1 (75KB)', 'has_clients': False, 'client_count': 0, 'broadcaster_id': 2361389686224, 'all_sessions': []}
[2025-09-29 12:35:35] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | No clients found for session async_c166a9acfe64 - update buffered (total: 3) | Data: {}
[2025-09-29 12:35:35] [INFO] [progress_debug] 🔗 SSE_CONNECTION | Session: async_c166a9acfe64 | Client: {'user_id': UUID('cd218226-a7f5-4bd8-a242-eadc1d480326'), 'user_email': '<EMAIL>', 'endpoint': '/api/v1/progress/stream/async_c166a9acfe64'}
[2025-09-29 12:35:35] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Adding client for session async_c166a9acfe64 | Data: {}
[2025-09-29 12:35:35] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | ✅ CLIENT ADDED for session async_c166a9acfe64 | Data: {'total_clients': 1, 'all_sessions': ['async_c166a9acfe64'], 'broadcaster_id': 2361389686224}
[2025-09-29 12:35:35] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Current broadcaster state | Data: {'session_id': 'async_c166a9acfe64', 'clients_dict': {'async_c166a9acfe64': 1}, 'total_sessions': 1}
[2025-09-29 12:35:35] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Sending 3 buffered updates to new client | Data: {}
[2025-09-29 12:35:35] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Cleared buffered updates for session async_c166a9acfe64 | Data: {}
[2025-09-29 12:35:35] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Client queue created for session async_c166a9acfe64 | Data: {}
[2025-09-29 12:35:35] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending connection confirmation: {'type': 'connected', 'session_id': 'async_c166a9acfe64'} | Data: {}
[2025-09-29 12:35:35] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_c166a9acfe64 | Data: {}
[2025-09-29 12:35:35] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Received progress update: {'step': 'chart_upload', 'progress': 5, 'message': 'Analysis request received, starting background processing...', 'timestamp': '2025-09-29T07:05:34.763435', 'details': {'user_id': 'cd218226-a7f5-4bd8-a242-eadc1d480326', 'analysis_type': 'Positional', 'image_count': 1}, 'tool_results': None} | Data: {}
[2025-09-29 12:35:35] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending SSE event: {'type': 'progress_update', 'data': {'step': 'chart_upload', 'progress': 5, 'message': 'Analysis request received, starting background processing...', 'timestamp': '2025-09-29T07:05:34.763435', 'details': {'user_id': 'cd218226-a7f5-4bd8-a242-eadc1d480326', 'analysis_type': 'Positional', 'image_count': 1}, 'tool_results': None}} | Data: {}
[2025-09-29 12:35:35] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_c166a9acfe64 | Data: {}
[2025-09-29 12:35:35] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Received progress update: {'step': 'chart_upload', 'progress': 5, 'message': '📊 Processing uploaded chart images...', 'timestamp': '2025-09-29T07:05:35.326968', 'details': {'user_id': 'cd218226-a7f5-4bd8-a242-eadc1d480326', 'analysis_type': 'Positional', 'image_count': 1}, 'tool_results': None} | Data: {}
[2025-09-29 12:35:35] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending SSE event: {'type': 'progress_update', 'data': {'step': 'chart_upload', 'progress': 5, 'message': '📊 Processing uploaded chart images...', 'timestamp': '2025-09-29T07:05:35.326968', 'details': {'user_id': 'cd218226-a7f5-4bd8-a242-eadc1d480326', 'analysis_type': 'Positional', 'image_count': 1}, 'tool_results': None}} | Data: {}
[2025-09-29 12:35:35] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_c166a9acfe64 | Data: {}
[2025-09-29 12:35:35] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Received progress update: {'step': 'chart_upload', 'progress': 5, 'message': '🖼️ Processed chart image 1/1 (75KB)', 'timestamp': '2025-09-29T07:05:35.358435', 'details': {'image_size': 77391, 'images_processed': 1}, 'tool_results': None} | Data: {}
[2025-09-29 12:35:35] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending SSE event: {'type': 'progress_update', 'data': {'step': 'chart_upload', 'progress': 5, 'message': '🖼️ Processed chart image 1/1 (75KB)', 'timestamp': '2025-09-29T07:05:35.358435', 'details': {'image_size': 77391, 'images_processed': 1}, 'tool_results': None}} | Data: {}
[2025-09-29 12:35:35] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_c166a9acfe64 | Data: {}
[2025-09-29 12:35:38] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 237454.171} | Data: {}
[2025-09-29 12:35:38] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_c166a9acfe64 | Data: {}
[2025-09-29 12:35:39] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 237455.171} | Data: {}
[2025-09-29 12:35:39] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_c166a9acfe64 | Data: {}
[2025-09-29 12:35:39] [INFO] [progress_debug] 🔄 SESSION_LIFECYCLE | Session: async_c166a9acfe64 | Event: workflow_start | Details: {'analysis_mode': 'positional', 'market_specialization': 'Crypto', 'has_progress_broadcaster': True, 'chart_images_count': 1, 'parameter_passing_issue': False}
[2025-09-29 12:35:40] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 237456.25} | Data: {}
[2025-09-29 12:35:40] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_c166a9acfe64 | Data: {}
[2025-09-29 12:35:41] [INFO] [progress_debug] 🔄 WORKFLOW_STEP | Session: async_c166a9acfe64 | Step: chart_analysis | Progress: 20% | Message: 🚀 Starting comprehensive AI analysis workflow...
[2025-09-29 12:35:41] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Mapped step 'chart_analysis' to chart_analysis | Data: {}
[2025-09-29 12:35:41] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Created progress update: ProgressUpdate(step=<WorkflowStep.CHART_ANALYSIS: 'chart_analysis'>, progress=20, message='🚀 Starting comprehensive AI analysis workflow...', timestamp='2025-09-29T07:05:41.959360', details={'step': 'chart_analysis', 'stage': 'start'}, tool_results=None) | Data: {}
[2025-09-29 12:35:41] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔥 BROADCASTING update for session async_c166a9acfe64 | Data: {'step': 'chart_analysis', 'progress': 20, 'message': '🚀 Starting comprehensive AI analysis workflow...', 'has_clients': True, 'client_count': 1, 'broadcaster_id': 2361389686224, 'all_sessions': ['async_c166a9acfe64']}
[2025-09-29 12:35:42] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Sent update to client 0 for session async_c166a9acfe64 | Data: {}
[2025-09-29 12:35:42] [INFO] [progress_debug] 📡 PROGRESS_BROADCAST | ✅ SUCCESS | Session: async_c166a9acfe64 | Step: chart_analysis | Details: {'workflow_step': 'chart_analysis', 'progress': 20, 'message': '🚀 Starting comprehensive AI analysis workflow...'}
[2025-09-29 12:35:42] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Received progress update: {'step': 'chart_analysis', 'progress': 20, 'message': '🚀 Starting comprehensive AI analysis workflow...', 'timestamp': '2025-09-29T07:05:41.959360', 'details': {'step': 'chart_analysis', 'stage': 'start'}, 'tool_results': None} | Data: {}
[2025-09-29 12:35:42] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending SSE event: {'type': 'progress_update', 'data': {'step': 'chart_analysis', 'progress': 20, 'message': '🚀 Starting comprehensive AI analysis workflow...', 'timestamp': '2025-09-29T07:05:41.959360', 'details': {'step': 'chart_analysis', 'stage': 'start'}, 'tool_results': None}} | Data: {}
[2025-09-29 12:35:42] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_c166a9acfe64 | Data: {}
[2025-09-29 12:35:43] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 237458.359} | Data: {}
[2025-09-29 12:35:43] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_c166a9acfe64 | Data: {}
[2025-09-29 12:35:44] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 237459.39} | Data: {}
[2025-09-29 12:35:44] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_c166a9acfe64 | Data: {}
[2025-09-29 12:35:45] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 237460.406} | Data: {}
[2025-09-29 12:35:45] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_c166a9acfe64 | Data: {}
[2025-09-29 12:35:46] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 237461.421} | Data: {}
[2025-09-29 12:35:46] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_c166a9acfe64 | Data: {}
[2025-09-29 12:35:50] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 237465.296} | Data: {}
[2025-09-29 12:35:50] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_c166a9acfe64 | Data: {}
[2025-09-29 12:35:53] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 237468.515} | Data: {}
[2025-09-29 12:35:53] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_c166a9acfe64 | Data: {}
[2025-09-29 12:35:54] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 237469.531} | Data: {}
[2025-09-29 12:35:54] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_c166a9acfe64 | Data: {}
[2025-09-29 12:35:55] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 237470.531} | Data: {}
[2025-09-29 12:35:55] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_c166a9acfe64 | Data: {}
[2025-09-29 12:35:56] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 237471.531} | Data: {}
[2025-09-29 12:35:56] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_c166a9acfe64 | Data: {}
[2025-09-29 12:35:57] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 237472.562} | Data: {}
[2025-09-29 12:35:57] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_c166a9acfe64 | Data: {}
[2025-09-29 12:35:58] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 237473.578} | Data: {}
[2025-09-29 12:35:58] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_c166a9acfe64 | Data: {}
[2025-09-29 12:35:59] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 237474.578} | Data: {}
[2025-09-29 12:35:59] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_c166a9acfe64 | Data: {}
[2025-09-29 12:36:00] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 237475.609} | Data: {}
[2025-09-29 12:36:00] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_c166a9acfe64 | Data: {}
[2025-09-29 12:36:01] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 237476.656} | Data: {}
[2025-09-29 12:36:01] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_c166a9acfe64 | Data: {}
[2025-09-29 12:36:02] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 237477.687} | Data: {}
[2025-09-29 12:36:02] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_c166a9acfe64 | Data: {}
[2025-09-29 12:36:03] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 237478.687} | Data: {}
[2025-09-29 12:36:03] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_c166a9acfe64 | Data: {}
[2025-09-29 12:36:04] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 237479.687} | Data: {}
[2025-09-29 12:36:04] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_c166a9acfe64 | Data: {}
[2025-09-29 12:36:05] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 237480.703} | Data: {}
[2025-09-29 12:36:05] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_c166a9acfe64 | Data: {}
[2025-09-29 12:36:06] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 237481.718} | Data: {}
[2025-09-29 12:36:06] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_c166a9acfe64 | Data: {}
[2025-09-29 12:36:07] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 237482.734} | Data: {}
[2025-09-29 12:36:07] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_c166a9acfe64 | Data: {}
[2025-09-29 12:36:08] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 237483.812} | Data: {}
[2025-09-29 12:36:08] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_c166a9acfe64 | Data: {}
[2025-09-29 12:36:09] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 237484.906} | Data: {}
[2025-09-29 12:36:09] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_c166a9acfe64 | Data: {}
[2025-09-29 12:36:09] [INFO] [progress_debug] 🔄 WORKFLOW_STEP | Session: async_c166a9acfe64 | Step: symbol_detection | Progress: 40% | Message: ✅ Symbol detected: Bitcoin / U.S. Dollar (crypto market)
[2025-09-29 12:36:09] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Mapped step 'symbol_detection' to symbol_detection | Data: {}
[2025-09-29 12:36:09] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Created progress update: ProgressUpdate(step=<WorkflowStep.SYMBOL_DETECTION: 'symbol_detection'>, progress=40, message='✅ Symbol detected: Bitcoin / U.S. Dollar (crypto market)', timestamp='2025-09-29T07:06:09.758389', details={'step': 'symbol_detection', 'symbol': 'Bitcoin / U.S. Dollar', 'market': 'crypto'}, tool_results=None) | Data: {}
[2025-09-29 12:36:09] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔥 BROADCASTING update for session async_c166a9acfe64 | Data: {'step': 'symbol_detection', 'progress': 40, 'message': '✅ Symbol detected: Bitcoin / U.S. Dollar (crypto market)', 'has_clients': True, 'client_count': 1, 'broadcaster_id': 2361389686224, 'all_sessions': ['async_c166a9acfe64']}
[2025-09-29 12:36:09] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Sent update to client 0 for session async_c166a9acfe64 | Data: {}
[2025-09-29 12:36:09] [INFO] [progress_debug] 📡 PROGRESS_BROADCAST | ✅ SUCCESS | Session: async_c166a9acfe64 | Step: symbol_detection | Details: {'workflow_step': 'symbol_detection', 'progress': 40, 'message': '✅ Symbol detected: Bitcoin / U.S. Dollar (crypto market)'}
[2025-09-29 12:36:09] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Received progress update: {'step': 'symbol_detection', 'progress': 40, 'message': '✅ Symbol detected: Bitcoin / U.S. Dollar (crypto market)', 'timestamp': '2025-09-29T07:06:09.758389', 'details': {'step': 'symbol_detection', 'symbol': 'Bitcoin / U.S. Dollar', 'market': 'crypto'}, 'tool_results': None} | Data: {}
[2025-09-29 12:36:09] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending SSE event: {'type': 'progress_update', 'data': {'step': 'symbol_detection', 'progress': 40, 'message': '✅ Symbol detected: Bitcoin / U.S. Dollar (crypto market)', 'timestamp': '2025-09-29T07:06:09.758389', 'details': {'step': 'symbol_detection', 'symbol': 'Bitcoin / U.S. Dollar', 'market': 'crypto'}, 'tool_results': None}} | Data: {}
[2025-09-29 12:36:09] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_c166a9acfe64 | Data: {}
[2025-09-29 12:36:09] [INFO] [progress_debug] 🔄 WORKFLOW_STEP | Session: async_c166a9acfe64 | Step: tool_execution | Progress: 50% | Message: 🎯 Executing market data tools...
[2025-09-29 12:36:09] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Mapped step 'tool_execution' to tool_execution | Data: {}
[2025-09-29 12:36:09] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Created progress update: ProgressUpdate(step=<WorkflowStep.TOOL_EXECUTION: 'tool_execution'>, progress=50, message='🎯 Executing market data tools...', timestamp='2025-09-29T07:06:09.967431', details={'step': 'tool_execution', 'market': 'crypto'}, tool_results=None) | Data: {}
[2025-09-29 12:36:09] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔥 BROADCASTING update for session async_c166a9acfe64 | Data: {'step': 'tool_execution', 'progress': 50, 'message': '🎯 Executing market data tools...', 'has_clients': True, 'client_count': 1, 'broadcaster_id': 2361389686224, 'all_sessions': ['async_c166a9acfe64']}
[2025-09-29 12:36:09] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Sent update to client 0 for session async_c166a9acfe64 | Data: {}
[2025-09-29 12:36:09] [INFO] [progress_debug] 📡 PROGRESS_BROADCAST | ✅ SUCCESS | Session: async_c166a9acfe64 | Step: tool_execution | Details: {'workflow_step': 'tool_execution', 'progress': 50, 'message': '🎯 Executing market data tools...'}
[2025-09-29 12:36:10] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Received progress update: {'step': 'tool_execution', 'progress': 50, 'message': '🎯 Executing market data tools...', 'timestamp': '2025-09-29T07:06:09.967431', 'details': {'step': 'tool_execution', 'market': 'crypto'}, 'tool_results': None} | Data: {}
[2025-09-29 12:36:10] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending SSE event: {'type': 'progress_update', 'data': {'step': 'tool_execution', 'progress': 50, 'message': '🎯 Executing market data tools...', 'timestamp': '2025-09-29T07:06:09.967431', 'details': {'step': 'tool_execution', 'market': 'crypto'}, 'tool_results': None}} | Data: {}
[2025-09-29 12:36:10] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_c166a9acfe64 | Data: {}
[2025-09-29 12:36:11] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 237486.421} | Data: {}
[2025-09-29 12:36:11] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_c166a9acfe64 | Data: {}
[2025-09-29 12:36:12] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 237487.468} | Data: {}
[2025-09-29 12:36:12] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_c166a9acfe64 | Data: {}
[2025-09-29 12:36:13] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 237488.484} | Data: {}
[2025-09-29 12:36:13] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_c166a9acfe64 | Data: {}
[2025-09-29 12:36:14] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 237489.515} | Data: {}
[2025-09-29 12:36:14] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_c166a9acfe64 | Data: {}
[2025-09-29 12:36:15] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 237490.546} | Data: {}
[2025-09-29 12:36:15] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_c166a9acfe64 | Data: {}
[2025-09-29 12:36:16] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 237491.562} | Data: {}
[2025-09-29 12:36:16] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_c166a9acfe64 | Data: {}
[2025-09-29 12:36:16] [INFO] [progress_debug] 🔄 WORKFLOW_STEP | Session: async_c166a9acfe64 | Step: tool_summarization | Progress: 60% | Message: 🎯 Summarizing market data with AI...
[2025-09-29 12:36:16] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Mapped step 'tool_summarization' to tool_summarization | Data: {}
[2025-09-29 12:36:16] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Created progress update: ProgressUpdate(step=<WorkflowStep.TOOL_SUMMARIZATION: 'tool_summarization'>, progress=60, message='🎯 Summarizing market data with AI...', timestamp='2025-09-29T07:06:16.613116', details={'step': 'tool_summarization'}, tool_results=None) | Data: {}
[2025-09-29 12:36:16] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔥 BROADCASTING update for session async_c166a9acfe64 | Data: {'step': 'tool_summarization', 'progress': 60, 'message': '🎯 Summarizing market data with AI...', 'has_clients': True, 'client_count': 1, 'broadcaster_id': 2361389686224, 'all_sessions': ['async_c166a9acfe64']}
[2025-09-29 12:36:16] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Sent update to client 0 for session async_c166a9acfe64 | Data: {}
[2025-09-29 12:36:16] [INFO] [progress_debug] 📡 PROGRESS_BROADCAST | ✅ SUCCESS | Session: async_c166a9acfe64 | Step: tool_summarization | Details: {'workflow_step': 'tool_summarization', 'progress': 60, 'message': '🎯 Summarizing market data with AI...'}
[2025-09-29 12:36:16] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Received progress update: {'step': 'tool_summarization', 'progress': 60, 'message': '🎯 Summarizing market data with AI...', 'timestamp': '2025-09-29T07:06:16.613116', 'details': {'step': 'tool_summarization'}, 'tool_results': None} | Data: {}
[2025-09-29 12:36:16] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending SSE event: {'type': 'progress_update', 'data': {'step': 'tool_summarization', 'progress': 60, 'message': '🎯 Summarizing market data with AI...', 'timestamp': '2025-09-29T07:06:16.613116', 'details': {'step': 'tool_summarization'}, 'tool_results': None}} | Data: {}
[2025-09-29 12:36:16] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_c166a9acfe64 | Data: {}
[2025-09-29 12:36:21] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 237496.296} | Data: {}
[2025-09-29 12:36:21] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_c166a9acfe64 | Data: {}
[2025-09-29 12:36:25] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 237500.828} | Data: {}
[2025-09-29 12:36:25] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_c166a9acfe64 | Data: {}
[2025-09-29 12:36:26] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 237501.859} | Data: {}
[2025-09-29 12:36:26] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_c166a9acfe64 | Data: {}
[2025-09-29 12:36:27] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 237502.875} | Data: {}
[2025-09-29 12:36:27] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_c166a9acfe64 | Data: {}
[2025-09-29 12:36:28] [INFO] [progress_debug] 🔄 WORKFLOW_STEP | Session: async_c166a9acfe64 | Step: rag_integration | Progress: 70% | Message: 💾 Integrating historical context and patterns...
[2025-09-29 12:36:28] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Mapped step 'rag_integration' to rag_integration | Data: {}
[2025-09-29 12:36:28] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Created progress update: ProgressUpdate(step=<WorkflowStep.RAG_INTEGRATION: 'rag_integration'>, progress=70, message='💾 Integrating historical context and patterns...', timestamp='2025-09-29T07:06:28.346716', details={'step': 'rag_integration'}, tool_results=None) | Data: {}
[2025-09-29 12:36:28] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔥 BROADCASTING update for session async_c166a9acfe64 | Data: {'step': 'rag_integration', 'progress': 70, 'message': '💾 Integrating historical context and patterns...', 'has_clients': True, 'client_count': 1, 'broadcaster_id': 2361389686224, 'all_sessions': ['async_c166a9acfe64']}
[2025-09-29 12:36:28] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Sent update to client 0 for session async_c166a9acfe64 | Data: {}
[2025-09-29 12:36:28] [INFO] [progress_debug] 📡 PROGRESS_BROADCAST | ✅ SUCCESS | Session: async_c166a9acfe64 | Step: rag_integration | Details: {'workflow_step': 'rag_integration', 'progress': 70, 'message': '💾 Integrating historical context and patterns...'}
[2025-09-29 12:36:28] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Received progress update: {'step': 'rag_integration', 'progress': 70, 'message': '💾 Integrating historical context and patterns...', 'timestamp': '2025-09-29T07:06:28.346716', 'details': {'step': 'rag_integration'}, 'tool_results': None} | Data: {}
[2025-09-29 12:36:28] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending SSE event: {'type': 'progress_update', 'data': {'step': 'rag_integration', 'progress': 70, 'message': '💾 Integrating historical context and patterns...', 'timestamp': '2025-09-29T07:06:28.346716', 'details': {'step': 'rag_integration'}, 'tool_results': None}} | Data: {}
[2025-09-29 12:36:28] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_c166a9acfe64 | Data: {}
[2025-09-29 12:36:29] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 237504.812} | Data: {}
[2025-09-29 12:36:29] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_c166a9acfe64 | Data: {}
[2025-09-29 12:36:30] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 237505.843} | Data: {}
[2025-09-29 12:36:30] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_c166a9acfe64 | Data: {}
[2025-09-29 12:36:31] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 237506.859} | Data: {}
[2025-09-29 12:36:31] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_c166a9acfe64 | Data: {}
[2025-09-29 12:36:37] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 237513.031} | Data: {}
[2025-09-29 12:36:37] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_c166a9acfe64 | Data: {}
[2025-09-29 12:36:38] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 237514.062} | Data: {}
[2025-09-29 12:36:38] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_c166a9acfe64 | Data: {}
[2025-09-29 12:36:39] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 237515.078} | Data: {}
[2025-09-29 12:36:39] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_c166a9acfe64 | Data: {}
[2025-09-29 12:36:40] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 237516.093} | Data: {}
[2025-09-29 12:36:40] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_c166a9acfe64 | Data: {}
[2025-09-29 12:36:41] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 237517.078} | Data: {}
[2025-09-29 12:36:41] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_c166a9acfe64 | Data: {}
[2025-09-29 12:36:42] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 237518.093} | Data: {}
[2025-09-29 12:36:42] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_c166a9acfe64 | Data: {}
[2025-09-29 12:36:43] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 237519.109} | Data: {}
[2025-09-29 12:36:43] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_c166a9acfe64 | Data: {}
[2025-09-29 12:36:44] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 237520.109} | Data: {}
[2025-09-29 12:36:44] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_c166a9acfe64 | Data: {}
[2025-09-29 12:36:45] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 237521.109} | Data: {}
[2025-09-29 12:36:45] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_c166a9acfe64 | Data: {}
[2025-09-29 12:36:46] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 237522.125} | Data: {}
[2025-09-29 12:36:46] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_c166a9acfe64 | Data: {}
[2025-09-29 12:36:49] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 237525.093} | Data: {}
[2025-09-29 12:36:49] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_c166a9acfe64 | Data: {}
[2025-09-29 12:36:52] [INFO] [progress_debug] 🔄 WORKFLOW_STEP | Session: async_c166a9acfe64 | Step: final_analysis | Progress: 80% | Message: 🎯 Generating final trading analysis...
[2025-09-29 12:36:52] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Mapped step 'final_analysis' to final_analysis | Data: {}
[2025-09-29 12:36:52] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Created progress update: ProgressUpdate(step=<WorkflowStep.FINAL_ANALYSIS: 'final_analysis'>, progress=80, message='🎯 Generating final trading analysis...', timestamp='2025-09-29T07:06:52.749298', details={'step': 'final_analysis'}, tool_results=None) | Data: {}
[2025-09-29 12:36:52] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔥 BROADCASTING update for session async_c166a9acfe64 | Data: {'step': 'final_analysis', 'progress': 80, 'message': '🎯 Generating final trading analysis...', 'has_clients': True, 'client_count': 1, 'broadcaster_id': 2361389686224, 'all_sessions': ['async_c166a9acfe64']}
[2025-09-29 12:36:52] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Sent update to client 0 for session async_c166a9acfe64 | Data: {}
[2025-09-29 12:36:52] [INFO] [progress_debug] 📡 PROGRESS_BROADCAST | ✅ SUCCESS | Session: async_c166a9acfe64 | Step: final_analysis | Details: {'workflow_step': 'final_analysis', 'progress': 80, 'message': '🎯 Generating final trading analysis...'}
[2025-09-29 12:36:54] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Received progress update: {'step': 'final_analysis', 'progress': 80, 'message': '🎯 Generating final trading analysis...', 'timestamp': '2025-09-29T07:06:52.749298', 'details': {'step': 'final_analysis'}, 'tool_results': None} | Data: {}
[2025-09-29 12:36:54] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending SSE event: {'type': 'progress_update', 'data': {'step': 'final_analysis', 'progress': 80, 'message': '🎯 Generating final trading analysis...', 'timestamp': '2025-09-29T07:06:52.749298', 'details': {'step': 'final_analysis'}, 'tool_results': None}} | Data: {}
[2025-09-29 12:36:54] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_c166a9acfe64 | Data: {}
[2025-09-29 12:36:55] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 237530.64} | Data: {}
[2025-09-29 12:36:55] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_c166a9acfe64 | Data: {}
[2025-09-29 12:36:56] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 237531.656} | Data: {}
[2025-09-29 12:36:56] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_c166a9acfe64 | Data: {}
[2025-09-29 12:36:57] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 237532.671} | Data: {}
[2025-09-29 12:36:57] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_c166a9acfe64 | Data: {}
[2025-09-29 12:36:58] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 237533.687} | Data: {}
[2025-09-29 12:36:58] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_c166a9acfe64 | Data: {}
[2025-09-29 12:36:59] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 237534.703} | Data: {}
[2025-09-29 12:36:59] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_c166a9acfe64 | Data: {}
[2025-09-29 12:37:00] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 237535.703} | Data: {}
[2025-09-29 12:37:00] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_c166a9acfe64 | Data: {}
[2025-09-29 12:37:01] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 237536.718} | Data: {}
[2025-09-29 12:37:01] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_c166a9acfe64 | Data: {}
[2025-09-29 12:37:02] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 237537.718} | Data: {}
[2025-09-29 12:37:02] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_c166a9acfe64 | Data: {}
[2025-09-29 12:37:03] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 237538.734} | Data: {}
[2025-09-29 12:37:03] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_c166a9acfe64 | Data: {}
[2025-09-29 12:37:04] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 237539.75} | Data: {}
[2025-09-29 12:37:04] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_c166a9acfe64 | Data: {}
[2025-09-29 12:37:05] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 237540.75} | Data: {}
[2025-09-29 12:37:05] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_c166a9acfe64 | Data: {}
[2025-09-29 12:37:06] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 237541.765} | Data: {}
[2025-09-29 12:37:06] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_c166a9acfe64 | Data: {}
[2025-09-29 12:37:07] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 237542.781} | Data: {}
[2025-09-29 12:37:07] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_c166a9acfe64 | Data: {}
[2025-09-29 12:37:08] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 237543.796} | Data: {}
[2025-09-29 12:37:08] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_c166a9acfe64 | Data: {}
[2025-09-29 12:37:09] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 237544.796} | Data: {}
[2025-09-29 12:37:09] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_c166a9acfe64 | Data: {}
[2025-09-29 12:37:10] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 237545.828} | Data: {}
[2025-09-29 12:37:10] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_c166a9acfe64 | Data: {}
[2025-09-29 12:37:11] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 237546.828} | Data: {}
[2025-09-29 12:37:11] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_c166a9acfe64 | Data: {}
[2025-09-29 12:37:12] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 237547.828} | Data: {}
[2025-09-29 12:37:12] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_c166a9acfe64 | Data: {}
[2025-09-29 12:37:13] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 237548.937} | Data: {}
[2025-09-29 12:37:13] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_c166a9acfe64 | Data: {}
[2025-09-29 12:37:14] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 237549.968} | Data: {}
[2025-09-29 12:37:14] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_c166a9acfe64 | Data: {}
[2025-09-29 12:37:15] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 237551.015} | Data: {}
[2025-09-29 12:37:15] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_c166a9acfe64 | Data: {}
[2025-09-29 12:37:16] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 237552.015} | Data: {}
[2025-09-29 12:37:16] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_c166a9acfe64 | Data: {}
[2025-09-29 12:37:17] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 237553.062} | Data: {}
[2025-09-29 12:37:17] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_c166a9acfe64 | Data: {}
[2025-09-29 12:37:18] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 237554.078} | Data: {}
[2025-09-29 12:37:18] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_c166a9acfe64 | Data: {}
[2025-09-29 12:37:19] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 237555.093} | Data: {}
[2025-09-29 12:37:19] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_c166a9acfe64 | Data: {}
[2025-09-29 12:37:20] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 237556.14} | Data: {}
[2025-09-29 12:37:20] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_c166a9acfe64 | Data: {}
[2025-09-29 12:37:21] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 237557.171} | Data: {}
[2025-09-29 12:37:21] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_c166a9acfe64 | Data: {}
[2025-09-29 12:37:22] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 237558.234} | Data: {}
[2025-09-29 12:37:22] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_c166a9acfe64 | Data: {}
[2025-09-29 12:37:24] [INFO] [progress_debug] 🔄 WORKFLOW_STEP | Session: async_c166a9acfe64 | Step: memory_storage | Progress: 90% | Message: 💾 Storing analysis for future reference...
[2025-09-29 12:37:24] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Mapped step 'memory_storage' to memory_storage | Data: {}
[2025-09-29 12:37:24] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Created progress update: ProgressUpdate(step=<WorkflowStep.MEMORY_STORAGE: 'memory_storage'>, progress=90, message='💾 Storing analysis for future reference...', timestamp='2025-09-29T07:07:24.111846', details={'step': 'memory_storage'}, tool_results=None) | Data: {}
[2025-09-29 12:37:24] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔥 BROADCASTING update for session async_c166a9acfe64 | Data: {'step': 'memory_storage', 'progress': 90, 'message': '💾 Storing analysis for future reference...', 'has_clients': True, 'client_count': 1, 'broadcaster_id': 2361389686224, 'all_sessions': ['async_c166a9acfe64']}
[2025-09-29 12:37:24] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Sent update to client 0 for session async_c166a9acfe64 | Data: {}
[2025-09-29 12:37:24] [INFO] [progress_debug] 📡 PROGRESS_BROADCAST | ✅ SUCCESS | Session: async_c166a9acfe64 | Step: memory_storage | Details: {'workflow_step': 'memory_storage', 'progress': 90, 'message': '💾 Storing analysis for future reference...'}
[2025-09-29 12:37:24] [INFO] [progress_debug] 🔄 WORKFLOW_STEP | Session: async_c166a9acfe64 | Step: complete | Progress: 100% | Message: 🎉 Analysis completed successfully!
[2025-09-29 12:37:24] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Mapped step 'complete' to complete | Data: {}
[2025-09-29 12:37:24] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Created progress update: ProgressUpdate(step=<WorkflowStep.COMPLETE: 'complete'>, progress=100, message='🎉 Analysis completed successfully!', timestamp='2025-09-29T07:07:24.196654', details={'step': 'complete', 'workflow_status': 'complete'}, tool_results=None) | Data: {}
[2025-09-29 12:37:24] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔥 BROADCASTING update for session async_c166a9acfe64 | Data: {'step': 'complete', 'progress': 100, 'message': '🎉 Analysis completed successfully!', 'has_clients': True, 'client_count': 1, 'broadcaster_id': 2361389686224, 'all_sessions': ['async_c166a9acfe64']}
[2025-09-29 12:37:24] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Sent update to client 0 for session async_c166a9acfe64 | Data: {}
[2025-09-29 12:37:24] [INFO] [progress_debug] 📡 PROGRESS_BROADCAST | ✅ SUCCESS | Session: async_c166a9acfe64 | Step: complete | Details: {'workflow_step': 'complete', 'progress': 100, 'message': '🎉 Analysis completed successfully!'}
[2025-09-29 12:37:26] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔥 BROADCASTING update for session async_c166a9acfe64 | Data: {'step': 'complete', 'progress': 100, 'message': '✅ AI analysis workflow completed successfully!', 'has_clients': True, 'client_count': 1, 'broadcaster_id': 2361389686224, 'all_sessions': ['async_c166a9acfe64']}
[2025-09-29 12:37:26] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Sent update to client 0 for session async_c166a9acfe64 | Data: {}
[2025-09-29 12:37:26] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔥 BROADCASTING update for session async_c166a9acfe64 | Data: {'step': 'complete', 'progress': 100, 'message': '✅ AI analysis workflow completed successfully!', 'has_clients': True, 'client_count': 1, 'broadcaster_id': 2361389686224, 'all_sessions': ['async_c166a9acfe64']}
[2025-09-29 12:37:26] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Sent update to client 0 for session async_c166a9acfe64 | Data: {}
[2025-09-29 12:37:26] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Received progress update: {'step': 'memory_storage', 'progress': 90, 'message': '💾 Storing analysis for future reference...', 'timestamp': '2025-09-29T07:07:24.111846', 'details': {'step': 'memory_storage'}, 'tool_results': None} | Data: {}
[2025-09-29 12:37:26] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending SSE event: {'type': 'progress_update', 'data': {'step': 'memory_storage', 'progress': 90, 'message': '💾 Storing analysis for future reference...', 'timestamp': '2025-09-29T07:07:24.111846', 'details': {'step': 'memory_storage'}, 'tool_results': None}} | Data: {}
[2025-09-29 12:37:26] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_c166a9acfe64 | Data: {}
[2025-09-29 12:37:26] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Received progress update: {'step': 'complete', 'progress': 100, 'message': '🎉 Analysis completed successfully!', 'timestamp': '2025-09-29T07:07:24.196654', 'details': {'step': 'complete', 'workflow_status': 'complete'}, 'tool_results': None} | Data: {}
[2025-09-29 12:37:26] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending SSE event: {'type': 'progress_update', 'data': {'step': 'complete', 'progress': 100, 'message': '🎉 Analysis completed successfully!', 'timestamp': '2025-09-29T07:07:24.196654', 'details': {'step': 'complete', 'workflow_status': 'complete'}, 'tool_results': None}} | Data: {}
[2025-09-29 12:37:26] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Workflow complete, sending: {'type': 'complete', 'session_id': 'async_c166a9acfe64'} | Data: {}
[2025-09-29 12:37:26] [INFO] [progress_debug] 🔌 SSE_DISCONNECT | Session: async_c166a9acfe64 | Reason: event_generator_cleanup
[2025-09-29 12:37:31] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔥 BROADCASTING update for session async_c166a9acfe64 | Data: {'step': 'complete', 'progress': 100, 'message': '🎉 Analysis completed successfully!', 'has_clients': False, 'client_count': 0, 'broadcaster_id': 2361389686224, 'all_sessions': []}
[2025-09-29 12:37:31] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | No clients found for session async_c166a9acfe64 - update buffered (total: 11) | Data: {}
[2025-09-29 13:21:41] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔥 BROADCASTING update for session progress_test_1759132301 | Data: {'step': 'chart_upload', 'progress': 25, 'message': 'Test progress update', 'has_clients': False, 'client_count': 0, 'broadcaster_id': 3095407307472, 'all_sessions': []}
[2025-09-29 13:21:41] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | No clients found for session progress_test_1759132301 - update buffered (total: 1) | Data: {}
[2025-09-29 13:24:12] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔥 BROADCASTING update for session progress_test_1759132452 | Data: {'step': 'chart_upload', 'progress': 25, 'message': 'Test progress update', 'has_clients': False, 'client_count': 0, 'broadcaster_id': 1357036284560, 'all_sessions': []}
[2025-09-29 13:24:12] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | No clients found for session progress_test_1759132452 - update buffered (total: 1) | Data: {}
[2025-09-29 13:39:27] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔥 BROADCASTING update for session async_4ca491b4062c | Data: {'step': 'chart_upload', 'progress': 5, 'message': 'Analysis request received, starting background processing...', 'has_clients': False, 'client_count': 0, 'broadcaster_id': 2248806047504, 'all_sessions': []}
[2025-09-29 13:39:28] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | No clients found for session async_4ca491b4062c - update buffered (total: 1) | Data: {}
[2025-09-29 13:39:28] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔥 BROADCASTING update for session async_4ca491b4062c | Data: {'step': 'chart_upload', 'progress': 5, 'message': '📊 Processing uploaded chart images...', 'has_clients': False, 'client_count': 0, 'broadcaster_id': 2248806047504, 'all_sessions': []}
[2025-09-29 13:39:28] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | No clients found for session async_4ca491b4062c - update buffered (total: 2) | Data: {}
[2025-09-29 13:39:28] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔥 BROADCASTING update for session async_4ca491b4062c | Data: {'step': 'chart_upload', 'progress': 5, 'message': '🖼️ Processed chart image 1/1 (75KB)', 'has_clients': False, 'client_count': 0, 'broadcaster_id': 2248806047504, 'all_sessions': []}
[2025-09-29 13:39:28] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | No clients found for session async_4ca491b4062c - update buffered (total: 3) | Data: {}
[2025-09-29 13:39:28] [INFO] [progress_debug] 🔗 SSE_CONNECTION | Session: async_4ca491b4062c | Client: {'user_id': UUID('9fbc7a39-ae9c-455a-ad81-53ed905e004b'), 'user_email': '<EMAIL>', 'endpoint': '/api/v1/progress/stream/async_4ca491b4062c'}
[2025-09-29 13:39:28] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Adding client for session async_4ca491b4062c | Data: {}
[2025-09-29 13:39:28] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | ✅ CLIENT ADDED for session async_4ca491b4062c | Data: {'total_clients': 1, 'all_sessions': ['async_4ca491b4062c'], 'broadcaster_id': 2248806047504}
[2025-09-29 13:39:28] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Current broadcaster state | Data: {'session_id': 'async_4ca491b4062c', 'clients_dict': {'async_4ca491b4062c': 1}, 'total_sessions': 1}
[2025-09-29 13:39:28] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔄 REPLAYING 3 buffered updates to new client for session async_4ca491b4062c | Data: {}
[2025-09-29 13:39:28] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | ✅ Replayed buffered update 1/3: chart_upload (5%) | Data: {}
[2025-09-29 13:39:28] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | ✅ Replayed buffered update 2/3: chart_upload (5%) | Data: {}
[2025-09-29 13:39:28] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | ✅ Replayed buffered update 3/3: chart_upload (5%) | Data: {}
[2025-09-29 13:39:28] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🎯 Successfully replayed 3 buffered updates for session async_4ca491b4062c | Data: {}
[2025-09-29 13:39:28] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Client queue created for session async_4ca491b4062c | Data: {}
[2025-09-29 13:39:28] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending connection confirmation: {'type': 'connected', 'session_id': 'async_4ca491b4062c'} | Data: {}
[2025-09-29 13:39:28] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_4ca491b4062c | Data: {}
[2025-09-29 13:39:28] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Received progress update: {'step': 'chart_upload', 'progress': 5, 'message': 'Analysis request received, starting background processing...', 'timestamp': '2025-09-29T08:09:27.941740', 'details': {'user_id': '9fbc7a39-ae9c-455a-ad81-53ed905e004b', 'analysis_type': 'Positional', 'image_count': 1}, 'tool_results': None} | Data: {}
[2025-09-29 13:39:28] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending SSE event: {'type': 'progress_update', 'data': {'step': 'chart_upload', 'progress': 5, 'message': 'Analysis request received, starting background processing...', 'timestamp': '2025-09-29T08:09:27.941740', 'details': {'user_id': '9fbc7a39-ae9c-455a-ad81-53ed905e004b', 'analysis_type': 'Positional', 'image_count': 1}, 'tool_results': None}} | Data: {}
[2025-09-29 13:39:28] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_4ca491b4062c | Data: {}
[2025-09-29 13:39:28] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Received progress update: {'step': 'chart_upload', 'progress': 5, 'message': '📊 Processing uploaded chart images...', 'timestamp': '2025-09-29T08:09:28.098755', 'details': {'user_id': '9fbc7a39-ae9c-455a-ad81-53ed905e004b', 'analysis_type': 'Positional', 'image_count': 1}, 'tool_results': None} | Data: {}
[2025-09-29 13:39:28] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending SSE event: {'type': 'progress_update', 'data': {'step': 'chart_upload', 'progress': 5, 'message': '📊 Processing uploaded chart images...', 'timestamp': '2025-09-29T08:09:28.098755', 'details': {'user_id': '9fbc7a39-ae9c-455a-ad81-53ed905e004b', 'analysis_type': 'Positional', 'image_count': 1}, 'tool_results': None}} | Data: {}
[2025-09-29 13:39:28] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_4ca491b4062c | Data: {}
[2025-09-29 13:39:28] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Received progress update: {'step': 'chart_upload', 'progress': 5, 'message': '🖼️ Processed chart image 1/1 (75KB)', 'timestamp': '2025-09-29T08:09:28.104748', 'details': {'image_size': 77391, 'images_processed': 1}, 'tool_results': None} | Data: {}
[2025-09-29 13:39:28] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending SSE event: {'type': 'progress_update', 'data': {'step': 'chart_upload', 'progress': 5, 'message': '🖼️ Processed chart image 1/1 (75KB)', 'timestamp': '2025-09-29T08:09:28.104748', 'details': {'image_size': 77391, 'images_processed': 1}, 'tool_results': None}} | Data: {}
[2025-09-29 13:39:28] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_4ca491b4062c | Data: {}
[2025-09-29 13:39:29] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 3113.375} | Data: {}
[2025-09-29 13:39:29] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_4ca491b4062c | Data: {}
[2025-09-29 13:39:29] [INFO] [progress_debug] 🔄 SESSION_LIFECYCLE | Session: async_4ca491b4062c | Event: workflow_start | Details: {'analysis_mode': 'positional', 'market_specialization': 'Crypto', 'has_progress_broadcaster': True, 'chart_images_count': 1, 'parameter_passing_issue': False}
[2025-09-29 13:39:29] [INFO] [progress_debug] 🔄 WORKFLOW_STEP | Session: async_4ca491b4062c | Step: chart_analysis | Progress: 20% | Message: 🚀 Starting comprehensive AI analysis workflow...
[2025-09-29 13:39:29] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Mapped step 'chart_analysis' to chart_analysis | Data: {}
[2025-09-29 13:39:29] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Created progress update: ProgressUpdate(step=<WorkflowStep.CHART_ANALYSIS: 'chart_analysis'>, progress=20, message='🚀 Starting comprehensive AI analysis workflow...', timestamp='2025-09-29T08:09:29.854788', details={'step': 'chart_analysis', 'stage': 'start'}, tool_results=None) | Data: {}
[2025-09-29 13:39:29] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔥 BROADCASTING update for session async_4ca491b4062c | Data: {'step': 'chart_analysis', 'progress': 20, 'message': '🚀 Starting comprehensive AI analysis workflow...', 'has_clients': True, 'client_count': 1, 'broadcaster_id': 2248806047504, 'all_sessions': ['async_4ca491b4062c']}
[2025-09-29 13:39:29] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Sent update to client 0 for session async_4ca491b4062c | Data: {}
[2025-09-29 13:39:29] [INFO] [progress_debug] 📡 PROGRESS_BROADCAST | ✅ SUCCESS | Session: async_4ca491b4062c | Step: chart_analysis | Details: {'workflow_step': 'chart_analysis', 'progress': 20, 'message': '🚀 Starting comprehensive AI analysis workflow...'}
[2025-09-29 13:39:29] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Received progress update: {'step': 'chart_analysis', 'progress': 20, 'message': '🚀 Starting comprehensive AI analysis workflow...', 'timestamp': '2025-09-29T08:09:29.854788', 'details': {'step': 'chart_analysis', 'stage': 'start'}, 'tool_results': None} | Data: {}
[2025-09-29 13:39:29] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending SSE event: {'type': 'progress_update', 'data': {'step': 'chart_analysis', 'progress': 20, 'message': '🚀 Starting comprehensive AI analysis workflow...', 'timestamp': '2025-09-29T08:09:29.854788', 'details': {'step': 'chart_analysis', 'stage': 'start'}, 'tool_results': None}} | Data: {}
[2025-09-29 13:39:29] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_4ca491b4062c | Data: {}
[2025-09-29 13:39:30] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 3114.64} | Data: {}
[2025-09-29 13:39:30] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_4ca491b4062c | Data: {}
[2025-09-29 13:39:31] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 3115.64} | Data: {}
[2025-09-29 13:39:31] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_4ca491b4062c | Data: {}
[2025-09-29 13:39:32] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 3116.671} | Data: {}
[2025-09-29 13:39:32] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_4ca491b4062c | Data: {}
[2025-09-29 13:39:33] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 3117.687} | Data: {}
[2025-09-29 13:39:33] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_4ca491b4062c | Data: {}
[2025-09-29 13:39:34] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 3118.687} | Data: {}
[2025-09-29 13:39:34] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_4ca491b4062c | Data: {}
[2025-09-29 13:39:35] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 3119.703} | Data: {}
[2025-09-29 13:39:35] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_4ca491b4062c | Data: {}
[2025-09-29 13:39:36] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 3120.718} | Data: {}
[2025-09-29 13:39:36] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_4ca491b4062c | Data: {}
[2025-09-29 13:39:37] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 3121.718} | Data: {}
[2025-09-29 13:39:37] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_4ca491b4062c | Data: {}
[2025-09-29 13:39:38] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 3122.703} | Data: {}
[2025-09-29 13:39:38] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_4ca491b4062c | Data: {}
[2025-09-29 13:39:39] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 3123.718} | Data: {}
[2025-09-29 13:39:39] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_4ca491b4062c | Data: {}
[2025-09-29 13:39:40] [INFO] [progress_debug] 🔄 WORKFLOW_STEP | Session: async_4ca491b4062c | Step: symbol_detection | Progress: 40% | Message: ✅ Symbol detected: BTCUSD (crypto market)
[2025-09-29 13:39:40] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Mapped step 'symbol_detection' to symbol_detection | Data: {}
[2025-09-29 13:39:40] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Created progress update: ProgressUpdate(step=<WorkflowStep.SYMBOL_DETECTION: 'symbol_detection'>, progress=40, message='✅ Symbol detected: BTCUSD (crypto market)', timestamp='2025-09-29T08:09:40.463925', details={'step': 'symbol_detection', 'symbol': 'BTCUSD', 'market': 'crypto'}, tool_results=None) | Data: {}
[2025-09-29 13:39:40] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔥 BROADCASTING update for session async_4ca491b4062c | Data: {'step': 'symbol_detection', 'progress': 40, 'message': '✅ Symbol detected: BTCUSD (crypto market)', 'has_clients': True, 'client_count': 1, 'broadcaster_id': 2248806047504, 'all_sessions': ['async_4ca491b4062c']}
[2025-09-29 13:39:40] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Sent update to client 0 for session async_4ca491b4062c | Data: {}
[2025-09-29 13:39:40] [INFO] [progress_debug] 📡 PROGRESS_BROADCAST | ✅ SUCCESS | Session: async_4ca491b4062c | Step: symbol_detection | Details: {'workflow_step': 'symbol_detection', 'progress': 40, 'message': '✅ Symbol detected: BTCUSD (crypto market)'}
[2025-09-29 13:39:40] [INFO] [progress_debug] 🔄 WORKFLOW_STEP | Session: async_4ca491b4062c | Step: tool_execution | Progress: 50% | Message: 🎯 Executing market data tools...
[2025-09-29 13:39:40] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Received progress update: {'step': 'symbol_detection', 'progress': 40, 'message': '✅ Symbol detected: BTCUSD (crypto market)', 'timestamp': '2025-09-29T08:09:40.463925', 'details': {'step': 'symbol_detection', 'symbol': 'BTCUSD', 'market': 'crypto'}, 'tool_results': None} | Data: {}
[2025-09-29 13:39:40] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending SSE event: {'type': 'progress_update', 'data': {'step': 'symbol_detection', 'progress': 40, 'message': '✅ Symbol detected: BTCUSD (crypto market)', 'timestamp': '2025-09-29T08:09:40.463925', 'details': {'step': 'symbol_detection', 'symbol': 'BTCUSD', 'market': 'crypto'}, 'tool_results': None}} | Data: {}
[2025-09-29 13:39:40] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_4ca491b4062c | Data: {}
[2025-09-29 13:39:40] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Mapped step 'tool_execution' to tool_execution | Data: {}
[2025-09-29 13:39:40] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Created progress update: ProgressUpdate(step=<WorkflowStep.TOOL_EXECUTION: 'tool_execution'>, progress=50, message='🎯 Executing market data tools...', timestamp='2025-09-29T08:09:40.503238', details={'step': 'tool_execution', 'market': 'crypto'}, tool_results=None) | Data: {}
[2025-09-29 13:39:40] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔥 BROADCASTING update for session async_4ca491b4062c | Data: {'step': 'tool_execution', 'progress': 50, 'message': '🎯 Executing market data tools...', 'has_clients': True, 'client_count': 1, 'broadcaster_id': 2248806047504, 'all_sessions': ['async_4ca491b4062c']}
[2025-09-29 13:39:40] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Sent update to client 0 for session async_4ca491b4062c | Data: {}
[2025-09-29 13:39:40] [INFO] [progress_debug] 📡 PROGRESS_BROADCAST | ✅ SUCCESS | Session: async_4ca491b4062c | Step: tool_execution | Details: {'workflow_step': 'tool_execution', 'progress': 50, 'message': '🎯 Executing market data tools...'}
[2025-09-29 13:39:40] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Received progress update: {'step': 'tool_execution', 'progress': 50, 'message': '🎯 Executing market data tools...', 'timestamp': '2025-09-29T08:09:40.503238', 'details': {'step': 'tool_execution', 'market': 'crypto'}, 'tool_results': None} | Data: {}
[2025-09-29 13:39:40] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending SSE event: {'type': 'progress_update', 'data': {'step': 'tool_execution', 'progress': 50, 'message': '🎯 Executing market data tools...', 'timestamp': '2025-09-29T08:09:40.503238', 'details': {'step': 'tool_execution', 'market': 'crypto'}, 'tool_results': None}} | Data: {}
[2025-09-29 13:39:40] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_4ca491b4062c | Data: {}
[2025-09-29 13:39:41] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 3125.406} | Data: {}
[2025-09-29 13:39:41] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_4ca491b4062c | Data: {}
[2025-09-29 13:39:42] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 3126.421} | Data: {}
[2025-09-29 13:39:42] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_4ca491b4062c | Data: {}
[2025-09-29 13:39:43] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 3127.437} | Data: {}
[2025-09-29 13:39:43] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_4ca491b4062c | Data: {}
[2025-09-29 13:39:44] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 3128.437} | Data: {}
[2025-09-29 13:39:44] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_4ca491b4062c | Data: {}
[2025-09-29 13:39:45] [INFO] [progress_debug] 🔄 WORKFLOW_STEP | Session: async_4ca491b4062c | Step: tool_summarization | Progress: 60% | Message: 🎯 Summarizing market data with AI...
[2025-09-29 13:39:45] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Mapped step 'tool_summarization' to tool_summarization | Data: {}
[2025-09-29 13:39:45] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Created progress update: ProgressUpdate(step=<WorkflowStep.TOOL_SUMMARIZATION: 'tool_summarization'>, progress=60, message='🎯 Summarizing market data with AI...', timestamp='2025-09-29T08:09:45.464225', details={'step': 'tool_summarization'}, tool_results=None) | Data: {}
[2025-09-29 13:39:45] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔥 BROADCASTING update for session async_4ca491b4062c | Data: {'step': 'tool_summarization', 'progress': 60, 'message': '🎯 Summarizing market data with AI...', 'has_clients': True, 'client_count': 1, 'broadcaster_id': 2248806047504, 'all_sessions': ['async_4ca491b4062c']}
[2025-09-29 13:39:45] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Sent update to client 0 for session async_4ca491b4062c | Data: {}
[2025-09-29 13:39:45] [INFO] [progress_debug] 📡 PROGRESS_BROADCAST | ✅ SUCCESS | Session: async_4ca491b4062c | Step: tool_summarization | Details: {'workflow_step': 'tool_summarization', 'progress': 60, 'message': '🎯 Summarizing market data with AI...'}
[2025-09-29 13:39:45] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Received progress update: {'step': 'tool_summarization', 'progress': 60, 'message': '🎯 Summarizing market data with AI...', 'timestamp': '2025-09-29T08:09:45.464225', 'details': {'step': 'tool_summarization'}, 'tool_results': None} | Data: {}
[2025-09-29 13:39:45] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending SSE event: {'type': 'progress_update', 'data': {'step': 'tool_summarization', 'progress': 60, 'message': '🎯 Summarizing market data with AI...', 'timestamp': '2025-09-29T08:09:45.464225', 'details': {'step': 'tool_summarization'}, 'tool_results': None}} | Data: {}
[2025-09-29 13:39:45] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_4ca491b4062c | Data: {}
[2025-09-29 13:39:58] [INFO] [progress_debug] 🔄 WORKFLOW_STEP | Session: async_4ca491b4062c | Step: rag_integration | Progress: 70% | Message: 💾 Integrating historical context and patterns...
[2025-09-29 13:39:58] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Mapped step 'rag_integration' to rag_integration | Data: {}
[2025-09-29 13:39:58] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Created progress update: ProgressUpdate(step=<WorkflowStep.RAG_INTEGRATION: 'rag_integration'>, progress=70, message='💾 Integrating historical context and patterns...', timestamp='2025-09-29T08:09:58.598508', details={'step': 'rag_integration'}, tool_results=None) | Data: {}
[2025-09-29 13:39:58] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔥 BROADCASTING update for session async_4ca491b4062c | Data: {'step': 'rag_integration', 'progress': 70, 'message': '💾 Integrating historical context and patterns...', 'has_clients': True, 'client_count': 1, 'broadcaster_id': 2248806047504, 'all_sessions': ['async_4ca491b4062c']}
[2025-09-29 13:39:58] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Sent update to client 0 for session async_4ca491b4062c | Data: {}
[2025-09-29 13:39:58] [INFO] [progress_debug] 📡 PROGRESS_BROADCAST | ✅ SUCCESS | Session: async_4ca491b4062c | Step: rag_integration | Details: {'workflow_step': 'rag_integration', 'progress': 70, 'message': '💾 Integrating historical context and patterns...'}
[2025-09-29 13:40:05] [INFO] [progress_debug] 🔄 WORKFLOW_STEP | Session: async_4ca491b4062c | Step: final_analysis | Progress: 80% | Message: 🎯 Generating final trading analysis...
[2025-09-29 13:40:05] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Mapped step 'final_analysis' to final_analysis | Data: {}
[2025-09-29 13:40:05] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Created progress update: ProgressUpdate(step=<WorkflowStep.FINAL_ANALYSIS: 'final_analysis'>, progress=80, message='🎯 Generating final trading analysis...', timestamp='2025-09-29T08:10:05.150646', details={'step': 'final_analysis'}, tool_results=None) | Data: {}
[2025-09-29 13:40:05] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔥 BROADCASTING update for session async_4ca491b4062c | Data: {'step': 'final_analysis', 'progress': 80, 'message': '🎯 Generating final trading analysis...', 'has_clients': True, 'client_count': 1, 'broadcaster_id': 2248806047504, 'all_sessions': ['async_4ca491b4062c']}
[2025-09-29 13:40:05] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Sent update to client 0 for session async_4ca491b4062c | Data: {}
[2025-09-29 13:40:05] [INFO] [progress_debug] 📡 PROGRESS_BROADCAST | ✅ SUCCESS | Session: async_4ca491b4062c | Step: final_analysis | Details: {'workflow_step': 'final_analysis', 'progress': 80, 'message': '🎯 Generating final trading analysis...'}
[2025-09-29 13:40:09] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Received progress update: {'step': 'rag_integration', 'progress': 70, 'message': '💾 Integrating historical context and patterns...', 'timestamp': '2025-09-29T08:09:58.598508', 'details': {'step': 'rag_integration'}, 'tool_results': None} | Data: {}
[2025-09-29 13:40:09] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending SSE event: {'type': 'progress_update', 'data': {'step': 'rag_integration', 'progress': 70, 'message': '💾 Integrating historical context and patterns...', 'timestamp': '2025-09-29T08:09:58.598508', 'details': {'step': 'rag_integration'}, 'tool_results': None}} | Data: {}
[2025-09-29 13:40:09] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_4ca491b4062c | Data: {}
[2025-09-29 13:40:09] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Received progress update: {'step': 'final_analysis', 'progress': 80, 'message': '🎯 Generating final trading analysis...', 'timestamp': '2025-09-29T08:10:05.150646', 'details': {'step': 'final_analysis'}, 'tool_results': None} | Data: {}
[2025-09-29 13:40:09] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending SSE event: {'type': 'progress_update', 'data': {'step': 'final_analysis', 'progress': 80, 'message': '🎯 Generating final trading analysis...', 'timestamp': '2025-09-29T08:10:05.150646', 'details': {'step': 'final_analysis'}, 'tool_results': None}} | Data: {}
[2025-09-29 13:40:09] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_4ca491b4062c | Data: {}
[2025-09-29 13:40:13] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 3157.015} | Data: {}
[2025-09-29 13:40:13] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_4ca491b4062c | Data: {}
[2025-09-29 13:40:14] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 3158.031} | Data: {}
[2025-09-29 13:40:14] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_4ca491b4062c | Data: {}
[2025-09-29 13:40:15] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 3159.046} | Data: {}
[2025-09-29 13:40:15] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_4ca491b4062c | Data: {}
[2025-09-29 13:40:18] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 3162.531} | Data: {}
[2025-09-29 13:40:18] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_4ca491b4062c | Data: {}
[2025-09-29 13:40:20] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 3164.343} | Data: {}
[2025-09-29 13:40:20] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_4ca491b4062c | Data: {}
[2025-09-29 13:40:21] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 3165.328} | Data: {}
[2025-09-29 13:40:21] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_4ca491b4062c | Data: {}
[2025-09-29 13:40:22] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 3166.343} | Data: {}
[2025-09-29 13:40:22] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_4ca491b4062c | Data: {}
[2025-09-29 13:40:23] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 3167.359} | Data: {}
[2025-09-29 13:40:23] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_4ca491b4062c | Data: {}
[2025-09-29 13:40:24] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 3168.375} | Data: {}
[2025-09-29 13:40:24] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_4ca491b4062c | Data: {}
[2025-09-29 13:40:25] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 3169.406} | Data: {}
[2025-09-29 13:40:25] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_4ca491b4062c | Data: {}
[2025-09-29 13:40:26] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 3170.421} | Data: {}
[2025-09-29 13:40:26] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_4ca491b4062c | Data: {}
[2025-09-29 13:40:27] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 3171.406} | Data: {}
[2025-09-29 13:40:27] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_4ca491b4062c | Data: {}
[2025-09-29 13:40:28] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 3172.406} | Data: {}
[2025-09-29 13:40:28] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_4ca491b4062c | Data: {}
[2025-09-29 13:40:29] [INFO] [progress_debug] 🔄 WORKFLOW_STEP | Session: async_4ca491b4062c | Step: memory_storage | Progress: 90% | Message: 💾 Storing analysis for future reference...
[2025-09-29 13:40:29] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Mapped step 'memory_storage' to memory_storage | Data: {}
[2025-09-29 13:40:29] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Created progress update: ProgressUpdate(step=<WorkflowStep.MEMORY_STORAGE: 'memory_storage'>, progress=90, message='💾 Storing analysis for future reference...', timestamp='2025-09-29T08:10:29.474553', details={'step': 'memory_storage'}, tool_results=None) | Data: {}
[2025-09-29 13:40:29] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔥 BROADCASTING update for session async_4ca491b4062c | Data: {'step': 'memory_storage', 'progress': 90, 'message': '💾 Storing analysis for future reference...', 'has_clients': True, 'client_count': 1, 'broadcaster_id': 2248806047504, 'all_sessions': ['async_4ca491b4062c']}
[2025-09-29 13:40:29] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Sent update to client 0 for session async_4ca491b4062c | Data: {}
[2025-09-29 13:40:29] [INFO] [progress_debug] 📡 PROGRESS_BROADCAST | ✅ SUCCESS | Session: async_4ca491b4062c | Step: memory_storage | Details: {'workflow_step': 'memory_storage', 'progress': 90, 'message': '💾 Storing analysis for future reference...'}
[2025-09-29 13:40:29] [INFO] [progress_debug] 🔄 WORKFLOW_STEP | Session: async_4ca491b4062c | Step: complete | Progress: 100% | Message: 🎉 Analysis completed successfully!
[2025-09-29 13:40:29] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Mapped step 'complete' to complete | Data: {}
[2025-09-29 13:40:29] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Created progress update: ProgressUpdate(step=<WorkflowStep.COMPLETE: 'complete'>, progress=100, message='🎉 Analysis completed successfully!', timestamp='2025-09-29T08:10:29.518378', details={'step': 'complete', 'workflow_status': 'complete'}, tool_results=None) | Data: {}
[2025-09-29 13:40:29] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Received progress update: {'step': 'memory_storage', 'progress': 90, 'message': '💾 Storing analysis for future reference...', 'timestamp': '2025-09-29T08:10:29.474553', 'details': {'step': 'memory_storage'}, 'tool_results': None} | Data: {}
[2025-09-29 13:40:29] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔥 BROADCASTING update for session async_4ca491b4062c | Data: {'step': 'complete', 'progress': 100, 'message': '🎉 Analysis completed successfully!', 'has_clients': True, 'client_count': 1, 'broadcaster_id': 2248806047504, 'all_sessions': ['async_4ca491b4062c']}
[2025-09-29 13:40:29] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending SSE event: {'type': 'progress_update', 'data': {'step': 'memory_storage', 'progress': 90, 'message': '💾 Storing analysis for future reference...', 'timestamp': '2025-09-29T08:10:29.474553', 'details': {'step': 'memory_storage'}, 'tool_results': None}} | Data: {}
[2025-09-29 13:40:29] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_4ca491b4062c | Data: {}
[2025-09-29 13:40:29] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Sent update to client 0 for session async_4ca491b4062c | Data: {}
[2025-09-29 13:40:29] [INFO] [progress_debug] 📡 PROGRESS_BROADCAST | ✅ SUCCESS | Session: async_4ca491b4062c | Step: complete | Details: {'workflow_step': 'complete', 'progress': 100, 'message': '🎉 Analysis completed successfully!'}
[2025-09-29 13:40:29] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔥 BROADCASTING update for session async_4ca491b4062c | Data: {'step': 'complete', 'progress': 100, 'message': '✅ AI analysis workflow completed successfully!', 'has_clients': True, 'client_count': 1, 'broadcaster_id': 2248806047504, 'all_sessions': ['async_4ca491b4062c']}
[2025-09-29 13:40:29] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Sent update to client 0 for session async_4ca491b4062c | Data: {}
[2025-09-29 13:40:29] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔥 BROADCASTING update for session async_4ca491b4062c | Data: {'step': 'complete', 'progress': 100, 'message': '✅ AI analysis workflow completed successfully!', 'has_clients': True, 'client_count': 1, 'broadcaster_id': 2248806047504, 'all_sessions': ['async_4ca491b4062c']}
[2025-09-29 13:40:29] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Sent update to client 0 for session async_4ca491b4062c | Data: {}
[2025-09-29 13:40:29] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Received progress update: {'step': 'complete', 'progress': 100, 'message': '🎉 Analysis completed successfully!', 'timestamp': '2025-09-29T08:10:29.518378', 'details': {'step': 'complete', 'workflow_status': 'complete'}, 'tool_results': None} | Data: {}
[2025-09-29 13:40:29] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending SSE event: {'type': 'progress_update', 'data': {'step': 'complete', 'progress': 100, 'message': '🎉 Analysis completed successfully!', 'timestamp': '2025-09-29T08:10:29.518378', 'details': {'step': 'complete', 'workflow_status': 'complete'}, 'tool_results': None}} | Data: {}
[2025-09-29 13:40:29] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Workflow complete, sending: {'type': 'complete', 'session_id': 'async_4ca491b4062c'} | Data: {}
[2025-09-29 13:40:29] [INFO] [progress_debug] 🔌 SSE_DISCONNECT | Session: async_4ca491b4062c | Reason: event_generator_cleanup
[2025-09-29 13:40:30] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔥 BROADCASTING update for session async_4ca491b4062c | Data: {'step': 'complete', 'progress': 100, 'message': '🎉 Analysis completed successfully!', 'has_clients': False, 'client_count': 0, 'broadcaster_id': 2248806047504, 'all_sessions': []}
[2025-09-29 13:40:30] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | No clients found for session async_4ca491b4062c - update buffered (total: 14) | Data: {}
[2025-09-29 13:48:24] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔥 BROADCASTING update for session test_diagnostic_1759133904 | Data: {'step': 'chart_analysis', 'progress': 20, 'message': 'Test diagnostic update', 'has_clients': False, 'client_count': 0, 'broadcaster_id': 1855422976784, 'all_sessions': []}
[2025-09-29 13:48:24] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | No clients found for session test_diagnostic_1759133904 - update buffered (total: 1) | Data: {}
[2025-09-29 13:48:24] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | ✅ CLIENT ADDED for session test_diagnostic_1759133904 | Data: {'total_clients': 1, 'all_sessions': ['test_diagnostic_1759133904'], 'broadcaster_id': 1855422976784}
[2025-09-29 13:48:24] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Current broadcaster state | Data: {'session_id': 'test_diagnostic_1759133904', 'clients_dict': {'test_diagnostic_1759133904': 1}, 'total_sessions': 1}
[2025-09-29 13:48:24] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔄 REPLAYING 1 buffered updates to new client for session test_diagnostic_1759133904 | Data: {}
[2025-09-29 13:48:24] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | ✅ Replayed buffered update 1/1: chart_analysis (20%) | Data: {}
[2025-09-29 13:48:24] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🎯 Successfully replayed 1 buffered updates for session test_diagnostic_1759133904 | Data: {}
[2025-09-29 13:49:43] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔥 BROADCASTING update for session test_diagnostic_1759133983 | Data: {'step': 'chart_analysis', 'progress': 20, 'message': 'Test diagnostic update', 'has_clients': False, 'client_count': 0, 'broadcaster_id': 2051130507216, 'all_sessions': []}
[2025-09-29 13:49:43] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | No clients found for session test_diagnostic_1759133983 - update buffered (total: 1) | Data: {}
[2025-09-29 13:49:43] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | ✅ CLIENT ADDED for session test_diagnostic_1759133983 | Data: {'total_clients': 1, 'all_sessions': ['test_diagnostic_1759133983'], 'broadcaster_id': 2051130507216}
[2025-09-29 13:49:43] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Current broadcaster state | Data: {'session_id': 'test_diagnostic_1759133983', 'clients_dict': {'test_diagnostic_1759133983': 1}, 'total_sessions': 1}
[2025-09-29 13:49:43] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔄 REPLAYING 1 buffered updates to new client for session test_diagnostic_1759133983 | Data: {}
[2025-09-29 13:49:43] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | ✅ Replayed buffered update 1/1: chart_analysis (20%) | Data: {}
[2025-09-29 13:49:43] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🎯 Successfully replayed 1 buffered updates for session test_diagnostic_1759133983 | Data: {}
[2025-09-29 13:59:24] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔥 BROADCASTING update for session test_enhanced_1759134564 | Data: {'step': 'chart_analysis', 'progress': 20, 'message': 'Test chart analysis', 'has_clients': False, 'client_count': 0, 'broadcaster_id': 2304112189712, 'all_sessions': []}
[2025-09-29 13:59:24] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | No clients found for session test_enhanced_1759134564 - update buffered (total: 1) | Data: {}
[2025-09-29 13:59:24] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔥 BROADCASTING update for session test_enhanced_1759134564 | Data: {'step': 'symbol_detection', 'progress': 30, 'message': 'Test symbol detection', 'has_clients': False, 'client_count': 0, 'broadcaster_id': 2304112189712, 'all_sessions': []}
[2025-09-29 13:59:24] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | No clients found for session test_enhanced_1759134564 - update buffered (total: 2) | Data: {}
[2025-09-29 13:59:24] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔥 BROADCASTING update for session test_enhanced_1759134564 | Data: {'step': 'tool_execution', 'progress': 50, 'message': 'Test tool execution', 'has_clients': False, 'client_count': 0, 'broadcaster_id': 2304112189712, 'all_sessions': []}
[2025-09-29 13:59:24] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | No clients found for session test_enhanced_1759134564 - update buffered (total: 3) | Data: {}
[2025-09-29 13:59:24] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | ✅ CLIENT ADDED for session test_enhanced_1759134564 | Data: {'total_clients': 1, 'all_sessions': ['test_enhanced_1759134564'], 'broadcaster_id': 2304112189712}
[2025-09-29 13:59:24] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Current broadcaster state | Data: {'session_id': 'test_enhanced_1759134564', 'clients_dict': {'test_enhanced_1759134564': 1}, 'total_sessions': 1}
[2025-09-29 13:59:24] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔄 REPLAYING 3 buffered updates to new client for session test_enhanced_1759134564 | Data: {}
[2025-09-29 13:59:24] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | ✅ Replayed buffered update 1/3: chart_analysis (20%) | Data: {}
[2025-09-29 13:59:24] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | ✅ Replayed buffered update 2/3: symbol_detection (30%) | Data: {}
[2025-09-29 13:59:24] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | ✅ Replayed buffered update 3/3: tool_execution (50%) | Data: {}
[2025-09-29 13:59:24] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🎯 Successfully replayed 3 buffered updates for session test_enhanced_1759134564 | Data: {}
[2025-09-29 13:59:24] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Removed client for session test_enhanced_1759134564 | Data: {'remaining_clients': 0}
[2025-09-29 13:59:24] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Cleaned up empty client list for session test_enhanced_1759134564 | Data: {}
[2025-09-29 13:59:24] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Keeping buffered updates for potential reconnection: test_enhanced_1759134564 | Data: {}
[2025-09-29 13:59:24] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Buffered updates count: 3 | Data: {}
[2025-09-29 13:59:24] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Marked session test_enhanced_1759134564 as complete | Data: {}
[2025-09-29 13:59:24] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Cleaned up buffered updates for completed session test_enhanced_1759134564 | Data: {}
[2025-09-29 14:00:47] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔥 BROADCASTING update for session test_enhanced_1759134647 | Data: {'step': 'chart_analysis', 'progress': 20, 'message': 'Test chart analysis', 'has_clients': False, 'client_count': 0, 'broadcaster_id': 2643267219008, 'all_sessions': []}
[2025-09-29 14:00:47] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | No clients found for session test_enhanced_1759134647 - update buffered (total: 1) | Data: {}
[2025-09-29 14:00:47] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔥 BROADCASTING update for session test_enhanced_1759134647 | Data: {'step': 'symbol_detection', 'progress': 30, 'message': 'Test symbol detection', 'has_clients': False, 'client_count': 0, 'broadcaster_id': 2643267219008, 'all_sessions': []}
[2025-09-29 14:00:47] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | No clients found for session test_enhanced_1759134647 - update buffered (total: 2) | Data: {}
[2025-09-29 14:00:47] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔥 BROADCASTING update for session test_enhanced_1759134647 | Data: {'step': 'tool_execution', 'progress': 50, 'message': 'Test tool execution', 'has_clients': False, 'client_count': 0, 'broadcaster_id': 2643267219008, 'all_sessions': []}
[2025-09-29 14:00:47] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | No clients found for session test_enhanced_1759134647 - update buffered (total: 3) | Data: {}
[2025-09-29 14:00:47] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | ✅ CLIENT ADDED for session test_enhanced_1759134647 | Data: {'total_clients': 1, 'all_sessions': ['test_enhanced_1759134647'], 'broadcaster_id': 2643267219008}
[2025-09-29 14:00:47] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Current broadcaster state | Data: {'session_id': 'test_enhanced_1759134647', 'clients_dict': {'test_enhanced_1759134647': 1}, 'total_sessions': 1}
[2025-09-29 14:00:47] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔄 REPLAYING 3 buffered updates to new client for session test_enhanced_1759134647 | Data: {}
[2025-09-29 14:00:47] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | ✅ Replayed buffered update 1/3: chart_analysis (20%) | Data: {}
[2025-09-29 14:00:47] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | ✅ Replayed buffered update 2/3: symbol_detection (30%) | Data: {}
[2025-09-29 14:00:47] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | ✅ Replayed buffered update 3/3: tool_execution (50%) | Data: {}
[2025-09-29 14:00:47] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🎯 Successfully replayed 3 buffered updates for session test_enhanced_1759134647 | Data: {}
[2025-09-29 14:00:47] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Removed client for session test_enhanced_1759134647 | Data: {'remaining_clients': 0}
[2025-09-29 14:00:47] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Cleaned up empty client list for session test_enhanced_1759134647 | Data: {}
[2025-09-29 14:00:47] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Keeping buffered updates for potential reconnection: test_enhanced_1759134647 | Data: {}
[2025-09-29 14:00:47] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Buffered updates count: 3 | Data: {}
[2025-09-29 14:00:47] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Marked session test_enhanced_1759134647 as complete | Data: {}
[2025-09-29 14:00:47] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Cleaned up buffered updates for completed session test_enhanced_1759134647 | Data: {}
[2025-09-29 15:08:24] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔥 BROADCASTING update for session async_a36ec6ebb42e | Data: {'step': 'chart_upload', 'progress': 5, 'message': 'Analysis request received, starting background processing...', 'has_clients': False, 'client_count': 0, 'broadcaster_id': 2379852619920, 'all_sessions': []}
[2025-09-29 15:08:24] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | No clients found for session async_a36ec6ebb42e - update buffered (total: 1) | Data: {}
[2025-09-29 15:08:24] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔥 BROADCASTING update for session async_a36ec6ebb42e | Data: {'step': 'chart_upload', 'progress': 5, 'message': '📊 Processing uploaded chart images...', 'has_clients': False, 'client_count': 0, 'broadcaster_id': 2379852619920, 'all_sessions': []}
[2025-09-29 15:08:24] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | No clients found for session async_a36ec6ebb42e - update buffered (total: 2) | Data: {}
[2025-09-29 15:08:24] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔥 BROADCASTING update for session async_a36ec6ebb42e | Data: {'step': 'chart_upload', 'progress': 5, 'message': '🖼️ Processed chart image 1/1 (75KB)', 'has_clients': False, 'client_count': 0, 'broadcaster_id': 2379852619920, 'all_sessions': []}
[2025-09-29 15:08:24] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | No clients found for session async_a36ec6ebb42e - update buffered (total: 3) | Data: {}
[2025-09-29 15:08:24] [INFO] [progress_debug] 🔗 SSE_CONNECTION | Session: async_a36ec6ebb42e | Client: {'user_id': UUID('9fbc7a39-ae9c-455a-ad81-53ed905e004b'), 'user_email': '<EMAIL>', 'endpoint': '/api/v1/progress/stream/async_a36ec6ebb42e'}
[2025-09-29 15:08:24] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Adding client for session async_a36ec6ebb42e | Data: {}
[2025-09-29 15:08:24] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | ✅ CLIENT ADDED for session async_a36ec6ebb42e | Data: {'total_clients': 1, 'all_sessions': ['async_a36ec6ebb42e'], 'broadcaster_id': 2379852619920}
[2025-09-29 15:08:24] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Current broadcaster state | Data: {'session_id': 'async_a36ec6ebb42e', 'clients_dict': {'async_a36ec6ebb42e': 1}, 'total_sessions': 1}
[2025-09-29 15:08:24] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔄 REPLAYING 3 buffered updates to new client for session async_a36ec6ebb42e | Data: {}
[2025-09-29 15:08:24] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | ✅ Replayed buffered update 1/3: chart_upload (5%) | Data: {}
[2025-09-29 15:08:24] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | ✅ Replayed buffered update 2/3: chart_upload (5%) | Data: {}
[2025-09-29 15:08:24] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | ✅ Replayed buffered update 3/3: chart_upload (5%) | Data: {}
[2025-09-29 15:08:24] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🎯 Successfully replayed 3 buffered updates for session async_a36ec6ebb42e | Data: {}
[2025-09-29 15:08:24] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Client queue created for session async_a36ec6ebb42e | Data: {}
[2025-09-29 15:08:24] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending connection confirmation: {'type': 'connected', 'session_id': 'async_a36ec6ebb42e'} | Data: {}
[2025-09-29 15:08:24] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_a36ec6ebb42e | Data: {}
[2025-09-29 15:08:24] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Received progress update: {'step': 'chart_upload', 'progress': 5, 'message': 'Analysis request received, starting background processing...', 'timestamp': '2025-09-29T09:38:24.341658', 'details': {'user_id': '9fbc7a39-ae9c-455a-ad81-53ed905e004b', 'analysis_type': 'Positional', 'image_count': 1}, 'tool_results': None} | Data: {}
[2025-09-29 15:08:24] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending SSE event: {'type': 'progress_update', 'data': {'step': 'chart_upload', 'progress': 5, 'message': 'Analysis request received, starting background processing...', 'timestamp': '2025-09-29T09:38:24.341658', 'details': {'user_id': '9fbc7a39-ae9c-455a-ad81-53ed905e004b', 'analysis_type': 'Positional', 'image_count': 1}, 'tool_results': None}} | Data: {}
[2025-09-29 15:08:24] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_a36ec6ebb42e | Data: {}
[2025-09-29 15:08:24] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Received progress update: {'step': 'chart_upload', 'progress': 5, 'message': '📊 Processing uploaded chart images...', 'timestamp': '2025-09-29T09:38:24.501653', 'details': {'user_id': '9fbc7a39-ae9c-455a-ad81-53ed905e004b', 'analysis_type': 'Positional', 'image_count': 1}, 'tool_results': None} | Data: {}
[2025-09-29 15:08:24] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending SSE event: {'type': 'progress_update', 'data': {'step': 'chart_upload', 'progress': 5, 'message': '📊 Processing uploaded chart images...', 'timestamp': '2025-09-29T09:38:24.501653', 'details': {'user_id': '9fbc7a39-ae9c-455a-ad81-53ed905e004b', 'analysis_type': 'Positional', 'image_count': 1}, 'tool_results': None}} | Data: {}
[2025-09-29 15:08:24] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_a36ec6ebb42e | Data: {}
[2025-09-29 15:08:24] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Received progress update: {'step': 'chart_upload', 'progress': 5, 'message': '🖼️ Processed chart image 1/1 (75KB)', 'timestamp': '2025-09-29T09:38:24.514835', 'details': {'image_size': 77391, 'images_processed': 1}, 'tool_results': None} | Data: {}
[2025-09-29 15:08:24] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending SSE event: {'type': 'progress_update', 'data': {'step': 'chart_upload', 'progress': 5, 'message': '🖼️ Processed chart image 1/1 (75KB)', 'timestamp': '2025-09-29T09:38:24.514835', 'details': {'image_size': 77391, 'images_processed': 1}, 'tool_results': None}} | Data: {}
[2025-09-29 15:08:24] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_a36ec6ebb42e | Data: {}
[2025-09-29 15:08:25] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 8449.703} | Data: {}
[2025-09-29 15:08:25] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_a36ec6ebb42e | Data: {}
[2025-09-29 15:08:26] [INFO] [progress_debug] 🔄 SESSION_LIFECYCLE | Session: async_a36ec6ebb42e | Event: workflow_start | Details: {'analysis_mode': 'positional', 'market_specialization': 'Crypto', 'has_progress_broadcaster': True, 'chart_images_count': 1, 'parameter_passing_issue': False}
[2025-09-29 15:08:26] [INFO] [progress_debug] 🔄 WORKFLOW_STEP | Session: async_a36ec6ebb42e | Step: chart_analysis | Progress: 20% | Message: 🚀 Starting comprehensive AI analysis workflow...
[2025-09-29 15:08:26] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Mapped step 'chart_analysis' to chart_analysis | Data: {}
[2025-09-29 15:08:26] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Created progress update: ProgressUpdate(step=<WorkflowStep.CHART_ANALYSIS: 'chart_analysis'>, progress=20, message='🚀 Starting comprehensive AI analysis workflow...', timestamp='2025-09-29T09:38:26.209394', details={'step': 'chart_analysis', 'stage': 'start'}, tool_results=None) | Data: {}
[2025-09-29 15:08:26] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔥 BROADCASTING update for session async_a36ec6ebb42e | Data: {'step': 'chart_analysis', 'progress': 20, 'message': '🚀 Starting comprehensive AI analysis workflow...', 'has_clients': True, 'client_count': 1, 'broadcaster_id': 2379852619920, 'all_sessions': ['async_a36ec6ebb42e']}
[2025-09-29 15:08:26] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Sent update to client 0 for session async_a36ec6ebb42e | Data: {}
[2025-09-29 15:08:26] [INFO] [progress_debug] 📡 PROGRESS_BROADCAST | ✅ SUCCESS | Session: async_a36ec6ebb42e | Step: chart_analysis | Details: {'workflow_step': 'chart_analysis', 'progress': 20, 'message': '🚀 Starting comprehensive AI analysis workflow...'}
[2025-09-29 15:08:26] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Received progress update: {'step': 'chart_analysis', 'progress': 20, 'message': '🚀 Starting comprehensive AI analysis workflow...', 'timestamp': '2025-09-29T09:38:26.209394', 'details': {'step': 'chart_analysis', 'stage': 'start'}, 'tool_results': None} | Data: {}
[2025-09-29 15:08:26] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending SSE event: {'type': 'progress_update', 'data': {'step': 'chart_analysis', 'progress': 20, 'message': '🚀 Starting comprehensive AI analysis workflow...', 'timestamp': '2025-09-29T09:38:26.209394', 'details': {'step': 'chart_analysis', 'stage': 'start'}, 'tool_results': None}} | Data: {}
[2025-09-29 15:08:26] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_a36ec6ebb42e | Data: {}
[2025-09-29 15:08:27] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 8451.078} | Data: {}
[2025-09-29 15:08:27] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_a36ec6ebb42e | Data: {}
[2025-09-29 15:08:28] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 8452.093} | Data: {}
[2025-09-29 15:08:28] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_a36ec6ebb42e | Data: {}
[2025-09-29 15:08:29] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 8453.125} | Data: {}
[2025-09-29 15:08:29] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_a36ec6ebb42e | Data: {}
[2025-09-29 15:08:30] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 8454.14} | Data: {}
[2025-09-29 15:08:30] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_a36ec6ebb42e | Data: {}
[2025-09-29 15:08:31] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 8455.14} | Data: {}
[2025-09-29 15:08:31] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_a36ec6ebb42e | Data: {}
[2025-09-29 15:08:32] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 8456.14} | Data: {}
[2025-09-29 15:08:32] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_a36ec6ebb42e | Data: {}
[2025-09-29 15:08:33] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 8457.125} | Data: {}
[2025-09-29 15:08:33] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_a36ec6ebb42e | Data: {}
[2025-09-29 15:08:34] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 8458.14} | Data: {}
[2025-09-29 15:08:34] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_a36ec6ebb42e | Data: {}
[2025-09-29 15:08:35] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 8459.171} | Data: {}
[2025-09-29 15:08:35] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_a36ec6ebb42e | Data: {}
[2025-09-29 15:08:36] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 8460.171} | Data: {}
[2025-09-29 15:08:36] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_a36ec6ebb42e | Data: {}
[2025-09-29 15:08:37] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 8461.171} | Data: {}
[2025-09-29 15:08:37] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_a36ec6ebb42e | Data: {}
[2025-09-29 15:08:38] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 8462.171} | Data: {}
[2025-09-29 15:08:38] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_a36ec6ebb42e | Data: {}
[2025-09-29 15:08:39] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 8463.171} | Data: {}
[2025-09-29 15:08:39] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_a36ec6ebb42e | Data: {}
[2025-09-29 15:08:40] [INFO] [progress_debug] 🔄 WORKFLOW_STEP | Session: async_a36ec6ebb42e | Step: symbol_detection | Progress: 40% | Message: ✅ Symbol detected: BTCUSD (crypto market)
[2025-09-29 15:08:40] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Mapped step 'symbol_detection' to symbol_detection | Data: {}
[2025-09-29 15:08:40] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Created progress update: ProgressUpdate(step=<WorkflowStep.SYMBOL_DETECTION: 'symbol_detection'>, progress=40, message='✅ Symbol detected: BTCUSD (crypto market)', timestamp='2025-09-29T09:38:40.129595', details={'step': 'symbol_detection', 'symbol': 'BTCUSD', 'market': 'crypto'}, tool_results=None) | Data: {}
[2025-09-29 15:08:40] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔥 BROADCASTING update for session async_a36ec6ebb42e | Data: {'step': 'symbol_detection', 'progress': 40, 'message': '✅ Symbol detected: BTCUSD (crypto market)', 'has_clients': True, 'client_count': 1, 'broadcaster_id': 2379852619920, 'all_sessions': ['async_a36ec6ebb42e']}
[2025-09-29 15:08:40] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Sent update to client 0 for session async_a36ec6ebb42e | Data: {}
[2025-09-29 15:08:40] [INFO] [progress_debug] 📡 PROGRESS_BROADCAST | ✅ SUCCESS | Session: async_a36ec6ebb42e | Step: symbol_detection | Details: {'workflow_step': 'symbol_detection', 'progress': 40, 'message': '✅ Symbol detected: BTCUSD (crypto market)'}
[2025-09-29 15:08:40] [INFO] [progress_debug] 🔄 WORKFLOW_STEP | Session: async_a36ec6ebb42e | Step: tool_execution | Progress: 50% | Message: 🎯 Executing market data tools...
[2025-09-29 15:08:40] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Mapped step 'tool_execution' to tool_execution | Data: {}
[2025-09-29 15:08:40] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Created progress update: ProgressUpdate(step=<WorkflowStep.TOOL_EXECUTION: 'tool_execution'>, progress=50, message='🎯 Executing market data tools...', timestamp='2025-09-29T09:38:40.138592', details={'step': 'tool_execution', 'market': 'crypto'}, tool_results=None) | Data: {}
[2025-09-29 15:08:40] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔥 BROADCASTING update for session async_a36ec6ebb42e | Data: {'step': 'tool_execution', 'progress': 50, 'message': '🎯 Executing market data tools...', 'has_clients': True, 'client_count': 1, 'broadcaster_id': 2379852619920, 'all_sessions': ['async_a36ec6ebb42e']}
[2025-09-29 15:08:40] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Sent update to client 0 for session async_a36ec6ebb42e | Data: {}
[2025-09-29 15:08:40] [INFO] [progress_debug] 📡 PROGRESS_BROADCAST | ✅ SUCCESS | Session: async_a36ec6ebb42e | Step: tool_execution | Details: {'workflow_step': 'tool_execution', 'progress': 50, 'message': '🎯 Executing market data tools...'}
[2025-09-29 15:08:40] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Received progress update: {'step': 'symbol_detection', 'progress': 40, 'message': '✅ Symbol detected: BTCUSD (crypto market)', 'timestamp': '2025-09-29T09:38:40.129595', 'details': {'step': 'symbol_detection', 'symbol': 'BTCUSD', 'market': 'crypto'}, 'tool_results': None} | Data: {}
[2025-09-29 15:08:40] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending SSE event: {'type': 'progress_update', 'data': {'step': 'symbol_detection', 'progress': 40, 'message': '✅ Symbol detected: BTCUSD (crypto market)', 'timestamp': '2025-09-29T09:38:40.129595', 'details': {'step': 'symbol_detection', 'symbol': 'BTCUSD', 'market': 'crypto'}, 'tool_results': None}} | Data: {}
[2025-09-29 15:08:40] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_a36ec6ebb42e | Data: {}
[2025-09-29 15:08:40] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Received progress update: {'step': 'tool_execution', 'progress': 50, 'message': '🎯 Executing market data tools...', 'timestamp': '2025-09-29T09:38:40.138592', 'details': {'step': 'tool_execution', 'market': 'crypto'}, 'tool_results': None} | Data: {}
[2025-09-29 15:08:40] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending SSE event: {'type': 'progress_update', 'data': {'step': 'tool_execution', 'progress': 50, 'message': '🎯 Executing market data tools...', 'timestamp': '2025-09-29T09:38:40.138592', 'details': {'step': 'tool_execution', 'market': 'crypto'}, 'tool_results': None}} | Data: {}
[2025-09-29 15:08:40] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_a36ec6ebb42e | Data: {}
[2025-09-29 15:08:41] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 8464.968} | Data: {}
[2025-09-29 15:08:41] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_a36ec6ebb42e | Data: {}
[2025-09-29 15:08:43] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 8467.515} | Data: {}
[2025-09-29 15:08:43] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_a36ec6ebb42e | Data: {}
[2025-09-29 15:08:45] [INFO] [progress_debug] 🔄 WORKFLOW_STEP | Session: async_a36ec6ebb42e | Step: tool_summarization | Progress: 60% | Message: 🎯 Summarizing market data with AI...
[2025-09-29 15:08:45] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Mapped step 'tool_summarization' to tool_summarization | Data: {}
[2025-09-29 15:08:45] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Created progress update: ProgressUpdate(step=<WorkflowStep.TOOL_SUMMARIZATION: 'tool_summarization'>, progress=60, message='🎯 Summarizing market data with AI...', timestamp='2025-09-29T09:38:45.149302', details={'step': 'tool_summarization'}, tool_results=None) | Data: {}
[2025-09-29 15:08:45] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔥 BROADCASTING update for session async_a36ec6ebb42e | Data: {'step': 'tool_summarization', 'progress': 60, 'message': '🎯 Summarizing market data with AI...', 'has_clients': True, 'client_count': 1, 'broadcaster_id': 2379852619920, 'all_sessions': ['async_a36ec6ebb42e']}
[2025-09-29 15:08:45] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Sent update to client 0 for session async_a36ec6ebb42e | Data: {}
[2025-09-29 15:08:45] [INFO] [progress_debug] 📡 PROGRESS_BROADCAST | ✅ SUCCESS | Session: async_a36ec6ebb42e | Step: tool_summarization | Details: {'workflow_step': 'tool_summarization', 'progress': 60, 'message': '🎯 Summarizing market data with AI...'}
[2025-09-29 15:08:47] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Received progress update: {'step': 'tool_summarization', 'progress': 60, 'message': '🎯 Summarizing market data with AI...', 'timestamp': '2025-09-29T09:38:45.149302', 'details': {'step': 'tool_summarization'}, 'tool_results': None} | Data: {}
[2025-09-29 15:08:47] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending SSE event: {'type': 'progress_update', 'data': {'step': 'tool_summarization', 'progress': 60, 'message': '🎯 Summarizing market data with AI...', 'timestamp': '2025-09-29T09:38:45.149302', 'details': {'step': 'tool_summarization'}, 'tool_results': None}} | Data: {}
[2025-09-29 15:08:47] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_a36ec6ebb42e | Data: {}
[2025-09-29 15:08:48] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 8472.312} | Data: {}
[2025-09-29 15:08:48] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_a36ec6ebb42e | Data: {}
[2025-09-29 15:08:49] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 8473.343} | Data: {}
[2025-09-29 15:08:49] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_a36ec6ebb42e | Data: {}
[2025-09-29 15:08:50] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 8474.343} | Data: {}
[2025-09-29 15:08:50] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_a36ec6ebb42e | Data: {}
[2025-09-29 15:08:51] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 8475.359} | Data: {}
[2025-09-29 15:08:51] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_a36ec6ebb42e | Data: {}
[2025-09-29 15:08:52] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 8476.375} | Data: {}
[2025-09-29 15:08:52] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_a36ec6ebb42e | Data: {}
[2025-09-29 15:08:53] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 8477.39} | Data: {}
[2025-09-29 15:08:53] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_a36ec6ebb42e | Data: {}
[2025-09-29 15:08:54] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 8478.39} | Data: {}
[2025-09-29 15:08:54] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_a36ec6ebb42e | Data: {}
[2025-09-29 15:08:55] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 8479.39} | Data: {}
[2025-09-29 15:08:55] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_a36ec6ebb42e | Data: {}
[2025-09-29 15:08:56] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 8480.375} | Data: {}
[2025-09-29 15:08:56] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_a36ec6ebb42e | Data: {}
[2025-09-29 15:08:57] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 8481.39} | Data: {}
[2025-09-29 15:08:57] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_a36ec6ebb42e | Data: {}
[2025-09-29 15:08:58] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 8482.406} | Data: {}
[2025-09-29 15:08:58] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_a36ec6ebb42e | Data: {}
[2025-09-29 15:08:58] [INFO] [progress_debug] 🔄 WORKFLOW_STEP | Session: async_a36ec6ebb42e | Step: rag_integration | Progress: 70% | Message: 💾 Integrating historical context and patterns...
[2025-09-29 15:08:58] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Mapped step 'rag_integration' to rag_integration | Data: {}
[2025-09-29 15:08:58] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Created progress update: ProgressUpdate(step=<WorkflowStep.RAG_INTEGRATION: 'rag_integration'>, progress=70, message='💾 Integrating historical context and patterns...', timestamp='2025-09-29T09:38:58.926977', details={'step': 'rag_integration'}, tool_results=None) | Data: {}
[2025-09-29 15:08:58] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔥 BROADCASTING update for session async_a36ec6ebb42e | Data: {'step': 'rag_integration', 'progress': 70, 'message': '💾 Integrating historical context and patterns...', 'has_clients': True, 'client_count': 1, 'broadcaster_id': 2379852619920, 'all_sessions': ['async_a36ec6ebb42e']}
[2025-09-29 15:08:58] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Sent update to client 0 for session async_a36ec6ebb42e | Data: {}
[2025-09-29 15:08:58] [INFO] [progress_debug] 📡 PROGRESS_BROADCAST | ✅ SUCCESS | Session: async_a36ec6ebb42e | Step: rag_integration | Details: {'workflow_step': 'rag_integration', 'progress': 70, 'message': '💾 Integrating historical context and patterns...'}
[2025-09-29 15:08:59] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Received progress update: {'step': 'rag_integration', 'progress': 70, 'message': '💾 Integrating historical context and patterns...', 'timestamp': '2025-09-29T09:38:58.926977', 'details': {'step': 'rag_integration'}, 'tool_results': None} | Data: {}
[2025-09-29 15:08:59] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending SSE event: {'type': 'progress_update', 'data': {'step': 'rag_integration', 'progress': 70, 'message': '💾 Integrating historical context and patterns...', 'timestamp': '2025-09-29T09:38:58.926977', 'details': {'step': 'rag_integration'}, 'tool_results': None}} | Data: {}
[2025-09-29 15:08:59] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_a36ec6ebb42e | Data: {}
[2025-09-29 15:09:01] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 8484.843} | Data: {}
[2025-09-29 15:09:01] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_a36ec6ebb42e | Data: {}
[2025-09-29 15:09:02] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 8485.828} | Data: {}
[2025-09-29 15:09:02] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_a36ec6ebb42e | Data: {}
[2025-09-29 15:09:03] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 8486.843} | Data: {}
[2025-09-29 15:09:03] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_a36ec6ebb42e | Data: {}
[2025-09-29 15:09:04] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 8487.843} | Data: {}
[2025-09-29 15:09:04] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_a36ec6ebb42e | Data: {}
[2025-09-29 15:09:04] [INFO] [progress_debug] 🔄 WORKFLOW_STEP | Session: async_a36ec6ebb42e | Step: final_analysis | Progress: 80% | Message: 🎯 Generating final trading analysis...
[2025-09-29 15:09:04] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Mapped step 'final_analysis' to final_analysis | Data: {}
[2025-09-29 15:09:04] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Created progress update: ProgressUpdate(step=<WorkflowStep.FINAL_ANALYSIS: 'final_analysis'>, progress=80, message='🎯 Generating final trading analysis...', timestamp='2025-09-29T09:39:04.538569', details={'step': 'final_analysis'}, tool_results=None) | Data: {}
[2025-09-29 15:09:04] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔥 BROADCASTING update for session async_a36ec6ebb42e | Data: {'step': 'final_analysis', 'progress': 80, 'message': '🎯 Generating final trading analysis...', 'has_clients': True, 'client_count': 1, 'broadcaster_id': 2379852619920, 'all_sessions': ['async_a36ec6ebb42e']}
[2025-09-29 15:09:04] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Sent update to client 0 for session async_a36ec6ebb42e | Data: {}
[2025-09-29 15:09:04] [INFO] [progress_debug] 📡 PROGRESS_BROADCAST | ✅ SUCCESS | Session: async_a36ec6ebb42e | Step: final_analysis | Details: {'workflow_step': 'final_analysis', 'progress': 80, 'message': '🎯 Generating final trading analysis...'}
[2025-09-29 15:09:04] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Received progress update: {'step': 'final_analysis', 'progress': 80, 'message': '🎯 Generating final trading analysis...', 'timestamp': '2025-09-29T09:39:04.538569', 'details': {'step': 'final_analysis'}, 'tool_results': None} | Data: {}
[2025-09-29 15:09:04] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending SSE event: {'type': 'progress_update', 'data': {'step': 'final_analysis', 'progress': 80, 'message': '🎯 Generating final trading analysis...', 'timestamp': '2025-09-29T09:39:04.538569', 'details': {'step': 'final_analysis'}, 'tool_results': None}} | Data: {}
[2025-09-29 15:09:04] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_a36ec6ebb42e | Data: {}
[2025-09-29 15:09:05] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 8489.328} | Data: {}
[2025-09-29 15:09:05] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_a36ec6ebb42e | Data: {}
[2025-09-29 15:09:06] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 8490.343} | Data: {}
[2025-09-29 15:09:06] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_a36ec6ebb42e | Data: {}
[2025-09-29 15:09:07] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 8491.359} | Data: {}
[2025-09-29 15:09:07] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_a36ec6ebb42e | Data: {}
[2025-09-29 15:09:08] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 8492.359} | Data: {}
[2025-09-29 15:09:08] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_a36ec6ebb42e | Data: {}
[2025-09-29 15:09:09] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 8493.359} | Data: {}
[2025-09-29 15:09:09] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_a36ec6ebb42e | Data: {}
[2025-09-29 15:09:10] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 8494.375} | Data: {}
[2025-09-29 15:09:10] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_a36ec6ebb42e | Data: {}
[2025-09-29 15:09:13] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 8497.593} | Data: {}
[2025-09-29 15:09:13] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_a36ec6ebb42e | Data: {}
[2025-09-29 15:09:16] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 8500.593} | Data: {}
[2025-09-29 15:09:16] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_a36ec6ebb42e | Data: {}
[2025-09-29 15:09:17] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 8501.578} | Data: {}
[2025-09-29 15:09:17] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_a36ec6ebb42e | Data: {}
[2025-09-29 15:09:18] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 8502.578} | Data: {}
[2025-09-29 15:09:18] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_a36ec6ebb42e | Data: {}
[2025-09-29 15:09:19] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 8503.593} | Data: {}
[2025-09-29 15:09:19] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_a36ec6ebb42e | Data: {}
[2025-09-29 15:09:20] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 8504.593} | Data: {}
[2025-09-29 15:09:20] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_a36ec6ebb42e | Data: {}
[2025-09-29 15:09:21] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 8505.609} | Data: {}
[2025-09-29 15:09:21] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_a36ec6ebb42e | Data: {}
[2025-09-29 15:09:22] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 8506.625} | Data: {}
[2025-09-29 15:09:22] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_a36ec6ebb42e | Data: {}
[2025-09-29 15:09:23] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 8507.64} | Data: {}
[2025-09-29 15:09:23] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_a36ec6ebb42e | Data: {}
[2025-09-29 15:09:24] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 8508.656} | Data: {}
[2025-09-29 15:09:24] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_a36ec6ebb42e | Data: {}
[2025-09-29 15:09:25] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 8509.671} | Data: {}
[2025-09-29 15:09:25] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_a36ec6ebb42e | Data: {}
[2025-09-29 15:09:26] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 8510.703} | Data: {}
[2025-09-29 15:09:26] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_a36ec6ebb42e | Data: {}
[2025-09-29 15:09:27] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 8511.718} | Data: {}
[2025-09-29 15:09:27] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_a36ec6ebb42e | Data: {}
[2025-09-29 15:09:28] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 8512.718} | Data: {}
[2025-09-29 15:09:28] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_a36ec6ebb42e | Data: {}
[2025-09-29 15:09:29] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 8513.718} | Data: {}
[2025-09-29 15:09:29] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_a36ec6ebb42e | Data: {}
[2025-09-29 15:09:30] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 8514.703} | Data: {}
[2025-09-29 15:09:30] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_a36ec6ebb42e | Data: {}
[2025-09-29 15:09:31] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 8515.718} | Data: {}
[2025-09-29 15:09:31] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_a36ec6ebb42e | Data: {}
[2025-09-29 15:09:32] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 8516.734} | Data: {}
[2025-09-29 15:09:33] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_a36ec6ebb42e | Data: {}
[2025-09-29 15:09:33] [INFO] [progress_debug] 🔄 WORKFLOW_STEP | Session: async_a36ec6ebb42e | Step: memory_storage | Progress: 90% | Message: 💾 Storing analysis for future reference...
[2025-09-29 15:09:33] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Mapped step 'memory_storage' to memory_storage | Data: {}
[2025-09-29 15:09:33] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Created progress update: ProgressUpdate(step=<WorkflowStep.MEMORY_STORAGE: 'memory_storage'>, progress=90, message='💾 Storing analysis for future reference...', timestamp='2025-09-29T09:39:33.943377', details={'step': 'memory_storage'}, tool_results=None) | Data: {}
[2025-09-29 15:09:33] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔥 BROADCASTING update for session async_a36ec6ebb42e | Data: {'step': 'memory_storage', 'progress': 90, 'message': '💾 Storing analysis for future reference...', 'has_clients': True, 'client_count': 1, 'broadcaster_id': 2379852619920, 'all_sessions': ['async_a36ec6ebb42e']}
[2025-09-29 15:09:33] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Sent update to client 0 for session async_a36ec6ebb42e | Data: {}
[2025-09-29 15:09:33] [INFO] [progress_debug] 📡 PROGRESS_BROADCAST | ✅ SUCCESS | Session: async_a36ec6ebb42e | Step: memory_storage | Details: {'workflow_step': 'memory_storage', 'progress': 90, 'message': '💾 Storing analysis for future reference...'}
[2025-09-29 15:09:33] [INFO] [progress_debug] 🔄 WORKFLOW_STEP | Session: async_a36ec6ebb42e | Step: complete | Progress: 100% | Message: 🎉 Analysis completed successfully!
[2025-09-29 15:09:33] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Mapped step 'complete' to complete | Data: {}
[2025-09-29 15:09:33] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Created progress update: ProgressUpdate(step=<WorkflowStep.COMPLETE: 'complete'>, progress=100, message='🎉 Analysis completed successfully!', timestamp='2025-09-29T09:39:33.954384', details={'step': 'complete', 'workflow_status': 'complete'}, tool_results=None) | Data: {}
[2025-09-29 15:09:33] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔥 BROADCASTING update for session async_a36ec6ebb42e | Data: {'step': 'complete', 'progress': 100, 'message': '🎉 Analysis completed successfully!', 'has_clients': True, 'client_count': 1, 'broadcaster_id': 2379852619920, 'all_sessions': ['async_a36ec6ebb42e']}
[2025-09-29 15:09:33] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Sent update to client 0 for session async_a36ec6ebb42e | Data: {}
[2025-09-29 15:09:33] [INFO] [progress_debug] 📡 PROGRESS_BROADCAST | ✅ SUCCESS | Session: async_a36ec6ebb42e | Step: complete | Details: {'workflow_step': 'complete', 'progress': 100, 'message': '🎉 Analysis completed successfully!'}
[2025-09-29 15:09:33] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔥 BROADCASTING update for session async_a36ec6ebb42e | Data: {'step': 'complete', 'progress': 100, 'message': '✅ AI analysis workflow completed successfully!', 'has_clients': True, 'client_count': 1, 'broadcaster_id': 2379852619920, 'all_sessions': ['async_a36ec6ebb42e']}
[2025-09-29 15:09:33] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Sent update to client 0 for session async_a36ec6ebb42e | Data: {}
[2025-09-29 15:09:33] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔥 BROADCASTING update for session async_a36ec6ebb42e | Data: {'step': 'complete', 'progress': 100, 'message': '✅ AI analysis workflow completed successfully!', 'has_clients': True, 'client_count': 1, 'broadcaster_id': 2379852619920, 'all_sessions': ['async_a36ec6ebb42e']}
[2025-09-29 15:09:33] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Sent update to client 0 for session async_a36ec6ebb42e | Data: {}
[2025-09-29 15:09:33] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Received progress update: {'step': 'memory_storage', 'progress': 90, 'message': '💾 Storing analysis for future reference...', 'timestamp': '2025-09-29T09:39:33.943377', 'details': {'step': 'memory_storage'}, 'tool_results': None} | Data: {}
[2025-09-29 15:09:33] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending SSE event: {'type': 'progress_update', 'data': {'step': 'memory_storage', 'progress': 90, 'message': '💾 Storing analysis for future reference...', 'timestamp': '2025-09-29T09:39:33.943377', 'details': {'step': 'memory_storage'}, 'tool_results': None}} | Data: {}
[2025-09-29 15:09:33] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_a36ec6ebb42e | Data: {}
[2025-09-29 15:09:33] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Received progress update: {'step': 'complete', 'progress': 100, 'message': '🎉 Analysis completed successfully!', 'timestamp': '2025-09-29T09:39:33.954384', 'details': {'step': 'complete', 'workflow_status': 'complete'}, 'tool_results': None} | Data: {}
[2025-09-29 15:09:33] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending SSE event: {'type': 'progress_update', 'data': {'step': 'complete', 'progress': 100, 'message': '🎉 Analysis completed successfully!', 'timestamp': '2025-09-29T09:39:33.954384', 'details': {'step': 'complete', 'workflow_status': 'complete'}, 'tool_results': None}} | Data: {}
[2025-09-29 15:09:33] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Workflow complete, sending: {'type': 'complete', 'session_id': 'async_a36ec6ebb42e'} | Data: {}
[2025-09-29 15:09:33] [INFO] [progress_debug] 🔌 SSE_DISCONNECT | Session: async_a36ec6ebb42e | Reason: event_generator_cleanup
[2025-09-29 15:09:33] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Removed client for session async_a36ec6ebb42e | Data: {'remaining_clients': 0}
[2025-09-29 15:09:33] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Cleaned up empty client list for session async_a36ec6ebb42e | Data: {}
[2025-09-29 15:09:33] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Keeping buffered updates for potential reconnection: async_a36ec6ebb42e | Data: {}
[2025-09-29 15:09:33] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Buffered updates count: 13 | Data: {}
[2025-09-29 15:09:34] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔥 BROADCASTING update for session async_a36ec6ebb42e | Data: {'step': 'complete', 'progress': 100, 'message': '🎉 Analysis completed successfully!', 'has_clients': False, 'client_count': 0, 'broadcaster_id': 2379852619920, 'all_sessions': []}
[2025-09-29 15:09:34] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | No clients found for session async_a36ec6ebb42e - update buffered (total: 14) | Data: {}
[2025-09-29 15:09:34] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Marked session async_a36ec6ebb42e as complete | Data: {}
[2025-09-29 15:09:34] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Cleaned up buffered updates for completed session async_a36ec6ebb42e | Data: {}
[2025-09-29 15:43:08] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔥 BROADCASTING update for session async_11c262c36dbb | Data: {'step': 'chart_upload', 'progress': 5, 'message': 'Analysis request received, starting background processing...', 'has_clients': False, 'client_count': 0, 'broadcaster_id': 2522352460880, 'all_sessions': []}
[2025-09-29 15:43:08] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | No clients found for session async_11c262c36dbb - update buffered (total: 1) | Data: {}
[2025-09-29 15:43:08] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔥 BROADCASTING update for session async_11c262c36dbb | Data: {'step': 'chart_upload', 'progress': 5, 'message': '📊 Processing uploaded chart images...', 'has_clients': False, 'client_count': 0, 'broadcaster_id': 2522352460880, 'all_sessions': []}
[2025-09-29 15:43:08] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | No clients found for session async_11c262c36dbb - update buffered (total: 2) | Data: {}
[2025-09-29 15:43:08] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔥 BROADCASTING update for session async_11c262c36dbb | Data: {'step': 'chart_upload', 'progress': 5, 'message': '🖼️ Processed chart image 1/1 (0KB)', 'has_clients': False, 'client_count': 0, 'broadcaster_id': 2522352460880, 'all_sessions': []}
[2025-09-29 15:43:08] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | No clients found for session async_11c262c36dbb - update buffered (total: 3) | Data: {}
[2025-09-29 15:43:10] [INFO] [progress_debug] 🔄 SESSION_LIFECYCLE | Session: async_11c262c36dbb | Event: workflow_start | Details: {'analysis_mode': 'positional', 'market_specialization': 'Crypto', 'has_progress_broadcaster': True, 'chart_images_count': 1, 'parameter_passing_issue': False}
[2025-09-29 15:43:10] [INFO] [progress_debug] 🔄 WORKFLOW_STEP | Session: async_11c262c36dbb | Step: chart_analysis | Progress: 20% | Message: 🚀 Starting comprehensive AI analysis workflow...
[2025-09-29 15:43:10] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Mapped step 'chart_analysis' to chart_analysis | Data: {}
[2025-09-29 15:43:10] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Created progress update: ProgressUpdate(step=<WorkflowStep.CHART_ANALYSIS: 'chart_analysis'>, progress=20, message='🚀 Starting comprehensive AI analysis workflow...', timestamp='2025-09-29T10:13:10.490466', details={'step': 'chart_analysis', 'stage': 'start'}, tool_results=None) | Data: {}
[2025-09-29 15:43:10] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔥 BROADCASTING update for session async_11c262c36dbb | Data: {'step': 'chart_analysis', 'progress': 20, 'message': '🚀 Starting comprehensive AI analysis workflow...', 'has_clients': False, 'client_count': 0, 'broadcaster_id': 2522352460880, 'all_sessions': []}
[2025-09-29 15:43:10] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | No clients found for session async_11c262c36dbb - update buffered (total: 4) | Data: {}
[2025-09-29 15:43:10] [INFO] [progress_debug] 📡 PROGRESS_BROADCAST | ✅ SUCCESS | Session: async_11c262c36dbb | Step: chart_analysis | Details: {'workflow_step': 'chart_analysis', 'progress': 20, 'message': '🚀 Starting comprehensive AI analysis workflow...'}
[2025-09-29 15:43:10] [INFO] [progress_debug] 🔗 SSE_CONNECTION | Session: async_11c262c36dbb | Client: {'user_id': UUID('ee801bc0-845a-4e55-952f-b648b2c61a93'), 'user_email': '<EMAIL>', 'endpoint': '/api/v1/progress/stream/async_11c262c36dbb'}
[2025-09-29 15:43:10] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Adding client for session async_11c262c36dbb | Data: {}
[2025-09-29 15:43:10] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | ✅ CLIENT ADDED for session async_11c262c36dbb | Data: {'total_clients': 1, 'all_sessions': ['async_11c262c36dbb'], 'broadcaster_id': 2522352460880}
[2025-09-29 15:43:10] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Current broadcaster state | Data: {'session_id': 'async_11c262c36dbb', 'clients_dict': {'async_11c262c36dbb': 1}, 'total_sessions': 1}
[2025-09-29 15:43:10] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔄 REPLAYING 4 buffered updates to new client for session async_11c262c36dbb | Data: {}
[2025-09-29 15:43:10] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | ✅ Replayed buffered update 1/4: chart_upload (5%) | Data: {}
[2025-09-29 15:43:10] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | ✅ Replayed buffered update 2/4: chart_upload (5%) | Data: {}
[2025-09-29 15:43:10] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | ✅ Replayed buffered update 3/4: chart_upload (5%) | Data: {}
[2025-09-29 15:43:10] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | ✅ Replayed buffered update 4/4: chart_analysis (20%) | Data: {}
[2025-09-29 15:43:10] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🎯 Successfully replayed 4 buffered updates for session async_11c262c36dbb | Data: {}
[2025-09-29 15:43:10] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Client queue created for session async_11c262c36dbb | Data: {}
[2025-09-29 15:43:10] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending connection confirmation: {'type': 'connected', 'session_id': 'async_11c262c36dbb'} | Data: {}
[2025-09-29 15:43:10] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_11c262c36dbb | Data: {}
[2025-09-29 15:43:10] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Received progress update: {'step': 'chart_upload', 'progress': 5, 'message': 'Analysis request received, starting background processing...', 'timestamp': '2025-09-29T10:13:08.510985', 'details': {'user_id': 'ee801bc0-845a-4e55-952f-b648b2c61a93', 'analysis_type': 'Positional', 'image_count': 1}, 'tool_results': None} | Data: {}
[2025-09-29 15:43:10] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending SSE event: {'type': 'progress_update', 'data': {'step': 'chart_upload', 'progress': 5, 'message': 'Analysis request received, starting background processing...', 'timestamp': '2025-09-29T10:13:08.510985', 'details': {'user_id': 'ee801bc0-845a-4e55-952f-b648b2c61a93', 'analysis_type': 'Positional', 'image_count': 1}, 'tool_results': None}} | Data: {}
[2025-09-29 15:43:10] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_11c262c36dbb | Data: {}
[2025-09-29 15:43:10] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Received progress update: {'step': 'chart_upload', 'progress': 5, 'message': '📊 Processing uploaded chart images...', 'timestamp': '2025-09-29T10:13:08.675995', 'details': {'user_id': 'ee801bc0-845a-4e55-952f-b648b2c61a93', 'analysis_type': 'Positional', 'image_count': 1}, 'tool_results': None} | Data: {}
[2025-09-29 15:43:10] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending SSE event: {'type': 'progress_update', 'data': {'step': 'chart_upload', 'progress': 5, 'message': '📊 Processing uploaded chart images...', 'timestamp': '2025-09-29T10:13:08.675995', 'details': {'user_id': 'ee801bc0-845a-4e55-952f-b648b2c61a93', 'analysis_type': 'Positional', 'image_count': 1}, 'tool_results': None}} | Data: {}
[2025-09-29 15:43:10] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_11c262c36dbb | Data: {}
[2025-09-29 15:43:10] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Received progress update: {'step': 'chart_upload', 'progress': 5, 'message': '🖼️ Processed chart image 1/1 (0KB)', 'timestamp': '2025-09-29T10:13:08.686986', 'details': {'image_size': 330, 'images_processed': 1}, 'tool_results': None} | Data: {}
[2025-09-29 15:43:10] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending SSE event: {'type': 'progress_update', 'data': {'step': 'chart_upload', 'progress': 5, 'message': '🖼️ Processed chart image 1/1 (0KB)', 'timestamp': '2025-09-29T10:13:08.686986', 'details': {'image_size': 330, 'images_processed': 1}, 'tool_results': None}} | Data: {}
[2025-09-29 15:43:10] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_11c262c36dbb | Data: {}
[2025-09-29 15:43:10] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Received progress update: {'step': 'chart_analysis', 'progress': 20, 'message': '🚀 Starting comprehensive AI analysis workflow...', 'timestamp': '2025-09-29T10:13:10.490466', 'details': {'step': 'chart_analysis', 'stage': 'start'}, 'tool_results': None} | Data: {}
[2025-09-29 15:43:10] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending SSE event: {'type': 'progress_update', 'data': {'step': 'chart_analysis', 'progress': 20, 'message': '🚀 Starting comprehensive AI analysis workflow...', 'timestamp': '2025-09-29T10:13:10.490466', 'details': {'step': 'chart_analysis', 'stage': 'start'}, 'tool_results': None}} | Data: {}
[2025-09-29 15:43:10] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_11c262c36dbb | Data: {}
[2025-09-29 15:43:11] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 10535.593} | Data: {}
[2025-09-29 15:43:11] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_11c262c36dbb | Data: {}
[2025-09-29 15:43:12] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 10536.609} | Data: {}
[2025-09-29 15:43:12] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_11c262c36dbb | Data: {}
[2025-09-29 15:43:13] [INFO] [progress_debug] 🔄 WORKFLOW_STEP | Session: async_11c262c36dbb | Step: symbol_detection | Progress: 40% | Message: ✅ Symbol detected: N/A - No chart image provided for analysis (N/A - No chart image provided for analysis market)
[2025-09-29 15:43:13] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Mapped step 'symbol_detection' to symbol_detection | Data: {}
[2025-09-29 15:43:13] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Created progress update: ProgressUpdate(step=<WorkflowStep.SYMBOL_DETECTION: 'symbol_detection'>, progress=40, message='✅ Symbol detected: N/A - No chart image provided for analysis (N/A - No chart image provided for analysis market)', timestamp='2025-09-29T10:13:13.468304', details={'step': 'symbol_detection', 'symbol': 'N/A - No chart image provided for analysis', 'market': 'N/A - No chart image provided for analysis'}, tool_results=None) | Data: {}
[2025-09-29 15:43:13] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔥 BROADCASTING update for session async_11c262c36dbb | Data: {'step': 'symbol_detection', 'progress': 40, 'message': '✅ Symbol detected: N/A - No chart image provided for analysis (N/A - No chart image provided for analysis market)', 'has_clients': True, 'client_count': 1, 'broadcaster_id': 2522352460880, 'all_sessions': ['async_11c262c36dbb']}
[2025-09-29 15:43:13] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Sent update to client 0 for session async_11c262c36dbb | Data: {}
[2025-09-29 15:43:13] [INFO] [progress_debug] 📡 PROGRESS_BROADCAST | ✅ SUCCESS | Session: async_11c262c36dbb | Step: symbol_detection | Details: {'workflow_step': 'symbol_detection', 'progress': 40, 'message': '✅ Symbol detected: N/A - No chart image provided for analysis (N/A - No chart image provided for analysis market)'}
[2025-09-29 15:43:13] [INFO] [progress_debug] 🔄 WORKFLOW_STEP | Session: async_11c262c36dbb | Step: tool_execution | Progress: 50% | Message: 🎯 Executing market data tools...
[2025-09-29 15:43:13] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Mapped step 'tool_execution' to tool_execution | Data: {}
[2025-09-29 15:43:13] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Created progress update: ProgressUpdate(step=<WorkflowStep.TOOL_EXECUTION: 'tool_execution'>, progress=50, message='🎯 Executing market data tools...', timestamp='2025-09-29T10:13:13.488157', details={'step': 'tool_execution', 'market': 'N/A - No chart image provided for analysis'}, tool_results=None) | Data: {}
[2025-09-29 15:43:13] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔥 BROADCASTING update for session async_11c262c36dbb | Data: {'step': 'tool_execution', 'progress': 50, 'message': '🎯 Executing market data tools...', 'has_clients': True, 'client_count': 1, 'broadcaster_id': 2522352460880, 'all_sessions': ['async_11c262c36dbb']}
[2025-09-29 15:43:13] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Sent update to client 0 for session async_11c262c36dbb | Data: {}
[2025-09-29 15:43:13] [INFO] [progress_debug] 📡 PROGRESS_BROADCAST | ✅ SUCCESS | Session: async_11c262c36dbb | Step: tool_execution | Details: {'workflow_step': 'tool_execution', 'progress': 50, 'message': '🎯 Executing market data tools...'}
[2025-09-29 15:43:13] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Received progress update: {'step': 'symbol_detection', 'progress': 40, 'message': '✅ Symbol detected: N/A - No chart image provided for analysis (N/A - No chart image provided for analysis market)', 'timestamp': '2025-09-29T10:13:13.468304', 'details': {'step': 'symbol_detection', 'symbol': 'N/A - No chart image provided for analysis', 'market': 'N/A - No chart image provided for analysis'}, 'tool_results': None} | Data: {}
[2025-09-29 15:43:13] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending SSE event: {'type': 'progress_update', 'data': {'step': 'symbol_detection', 'progress': 40, 'message': '✅ Symbol detected: N/A - No chart image provided for analysis (N/A - No chart image provided for analysis market)', 'timestamp': '2025-09-29T10:13:13.468304', 'details': {'step': 'symbol_detection', 'symbol': 'N/A - No chart image provided for analysis', 'market': 'N/A - No chart image provided for analysis'}, 'tool_results': None}} | Data: {}
[2025-09-29 15:43:13] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_11c262c36dbb | Data: {}
[2025-09-29 15:43:13] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Received progress update: {'step': 'tool_execution', 'progress': 50, 'message': '🎯 Executing market data tools...', 'timestamp': '2025-09-29T10:13:13.488157', 'details': {'step': 'tool_execution', 'market': 'N/A - No chart image provided for analysis'}, 'tool_results': None} | Data: {}
[2025-09-29 15:43:13] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending SSE event: {'type': 'progress_update', 'data': {'step': 'tool_execution', 'progress': 50, 'message': '🎯 Executing market data tools...', 'timestamp': '2025-09-29T10:13:13.488157', 'details': {'step': 'tool_execution', 'market': 'N/A - No chart image provided for analysis'}, 'tool_results': None}} | Data: {}
[2025-09-29 15:43:13] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_11c262c36dbb | Data: {}
[2025-09-29 15:43:14] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 10538.39} | Data: {}
[2025-09-29 15:43:14] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_11c262c36dbb | Data: {}
[2025-09-29 15:43:15] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 10539.406} | Data: {}
[2025-09-29 15:43:15] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_11c262c36dbb | Data: {}
[2025-09-29 15:43:16] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 10540.421} | Data: {}
[2025-09-29 15:43:16] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_11c262c36dbb | Data: {}
[2025-09-29 15:43:17] [INFO] [progress_debug] 🔄 WORKFLOW_STEP | Session: async_11c262c36dbb | Step: tool_summarization | Progress: 60% | Message: 🎯 Summarizing market data with AI...
[2025-09-29 15:43:17] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Mapped step 'tool_summarization' to tool_summarization | Data: {}
[2025-09-29 15:43:17] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Created progress update: ProgressUpdate(step=<WorkflowStep.TOOL_SUMMARIZATION: 'tool_summarization'>, progress=60, message='🎯 Summarizing market data with AI...', timestamp='2025-09-29T10:13:17.637525', details={'step': 'tool_summarization'}, tool_results=None) | Data: {}
[2025-09-29 15:43:17] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔥 BROADCASTING update for session async_11c262c36dbb | Data: {'step': 'tool_summarization', 'progress': 60, 'message': '🎯 Summarizing market data with AI...', 'has_clients': True, 'client_count': 1, 'broadcaster_id': 2522352460880, 'all_sessions': ['async_11c262c36dbb']}
[2025-09-29 15:43:17] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Sent update to client 0 for session async_11c262c36dbb | Data: {}
[2025-09-29 15:43:17] [INFO] [progress_debug] 📡 PROGRESS_BROADCAST | ✅ SUCCESS | Session: async_11c262c36dbb | Step: tool_summarization | Details: {'workflow_step': 'tool_summarization', 'progress': 60, 'message': '🎯 Summarizing market data with AI...'}
[2025-09-29 15:43:17] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Received progress update: {'step': 'tool_summarization', 'progress': 60, 'message': '🎯 Summarizing market data with AI...', 'timestamp': '2025-09-29T10:13:17.637525', 'details': {'step': 'tool_summarization'}, 'tool_results': None} | Data: {}
[2025-09-29 15:43:17] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending SSE event: {'type': 'progress_update', 'data': {'step': 'tool_summarization', 'progress': 60, 'message': '🎯 Summarizing market data with AI...', 'timestamp': '2025-09-29T10:13:17.637525', 'details': {'step': 'tool_summarization'}, 'tool_results': None}} | Data: {}
[2025-09-29 15:43:17] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_11c262c36dbb | Data: {}
[2025-09-29 15:43:18] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 10542.453} | Data: {}
[2025-09-29 15:43:18] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_11c262c36dbb | Data: {}
[2025-09-29 15:43:19] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 10543.468} | Data: {}
[2025-09-29 15:43:19] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_11c262c36dbb | Data: {}
[2025-09-29 15:43:20] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 10544.484} | Data: {}
[2025-09-29 15:43:20] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_11c262c36dbb | Data: {}
[2025-09-29 15:43:21] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 10545.5} | Data: {}
[2025-09-29 15:43:21] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_11c262c36dbb | Data: {}
[2025-09-29 15:43:22] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 10546.515} | Data: {}
[2025-09-29 15:43:22] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_11c262c36dbb | Data: {}
[2025-09-29 15:43:23] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 10547.546} | Data: {}
[2025-09-29 15:43:23] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_11c262c36dbb | Data: {}
[2025-09-29 15:43:24] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 10548.562} | Data: {}
[2025-09-29 15:43:24] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_11c262c36dbb | Data: {}
[2025-09-29 15:43:25] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 10549.562} | Data: {}
[2025-09-29 15:43:25] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_11c262c36dbb | Data: {}
[2025-09-29 15:43:26] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 10550.578} | Data: {}
[2025-09-29 15:43:26] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_11c262c36dbb | Data: {}
[2025-09-29 15:43:27] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 10551.593} | Data: {}
[2025-09-29 15:43:27] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_11c262c36dbb | Data: {}
[2025-09-29 15:43:28] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 10552.609} | Data: {}
[2025-09-29 15:43:28] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_11c262c36dbb | Data: {}
[2025-09-29 15:43:29] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 10553.656} | Data: {}
[2025-09-29 15:43:29] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_11c262c36dbb | Data: {}
[2025-09-29 15:43:30] [INFO] [progress_debug] 🔄 WORKFLOW_STEP | Session: async_11c262c36dbb | Step: rag_integration | Progress: 70% | Message: 💾 Integrating historical context and patterns...
[2025-09-29 15:43:30] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Mapped step 'rag_integration' to rag_integration | Data: {}
[2025-09-29 15:43:30] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Created progress update: ProgressUpdate(step=<WorkflowStep.RAG_INTEGRATION: 'rag_integration'>, progress=70, message='💾 Integrating historical context and patterns...', timestamp='2025-09-29T10:13:30.058265', details={'step': 'rag_integration'}, tool_results=None) | Data: {}
[2025-09-29 15:43:30] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔥 BROADCASTING update for session async_11c262c36dbb | Data: {'step': 'rag_integration', 'progress': 70, 'message': '💾 Integrating historical context and patterns...', 'has_clients': True, 'client_count': 1, 'broadcaster_id': 2522352460880, 'all_sessions': ['async_11c262c36dbb']}
[2025-09-29 15:43:30] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Sent update to client 0 for session async_11c262c36dbb | Data: {}
[2025-09-29 15:43:30] [INFO] [progress_debug] 📡 PROGRESS_BROADCAST | ✅ SUCCESS | Session: async_11c262c36dbb | Step: rag_integration | Details: {'workflow_step': 'rag_integration', 'progress': 70, 'message': '💾 Integrating historical context and patterns...'}
[2025-09-29 15:43:30] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Received progress update: {'step': 'rag_integration', 'progress': 70, 'message': '💾 Integrating historical context and patterns...', 'timestamp': '2025-09-29T10:13:30.058265', 'details': {'step': 'rag_integration'}, 'tool_results': None} | Data: {}
[2025-09-29 15:43:30] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending SSE event: {'type': 'progress_update', 'data': {'step': 'rag_integration', 'progress': 70, 'message': '💾 Integrating historical context and patterns...', 'timestamp': '2025-09-29T10:13:30.058265', 'details': {'step': 'rag_integration'}, 'tool_results': None}} | Data: {}
[2025-09-29 15:43:30] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_11c262c36dbb | Data: {}
[2025-09-29 15:43:31] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 10554.89} | Data: {}
[2025-09-29 15:43:31] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_11c262c36dbb | Data: {}
[2025-09-29 15:43:32] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 10555.906} | Data: {}
[2025-09-29 15:43:32] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_11c262c36dbb | Data: {}
[2025-09-29 15:43:33] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 10556.921} | Data: {}
[2025-09-29 15:43:33] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_11c262c36dbb | Data: {}
[2025-09-29 15:43:34] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 10557.937} | Data: {}
[2025-09-29 15:43:34] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_11c262c36dbb | Data: {}
[2025-09-29 15:43:35] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 10558.937} | Data: {}
[2025-09-29 15:43:35] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_11c262c36dbb | Data: {}
[2025-09-29 15:43:36] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 10559.968} | Data: {}
[2025-09-29 15:43:36] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_11c262c36dbb | Data: {}
[2025-09-29 15:43:38] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 10562.296} | Data: {}
[2025-09-29 15:43:38] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_11c262c36dbb | Data: {}
[2025-09-29 15:43:39] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 10563.281} | Data: {}
[2025-09-29 15:43:39] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_11c262c36dbb | Data: {}
[2025-09-29 15:43:39] [INFO] [progress_debug] 🔄 WORKFLOW_STEP | Session: async_11c262c36dbb | Step: final_analysis | Progress: 80% | Message: 🎯 Generating final trading analysis...
[2025-09-29 15:43:39] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Mapped step 'final_analysis' to final_analysis | Data: {}
[2025-09-29 15:43:39] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Created progress update: ProgressUpdate(step=<WorkflowStep.FINAL_ANALYSIS: 'final_analysis'>, progress=80, message='🎯 Generating final trading analysis...', timestamp='2025-09-29T10:13:39.611447', details={'step': 'final_analysis'}, tool_results=None) | Data: {}
[2025-09-29 15:43:39] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔥 BROADCASTING update for session async_11c262c36dbb | Data: {'step': 'final_analysis', 'progress': 80, 'message': '🎯 Generating final trading analysis...', 'has_clients': True, 'client_count': 1, 'broadcaster_id': 2522352460880, 'all_sessions': ['async_11c262c36dbb']}
[2025-09-29 15:43:39] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Sent update to client 0 for session async_11c262c36dbb | Data: {}
[2025-09-29 15:43:39] [INFO] [progress_debug] 📡 PROGRESS_BROADCAST | ✅ SUCCESS | Session: async_11c262c36dbb | Step: final_analysis | Details: {'workflow_step': 'final_analysis', 'progress': 80, 'message': '🎯 Generating final trading analysis...'}
[2025-09-29 15:43:39] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Received progress update: {'step': 'final_analysis', 'progress': 80, 'message': '🎯 Generating final trading analysis...', 'timestamp': '2025-09-29T10:13:39.611447', 'details': {'step': 'final_analysis'}, 'tool_results': None} | Data: {}
[2025-09-29 15:43:39] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending SSE event: {'type': 'progress_update', 'data': {'step': 'final_analysis', 'progress': 80, 'message': '🎯 Generating final trading analysis...', 'timestamp': '2025-09-29T10:13:39.611447', 'details': {'step': 'final_analysis'}, 'tool_results': None}} | Data: {}
[2025-09-29 15:43:39] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_11c262c36dbb | Data: {}
[2025-09-29 15:43:40] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 10564.39} | Data: {}
[2025-09-29 15:43:40] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_11c262c36dbb | Data: {}
[2025-09-29 15:43:41] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 10565.421} | Data: {}
[2025-09-29 15:43:41] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_11c262c36dbb | Data: {}
[2025-09-29 15:43:42] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 10566.437} | Data: {}
[2025-09-29 15:43:42] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_11c262c36dbb | Data: {}
[2025-09-29 15:43:43] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 10567.453} | Data: {}
[2025-09-29 15:43:43] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_11c262c36dbb | Data: {}
[2025-09-29 15:43:44] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 10568.468} | Data: {}
[2025-09-29 15:43:44] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_11c262c36dbb | Data: {}
[2025-09-29 15:43:45] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 10569.468} | Data: {}
[2025-09-29 15:43:45] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_11c262c36dbb | Data: {}
[2025-09-29 15:43:46] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 10570.453} | Data: {}
[2025-09-29 15:43:46] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_11c262c36dbb | Data: {}
[2025-09-29 15:43:47] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 10571.484} | Data: {}
[2025-09-29 15:43:47] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_11c262c36dbb | Data: {}
[2025-09-29 15:43:48] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 10572.484} | Data: {}
[2025-09-29 15:43:48] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_11c262c36dbb | Data: {}
[2025-09-29 15:43:49] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 10573.5} | Data: {}
[2025-09-29 15:43:49] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_11c262c36dbb | Data: {}
[2025-09-29 15:43:50] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 10574.515} | Data: {}
[2025-09-29 15:43:50] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_11c262c36dbb | Data: {}
[2025-09-29 15:43:51] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 10575.531} | Data: {}
[2025-09-29 15:43:51] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_11c262c36dbb | Data: {}
[2025-09-29 15:43:52] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 10576.546} | Data: {}
[2025-09-29 15:43:52] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_11c262c36dbb | Data: {}
[2025-09-29 15:43:53] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 10577.562} | Data: {}
[2025-09-29 15:43:53] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_11c262c36dbb | Data: {}
[2025-09-29 15:43:54] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 10578.578} | Data: {}
[2025-09-29 15:43:54] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_11c262c36dbb | Data: {}
[2025-09-29 15:43:55] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 10579.578} | Data: {}
[2025-09-29 15:43:55] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_11c262c36dbb | Data: {}
[2025-09-29 15:43:56] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 10580.609} | Data: {}
[2025-09-29 15:43:56] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_11c262c36dbb | Data: {}
[2025-09-29 15:43:57] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 10581.609} | Data: {}
[2025-09-29 15:43:57] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_11c262c36dbb | Data: {}
[2025-09-29 15:43:58] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 10582.64} | Data: {}
[2025-09-29 15:43:58] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_11c262c36dbb | Data: {}
[2025-09-29 15:43:59] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 10583.64} | Data: {}
[2025-09-29 15:43:59] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_11c262c36dbb | Data: {}
[2025-09-29 15:44:00] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 10584.671} | Data: {}
[2025-09-29 15:44:00] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_11c262c36dbb | Data: {}
[2025-09-29 15:44:01] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 10585.671} | Data: {}
[2025-09-29 15:44:01] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_11c262c36dbb | Data: {}
[2025-09-29 15:44:02] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 10586.687} | Data: {}
[2025-09-29 15:44:02] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_11c262c36dbb | Data: {}
[2025-09-29 15:44:03] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 10587.703} | Data: {}
[2025-09-29 15:44:03] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_11c262c36dbb | Data: {}
[2025-09-29 15:44:04] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 10588.718} | Data: {}
[2025-09-29 15:44:04] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_11c262c36dbb | Data: {}
[2025-09-29 15:44:05] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 10589.703} | Data: {}
[2025-09-29 15:44:05] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_11c262c36dbb | Data: {}
[2025-09-29 15:44:06] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 10590.718} | Data: {}
[2025-09-29 15:44:06] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_11c262c36dbb | Data: {}
[2025-09-29 15:44:08] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 10591.734} | Data: {}
[2025-09-29 15:44:08] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_11c262c36dbb | Data: {}
[2025-09-29 15:44:09] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 10592.765} | Data: {}
[2025-09-29 15:44:09] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_11c262c36dbb | Data: {}
[2025-09-29 15:44:10] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 10593.765} | Data: {}
[2025-09-29 15:44:10] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_11c262c36dbb | Data: {}
[2025-09-29 15:44:10] [INFO] [progress_debug] 🔄 WORKFLOW_STEP | Session: async_11c262c36dbb | Step: memory_storage | Progress: 90% | Message: 💾 Storing analysis for future reference...
[2025-09-29 15:44:10] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Mapped step 'memory_storage' to memory_storage | Data: {}
[2025-09-29 15:44:10] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Created progress update: ProgressUpdate(step=<WorkflowStep.MEMORY_STORAGE: 'memory_storage'>, progress=90, message='💾 Storing analysis for future reference...', timestamp='2025-09-29T10:14:10.906703', details={'step': 'memory_storage'}, tool_results=None) | Data: {}
[2025-09-29 15:44:10] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔥 BROADCASTING update for session async_11c262c36dbb | Data: {'step': 'memory_storage', 'progress': 90, 'message': '💾 Storing analysis for future reference...', 'has_clients': True, 'client_count': 1, 'broadcaster_id': 2522352460880, 'all_sessions': ['async_11c262c36dbb']}
[2025-09-29 15:44:10] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Sent update to client 0 for session async_11c262c36dbb | Data: {}
[2025-09-29 15:44:10] [INFO] [progress_debug] 📡 PROGRESS_BROADCAST | ✅ SUCCESS | Session: async_11c262c36dbb | Step: memory_storage | Details: {'workflow_step': 'memory_storage', 'progress': 90, 'message': '💾 Storing analysis for future reference...'}
[2025-09-29 15:44:10] [INFO] [progress_debug] 🔄 WORKFLOW_STEP | Session: async_11c262c36dbb | Step: complete | Progress: 100% | Message: 🎉 Analysis completed successfully!
[2025-09-29 15:44:10] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Mapped step 'complete' to complete | Data: {}
[2025-09-29 15:44:10] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Created progress update: ProgressUpdate(step=<WorkflowStep.COMPLETE: 'complete'>, progress=100, message='🎉 Analysis completed successfully!', timestamp='2025-09-29T10:14:10.931348', details={'step': 'complete', 'workflow_status': 'complete'}, tool_results=None) | Data: {}
[2025-09-29 15:44:10] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔥 BROADCASTING update for session async_11c262c36dbb | Data: {'step': 'complete', 'progress': 100, 'message': '🎉 Analysis completed successfully!', 'has_clients': True, 'client_count': 1, 'broadcaster_id': 2522352460880, 'all_sessions': ['async_11c262c36dbb']}
[2025-09-29 15:44:10] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Sent update to client 0 for session async_11c262c36dbb | Data: {}
[2025-09-29 15:44:10] [INFO] [progress_debug] 📡 PROGRESS_BROADCAST | ✅ SUCCESS | Session: async_11c262c36dbb | Step: complete | Details: {'workflow_step': 'complete', 'progress': 100, 'message': '🎉 Analysis completed successfully!'}
[2025-09-29 15:44:10] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Received progress update: {'step': 'memory_storage', 'progress': 90, 'message': '💾 Storing analysis for future reference...', 'timestamp': '2025-09-29T10:14:10.906703', 'details': {'step': 'memory_storage'}, 'tool_results': None} | Data: {}
[2025-09-29 15:44:10] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending SSE event: {'type': 'progress_update', 'data': {'step': 'memory_storage', 'progress': 90, 'message': '💾 Storing analysis for future reference...', 'timestamp': '2025-09-29T10:14:10.906703', 'details': {'step': 'memory_storage'}, 'tool_results': None}} | Data: {}
[2025-09-29 15:44:10] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_11c262c36dbb | Data: {}
[2025-09-29 15:44:10] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Received progress update: {'step': 'complete', 'progress': 100, 'message': '🎉 Analysis completed successfully!', 'timestamp': '2025-09-29T10:14:10.931348', 'details': {'step': 'complete', 'workflow_status': 'complete'}, 'tool_results': None} | Data: {}
[2025-09-29 15:44:10] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending SSE event: {'type': 'progress_update', 'data': {'step': 'complete', 'progress': 100, 'message': '🎉 Analysis completed successfully!', 'timestamp': '2025-09-29T10:14:10.931348', 'details': {'step': 'complete', 'workflow_status': 'complete'}, 'tool_results': None}} | Data: {}
[2025-09-29 15:44:10] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Workflow complete, sending: {'type': 'complete', 'session_id': 'async_11c262c36dbb'} | Data: {}
[2025-09-29 15:44:10] [INFO] [progress_debug] 🔌 SSE_DISCONNECT | Session: async_11c262c36dbb | Reason: event_generator_cleanup
[2025-09-29 15:44:10] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Removed client for session async_11c262c36dbb | Data: {'remaining_clients': 0}
[2025-09-29 15:44:10] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Cleaned up empty client list for session async_11c262c36dbb | Data: {}
[2025-09-29 15:44:10] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Keeping buffered updates for potential reconnection: async_11c262c36dbb | Data: {}
[2025-09-29 15:44:10] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Buffered updates count: 11 | Data: {}
[2025-09-29 15:44:10] [INFO] [progress_debug] 🔄 WORKFLOW_STEP | Session: async_11c262c36dbb | Step: memory_storage | Progress: 90% | Message: 💾 Memory storage completed
[2025-09-29 15:44:10] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Mapped step 'memory_storage' to memory_storage | Data: {}
[2025-09-29 15:44:10] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Created progress update: ProgressUpdate(step=<WorkflowStep.MEMORY_STORAGE: 'memory_storage'>, progress=90, message='💾 Memory storage completed', timestamp='2025-09-29T10:14:10.972504', details={'node': 'memory_storage'}, tool_results=None) | Data: {}
[2025-09-29 15:44:10] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔥 BROADCASTING update for session async_11c262c36dbb | Data: {'step': 'memory_storage', 'progress': 90, 'message': '💾 Memory storage completed', 'has_clients': False, 'client_count': 0, 'broadcaster_id': 2522352460880, 'all_sessions': []}
[2025-09-29 15:44:10] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | No clients found for session async_11c262c36dbb - update buffered (total: 12) | Data: {}
[2025-09-29 15:44:10] [INFO] [progress_debug] 📡 PROGRESS_BROADCAST | ✅ SUCCESS | Session: async_11c262c36dbb | Step: memory_storage | Details: {'workflow_step': 'memory_storage', 'progress': 90, 'message': '💾 Memory storage completed'}
[2025-09-29 15:44:10] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔥 BROADCASTING update for session async_11c262c36dbb | Data: {'step': 'complete', 'progress': 100, 'message': '✅ AI analysis workflow completed successfully!', 'has_clients': False, 'client_count': 0, 'broadcaster_id': 2522352460880, 'all_sessions': []}
[2025-09-29 15:44:10] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | No clients found for session async_11c262c36dbb - update buffered (total: 13) | Data: {}
[2025-09-29 15:44:10] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔥 BROADCASTING update for session async_11c262c36dbb | Data: {'step': 'complete', 'progress': 100, 'message': '✅ AI analysis workflow completed successfully!', 'has_clients': False, 'client_count': 0, 'broadcaster_id': 2522352460880, 'all_sessions': []}
[2025-09-29 15:44:11] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | No clients found for session async_11c262c36dbb - update buffered (total: 14) | Data: {}
[2025-09-29 15:44:11] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔥 BROADCASTING update for session async_11c262c36dbb | Data: {'step': 'complete', 'progress': 100, 'message': '🎉 Analysis completed successfully!', 'has_clients': False, 'client_count': 0, 'broadcaster_id': 2522352460880, 'all_sessions': []}
[2025-09-29 15:44:11] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | No clients found for session async_11c262c36dbb - update buffered (total: 15) | Data: {}
[2025-09-29 15:44:11] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Marked session async_11c262c36dbb as complete | Data: {}
[2025-09-29 15:44:11] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Cleaned up buffered updates for completed session async_11c262c36dbb | Data: {}
[2025-09-29 15:57:16] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔥 BROADCASTING update for session async_0a3a571ab098 | Data: {'step': 'chart_upload', 'progress': 5, 'message': 'Analysis request received, starting background processing...', 'has_clients': False, 'client_count': 0, 'broadcaster_id': 1758144277264, 'all_sessions': []}
[2025-09-29 15:57:16] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | No clients found for session async_0a3a571ab098 - update buffered (total: 1) | Data: {}
[2025-09-29 15:57:16] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔥 BROADCASTING update for session async_0a3a571ab098 | Data: {'step': 'chart_upload', 'progress': 5, 'message': '📊 Processing uploaded chart images...', 'has_clients': False, 'client_count': 0, 'broadcaster_id': 1758144277264, 'all_sessions': []}
[2025-09-29 15:57:16] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | No clients found for session async_0a3a571ab098 - update buffered (total: 2) | Data: {}
[2025-09-29 15:57:16] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔥 BROADCASTING update for session async_0a3a571ab098 | Data: {'step': 'chart_upload', 'progress': 5, 'message': '🖼️ Processed chart image 1/1 (3KB)', 'has_clients': False, 'client_count': 0, 'broadcaster_id': 1758144277264, 'all_sessions': []}
[2025-09-29 15:57:16] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | No clients found for session async_0a3a571ab098 - update buffered (total: 3) | Data: {}
[2025-09-29 15:57:17] [INFO] [progress_debug] 🔄 SESSION_LIFECYCLE | Session: async_0a3a571ab098 | Event: workflow_start | Details: {'analysis_mode': 'positional', 'market_specialization': 'Crypto', 'has_progress_broadcaster': True, 'chart_images_count': 1, 'parameter_passing_issue': False}
[2025-09-29 15:57:17] [INFO] [progress_debug] 🔄 WORKFLOW_STEP | Session: async_0a3a571ab098 | Step: chart_analysis | Progress: 20% | Message: 📊 Chart analysis completed
[2025-09-29 15:57:17] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Mapped step 'chart_analysis' to chart_analysis | Data: {}
[2025-09-29 15:57:17] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Created progress update: ProgressUpdate(step=<WorkflowStep.CHART_ANALYSIS: 'chart_analysis'>, progress=20, message='📊 Chart analysis completed', timestamp='2025-09-29T10:27:17.644932', details={'node': 'chart_analysis'}, tool_results=None) | Data: {}
[2025-09-29 15:57:17] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔥 BROADCASTING update for session async_0a3a571ab098 | Data: {'step': 'chart_analysis', 'progress': 20, 'message': '📊 Chart analysis completed', 'has_clients': False, 'client_count': 0, 'broadcaster_id': 1758144277264, 'all_sessions': []}
[2025-09-29 15:57:17] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | No clients found for session async_0a3a571ab098 - update buffered (total: 4) | Data: {}
[2025-09-29 15:57:17] [INFO] [progress_debug] 📡 PROGRESS_BROADCAST | ✅ SUCCESS | Session: async_0a3a571ab098 | Step: chart_analysis | Details: {'workflow_step': 'chart_analysis', 'progress': 20, 'message': '📊 Chart analysis completed'}
[2025-09-29 15:57:18] [INFO] [progress_debug] 🔗 SSE_CONNECTION | Session: async_0a3a571ab098 | Client: {'user_id': UUID('ee801bc0-845a-4e55-952f-b648b2c61a93'), 'user_email': '<EMAIL>', 'endpoint': '/api/v1/progress/stream/async_0a3a571ab098'}
[2025-09-29 15:57:18] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Adding client for session async_0a3a571ab098 | Data: {}
[2025-09-29 15:57:18] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | ✅ CLIENT ADDED for session async_0a3a571ab098 | Data: {'total_clients': 1, 'all_sessions': ['async_0a3a571ab098'], 'broadcaster_id': 1758144277264}
[2025-09-29 15:57:18] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Current broadcaster state | Data: {'session_id': 'async_0a3a571ab098', 'clients_dict': {'async_0a3a571ab098': 1}, 'total_sessions': 1}
[2025-09-29 15:57:18] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔄 REPLAYING 4 buffered updates to new client for session async_0a3a571ab098 | Data: {}
[2025-09-29 15:57:18] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | ✅ Replayed buffered update 1/4: chart_upload (5%) | Data: {}
[2025-09-29 15:57:18] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | ✅ Replayed buffered update 2/4: chart_upload (5%) | Data: {}
[2025-09-29 15:57:18] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | ✅ Replayed buffered update 3/4: chart_upload (5%) | Data: {}
[2025-09-29 15:57:18] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | ✅ Replayed buffered update 4/4: chart_analysis (20%) | Data: {}
[2025-09-29 15:57:18] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🎯 Successfully replayed 4 buffered updates for session async_0a3a571ab098 | Data: {}
[2025-09-29 15:57:18] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Client queue created for session async_0a3a571ab098 | Data: {}
[2025-09-29 15:57:18] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending connection confirmation: {'type': 'connected', 'session_id': 'async_0a3a571ab098'} | Data: {}
[2025-09-29 15:57:18] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_0a3a571ab098 | Data: {}
[2025-09-29 15:57:18] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Received progress update: {'step': 'chart_upload', 'progress': 5, 'message': 'Analysis request received, starting background processing...', 'timestamp': '2025-09-29T10:27:16.155705', 'details': {'user_id': 'ee801bc0-845a-4e55-952f-b648b2c61a93', 'analysis_type': 'Positional', 'image_count': 1}, 'tool_results': None} | Data: {}
[2025-09-29 15:57:18] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending SSE event: {'type': 'progress_update', 'data': {'step': 'chart_upload', 'progress': 5, 'message': 'Analysis request received, starting background processing...', 'timestamp': '2025-09-29T10:27:16.155705', 'details': {'user_id': 'ee801bc0-845a-4e55-952f-b648b2c61a93', 'analysis_type': 'Positional', 'image_count': 1}, 'tool_results': None}} | Data: {}
[2025-09-29 15:57:18] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_0a3a571ab098 | Data: {}
[2025-09-29 15:57:18] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Received progress update: {'step': 'chart_upload', 'progress': 5, 'message': '📊 Processing uploaded chart images...', 'timestamp': '2025-09-29T10:27:16.232255', 'details': {'user_id': 'ee801bc0-845a-4e55-952f-b648b2c61a93', 'analysis_type': 'Positional', 'image_count': 1}, 'tool_results': None} | Data: {}
[2025-09-29 15:57:18] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending SSE event: {'type': 'progress_update', 'data': {'step': 'chart_upload', 'progress': 5, 'message': '📊 Processing uploaded chart images...', 'timestamp': '2025-09-29T10:27:16.232255', 'details': {'user_id': 'ee801bc0-845a-4e55-952f-b648b2c61a93', 'analysis_type': 'Positional', 'image_count': 1}, 'tool_results': None}} | Data: {}
[2025-09-29 15:57:18] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_0a3a571ab098 | Data: {}
[2025-09-29 15:57:18] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Received progress update: {'step': 'chart_upload', 'progress': 5, 'message': '🖼️ Processed chart image 1/1 (3KB)', 'timestamp': '2025-09-29T10:27:16.240750', 'details': {'image_size': 4003, 'images_processed': 1}, 'tool_results': None} | Data: {}
[2025-09-29 15:57:18] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending SSE event: {'type': 'progress_update', 'data': {'step': 'chart_upload', 'progress': 5, 'message': '🖼️ Processed chart image 1/1 (3KB)', 'timestamp': '2025-09-29T10:27:16.240750', 'details': {'image_size': 4003, 'images_processed': 1}, 'tool_results': None}} | Data: {}
[2025-09-29 15:57:18] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_0a3a571ab098 | Data: {}
[2025-09-29 15:57:18] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Received progress update: {'step': 'chart_analysis', 'progress': 20, 'message': '📊 Chart analysis completed', 'timestamp': '2025-09-29T10:27:17.644932', 'details': {'node': 'chart_analysis'}, 'tool_results': None} | Data: {}
[2025-09-29 15:57:18] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending SSE event: {'type': 'progress_update', 'data': {'step': 'chart_analysis', 'progress': 20, 'message': '📊 Chart analysis completed', 'timestamp': '2025-09-29T10:27:17.644932', 'details': {'node': 'chart_analysis'}, 'tool_results': None}} | Data: {}
[2025-09-29 15:57:18] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_0a3a571ab098 | Data: {}
[2025-09-29 15:57:19] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 11383.093} | Data: {}
[2025-09-29 15:57:19] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_0a3a571ab098 | Data: {}
[2025-09-29 15:57:20] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 11384.093} | Data: {}
[2025-09-29 15:57:20] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_0a3a571ab098 | Data: {}
[2025-09-29 15:57:21] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 11385.078} | Data: {}
[2025-09-29 15:57:21] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_0a3a571ab098 | Data: {}
[2025-09-29 15:57:22] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 11386.093} | Data: {}
[2025-09-29 15:57:22] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_0a3a571ab098 | Data: {}
[2025-09-29 15:57:23] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 11387.109} | Data: {}
[2025-09-29 15:57:23] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_0a3a571ab098 | Data: {}
[2025-09-29 15:57:24] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 11388.125} | Data: {}
[2025-09-29 15:57:24] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_0a3a571ab098 | Data: {}
[2025-09-29 15:57:25] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 11389.14} | Data: {}
[2025-09-29 15:57:25] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_0a3a571ab098 | Data: {}
[2025-09-29 15:57:26] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 11390.156} | Data: {}
[2025-09-29 15:57:26] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_0a3a571ab098 | Data: {}
[2025-09-29 15:57:27] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 11391.171} | Data: {}
[2025-09-29 15:57:27] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_0a3a571ab098 | Data: {}
[2025-09-29 15:57:28] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 11392.171} | Data: {}
[2025-09-29 15:57:28] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_0a3a571ab098 | Data: {}
[2025-09-29 15:57:29] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 11393.187} | Data: {}
[2025-09-29 15:57:29] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_0a3a571ab098 | Data: {}
[2025-09-29 15:57:30] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 11394.187} | Data: {}
[2025-09-29 15:57:30] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_0a3a571ab098 | Data: {}
[2025-09-29 15:57:31] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 11395.203} | Data: {}
[2025-09-29 15:57:31] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_0a3a571ab098 | Data: {}
[2025-09-29 15:57:32] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 11396.218} | Data: {}
[2025-09-29 15:57:32] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_0a3a571ab098 | Data: {}
[2025-09-29 15:57:33] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 11397.234} | Data: {}
[2025-09-29 15:57:33] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_0a3a571ab098 | Data: {}
[2025-09-29 15:57:34] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 11398.234} | Data: {}
[2025-09-29 15:57:34] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_0a3a571ab098 | Data: {}
[2025-09-29 15:57:35] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 11399.25} | Data: {}
[2025-09-29 15:57:35] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_0a3a571ab098 | Data: {}
[2025-09-29 15:57:36] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 11400.265} | Data: {}
[2025-09-29 15:57:36] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_0a3a571ab098 | Data: {}
[2025-09-29 15:57:40] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 11403.953} | Data: {}
[2025-09-29 15:57:40] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_0a3a571ab098 | Data: {}
[2025-09-29 15:57:47] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 11411.39} | Data: {}
[2025-09-29 15:57:47] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_0a3a571ab098 | Data: {}
[2025-09-29 15:57:47] [INFO] [progress_debug] 🔄 WORKFLOW_STEP | Session: async_0a3a571ab098 | Step: symbol_detection | Progress: 30% | Message: 🔍 Symbol detection completed
[2025-09-29 15:57:47] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Mapped step 'symbol_detection' to symbol_detection | Data: {}
[2025-09-29 15:57:47] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Created progress update: ProgressUpdate(step=<WorkflowStep.SYMBOL_DETECTION: 'symbol_detection'>, progress=40, message='🔍 Symbol detection completed', timestamp='2025-09-29T10:27:47.693703', details={'node': 'symbol_detection'}, tool_results=None) | Data: {}
[2025-09-29 15:57:47] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔥 BROADCASTING update for session async_0a3a571ab098 | Data: {'step': 'symbol_detection', 'progress': 40, 'message': '🔍 Symbol detection completed', 'has_clients': True, 'client_count': 1, 'broadcaster_id': 1758144277264, 'all_sessions': ['async_0a3a571ab098']}
[2025-09-29 15:57:47] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Sent update to client 0 for session async_0a3a571ab098 | Data: {}
[2025-09-29 15:57:47] [INFO] [progress_debug] 📡 PROGRESS_BROADCAST | ✅ SUCCESS | Session: async_0a3a571ab098 | Step: symbol_detection | Details: {'workflow_step': 'symbol_detection', 'progress': 40, 'message': '🔍 Symbol detection completed'}
[2025-09-29 15:57:47] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Received progress update: {'step': 'symbol_detection', 'progress': 40, 'message': '🔍 Symbol detection completed', 'timestamp': '2025-09-29T10:27:47.693703', 'details': {'node': 'symbol_detection'}, 'tool_results': None} | Data: {}
[2025-09-29 15:57:47] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending SSE event: {'type': 'progress_update', 'data': {'step': 'symbol_detection', 'progress': 40, 'message': '🔍 Symbol detection completed', 'timestamp': '2025-09-29T10:27:47.693703', 'details': {'node': 'symbol_detection'}, 'tool_results': None}} | Data: {}
[2025-09-29 15:57:47] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_0a3a571ab098 | Data: {}
[2025-09-29 15:57:47] [INFO] [progress_debug] 🔄 WORKFLOW_STEP | Session: async_0a3a571ab098 | Step: tool_execution | Progress: 50% | Message: 🛠️ Data collection completed
[2025-09-29 15:57:47] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Mapped step 'tool_execution' to tool_execution | Data: {}
[2025-09-29 15:57:47] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Created progress update: ProgressUpdate(step=<WorkflowStep.TOOL_EXECUTION: 'tool_execution'>, progress=50, message='🛠️ Data collection completed', timestamp='2025-09-29T10:27:47.749357', details={'node': 'tool_execution'}, tool_results=None) | Data: {}
[2025-09-29 15:57:47] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔥 BROADCASTING update for session async_0a3a571ab098 | Data: {'step': 'tool_execution', 'progress': 50, 'message': '🛠️ Data collection completed', 'has_clients': True, 'client_count': 1, 'broadcaster_id': 1758144277264, 'all_sessions': ['async_0a3a571ab098']}
[2025-09-29 15:57:47] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Sent update to client 0 for session async_0a3a571ab098 | Data: {}
[2025-09-29 15:57:47] [INFO] [progress_debug] 📡 PROGRESS_BROADCAST | ✅ SUCCESS | Session: async_0a3a571ab098 | Step: tool_execution | Details: {'workflow_step': 'tool_execution', 'progress': 50, 'message': '🛠️ Data collection completed'}
[2025-09-29 15:57:47] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Received progress update: {'step': 'tool_execution', 'progress': 50, 'message': '🛠️ Data collection completed', 'timestamp': '2025-09-29T10:27:47.749357', 'details': {'node': 'tool_execution'}, 'tool_results': None} | Data: {}
[2025-09-29 15:57:47] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending SSE event: {'type': 'progress_update', 'data': {'step': 'tool_execution', 'progress': 50, 'message': '🛠️ Data collection completed', 'timestamp': '2025-09-29T10:27:47.749357', 'details': {'node': 'tool_execution'}, 'tool_results': None}} | Data: {}
[2025-09-29 15:57:47] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_0a3a571ab098 | Data: {}
[2025-09-29 15:57:47] [INFO] [progress_debug] 🔄 WORKFLOW_STEP | Session: async_0a3a571ab098 | Step: tool_summarization | Progress: 60% | Message: 📋 Data processing completed
[2025-09-29 15:57:47] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Mapped step 'tool_summarization' to tool_summarization | Data: {}
[2025-09-29 15:57:47] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Created progress update: ProgressUpdate(step=<WorkflowStep.TOOL_SUMMARIZATION: 'tool_summarization'>, progress=60, message='📋 Data processing completed', timestamp='2025-09-29T10:27:47.813061', details={'node': 'tool_summarization'}, tool_results=None) | Data: {}
[2025-09-29 15:57:47] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔥 BROADCASTING update for session async_0a3a571ab098 | Data: {'step': 'tool_summarization', 'progress': 60, 'message': '📋 Data processing completed', 'has_clients': True, 'client_count': 1, 'broadcaster_id': 1758144277264, 'all_sessions': ['async_0a3a571ab098']}
[2025-09-29 15:57:47] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Sent update to client 0 for session async_0a3a571ab098 | Data: {}
[2025-09-29 15:57:47] [INFO] [progress_debug] 📡 PROGRESS_BROADCAST | ✅ SUCCESS | Session: async_0a3a571ab098 | Step: tool_summarization | Details: {'workflow_step': 'tool_summarization', 'progress': 60, 'message': '📋 Data processing completed'}
[2025-09-29 15:57:47] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Received progress update: {'step': 'tool_summarization', 'progress': 60, 'message': '📋 Data processing completed', 'timestamp': '2025-09-29T10:27:47.813061', 'details': {'node': 'tool_summarization'}, 'tool_results': None} | Data: {}
[2025-09-29 15:57:47] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending SSE event: {'type': 'progress_update', 'data': {'step': 'tool_summarization', 'progress': 60, 'message': '📋 Data processing completed', 'timestamp': '2025-09-29T10:27:47.813061', 'details': {'node': 'tool_summarization'}, 'tool_results': None}} | Data: {}
[2025-09-29 15:57:47] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_0a3a571ab098 | Data: {}
[2025-09-29 15:57:47] [INFO] [progress_debug] 🔄 WORKFLOW_STEP | Session: async_0a3a571ab098 | Step: rag_integration | Progress: 70% | Message: 🧠 RAG integration completed
[2025-09-29 15:57:47] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Mapped step 'rag_integration' to rag_integration | Data: {}
[2025-09-29 15:57:47] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Created progress update: ProgressUpdate(step=<WorkflowStep.RAG_INTEGRATION: 'rag_integration'>, progress=70, message='🧠 RAG integration completed', timestamp='2025-09-29T10:27:47.864064', details={'node': 'rag_integration'}, tool_results=None) | Data: {}
[2025-09-29 15:57:47] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔥 BROADCASTING update for session async_0a3a571ab098 | Data: {'step': 'rag_integration', 'progress': 70, 'message': '🧠 RAG integration completed', 'has_clients': True, 'client_count': 1, 'broadcaster_id': 1758144277264, 'all_sessions': ['async_0a3a571ab098']}
[2025-09-29 15:57:47] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Sent update to client 0 for session async_0a3a571ab098 | Data: {}
[2025-09-29 15:57:47] [INFO] [progress_debug] 📡 PROGRESS_BROADCAST | ✅ SUCCESS | Session: async_0a3a571ab098 | Step: rag_integration | Details: {'workflow_step': 'rag_integration', 'progress': 70, 'message': '🧠 RAG integration completed'}
[2025-09-29 15:57:47] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Received progress update: {'step': 'rag_integration', 'progress': 70, 'message': '🧠 RAG integration completed', 'timestamp': '2025-09-29T10:27:47.864064', 'details': {'node': 'rag_integration'}, 'tool_results': None} | Data: {}
[2025-09-29 15:57:47] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending SSE event: {'type': 'progress_update', 'data': {'step': 'rag_integration', 'progress': 70, 'message': '🧠 RAG integration completed', 'timestamp': '2025-09-29T10:27:47.864064', 'details': {'node': 'rag_integration'}, 'tool_results': None}} | Data: {}
[2025-09-29 15:57:47] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_0a3a571ab098 | Data: {}
[2025-09-29 15:57:51] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 11415.531} | Data: {}
[2025-09-29 15:57:51] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_0a3a571ab098 | Data: {}
[2025-09-29 15:57:51] [INFO] [progress_debug] 🔄 WORKFLOW_STEP | Session: async_0a3a571ab098 | Step: final_analysis | Progress: 80% | Message: 🎯 AI analysis completed
[2025-09-29 15:57:51] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Mapped step 'final_analysis' to final_analysis | Data: {}
[2025-09-29 15:57:51] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Created progress update: ProgressUpdate(step=<WorkflowStep.FINAL_ANALYSIS: 'final_analysis'>, progress=80, message='🎯 AI analysis completed', timestamp='2025-09-29T10:27:51.821014', details={'node': 'final_analysis'}, tool_results=None) | Data: {}
[2025-09-29 15:57:51] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔥 BROADCASTING update for session async_0a3a571ab098 | Data: {'step': 'final_analysis', 'progress': 80, 'message': '🎯 AI analysis completed', 'has_clients': True, 'client_count': 1, 'broadcaster_id': 1758144277264, 'all_sessions': ['async_0a3a571ab098']}
[2025-09-29 15:57:51] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Sent update to client 0 for session async_0a3a571ab098 | Data: {}
[2025-09-29 15:57:51] [INFO] [progress_debug] 📡 PROGRESS_BROADCAST | ✅ SUCCESS | Session: async_0a3a571ab098 | Step: final_analysis | Details: {'workflow_step': 'final_analysis', 'progress': 80, 'message': '🎯 AI analysis completed'}
[2025-09-29 15:57:51] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Received progress update: {'step': 'final_analysis', 'progress': 80, 'message': '🎯 AI analysis completed', 'timestamp': '2025-09-29T10:27:51.821014', 'details': {'node': 'final_analysis'}, 'tool_results': None} | Data: {}
[2025-09-29 15:57:51] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending SSE event: {'type': 'progress_update', 'data': {'step': 'final_analysis', 'progress': 80, 'message': '🎯 AI analysis completed', 'timestamp': '2025-09-29T10:27:51.821014', 'details': {'node': 'final_analysis'}, 'tool_results': None}} | Data: {}
[2025-09-29 15:57:51] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_0a3a571ab098 | Data: {}
[2025-09-29 15:57:51] [INFO] [progress_debug] 🔄 WORKFLOW_STEP | Session: async_0a3a571ab098 | Step: memory_storage | Progress: 90% | Message: 💾 Storing analysis for future reference...
[2025-09-29 15:57:51] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Mapped step 'memory_storage' to memory_storage | Data: {}
[2025-09-29 15:57:51] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Created progress update: ProgressUpdate(step=<WorkflowStep.MEMORY_STORAGE: 'memory_storage'>, progress=90, message='💾 Storing analysis for future reference...', timestamp='2025-09-29T10:27:51.872291', details={'step': 'memory_storage'}, tool_results=None) | Data: {}
[2025-09-29 15:57:51] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔥 BROADCASTING update for session async_0a3a571ab098 | Data: {'step': 'memory_storage', 'progress': 90, 'message': '💾 Storing analysis for future reference...', 'has_clients': True, 'client_count': 1, 'broadcaster_id': 1758144277264, 'all_sessions': ['async_0a3a571ab098']}
[2025-09-29 15:57:51] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Sent update to client 0 for session async_0a3a571ab098 | Data: {}
[2025-09-29 15:57:51] [INFO] [progress_debug] 📡 PROGRESS_BROADCAST | ✅ SUCCESS | Session: async_0a3a571ab098 | Step: memory_storage | Details: {'workflow_step': 'memory_storage', 'progress': 90, 'message': '💾 Storing analysis for future reference...'}
[2025-09-29 15:57:51] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Received progress update: {'step': 'memory_storage', 'progress': 90, 'message': '💾 Storing analysis for future reference...', 'timestamp': '2025-09-29T10:27:51.872291', 'details': {'step': 'memory_storage'}, 'tool_results': None} | Data: {}
[2025-09-29 15:57:51] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending SSE event: {'type': 'progress_update', 'data': {'step': 'memory_storage', 'progress': 90, 'message': '💾 Storing analysis for future reference...', 'timestamp': '2025-09-29T10:27:51.872291', 'details': {'step': 'memory_storage'}, 'tool_results': None}} | Data: {}
[2025-09-29 15:57:51] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_0a3a571ab098 | Data: {}
[2025-09-29 15:57:51] [INFO] [progress_debug] 🔄 WORKFLOW_STEP | Session: async_0a3a571ab098 | Step: complete | Progress: 100% | Message: 🎉 Analysis completed successfully!
[2025-09-29 15:57:51] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Mapped step 'complete' to complete | Data: {}
[2025-09-29 15:57:51] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Created progress update: ProgressUpdate(step=<WorkflowStep.COMPLETE: 'complete'>, progress=100, message='🎉 Analysis completed successfully!', timestamp='2025-09-29T10:27:51.951190', details={'step': 'complete', 'workflow_status': 'complete'}, tool_results=None) | Data: {}
[2025-09-29 15:57:51] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔥 BROADCASTING update for session async_0a3a571ab098 | Data: {'step': 'complete', 'progress': 100, 'message': '🎉 Analysis completed successfully!', 'has_clients': True, 'client_count': 1, 'broadcaster_id': 1758144277264, 'all_sessions': ['async_0a3a571ab098']}
[2025-09-29 15:57:51] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Sent update to client 0 for session async_0a3a571ab098 | Data: {}
[2025-09-29 15:57:51] [INFO] [progress_debug] 📡 PROGRESS_BROADCAST | ✅ SUCCESS | Session: async_0a3a571ab098 | Step: complete | Details: {'workflow_step': 'complete', 'progress': 100, 'message': '🎉 Analysis completed successfully!'}
[2025-09-29 15:57:51] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Received progress update: {'step': 'complete', 'progress': 100, 'message': '🎉 Analysis completed successfully!', 'timestamp': '2025-09-29T10:27:51.951190', 'details': {'step': 'complete', 'workflow_status': 'complete'}, 'tool_results': None} | Data: {}
[2025-09-29 15:57:51] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending SSE event: {'type': 'progress_update', 'data': {'step': 'complete', 'progress': 100, 'message': '🎉 Analysis completed successfully!', 'timestamp': '2025-09-29T10:27:51.951190', 'details': {'step': 'complete', 'workflow_status': 'complete'}, 'tool_results': None}} | Data: {}
[2025-09-29 15:57:51] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Workflow complete, sending: {'type': 'complete', 'session_id': 'async_0a3a571ab098'} | Data: {}
[2025-09-29 15:57:52] [INFO] [progress_debug] 🔌 SSE_DISCONNECT | Session: async_0a3a571ab098 | Reason: event_generator_cleanup
[2025-09-29 15:57:52] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Removed client for session async_0a3a571ab098 | Data: {'remaining_clients': 0}
[2025-09-29 15:57:52] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Cleaned up empty client list for session async_0a3a571ab098 | Data: {}
[2025-09-29 15:57:52] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Keeping buffered updates for potential reconnection: async_0a3a571ab098 | Data: {}
[2025-09-29 15:57:52] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Buffered updates count: 11 | Data: {}
[2025-09-29 15:57:52] [INFO] [progress_debug] 🔄 WORKFLOW_STEP | Session: async_0a3a571ab098 | Step: memory_storage | Progress: 90% | Message: 💾 Memory storage completed
[2025-09-29 15:57:52] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Mapped step 'memory_storage' to memory_storage | Data: {}
[2025-09-29 15:57:52] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Created progress update: ProgressUpdate(step=<WorkflowStep.MEMORY_STORAGE: 'memory_storage'>, progress=90, message='💾 Memory storage completed', timestamp='2025-09-29T10:27:52.084896', details={'node': 'memory_storage'}, tool_results=None) | Data: {}
[2025-09-29 15:57:52] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔥 BROADCASTING update for session async_0a3a571ab098 | Data: {'step': 'memory_storage', 'progress': 90, 'message': '💾 Memory storage completed', 'has_clients': False, 'client_count': 0, 'broadcaster_id': 1758144277264, 'all_sessions': []}
[2025-09-29 15:57:52] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | No clients found for session async_0a3a571ab098 - update buffered (total: 12) | Data: {}
[2025-09-29 15:57:52] [INFO] [progress_debug] 📡 PROGRESS_BROADCAST | ✅ SUCCESS | Session: async_0a3a571ab098 | Step: memory_storage | Details: {'workflow_step': 'memory_storage', 'progress': 90, 'message': '💾 Memory storage completed'}
[2025-09-29 15:57:52] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔥 BROADCASTING update for session async_0a3a571ab098 | Data: {'step': 'complete', 'progress': 100, 'message': '✅ AI analysis workflow completed successfully!', 'has_clients': False, 'client_count': 0, 'broadcaster_id': 1758144277264, 'all_sessions': []}
[2025-09-29 15:57:52] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | No clients found for session async_0a3a571ab098 - update buffered (total: 13) | Data: {}
[2025-09-29 15:57:52] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔥 BROADCASTING update for session async_0a3a571ab098 | Data: {'step': 'complete', 'progress': 100, 'message': '✅ AI analysis workflow completed successfully!', 'has_clients': False, 'client_count': 0, 'broadcaster_id': 1758144277264, 'all_sessions': []}
[2025-09-29 15:57:52] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | No clients found for session async_0a3a571ab098 - update buffered (total: 14) | Data: {}
[2025-09-29 15:58:01] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔥 BROADCASTING update for session async_0a3a571ab098 | Data: {'step': 'complete', 'progress': 100, 'message': '🎉 Analysis completed successfully!', 'has_clients': False, 'client_count': 0, 'broadcaster_id': 1758144277264, 'all_sessions': []}
[2025-09-29 15:58:01] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | No clients found for session async_0a3a571ab098 - update buffered (total: 15) | Data: {}
[2025-09-29 15:58:01] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Marked session async_0a3a571ab098 as complete | Data: {}
[2025-09-29 15:58:01] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Cleaned up buffered updates for completed session async_0a3a571ab098 | Data: {}
[2025-09-29 15:58:48] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔥 BROADCASTING update for session async_56ff7d1c2fe1 | Data: {'step': 'chart_upload', 'progress': 5, 'message': 'Analysis request received, starting background processing...', 'has_clients': False, 'client_count': 0, 'broadcaster_id': 1758144277264, 'all_sessions': []}
[2025-09-29 15:58:48] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | No clients found for session async_56ff7d1c2fe1 - update buffered (total: 1) | Data: {}
[2025-09-29 15:58:48] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔥 BROADCASTING update for session async_56ff7d1c2fe1 | Data: {'step': 'chart_upload', 'progress': 5, 'message': '📊 Processing uploaded chart images...', 'has_clients': False, 'client_count': 0, 'broadcaster_id': 1758144277264, 'all_sessions': []}
[2025-09-29 15:58:48] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | No clients found for session async_56ff7d1c2fe1 - update buffered (total: 2) | Data: {}
[2025-09-29 15:58:48] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔥 BROADCASTING update for session async_56ff7d1c2fe1 | Data: {'step': 'chart_upload', 'progress': 5, 'message': '🖼️ Processed chart image 1/1 (75KB)', 'has_clients': False, 'client_count': 0, 'broadcaster_id': 1758144277264, 'all_sessions': []}
[2025-09-29 15:58:48] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | No clients found for session async_56ff7d1c2fe1 - update buffered (total: 3) | Data: {}
[2025-09-29 15:58:48] [INFO] [progress_debug] 🔗 SSE_CONNECTION | Session: async_56ff7d1c2fe1 | Client: {'user_id': UUID('9fbc7a39-ae9c-455a-ad81-53ed905e004b'), 'user_email': '<EMAIL>', 'endpoint': '/api/v1/progress/stream/async_56ff7d1c2fe1'}
[2025-09-29 15:58:48] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Adding client for session async_56ff7d1c2fe1 | Data: {}
[2025-09-29 15:58:48] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | ✅ CLIENT ADDED for session async_56ff7d1c2fe1 | Data: {'total_clients': 1, 'all_sessions': ['async_56ff7d1c2fe1'], 'broadcaster_id': 1758144277264}
[2025-09-29 15:58:48] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Current broadcaster state | Data: {'session_id': 'async_56ff7d1c2fe1', 'clients_dict': {'async_56ff7d1c2fe1': 1}, 'total_sessions': 1}
[2025-09-29 15:58:48] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔄 REPLAYING 3 buffered updates to new client for session async_56ff7d1c2fe1 | Data: {}
[2025-09-29 15:58:48] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | ✅ Replayed buffered update 1/3: chart_upload (5%) | Data: {}
[2025-09-29 15:58:48] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | ✅ Replayed buffered update 2/3: chart_upload (5%) | Data: {}
[2025-09-29 15:58:48] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | ✅ Replayed buffered update 3/3: chart_upload (5%) | Data: {}
[2025-09-29 15:58:48] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🎯 Successfully replayed 3 buffered updates for session async_56ff7d1c2fe1 | Data: {}
[2025-09-29 15:58:48] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Client queue created for session async_56ff7d1c2fe1 | Data: {}
[2025-09-29 15:58:48] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending connection confirmation: {'type': 'connected', 'session_id': 'async_56ff7d1c2fe1'} | Data: {}
[2025-09-29 15:58:48] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_56ff7d1c2fe1 | Data: {}
[2025-09-29 15:58:48] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Received progress update: {'step': 'chart_upload', 'progress': 5, 'message': 'Analysis request received, starting background processing...', 'timestamp': '2025-09-29T10:28:48.186702', 'details': {'user_id': '9fbc7a39-ae9c-455a-ad81-53ed905e004b', 'analysis_type': 'Positional', 'image_count': 1}, 'tool_results': None} | Data: {}
[2025-09-29 15:58:48] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending SSE event: {'type': 'progress_update', 'data': {'step': 'chart_upload', 'progress': 5, 'message': 'Analysis request received, starting background processing...', 'timestamp': '2025-09-29T10:28:48.186702', 'details': {'user_id': '9fbc7a39-ae9c-455a-ad81-53ed905e004b', 'analysis_type': 'Positional', 'image_count': 1}, 'tool_results': None}} | Data: {}
[2025-09-29 15:58:48] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_56ff7d1c2fe1 | Data: {}
[2025-09-29 15:58:48] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Received progress update: {'step': 'chart_upload', 'progress': 5, 'message': '📊 Processing uploaded chart images...', 'timestamp': '2025-09-29T10:28:48.256893', 'details': {'user_id': '9fbc7a39-ae9c-455a-ad81-53ed905e004b', 'analysis_type': 'Positional', 'image_count': 1}, 'tool_results': None} | Data: {}
[2025-09-29 15:58:48] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending SSE event: {'type': 'progress_update', 'data': {'step': 'chart_upload', 'progress': 5, 'message': '📊 Processing uploaded chart images...', 'timestamp': '2025-09-29T10:28:48.256893', 'details': {'user_id': '9fbc7a39-ae9c-455a-ad81-53ed905e004b', 'analysis_type': 'Positional', 'image_count': 1}, 'tool_results': None}} | Data: {}
[2025-09-29 15:58:48] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_56ff7d1c2fe1 | Data: {}
[2025-09-29 15:58:48] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Received progress update: {'step': 'chart_upload', 'progress': 5, 'message': '🖼️ Processed chart image 1/1 (75KB)', 'timestamp': '2025-09-29T10:28:48.265886', 'details': {'image_size': 77391, 'images_processed': 1}, 'tool_results': None} | Data: {}
[2025-09-29 15:58:48] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending SSE event: {'type': 'progress_update', 'data': {'step': 'chart_upload', 'progress': 5, 'message': '🖼️ Processed chart image 1/1 (75KB)', 'timestamp': '2025-09-29T10:28:48.265886', 'details': {'image_size': 77391, 'images_processed': 1}, 'tool_results': None}} | Data: {}
[2025-09-29 15:58:48] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_56ff7d1c2fe1 | Data: {}
[2025-09-29 15:58:49] [INFO] [progress_debug] 🔄 SESSION_LIFECYCLE | Session: async_56ff7d1c2fe1 | Event: workflow_start | Details: {'analysis_mode': 'positional', 'market_specialization': 'Crypto', 'has_progress_broadcaster': True, 'chart_images_count': 1, 'parameter_passing_issue': False}
[2025-09-29 15:58:49] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 11473.328} | Data: {}
[2025-09-29 15:58:49] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_56ff7d1c2fe1 | Data: {}
[2025-09-29 15:58:49] [INFO] [progress_debug] 🔄 WORKFLOW_STEP | Session: async_56ff7d1c2fe1 | Step: chart_analysis | Progress: 20% | Message: 📊 Chart analysis completed
[2025-09-29 15:58:49] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Mapped step 'chart_analysis' to chart_analysis | Data: {}
[2025-09-29 15:58:49] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Created progress update: ProgressUpdate(step=<WorkflowStep.CHART_ANALYSIS: 'chart_analysis'>, progress=20, message='📊 Chart analysis completed', timestamp='2025-09-29T10:28:49.644380', details={'node': 'chart_analysis'}, tool_results=None) | Data: {}
[2025-09-29 15:58:49] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔥 BROADCASTING update for session async_56ff7d1c2fe1 | Data: {'step': 'chart_analysis', 'progress': 20, 'message': '📊 Chart analysis completed', 'has_clients': True, 'client_count': 1, 'broadcaster_id': 1758144277264, 'all_sessions': ['async_56ff7d1c2fe1']}
[2025-09-29 15:58:49] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Sent update to client 0 for session async_56ff7d1c2fe1 | Data: {}
[2025-09-29 15:58:49] [INFO] [progress_debug] 📡 PROGRESS_BROADCAST | ✅ SUCCESS | Session: async_56ff7d1c2fe1 | Step: chart_analysis | Details: {'workflow_step': 'chart_analysis', 'progress': 20, 'message': '📊 Chart analysis completed'}
[2025-09-29 15:58:49] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Received progress update: {'step': 'chart_analysis', 'progress': 20, 'message': '📊 Chart analysis completed', 'timestamp': '2025-09-29T10:28:49.644380', 'details': {'node': 'chart_analysis'}, 'tool_results': None} | Data: {}
[2025-09-29 15:58:49] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending SSE event: {'type': 'progress_update', 'data': {'step': 'chart_analysis', 'progress': 20, 'message': '📊 Chart analysis completed', 'timestamp': '2025-09-29T10:28:49.644380', 'details': {'node': 'chart_analysis'}, 'tool_results': None}} | Data: {}
[2025-09-29 15:58:49] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_56ff7d1c2fe1 | Data: {}
[2025-09-29 15:58:50] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 11474.437} | Data: {}
[2025-09-29 15:58:50] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_56ff7d1c2fe1 | Data: {}
[2025-09-29 15:58:51] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 11475.453} | Data: {}
[2025-09-29 15:58:51] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_56ff7d1c2fe1 | Data: {}
[2025-09-29 15:58:52] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 11476.468} | Data: {}
[2025-09-29 15:58:52] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_56ff7d1c2fe1 | Data: {}
[2025-09-29 15:58:53] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 11477.484} | Data: {}
[2025-09-29 15:58:53] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_56ff7d1c2fe1 | Data: {}
[2025-09-29 15:58:54] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 11478.5} | Data: {}
[2025-09-29 15:58:54] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_56ff7d1c2fe1 | Data: {}
[2025-09-29 15:58:55] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 11479.5} | Data: {}
[2025-09-29 15:58:55] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_56ff7d1c2fe1 | Data: {}
[2025-09-29 15:58:56] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 11480.5} | Data: {}
[2025-09-29 15:58:56] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_56ff7d1c2fe1 | Data: {}
[2025-09-29 15:58:57] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 11481.531} | Data: {}
[2025-09-29 15:58:57] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_56ff7d1c2fe1 | Data: {}
[2025-09-29 15:58:58] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 11482.531} | Data: {}
[2025-09-29 15:58:58] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_56ff7d1c2fe1 | Data: {}
[2025-09-29 15:58:59] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 11483.546} | Data: {}
[2025-09-29 15:58:59] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_56ff7d1c2fe1 | Data: {}
[2025-09-29 15:59:00] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 11484.562} | Data: {}
[2025-09-29 15:59:00] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_56ff7d1c2fe1 | Data: {}
[2025-09-29 15:59:01] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 11485.562} | Data: {}
[2025-09-29 15:59:01] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_56ff7d1c2fe1 | Data: {}
[2025-09-29 15:59:02] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 11486.578} | Data: {}
[2025-09-29 15:59:02] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_56ff7d1c2fe1 | Data: {}
[2025-09-29 15:59:03] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 11487.578} | Data: {}
[2025-09-29 15:59:03] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_56ff7d1c2fe1 | Data: {}
[2025-09-29 15:59:04] [INFO] [progress_debug] 🔄 WORKFLOW_STEP | Session: async_56ff7d1c2fe1 | Step: symbol_detection | Progress: 30% | Message: 🔍 Symbol detection completed
[2025-09-29 15:59:04] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Mapped step 'symbol_detection' to symbol_detection | Data: {}
[2025-09-29 15:59:04] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Created progress update: ProgressUpdate(step=<WorkflowStep.SYMBOL_DETECTION: 'symbol_detection'>, progress=40, message='🔍 Symbol detection completed', timestamp='2025-09-29T10:29:04.176547', details={'node': 'symbol_detection'}, tool_results=None) | Data: {}
[2025-09-29 15:59:04] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔥 BROADCASTING update for session async_56ff7d1c2fe1 | Data: {'step': 'symbol_detection', 'progress': 40, 'message': '🔍 Symbol detection completed', 'has_clients': True, 'client_count': 1, 'broadcaster_id': 1758144277264, 'all_sessions': ['async_56ff7d1c2fe1']}
[2025-09-29 15:59:04] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Sent update to client 0 for session async_56ff7d1c2fe1 | Data: {}
[2025-09-29 15:59:04] [INFO] [progress_debug] 📡 PROGRESS_BROADCAST | ✅ SUCCESS | Session: async_56ff7d1c2fe1 | Step: symbol_detection | Details: {'workflow_step': 'symbol_detection', 'progress': 40, 'message': '🔍 Symbol detection completed'}
[2025-09-29 15:59:04] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Received progress update: {'step': 'symbol_detection', 'progress': 40, 'message': '🔍 Symbol detection completed', 'timestamp': '2025-09-29T10:29:04.176547', 'details': {'node': 'symbol_detection'}, 'tool_results': None} | Data: {}
[2025-09-29 15:59:04] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending SSE event: {'type': 'progress_update', 'data': {'step': 'symbol_detection', 'progress': 40, 'message': '🔍 Symbol detection completed', 'timestamp': '2025-09-29T10:29:04.176547', 'details': {'node': 'symbol_detection'}, 'tool_results': None}} | Data: {}
[2025-09-29 15:59:04] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_56ff7d1c2fe1 | Data: {}
[2025-09-29 15:59:04] [INFO] [progress_debug] 🔄 WORKFLOW_STEP | Session: async_56ff7d1c2fe1 | Step: tool_execution | Progress: 50% | Message: 🛠️ Data collection completed
[2025-09-29 15:59:04] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Mapped step 'tool_execution' to tool_execution | Data: {}
[2025-09-29 15:59:04] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Created progress update: ProgressUpdate(step=<WorkflowStep.TOOL_EXECUTION: 'tool_execution'>, progress=50, message='🛠️ Data collection completed', timestamp='2025-09-29T10:29:04.193113', details={'node': 'tool_execution'}, tool_results=None) | Data: {}
[2025-09-29 15:59:04] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔥 BROADCASTING update for session async_56ff7d1c2fe1 | Data: {'step': 'tool_execution', 'progress': 50, 'message': '🛠️ Data collection completed', 'has_clients': True, 'client_count': 1, 'broadcaster_id': 1758144277264, 'all_sessions': ['async_56ff7d1c2fe1']}
[2025-09-29 15:59:04] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Sent update to client 0 for session async_56ff7d1c2fe1 | Data: {}
[2025-09-29 15:59:04] [INFO] [progress_debug] 📡 PROGRESS_BROADCAST | ✅ SUCCESS | Session: async_56ff7d1c2fe1 | Step: tool_execution | Details: {'workflow_step': 'tool_execution', 'progress': 50, 'message': '🛠️ Data collection completed'}
[2025-09-29 15:59:04] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Received progress update: {'step': 'tool_execution', 'progress': 50, 'message': '🛠️ Data collection completed', 'timestamp': '2025-09-29T10:29:04.193113', 'details': {'node': 'tool_execution'}, 'tool_results': None} | Data: {}
[2025-09-29 15:59:04] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending SSE event: {'type': 'progress_update', 'data': {'step': 'tool_execution', 'progress': 50, 'message': '🛠️ Data collection completed', 'timestamp': '2025-09-29T10:29:04.193113', 'details': {'node': 'tool_execution'}, 'tool_results': None}} | Data: {}
[2025-09-29 15:59:04] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_56ff7d1c2fe1 | Data: {}
[2025-09-29 15:59:04] [INFO] [progress_debug] 🔄 WORKFLOW_STEP | Session: async_56ff7d1c2fe1 | Step: tool_summarization | Progress: 60% | Message: 📋 Data processing completed
[2025-09-29 15:59:04] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Mapped step 'tool_summarization' to tool_summarization | Data: {}
[2025-09-29 15:59:04] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Created progress update: ProgressUpdate(step=<WorkflowStep.TOOL_SUMMARIZATION: 'tool_summarization'>, progress=60, message='📋 Data processing completed', timestamp='2025-09-29T10:29:04.208679', details={'node': 'tool_summarization'}, tool_results=None) | Data: {}
[2025-09-29 15:59:04] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔥 BROADCASTING update for session async_56ff7d1c2fe1 | Data: {'step': 'tool_summarization', 'progress': 60, 'message': '📋 Data processing completed', 'has_clients': True, 'client_count': 1, 'broadcaster_id': 1758144277264, 'all_sessions': ['async_56ff7d1c2fe1']}
[2025-09-29 15:59:04] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Sent update to client 0 for session async_56ff7d1c2fe1 | Data: {}
[2025-09-29 15:59:04] [INFO] [progress_debug] 📡 PROGRESS_BROADCAST | ✅ SUCCESS | Session: async_56ff7d1c2fe1 | Step: tool_summarization | Details: {'workflow_step': 'tool_summarization', 'progress': 60, 'message': '📋 Data processing completed'}
[2025-09-29 15:59:04] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Received progress update: {'step': 'tool_summarization', 'progress': 60, 'message': '📋 Data processing completed', 'timestamp': '2025-09-29T10:29:04.208679', 'details': {'node': 'tool_summarization'}, 'tool_results': None} | Data: {}
[2025-09-29 15:59:04] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending SSE event: {'type': 'progress_update', 'data': {'step': 'tool_summarization', 'progress': 60, 'message': '📋 Data processing completed', 'timestamp': '2025-09-29T10:29:04.208679', 'details': {'node': 'tool_summarization'}, 'tool_results': None}} | Data: {}
[2025-09-29 15:59:04] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_56ff7d1c2fe1 | Data: {}
[2025-09-29 15:59:04] [INFO] [progress_debug] 🔄 WORKFLOW_STEP | Session: async_56ff7d1c2fe1 | Step: rag_integration | Progress: 70% | Message: 🧠 RAG integration completed
[2025-09-29 15:59:04] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Mapped step 'rag_integration' to rag_integration | Data: {}
[2025-09-29 15:59:04] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Created progress update: ProgressUpdate(step=<WorkflowStep.RAG_INTEGRATION: 'rag_integration'>, progress=70, message='🧠 RAG integration completed', timestamp='2025-09-29T10:29:04.223570', details={'node': 'rag_integration'}, tool_results=None) | Data: {}
[2025-09-29 15:59:04] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔥 BROADCASTING update for session async_56ff7d1c2fe1 | Data: {'step': 'rag_integration', 'progress': 70, 'message': '🧠 RAG integration completed', 'has_clients': True, 'client_count': 1, 'broadcaster_id': 1758144277264, 'all_sessions': ['async_56ff7d1c2fe1']}
[2025-09-29 15:59:04] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Sent update to client 0 for session async_56ff7d1c2fe1 | Data: {}
[2025-09-29 15:59:04] [INFO] [progress_debug] 📡 PROGRESS_BROADCAST | ✅ SUCCESS | Session: async_56ff7d1c2fe1 | Step: rag_integration | Details: {'workflow_step': 'rag_integration', 'progress': 70, 'message': '🧠 RAG integration completed'}
[2025-09-29 15:59:04] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Received progress update: {'step': 'rag_integration', 'progress': 70, 'message': '🧠 RAG integration completed', 'timestamp': '2025-09-29T10:29:04.223570', 'details': {'node': 'rag_integration'}, 'tool_results': None} | Data: {}
[2025-09-29 15:59:04] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending SSE event: {'type': 'progress_update', 'data': {'step': 'rag_integration', 'progress': 70, 'message': '🧠 RAG integration completed', 'timestamp': '2025-09-29T10:29:04.223570', 'details': {'node': 'rag_integration'}, 'tool_results': None}} | Data: {}
[2025-09-29 15:59:04] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_56ff7d1c2fe1 | Data: {}
[2025-09-29 15:59:04] [INFO] [progress_debug] 🔄 WORKFLOW_STEP | Session: async_56ff7d1c2fe1 | Step: final_analysis | Progress: 80% | Message: 🎯 AI analysis completed
[2025-09-29 15:59:04] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Mapped step 'final_analysis' to final_analysis | Data: {}
[2025-09-29 15:59:04] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Created progress update: ProgressUpdate(step=<WorkflowStep.FINAL_ANALYSIS: 'final_analysis'>, progress=80, message='🎯 AI analysis completed', timestamp='2025-09-29T10:29:04.238711', details={'node': 'final_analysis'}, tool_results=None) | Data: {}
[2025-09-29 15:59:04] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔥 BROADCASTING update for session async_56ff7d1c2fe1 | Data: {'step': 'final_analysis', 'progress': 80, 'message': '🎯 AI analysis completed', 'has_clients': True, 'client_count': 1, 'broadcaster_id': 1758144277264, 'all_sessions': ['async_56ff7d1c2fe1']}
[2025-09-29 15:59:04] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Sent update to client 0 for session async_56ff7d1c2fe1 | Data: {}
[2025-09-29 15:59:04] [INFO] [progress_debug] 📡 PROGRESS_BROADCAST | ✅ SUCCESS | Session: async_56ff7d1c2fe1 | Step: final_analysis | Details: {'workflow_step': 'final_analysis', 'progress': 80, 'message': '🎯 AI analysis completed'}
[2025-09-29 15:59:04] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Received progress update: {'step': 'final_analysis', 'progress': 80, 'message': '🎯 AI analysis completed', 'timestamp': '2025-09-29T10:29:04.238711', 'details': {'node': 'final_analysis'}, 'tool_results': None} | Data: {}
[2025-09-29 15:59:04] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending SSE event: {'type': 'progress_update', 'data': {'step': 'final_analysis', 'progress': 80, 'message': '🎯 AI analysis completed', 'timestamp': '2025-09-29T10:29:04.238711', 'details': {'node': 'final_analysis'}, 'tool_results': None}} | Data: {}
[2025-09-29 15:59:04] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_56ff7d1c2fe1 | Data: {}
[2025-09-29 15:59:04] [INFO] [progress_debug] 🔄 WORKFLOW_STEP | Session: async_56ff7d1c2fe1 | Step: memory_storage | Progress: 90% | Message: 💾 Storing analysis for future reference...
[2025-09-29 15:59:04] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Mapped step 'memory_storage' to memory_storage | Data: {}
[2025-09-29 15:59:04] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Created progress update: ProgressUpdate(step=<WorkflowStep.MEMORY_STORAGE: 'memory_storage'>, progress=90, message='💾 Storing analysis for future reference...', timestamp='2025-09-29T10:29:04.255228', details={'step': 'memory_storage'}, tool_results=None) | Data: {}
[2025-09-29 15:59:04] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔥 BROADCASTING update for session async_56ff7d1c2fe1 | Data: {'step': 'memory_storage', 'progress': 90, 'message': '💾 Storing analysis for future reference...', 'has_clients': True, 'client_count': 1, 'broadcaster_id': 1758144277264, 'all_sessions': ['async_56ff7d1c2fe1']}
[2025-09-29 15:59:04] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Sent update to client 0 for session async_56ff7d1c2fe1 | Data: {}
[2025-09-29 15:59:04] [INFO] [progress_debug] 📡 PROGRESS_BROADCAST | ✅ SUCCESS | Session: async_56ff7d1c2fe1 | Step: memory_storage | Details: {'workflow_step': 'memory_storage', 'progress': 90, 'message': '💾 Storing analysis for future reference...'}
[2025-09-29 15:59:04] [INFO] [progress_debug] 🔄 WORKFLOW_STEP | Session: async_56ff7d1c2fe1 | Step: complete | Progress: 100% | Message: 🎉 Analysis completed successfully!
[2025-09-29 15:59:04] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Mapped step 'complete' to complete | Data: {}
[2025-09-29 15:59:04] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Created progress update: ProgressUpdate(step=<WorkflowStep.COMPLETE: 'complete'>, progress=100, message='🎉 Analysis completed successfully!', timestamp='2025-09-29T10:29:04.265227', details={'step': 'complete', 'workflow_status': 'complete'}, tool_results=None) | Data: {}
[2025-09-29 15:59:04] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔥 BROADCASTING update for session async_56ff7d1c2fe1 | Data: {'step': 'complete', 'progress': 100, 'message': '🎉 Analysis completed successfully!', 'has_clients': True, 'client_count': 1, 'broadcaster_id': 1758144277264, 'all_sessions': ['async_56ff7d1c2fe1']}
[2025-09-29 15:59:04] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Sent update to client 0 for session async_56ff7d1c2fe1 | Data: {}
[2025-09-29 15:59:04] [INFO] [progress_debug] 📡 PROGRESS_BROADCAST | ✅ SUCCESS | Session: async_56ff7d1c2fe1 | Step: complete | Details: {'workflow_step': 'complete', 'progress': 100, 'message': '🎉 Analysis completed successfully!'}
[2025-09-29 15:59:04] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Received progress update: {'step': 'memory_storage', 'progress': 90, 'message': '💾 Storing analysis for future reference...', 'timestamp': '2025-09-29T10:29:04.255228', 'details': {'step': 'memory_storage'}, 'tool_results': None} | Data: {}
[2025-09-29 15:59:04] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending SSE event: {'type': 'progress_update', 'data': {'step': 'memory_storage', 'progress': 90, 'message': '💾 Storing analysis for future reference...', 'timestamp': '2025-09-29T10:29:04.255228', 'details': {'step': 'memory_storage'}, 'tool_results': None}} | Data: {}
[2025-09-29 15:59:04] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_56ff7d1c2fe1 | Data: {}
[2025-09-29 15:59:04] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Received progress update: {'step': 'complete', 'progress': 100, 'message': '🎉 Analysis completed successfully!', 'timestamp': '2025-09-29T10:29:04.265227', 'details': {'step': 'complete', 'workflow_status': 'complete'}, 'tool_results': None} | Data: {}
[2025-09-29 15:59:04] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending SSE event: {'type': 'progress_update', 'data': {'step': 'complete', 'progress': 100, 'message': '🎉 Analysis completed successfully!', 'timestamp': '2025-09-29T10:29:04.265227', 'details': {'step': 'complete', 'workflow_status': 'complete'}, 'tool_results': None}} | Data: {}
[2025-09-29 15:59:04] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Workflow complete, sending: {'type': 'complete', 'session_id': 'async_56ff7d1c2fe1'} | Data: {}
[2025-09-29 15:59:04] [INFO] [progress_debug] 🔌 SSE_DISCONNECT | Session: async_56ff7d1c2fe1 | Reason: event_generator_cleanup
[2025-09-29 15:59:04] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Removed client for session async_56ff7d1c2fe1 | Data: {'remaining_clients': 0}
[2025-09-29 15:59:04] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Cleaned up empty client list for session async_56ff7d1c2fe1 | Data: {}
[2025-09-29 15:59:04] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Keeping buffered updates for potential reconnection: async_56ff7d1c2fe1 | Data: {}
[2025-09-29 15:59:04] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Buffered updates count: 11 | Data: {}
[2025-09-29 15:59:04] [INFO] [progress_debug] 🔄 WORKFLOW_STEP | Session: async_56ff7d1c2fe1 | Step: memory_storage | Progress: 90% | Message: 💾 Memory storage completed
[2025-09-29 15:59:04] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Mapped step 'memory_storage' to memory_storage | Data: {}
[2025-09-29 15:59:04] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Created progress update: ProgressUpdate(step=<WorkflowStep.MEMORY_STORAGE: 'memory_storage'>, progress=90, message='💾 Memory storage completed', timestamp='2025-09-29T10:29:04.279020', details={'node': 'memory_storage'}, tool_results=None) | Data: {}
[2025-09-29 15:59:04] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔥 BROADCASTING update for session async_56ff7d1c2fe1 | Data: {'step': 'memory_storage', 'progress': 90, 'message': '💾 Memory storage completed', 'has_clients': False, 'client_count': 0, 'broadcaster_id': 1758144277264, 'all_sessions': []}
[2025-09-29 15:59:04] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | No clients found for session async_56ff7d1c2fe1 - update buffered (total: 12) | Data: {}
[2025-09-29 15:59:04] [INFO] [progress_debug] 📡 PROGRESS_BROADCAST | ✅ SUCCESS | Session: async_56ff7d1c2fe1 | Step: memory_storage | Details: {'workflow_step': 'memory_storage', 'progress': 90, 'message': '💾 Memory storage completed'}
[2025-09-29 15:59:04] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔥 BROADCASTING update for session async_56ff7d1c2fe1 | Data: {'step': 'complete', 'progress': 100, 'message': '✅ AI analysis workflow completed successfully!', 'has_clients': False, 'client_count': 0, 'broadcaster_id': 1758144277264, 'all_sessions': []}
[2025-09-29 15:59:04] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | No clients found for session async_56ff7d1c2fe1 - update buffered (total: 13) | Data: {}
[2025-09-29 15:59:04] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔥 BROADCASTING update for session async_56ff7d1c2fe1 | Data: {'step': 'complete', 'progress': 100, 'message': '✅ AI analysis workflow completed successfully!', 'has_clients': False, 'client_count': 0, 'broadcaster_id': 1758144277264, 'all_sessions': []}
[2025-09-29 15:59:04] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | No clients found for session async_56ff7d1c2fe1 - update buffered (total: 14) | Data: {}
[2025-09-29 15:59:04] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔥 BROADCASTING update for session async_56ff7d1c2fe1 | Data: {'step': 'complete', 'progress': 100, 'message': '🎉 Analysis completed successfully!', 'has_clients': False, 'client_count': 0, 'broadcaster_id': 1758144277264, 'all_sessions': []}
[2025-09-29 15:59:04] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | No clients found for session async_56ff7d1c2fe1 - update buffered (total: 15) | Data: {}
[2025-09-29 15:59:04] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Marked session async_56ff7d1c2fe1 as complete | Data: {}
[2025-09-29 15:59:04] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Cleaned up buffered updates for completed session async_56ff7d1c2fe1 | Data: {}
[2025-09-29 16:11:25] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔥 BROADCASTING update for session async_c50e7081c22c | Data: {'step': 'chart_upload', 'progress': 5, 'message': 'Analysis request received, starting background processing...', 'has_clients': False, 'client_count': 0, 'broadcaster_id': 1758144277264, 'all_sessions': []}
[2025-09-29 16:11:25] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | No clients found for session async_c50e7081c22c - update buffered (total: 1) | Data: {}
[2025-09-29 16:11:25] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔥 BROADCASTING update for session async_c50e7081c22c | Data: {'step': 'chart_upload', 'progress': 5, 'message': '📊 Processing uploaded chart images...', 'has_clients': False, 'client_count': 0, 'broadcaster_id': 1758144277264, 'all_sessions': []}
[2025-09-29 16:11:25] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | No clients found for session async_c50e7081c22c - update buffered (total: 2) | Data: {}
[2025-09-29 16:11:25] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔥 BROADCASTING update for session async_c50e7081c22c | Data: {'step': 'chart_upload', 'progress': 5, 'message': '🖼️ Processed chart image 1/1 (75KB)', 'has_clients': False, 'client_count': 0, 'broadcaster_id': 1758144277264, 'all_sessions': []}
[2025-09-29 16:11:25] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | No clients found for session async_c50e7081c22c - update buffered (total: 3) | Data: {}
[2025-09-29 16:11:25] [INFO] [progress_debug] 🔗 SSE_CONNECTION | Session: async_c50e7081c22c | Client: {'user_id': UUID('9fbc7a39-ae9c-455a-ad81-53ed905e004b'), 'user_email': '<EMAIL>', 'endpoint': '/api/v1/progress/stream/async_c50e7081c22c'}
[2025-09-29 16:11:25] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Adding client for session async_c50e7081c22c | Data: {}
[2025-09-29 16:11:25] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | ✅ CLIENT ADDED for session async_c50e7081c22c | Data: {'total_clients': 1, 'all_sessions': ['async_c50e7081c22c'], 'broadcaster_id': 1758144277264}
[2025-09-29 16:11:25] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Current broadcaster state | Data: {'session_id': 'async_c50e7081c22c', 'clients_dict': {'async_c50e7081c22c': 1}, 'total_sessions': 1}
[2025-09-29 16:11:25] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔄 REPLAYING 3 buffered updates to new client for session async_c50e7081c22c | Data: {}
[2025-09-29 16:11:25] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | ✅ Replayed buffered update 1/3: chart_upload (5%) | Data: {}
[2025-09-29 16:11:25] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | ✅ Replayed buffered update 2/3: chart_upload (5%) | Data: {}
[2025-09-29 16:11:25] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | ✅ Replayed buffered update 3/3: chart_upload (5%) | Data: {}
[2025-09-29 16:11:25] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🎯 Successfully replayed 3 buffered updates for session async_c50e7081c22c | Data: {}
[2025-09-29 16:11:25] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Client queue created for session async_c50e7081c22c | Data: {}
[2025-09-29 16:11:25] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending connection confirmation: {'type': 'connected', 'session_id': 'async_c50e7081c22c'} | Data: {}
[2025-09-29 16:11:25] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_c50e7081c22c | Data: {}
[2025-09-29 16:11:25] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Received progress update: {'step': 'chart_upload', 'progress': 5, 'message': 'Analysis request received, starting background processing...', 'timestamp': '2025-09-29T10:41:25.217420', 'details': {'user_id': '9fbc7a39-ae9c-455a-ad81-53ed905e004b', 'analysis_type': 'Positional', 'image_count': 1}, 'tool_results': None} | Data: {}
[2025-09-29 16:11:25] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending SSE event: {'type': 'progress_update', 'data': {'step': 'chart_upload', 'progress': 5, 'message': 'Analysis request received, starting background processing...', 'timestamp': '2025-09-29T10:41:25.217420', 'details': {'user_id': '9fbc7a39-ae9c-455a-ad81-53ed905e004b', 'analysis_type': 'Positional', 'image_count': 1}, 'tool_results': None}} | Data: {}
[2025-09-29 16:11:25] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_c50e7081c22c | Data: {}
[2025-09-29 16:11:25] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Received progress update: {'step': 'chart_upload', 'progress': 5, 'message': '📊 Processing uploaded chart images...', 'timestamp': '2025-09-29T10:41:25.320490', 'details': {'user_id': '9fbc7a39-ae9c-455a-ad81-53ed905e004b', 'analysis_type': 'Positional', 'image_count': 1}, 'tool_results': None} | Data: {}
[2025-09-29 16:11:25] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending SSE event: {'type': 'progress_update', 'data': {'step': 'chart_upload', 'progress': 5, 'message': '📊 Processing uploaded chart images...', 'timestamp': '2025-09-29T10:41:25.320490', 'details': {'user_id': '9fbc7a39-ae9c-455a-ad81-53ed905e004b', 'analysis_type': 'Positional', 'image_count': 1}, 'tool_results': None}} | Data: {}
[2025-09-29 16:11:25] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_c50e7081c22c | Data: {}
[2025-09-29 16:11:25] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Received progress update: {'step': 'chart_upload', 'progress': 5, 'message': '🖼️ Processed chart image 1/1 (75KB)', 'timestamp': '2025-09-29T10:41:25.331878', 'details': {'image_size': 77391, 'images_processed': 1}, 'tool_results': None} | Data: {}
[2025-09-29 16:11:25] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending SSE event: {'type': 'progress_update', 'data': {'step': 'chart_upload', 'progress': 5, 'message': '🖼️ Processed chart image 1/1 (75KB)', 'timestamp': '2025-09-29T10:41:25.331878', 'details': {'image_size': 77391, 'images_processed': 1}, 'tool_results': None}} | Data: {}
[2025-09-29 16:11:25] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_c50e7081c22c | Data: {}
[2025-09-29 16:11:26] [INFO] [progress_debug] 🔄 SESSION_LIFECYCLE | Session: async_c50e7081c22c | Event: workflow_start | Details: {'analysis_mode': 'positional', 'market_specialization': 'Crypto', 'has_progress_broadcaster': True, 'chart_images_count': 1, 'parameter_passing_issue': False}
[2025-09-29 16:11:26] [INFO] [progress_debug] 🔄 WORKFLOW_STEP | Session: async_c50e7081c22c | Step: chart_analysis | Progress: 20% | Message: 📊 Chart analysis completed
[2025-09-29 16:11:26] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Mapped step 'chart_analysis' to chart_analysis | Data: {}
[2025-09-29 16:11:26] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Created progress update: ProgressUpdate(step=<WorkflowStep.CHART_ANALYSIS: 'chart_analysis'>, progress=20, message='📊 Chart analysis completed', timestamp='2025-09-29T10:41:26.516468', details={'node': 'chart_analysis'}, tool_results=None) | Data: {}
[2025-09-29 16:11:26] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔥 BROADCASTING update for session async_c50e7081c22c | Data: {'step': 'chart_analysis', 'progress': 20, 'message': '📊 Chart analysis completed', 'has_clients': True, 'client_count': 1, 'broadcaster_id': 1758144277264, 'all_sessions': ['async_c50e7081c22c']}
[2025-09-29 16:11:26] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Sent update to client 0 for session async_c50e7081c22c | Data: {}
[2025-09-29 16:11:26] [INFO] [progress_debug] 📡 PROGRESS_BROADCAST | ✅ SUCCESS | Session: async_c50e7081c22c | Step: chart_analysis | Details: {'workflow_step': 'chart_analysis', 'progress': 20, 'message': '📊 Chart analysis completed'}
[2025-09-29 16:11:26] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Received progress update: {'step': 'chart_analysis', 'progress': 20, 'message': '📊 Chart analysis completed', 'timestamp': '2025-09-29T10:41:26.516468', 'details': {'node': 'chart_analysis'}, 'tool_results': None} | Data: {}
[2025-09-29 16:11:26] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending SSE event: {'type': 'progress_update', 'data': {'step': 'chart_analysis', 'progress': 20, 'message': '📊 Chart analysis completed', 'timestamp': '2025-09-29T10:41:26.516468', 'details': {'node': 'chart_analysis'}, 'tool_results': None}} | Data: {}
[2025-09-29 16:11:26] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_c50e7081c22c | Data: {}
[2025-09-29 16:11:27] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 12231.281} | Data: {}
[2025-09-29 16:11:27] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_c50e7081c22c | Data: {}
[2025-09-29 16:11:28] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 12232.296} | Data: {}
[2025-09-29 16:11:28] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_c50e7081c22c | Data: {}
[2025-09-29 16:11:29] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 12233.312} | Data: {}
[2025-09-29 16:11:29] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_c50e7081c22c | Data: {}
[2025-09-29 16:11:30] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 12234.328} | Data: {}
[2025-09-29 16:11:30] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_c50e7081c22c | Data: {}
[2025-09-29 16:11:31] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 12235.328} | Data: {}
[2025-09-29 16:11:31] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_c50e7081c22c | Data: {}
[2025-09-29 16:11:32] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 12236.343} | Data: {}
[2025-09-29 16:11:32] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_c50e7081c22c | Data: {}
[2025-09-29 16:11:33] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 12237.359} | Data: {}
[2025-09-29 16:11:33] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_c50e7081c22c | Data: {}
[2025-09-29 16:11:34] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 12238.375} | Data: {}
[2025-09-29 16:11:34] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_c50e7081c22c | Data: {}
[2025-09-29 16:11:35] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 12239.375} | Data: {}
[2025-09-29 16:11:35] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_c50e7081c22c | Data: {}
[2025-09-29 16:11:36] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 12240.39} | Data: {}
[2025-09-29 16:11:36] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_c50e7081c22c | Data: {}
[2025-09-29 16:11:37] [INFO] [progress_debug] 🔄 WORKFLOW_STEP | Session: async_c50e7081c22c | Step: symbol_detection | Progress: 30% | Message: 🔍 Symbol detection completed
[2025-09-29 16:11:37] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Mapped step 'symbol_detection' to symbol_detection | Data: {}
[2025-09-29 16:11:37] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Created progress update: ProgressUpdate(step=<WorkflowStep.SYMBOL_DETECTION: 'symbol_detection'>, progress=40, message='🔍 Symbol detection completed', timestamp='2025-09-29T10:41:37.407624', details={'node': 'symbol_detection'}, tool_results=None) | Data: {}
[2025-09-29 16:11:37] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔥 BROADCASTING update for session async_c50e7081c22c | Data: {'step': 'symbol_detection', 'progress': 40, 'message': '🔍 Symbol detection completed', 'has_clients': True, 'client_count': 1, 'broadcaster_id': 1758144277264, 'all_sessions': ['async_c50e7081c22c']}
[2025-09-29 16:11:37] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Sent update to client 0 for session async_c50e7081c22c | Data: {}
[2025-09-29 16:11:37] [INFO] [progress_debug] 📡 PROGRESS_BROADCAST | ✅ SUCCESS | Session: async_c50e7081c22c | Step: symbol_detection | Details: {'workflow_step': 'symbol_detection', 'progress': 40, 'message': '🔍 Symbol detection completed'}
[2025-09-29 16:11:37] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Received progress update: {'step': 'symbol_detection', 'progress': 40, 'message': '🔍 Symbol detection completed', 'timestamp': '2025-09-29T10:41:37.407624', 'details': {'node': 'symbol_detection'}, 'tool_results': None} | Data: {}
[2025-09-29 16:11:37] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending SSE event: {'type': 'progress_update', 'data': {'step': 'symbol_detection', 'progress': 40, 'message': '🔍 Symbol detection completed', 'timestamp': '2025-09-29T10:41:37.407624', 'details': {'node': 'symbol_detection'}, 'tool_results': None}} | Data: {}
[2025-09-29 16:11:37] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_c50e7081c22c | Data: {}
[2025-09-29 16:11:37] [INFO] [progress_debug] 🔄 WORKFLOW_STEP | Session: async_c50e7081c22c | Step: tool_execution | Progress: 50% | Message: 🛠️ Data collection completed
[2025-09-29 16:11:37] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Mapped step 'tool_execution' to tool_execution | Data: {}
[2025-09-29 16:11:37] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Created progress update: ProgressUpdate(step=<WorkflowStep.TOOL_EXECUTION: 'tool_execution'>, progress=50, message='🛠️ Data collection completed', timestamp='2025-09-29T10:41:37.454508', details={'node': 'tool_execution'}, tool_results=None) | Data: {}
[2025-09-29 16:11:37] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔥 BROADCASTING update for session async_c50e7081c22c | Data: {'step': 'tool_execution', 'progress': 50, 'message': '🛠️ Data collection completed', 'has_clients': True, 'client_count': 1, 'broadcaster_id': 1758144277264, 'all_sessions': ['async_c50e7081c22c']}
[2025-09-29 16:11:37] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Sent update to client 0 for session async_c50e7081c22c | Data: {}
[2025-09-29 16:11:37] [INFO] [progress_debug] 📡 PROGRESS_BROADCAST | ✅ SUCCESS | Session: async_c50e7081c22c | Step: tool_execution | Details: {'workflow_step': 'tool_execution', 'progress': 50, 'message': '🛠️ Data collection completed'}
[2025-09-29 16:11:37] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Received progress update: {'step': 'tool_execution', 'progress': 50, 'message': '🛠️ Data collection completed', 'timestamp': '2025-09-29T10:41:37.454508', 'details': {'node': 'tool_execution'}, 'tool_results': None} | Data: {}
[2025-09-29 16:11:37] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending SSE event: {'type': 'progress_update', 'data': {'step': 'tool_execution', 'progress': 50, 'message': '🛠️ Data collection completed', 'timestamp': '2025-09-29T10:41:37.454508', 'details': {'node': 'tool_execution'}, 'tool_results': None}} | Data: {}
[2025-09-29 16:11:37] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_c50e7081c22c | Data: {}
[2025-09-29 16:11:37] [INFO] [progress_debug] 🔄 WORKFLOW_STEP | Session: async_c50e7081c22c | Step: tool_summarization | Progress: 60% | Message: 📋 Data processing completed
[2025-09-29 16:11:37] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Mapped step 'tool_summarization' to tool_summarization | Data: {}
[2025-09-29 16:11:37] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Created progress update: ProgressUpdate(step=<WorkflowStep.TOOL_SUMMARIZATION: 'tool_summarization'>, progress=60, message='📋 Data processing completed', timestamp='2025-09-29T10:41:37.503884', details={'node': 'tool_summarization'}, tool_results=None) | Data: {}
[2025-09-29 16:11:37] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔥 BROADCASTING update for session async_c50e7081c22c | Data: {'step': 'tool_summarization', 'progress': 60, 'message': '📋 Data processing completed', 'has_clients': True, 'client_count': 1, 'broadcaster_id': 1758144277264, 'all_sessions': ['async_c50e7081c22c']}
[2025-09-29 16:11:37] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Sent update to client 0 for session async_c50e7081c22c | Data: {}
[2025-09-29 16:11:37] [INFO] [progress_debug] 📡 PROGRESS_BROADCAST | ✅ SUCCESS | Session: async_c50e7081c22c | Step: tool_summarization | Details: {'workflow_step': 'tool_summarization', 'progress': 60, 'message': '📋 Data processing completed'}
[2025-09-29 16:11:37] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Received progress update: {'step': 'tool_summarization', 'progress': 60, 'message': '📋 Data processing completed', 'timestamp': '2025-09-29T10:41:37.503884', 'details': {'node': 'tool_summarization'}, 'tool_results': None} | Data: {}
[2025-09-29 16:11:37] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending SSE event: {'type': 'progress_update', 'data': {'step': 'tool_summarization', 'progress': 60, 'message': '📋 Data processing completed', 'timestamp': '2025-09-29T10:41:37.503884', 'details': {'node': 'tool_summarization'}, 'tool_results': None}} | Data: {}
[2025-09-29 16:11:37] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_c50e7081c22c | Data: {}
[2025-09-29 16:11:37] [INFO] [progress_debug] 🔄 WORKFLOW_STEP | Session: async_c50e7081c22c | Step: rag_integration | Progress: 70% | Message: 🧠 RAG integration completed
[2025-09-29 16:11:37] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Mapped step 'rag_integration' to rag_integration | Data: {}
[2025-09-29 16:11:37] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Created progress update: ProgressUpdate(step=<WorkflowStep.RAG_INTEGRATION: 'rag_integration'>, progress=70, message='🧠 RAG integration completed', timestamp='2025-09-29T10:41:37.540865', details={'node': 'rag_integration'}, tool_results=None) | Data: {}
[2025-09-29 16:11:37] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔥 BROADCASTING update for session async_c50e7081c22c | Data: {'step': 'rag_integration', 'progress': 70, 'message': '🧠 RAG integration completed', 'has_clients': True, 'client_count': 1, 'broadcaster_id': 1758144277264, 'all_sessions': ['async_c50e7081c22c']}
[2025-09-29 16:11:37] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Sent update to client 0 for session async_c50e7081c22c | Data: {}
[2025-09-29 16:11:37] [INFO] [progress_debug] 📡 PROGRESS_BROADCAST | ✅ SUCCESS | Session: async_c50e7081c22c | Step: rag_integration | Details: {'workflow_step': 'rag_integration', 'progress': 70, 'message': '🧠 RAG integration completed'}
[2025-09-29 16:11:37] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Received progress update: {'step': 'rag_integration', 'progress': 70, 'message': '🧠 RAG integration completed', 'timestamp': '2025-09-29T10:41:37.540865', 'details': {'node': 'rag_integration'}, 'tool_results': None} | Data: {}
[2025-09-29 16:11:37] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending SSE event: {'type': 'progress_update', 'data': {'step': 'rag_integration', 'progress': 70, 'message': '🧠 RAG integration completed', 'timestamp': '2025-09-29T10:41:37.540865', 'details': {'node': 'rag_integration'}, 'tool_results': None}} | Data: {}
[2025-09-29 16:11:37] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_c50e7081c22c | Data: {}
[2025-09-29 16:11:37] [INFO] [progress_debug] 🔄 WORKFLOW_STEP | Session: async_c50e7081c22c | Step: final_analysis | Progress: 80% | Message: 🎯 AI analysis completed
[2025-09-29 16:11:37] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Mapped step 'final_analysis' to final_analysis | Data: {}
[2025-09-29 16:11:37] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Created progress update: ProgressUpdate(step=<WorkflowStep.FINAL_ANALYSIS: 'final_analysis'>, progress=80, message='🎯 AI analysis completed', timestamp='2025-09-29T10:41:37.590458', details={'node': 'final_analysis'}, tool_results=None) | Data: {}
[2025-09-29 16:11:37] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔥 BROADCASTING update for session async_c50e7081c22c | Data: {'step': 'final_analysis', 'progress': 80, 'message': '🎯 AI analysis completed', 'has_clients': True, 'client_count': 1, 'broadcaster_id': 1758144277264, 'all_sessions': ['async_c50e7081c22c']}
[2025-09-29 16:11:37] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Sent update to client 0 for session async_c50e7081c22c | Data: {}
[2025-09-29 16:11:37] [INFO] [progress_debug] 📡 PROGRESS_BROADCAST | ✅ SUCCESS | Session: async_c50e7081c22c | Step: final_analysis | Details: {'workflow_step': 'final_analysis', 'progress': 80, 'message': '🎯 AI analysis completed'}
[2025-09-29 16:11:37] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Received progress update: {'step': 'final_analysis', 'progress': 80, 'message': '🎯 AI analysis completed', 'timestamp': '2025-09-29T10:41:37.590458', 'details': {'node': 'final_analysis'}, 'tool_results': None} | Data: {}
[2025-09-29 16:11:37] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending SSE event: {'type': 'progress_update', 'data': {'step': 'final_analysis', 'progress': 80, 'message': '🎯 AI analysis completed', 'timestamp': '2025-09-29T10:41:37.590458', 'details': {'node': 'final_analysis'}, 'tool_results': None}} | Data: {}
[2025-09-29 16:11:37] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_c50e7081c22c | Data: {}
[2025-09-29 16:11:37] [INFO] [progress_debug] 🔄 WORKFLOW_STEP | Session: async_c50e7081c22c | Step: memory_storage | Progress: 90% | Message: 💾 Storing analysis for future reference...
[2025-09-29 16:11:37] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Mapped step 'memory_storage' to memory_storage | Data: {}
[2025-09-29 16:11:37] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Created progress update: ProgressUpdate(step=<WorkflowStep.MEMORY_STORAGE: 'memory_storage'>, progress=90, message='💾 Storing analysis for future reference...', timestamp='2025-09-29T10:41:37.626447', details={'step': 'memory_storage'}, tool_results=None) | Data: {}
[2025-09-29 16:11:37] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔥 BROADCASTING update for session async_c50e7081c22c | Data: {'step': 'memory_storage', 'progress': 90, 'message': '💾 Storing analysis for future reference...', 'has_clients': True, 'client_count': 1, 'broadcaster_id': 1758144277264, 'all_sessions': ['async_c50e7081c22c']}
[2025-09-29 16:11:37] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Sent update to client 0 for session async_c50e7081c22c | Data: {}
[2025-09-29 16:11:37] [INFO] [progress_debug] 📡 PROGRESS_BROADCAST | ✅ SUCCESS | Session: async_c50e7081c22c | Step: memory_storage | Details: {'workflow_step': 'memory_storage', 'progress': 90, 'message': '💾 Storing analysis for future reference...'}
[2025-09-29 16:11:37] [INFO] [progress_debug] 🔄 WORKFLOW_STEP | Session: async_c50e7081c22c | Step: complete | Progress: 100% | Message: 🎉 Analysis completed successfully!
[2025-09-29 16:11:37] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Mapped step 'complete' to complete | Data: {}
[2025-09-29 16:11:37] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Created progress update: ProgressUpdate(step=<WorkflowStep.COMPLETE: 'complete'>, progress=100, message='🎉 Analysis completed successfully!', timestamp='2025-09-29T10:41:37.654449', details={'step': 'complete', 'workflow_status': 'complete'}, tool_results=None) | Data: {}
[2025-09-29 16:11:37] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔥 BROADCASTING update for session async_c50e7081c22c | Data: {'step': 'complete', 'progress': 100, 'message': '🎉 Analysis completed successfully!', 'has_clients': True, 'client_count': 1, 'broadcaster_id': 1758144277264, 'all_sessions': ['async_c50e7081c22c']}
[2025-09-29 16:11:37] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Sent update to client 0 for session async_c50e7081c22c | Data: {}
[2025-09-29 16:11:37] [INFO] [progress_debug] 📡 PROGRESS_BROADCAST | ✅ SUCCESS | Session: async_c50e7081c22c | Step: complete | Details: {'workflow_step': 'complete', 'progress': 100, 'message': '🎉 Analysis completed successfully!'}
[2025-09-29 16:11:37] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Received progress update: {'step': 'memory_storage', 'progress': 90, 'message': '💾 Storing analysis for future reference...', 'timestamp': '2025-09-29T10:41:37.626447', 'details': {'step': 'memory_storage'}, 'tool_results': None} | Data: {}
[2025-09-29 16:11:37] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending SSE event: {'type': 'progress_update', 'data': {'step': 'memory_storage', 'progress': 90, 'message': '💾 Storing analysis for future reference...', 'timestamp': '2025-09-29T10:41:37.626447', 'details': {'step': 'memory_storage'}, 'tool_results': None}} | Data: {}
[2025-09-29 16:11:37] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_c50e7081c22c | Data: {}
[2025-09-29 16:11:37] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Received progress update: {'step': 'complete', 'progress': 100, 'message': '🎉 Analysis completed successfully!', 'timestamp': '2025-09-29T10:41:37.654449', 'details': {'step': 'complete', 'workflow_status': 'complete'}, 'tool_results': None} | Data: {}
[2025-09-29 16:11:37] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending SSE event: {'type': 'progress_update', 'data': {'step': 'complete', 'progress': 100, 'message': '🎉 Analysis completed successfully!', 'timestamp': '2025-09-29T10:41:37.654449', 'details': {'step': 'complete', 'workflow_status': 'complete'}, 'tool_results': None}} | Data: {}
[2025-09-29 16:11:37] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Workflow complete, sending: {'type': 'complete', 'session_id': 'async_c50e7081c22c'} | Data: {}
[2025-09-29 16:11:37] [INFO] [progress_debug] 🔌 SSE_DISCONNECT | Session: async_c50e7081c22c | Reason: event_generator_cleanup
[2025-09-29 16:11:37] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Removed client for session async_c50e7081c22c | Data: {'remaining_clients': 0}
[2025-09-29 16:11:37] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Cleaned up empty client list for session async_c50e7081c22c | Data: {}
[2025-09-29 16:11:37] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Keeping buffered updates for potential reconnection: async_c50e7081c22c | Data: {}
[2025-09-29 16:11:37] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Buffered updates count: 11 | Data: {}
[2025-09-29 16:11:37] [INFO] [progress_debug] 🔄 WORKFLOW_STEP | Session: async_c50e7081c22c | Step: memory_storage | Progress: 90% | Message: 💾 Memory storage completed
[2025-09-29 16:11:37] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Mapped step 'memory_storage' to memory_storage | Data: {}
[2025-09-29 16:11:37] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Created progress update: ProgressUpdate(step=<WorkflowStep.MEMORY_STORAGE: 'memory_storage'>, progress=90, message='💾 Memory storage completed', timestamp='2025-09-29T10:41:37.702051', details={'node': 'memory_storage'}, tool_results=None) | Data: {}
[2025-09-29 16:11:37] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔥 BROADCASTING update for session async_c50e7081c22c | Data: {'step': 'memory_storage', 'progress': 90, 'message': '💾 Memory storage completed', 'has_clients': False, 'client_count': 0, 'broadcaster_id': 1758144277264, 'all_sessions': []}
[2025-09-29 16:11:37] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | No clients found for session async_c50e7081c22c - update buffered (total: 12) | Data: {}
[2025-09-29 16:11:37] [INFO] [progress_debug] 📡 PROGRESS_BROADCAST | ✅ SUCCESS | Session: async_c50e7081c22c | Step: memory_storage | Details: {'workflow_step': 'memory_storage', 'progress': 90, 'message': '💾 Memory storage completed'}
[2025-09-29 16:11:37] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔥 BROADCASTING update for session async_c50e7081c22c | Data: {'step': 'complete', 'progress': 100, 'message': '✅ AI analysis workflow completed successfully!', 'has_clients': False, 'client_count': 0, 'broadcaster_id': 1758144277264, 'all_sessions': []}
[2025-09-29 16:11:37] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | No clients found for session async_c50e7081c22c - update buffered (total: 13) | Data: {}
[2025-09-29 16:11:37] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔥 BROADCASTING update for session async_c50e7081c22c | Data: {'step': 'complete', 'progress': 100, 'message': '✅ AI analysis workflow completed successfully!', 'has_clients': False, 'client_count': 0, 'broadcaster_id': 1758144277264, 'all_sessions': []}
[2025-09-29 16:11:37] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | No clients found for session async_c50e7081c22c - update buffered (total: 14) | Data: {}
[2025-09-29 16:11:38] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔥 BROADCASTING update for session async_c50e7081c22c | Data: {'step': 'complete', 'progress': 100, 'message': '🎉 Analysis completed successfully!', 'has_clients': False, 'client_count': 0, 'broadcaster_id': 1758144277264, 'all_sessions': []}
[2025-09-29 16:11:38] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | No clients found for session async_c50e7081c22c - update buffered (total: 15) | Data: {}
[2025-09-29 16:11:38] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Marked session async_c50e7081c22c as complete | Data: {}
[2025-09-29 16:11:38] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Cleaned up buffered updates for completed session async_c50e7081c22c | Data: {}
[2025-09-29 16:29:37] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔥 BROADCASTING update for session async_4f169b5f15ab | Data: {'step': 'chart_upload', 'progress': 5, 'message': 'Analysis request received, starting background processing...', 'has_clients': False, 'client_count': 0, 'broadcaster_id': 1789722241552, 'all_sessions': []}
[2025-09-29 16:29:37] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | No clients found for session async_4f169b5f15ab - update buffered (total: 1) | Data: {}
[2025-09-29 16:29:37] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔥 BROADCASTING update for session async_4f169b5f15ab | Data: {'step': 'chart_upload', 'progress': 5, 'message': '📊 Processing uploaded chart images...', 'has_clients': False, 'client_count': 0, 'broadcaster_id': 1789722241552, 'all_sessions': []}
[2025-09-29 16:29:37] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | No clients found for session async_4f169b5f15ab - update buffered (total: 2) | Data: {}
[2025-09-29 16:29:37] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔥 BROADCASTING update for session async_4f169b5f15ab | Data: {'step': 'chart_upload', 'progress': 5, 'message': '🖼️ Processed chart image 1/1 (75KB)', 'has_clients': False, 'client_count': 0, 'broadcaster_id': 1789722241552, 'all_sessions': []}
[2025-09-29 16:29:37] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | No clients found for session async_4f169b5f15ab - update buffered (total: 3) | Data: {}
[2025-09-29 16:29:37] [INFO] [progress_debug] 🔗 SSE_CONNECTION | Session: async_4f169b5f15ab | Client: {'user_id': UUID('9fbc7a39-ae9c-455a-ad81-53ed905e004b'), 'user_email': '<EMAIL>', 'endpoint': '/api/v1/progress/stream/async_4f169b5f15ab'}
[2025-09-29 16:29:37] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Adding client for session async_4f169b5f15ab | Data: {}
[2025-09-29 16:29:37] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | ✅ CLIENT ADDED for session async_4f169b5f15ab | Data: {'total_clients': 1, 'all_sessions': ['async_4f169b5f15ab'], 'broadcaster_id': 1789722241552}
[2025-09-29 16:29:37] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Current broadcaster state | Data: {'session_id': 'async_4f169b5f15ab', 'clients_dict': {'async_4f169b5f15ab': 1}, 'total_sessions': 1}
[2025-09-29 16:29:37] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔄 REPLAYING 3 buffered updates to new client for session async_4f169b5f15ab | Data: {}
[2025-09-29 16:29:37] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | ✅ Replayed buffered update 1/3: chart_upload (5%) | Data: {}
[2025-09-29 16:29:37] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | ✅ Replayed buffered update 2/3: chart_upload (5%) | Data: {}
[2025-09-29 16:29:37] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | ✅ Replayed buffered update 3/3: chart_upload (5%) | Data: {}
[2025-09-29 16:29:37] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🎯 Successfully replayed 3 buffered updates for session async_4f169b5f15ab | Data: {}
[2025-09-29 16:29:37] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Client queue created for session async_4f169b5f15ab | Data: {}
[2025-09-29 16:29:37] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending connection confirmation: {'type': 'connected', 'session_id': 'async_4f169b5f15ab'} | Data: {}
[2025-09-29 16:29:37] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_4f169b5f15ab | Data: {}
[2025-09-29 16:29:37] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Received progress update: {'step': 'chart_upload', 'progress': 5, 'message': 'Analysis request received, starting background processing...', 'timestamp': '2025-09-29T10:59:37.253781', 'details': {'user_id': '9fbc7a39-ae9c-455a-ad81-53ed905e004b', 'analysis_type': 'Positional', 'image_count': 1}, 'tool_results': None} | Data: {}
[2025-09-29 16:29:37] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending SSE event: {'type': 'progress_update', 'data': {'step': 'chart_upload', 'progress': 5, 'message': 'Analysis request received, starting background processing...', 'timestamp': '2025-09-29T10:59:37.253781', 'details': {'user_id': '9fbc7a39-ae9c-455a-ad81-53ed905e004b', 'analysis_type': 'Positional', 'image_count': 1}, 'tool_results': None}} | Data: {}
[2025-09-29 16:29:37] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_4f169b5f15ab | Data: {}
[2025-09-29 16:29:37] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Received progress update: {'step': 'chart_upload', 'progress': 5, 'message': '📊 Processing uploaded chart images...', 'timestamp': '2025-09-29T10:59:37.433209', 'details': {'user_id': '9fbc7a39-ae9c-455a-ad81-53ed905e004b', 'analysis_type': 'Positional', 'image_count': 1}, 'tool_results': None} | Data: {}
[2025-09-29 16:29:37] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending SSE event: {'type': 'progress_update', 'data': {'step': 'chart_upload', 'progress': 5, 'message': '📊 Processing uploaded chart images...', 'timestamp': '2025-09-29T10:59:37.433209', 'details': {'user_id': '9fbc7a39-ae9c-455a-ad81-53ed905e004b', 'analysis_type': 'Positional', 'image_count': 1}, 'tool_results': None}} | Data: {}
[2025-09-29 16:29:37] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_4f169b5f15ab | Data: {}
[2025-09-29 16:29:37] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Received progress update: {'step': 'chart_upload', 'progress': 5, 'message': '🖼️ Processed chart image 1/1 (75KB)', 'timestamp': '2025-09-29T10:59:37.439713', 'details': {'image_size': 77391, 'images_processed': 1}, 'tool_results': None} | Data: {}
[2025-09-29 16:29:37] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending SSE event: {'type': 'progress_update', 'data': {'step': 'chart_upload', 'progress': 5, 'message': '🖼️ Processed chart image 1/1 (75KB)', 'timestamp': '2025-09-29T10:59:37.439713', 'details': {'image_size': 77391, 'images_processed': 1}, 'tool_results': None}} | Data: {}
[2025-09-29 16:29:37] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_4f169b5f15ab | Data: {}
[2025-09-29 16:29:39] [INFO] [progress_debug] 🔄 SESSION_LIFECYCLE | Session: async_4f169b5f15ab | Event: workflow_start | Details: {'analysis_mode': 'positional', 'market_specialization': 'Crypto', 'has_progress_broadcaster': True, 'chart_images_count': 1, 'parameter_passing_issue': False}
[2025-09-29 16:29:39] [INFO] [progress_debug] 🔄 WORKFLOW_STEP | Session: async_4f169b5f15ab | Step: chart_analysis | Progress: 20% | Message: 📊 Chart analysis completed
[2025-09-29 16:29:39] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Mapped step 'chart_analysis' to chart_analysis | Data: {}
[2025-09-29 16:29:39] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Created progress update: ProgressUpdate(step=<WorkflowStep.CHART_ANALYSIS: 'chart_analysis'>, progress=20, message='📊 Chart analysis completed', timestamp='2025-09-29T10:59:39.258880', details={'node': 'chart_analysis'}, tool_results=None) | Data: {}
[2025-09-29 16:29:39] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔥 BROADCASTING update for session async_4f169b5f15ab | Data: {'step': 'chart_analysis', 'progress': 20, 'message': '📊 Chart analysis completed', 'has_clients': True, 'client_count': 1, 'broadcaster_id': 1789722241552, 'all_sessions': ['async_4f169b5f15ab']}
[2025-09-29 16:29:39] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Sent update to client 0 for session async_4f169b5f15ab | Data: {}
[2025-09-29 16:29:39] [INFO] [progress_debug] 📡 PROGRESS_BROADCAST | ✅ SUCCESS | Session: async_4f169b5f15ab | Step: chart_analysis | Details: {'workflow_step': 'chart_analysis', 'progress': 20, 'message': '📊 Chart analysis completed'}
[2025-09-29 16:29:39] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Received progress update: {'step': 'chart_analysis', 'progress': 20, 'message': '📊 Chart analysis completed', 'timestamp': '2025-09-29T10:59:39.258880', 'details': {'node': 'chart_analysis'}, 'tool_results': None} | Data: {}
[2025-09-29 16:29:39] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending SSE event: {'type': 'progress_update', 'data': {'step': 'chart_analysis', 'progress': 20, 'message': '📊 Chart analysis completed', 'timestamp': '2025-09-29T10:59:39.258880', 'details': {'node': 'chart_analysis'}, 'tool_results': None}} | Data: {}
[2025-09-29 16:29:39] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_4f169b5f15ab | Data: {}
[2025-09-29 16:29:41] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 13325.046} | Data: {}
[2025-09-29 16:29:41] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_4f169b5f15ab | Data: {}
[2025-09-29 16:29:43] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 13327.062} | Data: {}
[2025-09-29 16:29:43] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_4f169b5f15ab | Data: {}
[2025-09-29 16:29:45] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 13329.062} | Data: {}
[2025-09-29 16:29:45] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_4f169b5f15ab | Data: {}
[2025-09-29 16:29:47] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 13331.078} | Data: {}
[2025-09-29 16:29:47] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_4f169b5f15ab | Data: {}
[2025-09-29 16:29:51] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 13335.218} | Data: {}
[2025-09-29 16:29:51] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_4f169b5f15ab | Data: {}
[2025-09-29 16:29:55] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 13339.14} | Data: {}
[2025-09-29 16:29:55] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_4f169b5f15ab | Data: {}
[2025-09-29 16:29:55] [INFO] [progress_debug] 🔄 WORKFLOW_STEP | Session: async_4f169b5f15ab | Step: symbol_detection | Progress: 40% | Message: 🔍 Symbol detection completed
[2025-09-29 16:29:55] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Mapped step 'symbol_detection' to symbol_detection | Data: {}
[2025-09-29 16:29:55] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Created progress update: ProgressUpdate(step=<WorkflowStep.SYMBOL_DETECTION: 'symbol_detection'>, progress=40, message='🔍 Symbol detection completed', timestamp='2025-09-29T10:59:55.432451', details={'node': 'symbol_detection'}, tool_results=None) | Data: {}
[2025-09-29 16:29:55] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔥 BROADCASTING update for session async_4f169b5f15ab | Data: {'step': 'symbol_detection', 'progress': 40, 'message': '🔍 Symbol detection completed', 'has_clients': True, 'client_count': 1, 'broadcaster_id': 1789722241552, 'all_sessions': ['async_4f169b5f15ab']}
[2025-09-29 16:29:55] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Sent update to client 0 for session async_4f169b5f15ab | Data: {}
[2025-09-29 16:29:55] [INFO] [progress_debug] 📡 PROGRESS_BROADCAST | ✅ SUCCESS | Session: async_4f169b5f15ab | Step: symbol_detection | Details: {'workflow_step': 'symbol_detection', 'progress': 40, 'message': '🔍 Symbol detection completed'}
[2025-09-29 16:29:55] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Received progress update: {'step': 'symbol_detection', 'progress': 40, 'message': '🔍 Symbol detection completed', 'timestamp': '2025-09-29T10:59:55.432451', 'details': {'node': 'symbol_detection'}, 'tool_results': None} | Data: {}
[2025-09-29 16:29:55] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending SSE event: {'type': 'progress_update', 'data': {'step': 'symbol_detection', 'progress': 40, 'message': '🔍 Symbol detection completed', 'timestamp': '2025-09-29T10:59:55.432451', 'details': {'node': 'symbol_detection'}, 'tool_results': None}} | Data: {}
[2025-09-29 16:29:55] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_4f169b5f15ab | Data: {}
[2025-09-29 16:29:55] [INFO] [progress_debug] 🔄 WORKFLOW_STEP | Session: async_4f169b5f15ab | Step: tool_execution | Progress: 50% | Message: 🛠️ Data collection completed
[2025-09-29 16:29:55] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Mapped step 'tool_execution' to tool_execution | Data: {}
[2025-09-29 16:29:55] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Created progress update: ProgressUpdate(step=<WorkflowStep.TOOL_EXECUTION: 'tool_execution'>, progress=50, message='🛠️ Data collection completed', timestamp='2025-09-29T10:59:55.574274', details={'node': 'tool_execution'}, tool_results=None) | Data: {}
[2025-09-29 16:29:55] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔥 BROADCASTING update for session async_4f169b5f15ab | Data: {'step': 'tool_execution', 'progress': 50, 'message': '🛠️ Data collection completed', 'has_clients': True, 'client_count': 1, 'broadcaster_id': 1789722241552, 'all_sessions': ['async_4f169b5f15ab']}
[2025-09-29 16:29:55] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Sent update to client 0 for session async_4f169b5f15ab | Data: {}
[2025-09-29 16:29:55] [INFO] [progress_debug] 📡 PROGRESS_BROADCAST | ✅ SUCCESS | Session: async_4f169b5f15ab | Step: tool_execution | Details: {'workflow_step': 'tool_execution', 'progress': 50, 'message': '🛠️ Data collection completed'}
[2025-09-29 16:29:55] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Received progress update: {'step': 'tool_execution', 'progress': 50, 'message': '🛠️ Data collection completed', 'timestamp': '2025-09-29T10:59:55.574274', 'details': {'node': 'tool_execution'}, 'tool_results': None} | Data: {}
[2025-09-29 16:29:55] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending SSE event: {'type': 'progress_update', 'data': {'step': 'tool_execution', 'progress': 50, 'message': '🛠️ Data collection completed', 'timestamp': '2025-09-29T10:59:55.574274', 'details': {'node': 'tool_execution'}, 'tool_results': None}} | Data: {}
[2025-09-29 16:29:55] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_4f169b5f15ab | Data: {}
[2025-09-29 16:29:55] [INFO] [progress_debug] 🔄 WORKFLOW_STEP | Session: async_4f169b5f15ab | Step: tool_summarization | Progress: 60% | Message: 📋 Data processing completed
[2025-09-29 16:29:55] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Mapped step 'tool_summarization' to tool_summarization | Data: {}
[2025-09-29 16:29:55] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Created progress update: ProgressUpdate(step=<WorkflowStep.TOOL_SUMMARIZATION: 'tool_summarization'>, progress=60, message='📋 Data processing completed', timestamp='2025-09-29T10:59:55.671605', details={'node': 'tool_summarization'}, tool_results=None) | Data: {}
[2025-09-29 16:29:55] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔥 BROADCASTING update for session async_4f169b5f15ab | Data: {'step': 'tool_summarization', 'progress': 60, 'message': '📋 Data processing completed', 'has_clients': True, 'client_count': 1, 'broadcaster_id': 1789722241552, 'all_sessions': ['async_4f169b5f15ab']}
[2025-09-29 16:29:55] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Sent update to client 0 for session async_4f169b5f15ab | Data: {}
[2025-09-29 16:29:55] [INFO] [progress_debug] 📡 PROGRESS_BROADCAST | ✅ SUCCESS | Session: async_4f169b5f15ab | Step: tool_summarization | Details: {'workflow_step': 'tool_summarization', 'progress': 60, 'message': '📋 Data processing completed'}
[2025-09-29 16:29:55] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Received progress update: {'step': 'tool_summarization', 'progress': 60, 'message': '📋 Data processing completed', 'timestamp': '2025-09-29T10:59:55.671605', 'details': {'node': 'tool_summarization'}, 'tool_results': None} | Data: {}
[2025-09-29 16:29:55] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending SSE event: {'type': 'progress_update', 'data': {'step': 'tool_summarization', 'progress': 60, 'message': '📋 Data processing completed', 'timestamp': '2025-09-29T10:59:55.671605', 'details': {'node': 'tool_summarization'}, 'tool_results': None}} | Data: {}
[2025-09-29 16:29:55] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_4f169b5f15ab | Data: {}
[2025-09-29 16:29:55] [INFO] [progress_debug] 🔄 WORKFLOW_STEP | Session: async_4f169b5f15ab | Step: rag_integration | Progress: 70% | Message: 🧠 RAG integration completed
[2025-09-29 16:29:55] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Mapped step 'rag_integration' to rag_integration | Data: {}
[2025-09-29 16:29:55] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Created progress update: ProgressUpdate(step=<WorkflowStep.RAG_INTEGRATION: 'rag_integration'>, progress=70, message='🧠 RAG integration completed', timestamp='2025-09-29T10:59:55.741674', details={'node': 'rag_integration'}, tool_results=None) | Data: {}
[2025-09-29 16:29:55] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔥 BROADCASTING update for session async_4f169b5f15ab | Data: {'step': 'rag_integration', 'progress': 70, 'message': '🧠 RAG integration completed', 'has_clients': True, 'client_count': 1, 'broadcaster_id': 1789722241552, 'all_sessions': ['async_4f169b5f15ab']}
[2025-09-29 16:29:55] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Sent update to client 0 for session async_4f169b5f15ab | Data: {}
[2025-09-29 16:29:55] [INFO] [progress_debug] 📡 PROGRESS_BROADCAST | ✅ SUCCESS | Session: async_4f169b5f15ab | Step: rag_integration | Details: {'workflow_step': 'rag_integration', 'progress': 70, 'message': '🧠 RAG integration completed'}
[2025-09-29 16:29:55] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Received progress update: {'step': 'rag_integration', 'progress': 70, 'message': '🧠 RAG integration completed', 'timestamp': '2025-09-29T10:59:55.741674', 'details': {'node': 'rag_integration'}, 'tool_results': None} | Data: {}
[2025-09-29 16:29:55] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending SSE event: {'type': 'progress_update', 'data': {'step': 'rag_integration', 'progress': 70, 'message': '🧠 RAG integration completed', 'timestamp': '2025-09-29T10:59:55.741674', 'details': {'node': 'rag_integration'}, 'tool_results': None}} | Data: {}
[2025-09-29 16:29:55] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_4f169b5f15ab | Data: {}
[2025-09-29 16:29:55] [INFO] [progress_debug] 🔄 WORKFLOW_STEP | Session: async_4f169b5f15ab | Step: final_analysis | Progress: 80% | Message: 🎯 AI analysis completed
[2025-09-29 16:29:55] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Mapped step 'final_analysis' to final_analysis | Data: {}
[2025-09-29 16:29:55] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Created progress update: ProgressUpdate(step=<WorkflowStep.FINAL_ANALYSIS: 'final_analysis'>, progress=80, message='🎯 AI analysis completed', timestamp='2025-09-29T10:59:55.839195', details={'node': 'final_analysis'}, tool_results=None) | Data: {}
[2025-09-29 16:29:55] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔥 BROADCASTING update for session async_4f169b5f15ab | Data: {'step': 'final_analysis', 'progress': 80, 'message': '🎯 AI analysis completed', 'has_clients': True, 'client_count': 1, 'broadcaster_id': 1789722241552, 'all_sessions': ['async_4f169b5f15ab']}
[2025-09-29 16:29:55] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Sent update to client 0 for session async_4f169b5f15ab | Data: {}
[2025-09-29 16:29:55] [INFO] [progress_debug] 📡 PROGRESS_BROADCAST | ✅ SUCCESS | Session: async_4f169b5f15ab | Step: final_analysis | Details: {'workflow_step': 'final_analysis', 'progress': 80, 'message': '🎯 AI analysis completed'}
[2025-09-29 16:29:55] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Received progress update: {'step': 'final_analysis', 'progress': 80, 'message': '🎯 AI analysis completed', 'timestamp': '2025-09-29T10:59:55.839195', 'details': {'node': 'final_analysis'}, 'tool_results': None} | Data: {}
[2025-09-29 16:29:55] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending SSE event: {'type': 'progress_update', 'data': {'step': 'final_analysis', 'progress': 80, 'message': '🎯 AI analysis completed', 'timestamp': '2025-09-29T10:59:55.839195', 'details': {'node': 'final_analysis'}, 'tool_results': None}} | Data: {}
[2025-09-29 16:29:55] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_4f169b5f15ab | Data: {}
[2025-09-29 16:29:55] [INFO] [progress_debug] 🔄 WORKFLOW_STEP | Session: async_4f169b5f15ab | Step: memory_storage | Progress: 90% | Message: 💾 Storing analysis for future reference...
[2025-09-29 16:29:55] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Mapped step 'memory_storage' to memory_storage | Data: {}
[2025-09-29 16:29:55] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Created progress update: ProgressUpdate(step=<WorkflowStep.MEMORY_STORAGE: 'memory_storage'>, progress=90, message='💾 Storing analysis for future reference...', timestamp='2025-09-29T10:59:55.926621', details={'step': 'memory_storage'}, tool_results=None) | Data: {}
[2025-09-29 16:29:55] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔥 BROADCASTING update for session async_4f169b5f15ab | Data: {'step': 'memory_storage', 'progress': 90, 'message': '💾 Storing analysis for future reference...', 'has_clients': True, 'client_count': 1, 'broadcaster_id': 1789722241552, 'all_sessions': ['async_4f169b5f15ab']}
[2025-09-29 16:29:55] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Sent update to client 0 for session async_4f169b5f15ab | Data: {}
[2025-09-29 16:29:55] [INFO] [progress_debug] 📡 PROGRESS_BROADCAST | ✅ SUCCESS | Session: async_4f169b5f15ab | Step: memory_storage | Details: {'workflow_step': 'memory_storage', 'progress': 90, 'message': '💾 Storing analysis for future reference...'}
[2025-09-29 16:29:55] [INFO] [progress_debug] 🔄 WORKFLOW_STEP | Session: async_4f169b5f15ab | Step: complete | Progress: 100% | Message: 🎉 Analysis completed successfully!
[2025-09-29 16:29:55] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Mapped step 'complete' to complete | Data: {}
[2025-09-29 16:29:55] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Created progress update: ProgressUpdate(step=<WorkflowStep.COMPLETE: 'complete'>, progress=100, message='🎉 Analysis completed successfully!', timestamp='2025-09-29T10:59:55.972182', details={'step': 'complete', 'workflow_status': 'complete'}, tool_results=None) | Data: {}
[2025-09-29 16:29:55] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔥 BROADCASTING update for session async_4f169b5f15ab | Data: {'step': 'complete', 'progress': 100, 'message': '🎉 Analysis completed successfully!', 'has_clients': True, 'client_count': 1, 'broadcaster_id': 1789722241552, 'all_sessions': ['async_4f169b5f15ab']}
[2025-09-29 16:29:55] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Sent update to client 0 for session async_4f169b5f15ab | Data: {}
[2025-09-29 16:29:55] [INFO] [progress_debug] 📡 PROGRESS_BROADCAST | ✅ SUCCESS | Session: async_4f169b5f15ab | Step: complete | Details: {'workflow_step': 'complete', 'progress': 100, 'message': '🎉 Analysis completed successfully!'}
[2025-09-29 16:29:55] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Received progress update: {'step': 'memory_storage', 'progress': 90, 'message': '💾 Storing analysis for future reference...', 'timestamp': '2025-09-29T10:59:55.926621', 'details': {'step': 'memory_storage'}, 'tool_results': None} | Data: {}
[2025-09-29 16:29:56] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending SSE event: {'type': 'progress_update', 'data': {'step': 'memory_storage', 'progress': 90, 'message': '💾 Storing analysis for future reference...', 'timestamp': '2025-09-29T10:59:55.926621', 'details': {'step': 'memory_storage'}, 'tool_results': None}} | Data: {}
[2025-09-29 16:29:56] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_4f169b5f15ab | Data: {}
[2025-09-29 16:29:56] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Received progress update: {'step': 'complete', 'progress': 100, 'message': '🎉 Analysis completed successfully!', 'timestamp': '2025-09-29T10:59:55.972182', 'details': {'step': 'complete', 'workflow_status': 'complete'}, 'tool_results': None} | Data: {}
[2025-09-29 16:29:56] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending SSE event: {'type': 'progress_update', 'data': {'step': 'complete', 'progress': 100, 'message': '🎉 Analysis completed successfully!', 'timestamp': '2025-09-29T10:59:55.972182', 'details': {'step': 'complete', 'workflow_status': 'complete'}, 'tool_results': None}} | Data: {}
[2025-09-29 16:29:56] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Workflow complete, sending: {'type': 'complete', 'session_id': 'async_4f169b5f15ab'} | Data: {}
[2025-09-29 16:29:56] [INFO] [progress_debug] 🔌 SSE_DISCONNECT | Session: async_4f169b5f15ab | Reason: event_generator_cleanup_normal_completion
[2025-09-29 16:29:56] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Removed client for session async_4f169b5f15ab | Data: {'remaining_clients': 0}
[2025-09-29 16:29:56] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Cleaned up empty client list for session async_4f169b5f15ab | Data: {}
[2025-09-29 16:29:56] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Keeping buffered updates for potential reconnection: async_4f169b5f15ab | Data: {}
[2025-09-29 16:29:56] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Buffered updates count: 11 | Data: {}
[2025-09-29 16:29:56] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Successfully cleaned up client for session async_4f169b5f15ab | Data: {}
[2025-09-29 16:29:56] [INFO] [progress_debug] 🔄 WORKFLOW_STEP | Session: async_4f169b5f15ab | Step: memory_storage | Progress: 90% | Message: 💾 Memory storage completed
[2025-09-29 16:29:56] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Mapped step 'memory_storage' to memory_storage | Data: {}
[2025-09-29 16:29:56] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Created progress update: ProgressUpdate(step=<WorkflowStep.MEMORY_STORAGE: 'memory_storage'>, progress=90, message='💾 Memory storage completed', timestamp='2025-09-29T10:59:56.041919', details={'node': 'memory_storage'}, tool_results=None) | Data: {}
[2025-09-29 16:29:56] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔥 BROADCASTING update for session async_4f169b5f15ab | Data: {'step': 'memory_storage', 'progress': 90, 'message': '💾 Memory storage completed', 'has_clients': False, 'client_count': 0, 'broadcaster_id': 1789722241552, 'all_sessions': []}
[2025-09-29 16:29:56] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | No clients found for session async_4f169b5f15ab - update buffered (total: 12) | Data: {}
[2025-09-29 16:29:56] [INFO] [progress_debug] 📡 PROGRESS_BROADCAST | ✅ SUCCESS | Session: async_4f169b5f15ab | Step: memory_storage | Details: {'workflow_step': 'memory_storage', 'progress': 90, 'message': '💾 Memory storage completed'}
[2025-09-29 16:29:56] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔥 BROADCASTING update for session async_4f169b5f15ab | Data: {'step': 'complete', 'progress': 100, 'message': '✅ AI analysis workflow completed successfully!', 'has_clients': False, 'client_count': 0, 'broadcaster_id': 1789722241552, 'all_sessions': []}
[2025-09-29 16:29:56] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | No clients found for session async_4f169b5f15ab - update buffered (total: 13) | Data: {}
[2025-09-29 16:29:56] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔥 BROADCASTING update for session async_4f169b5f15ab | Data: {'step': 'complete', 'progress': 100, 'message': '✅ AI analysis workflow completed successfully!', 'has_clients': False, 'client_count': 0, 'broadcaster_id': 1789722241552, 'all_sessions': []}
[2025-09-29 16:29:56] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | No clients found for session async_4f169b5f15ab - update buffered (total: 14) | Data: {}
[2025-09-29 16:30:06] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔥 BROADCASTING update for session async_4f169b5f15ab | Data: {'step': 'complete', 'progress': 100, 'message': '🎉 Analysis completed successfully!', 'has_clients': False, 'client_count': 0, 'broadcaster_id': 1789722241552, 'all_sessions': []}
[2025-09-29 16:30:06] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | No clients found for session async_4f169b5f15ab - update buffered (total: 15) | Data: {}
[2025-09-29 16:30:06] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Marked session async_4f169b5f15ab as complete | Data: {}
[2025-09-29 16:30:06] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Cleaned up buffered updates for completed session async_4f169b5f15ab | Data: {}
[2025-09-29 16:50:42] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔥 BROADCASTING update for session async_98d0b880defc | Data: {'step': 'chart_upload', 'progress': 5, 'message': 'Analysis request received, starting background processing...', 'has_clients': False, 'client_count': 0, 'broadcaster_id': 2644555443536, 'all_sessions': []}
[2025-09-29 16:50:42] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | No clients found for session async_98d0b880defc - update buffered (total: 1) | Data: {}
[2025-09-29 16:50:43] [INFO] [progress_debug] 🔗 SSE_CONNECTION | Session: async_98d0b880defc | Client: {'user_id': UUID('9fbc7a39-ae9c-455a-ad81-53ed905e004b'), 'user_email': '<EMAIL>', 'endpoint': '/api/v1/progress/stream/async_98d0b880defc'}
[2025-09-29 16:50:43] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔥 BROADCASTING update for session async_98d0b880defc | Data: {'step': 'chart_upload', 'progress': 5, 'message': '📊 Processing uploaded chart images...', 'has_clients': False, 'client_count': 0, 'broadcaster_id': 2644555443536, 'all_sessions': []}
[2025-09-29 16:50:43] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | No clients found for session async_98d0b880defc - update buffered (total: 2) | Data: {}
[2025-09-29 16:50:43] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔥 BROADCASTING update for session async_98d0b880defc | Data: {'step': 'chart_upload', 'progress': 5, 'message': '🖼️ Processed chart image 1/1 (75KB)', 'has_clients': False, 'client_count': 0, 'broadcaster_id': 2644555443536, 'all_sessions': []}
[2025-09-29 16:50:43] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | No clients found for session async_98d0b880defc - update buffered (total: 3) | Data: {}
[2025-09-29 16:50:43] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Adding client for session async_98d0b880defc | Data: {}
[2025-09-29 16:50:43] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | ✅ CLIENT ADDED for session async_98d0b880defc | Data: {'total_clients': 1, 'all_sessions': ['async_98d0b880defc'], 'broadcaster_id': 2644555443536}
[2025-09-29 16:50:43] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Current broadcaster state | Data: {'session_id': 'async_98d0b880defc', 'clients_dict': {'async_98d0b880defc': 1}, 'total_sessions': 1}
[2025-09-29 16:50:43] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔄 REPLAYING 3 buffered updates to new client for session async_98d0b880defc | Data: {}
[2025-09-29 16:50:43] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | ✅ Replayed buffered update 1/3: chart_upload (5%) | Data: {}
[2025-09-29 16:50:43] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | ✅ Replayed buffered update 2/3: chart_upload (5%) | Data: {}
[2025-09-29 16:50:43] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | ✅ Replayed buffered update 3/3: chart_upload (5%) | Data: {}
[2025-09-29 16:50:43] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🎯 Successfully replayed 3 buffered updates for session async_98d0b880defc | Data: {}
[2025-09-29 16:50:43] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Client queue created for session async_98d0b880defc | Data: {}
[2025-09-29 16:50:43] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending connection confirmation: {'type': 'connected', 'session_id': 'async_98d0b880defc'} | Data: {}
[2025-09-29 16:50:43] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_98d0b880defc | Data: {}
[2025-09-29 16:50:43] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Received progress update: {'step': 'chart_upload', 'progress': 5, 'message': 'Analysis request received, starting background processing...', 'timestamp': '2025-09-29T11:20:42.910061', 'details': {'user_id': '9fbc7a39-ae9c-455a-ad81-53ed905e004b', 'analysis_type': 'Positional', 'image_count': 1}, 'tool_results': None} | Data: {}
[2025-09-29 16:50:43] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending SSE event: {'type': 'progress_update', 'data': {'step': 'chart_upload', 'progress': 5, 'message': 'Analysis request received, starting background processing...', 'timestamp': '2025-09-29T11:20:42.910061', 'details': {'user_id': '9fbc7a39-ae9c-455a-ad81-53ed905e004b', 'analysis_type': 'Positional', 'image_count': 1}, 'tool_results': None}} | Data: {}
[2025-09-29 16:50:43] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_98d0b880defc | Data: {}
[2025-09-29 16:50:43] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Received progress update: {'step': 'chart_upload', 'progress': 5, 'message': '📊 Processing uploaded chart images...', 'timestamp': '2025-09-29T11:20:43.150239', 'details': {'user_id': '9fbc7a39-ae9c-455a-ad81-53ed905e004b', 'analysis_type': 'Positional', 'image_count': 1}, 'tool_results': None} | Data: {}
[2025-09-29 16:50:43] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending SSE event: {'type': 'progress_update', 'data': {'step': 'chart_upload', 'progress': 5, 'message': '📊 Processing uploaded chart images...', 'timestamp': '2025-09-29T11:20:43.150239', 'details': {'user_id': '9fbc7a39-ae9c-455a-ad81-53ed905e004b', 'analysis_type': 'Positional', 'image_count': 1}, 'tool_results': None}} | Data: {}
[2025-09-29 16:50:43] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_98d0b880defc | Data: {}
[2025-09-29 16:50:43] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Received progress update: {'step': 'chart_upload', 'progress': 5, 'message': '🖼️ Processed chart image 1/1 (75KB)', 'timestamp': '2025-09-29T11:20:43.163249', 'details': {'image_size': 77391, 'images_processed': 1}, 'tool_results': None} | Data: {}
[2025-09-29 16:50:43] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending SSE event: {'type': 'progress_update', 'data': {'step': 'chart_upload', 'progress': 5, 'message': '🖼️ Processed chart image 1/1 (75KB)', 'timestamp': '2025-09-29T11:20:43.163249', 'details': {'image_size': 77391, 'images_processed': 1}, 'tool_results': None}} | Data: {}
[2025-09-29 16:50:43] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_98d0b880defc | Data: {}
[2025-09-29 16:50:45] [INFO] [progress_debug] 🔄 SESSION_LIFECYCLE | Session: async_98d0b880defc | Event: workflow_start | Details: {'analysis_mode': 'positional', 'market_specialization': 'Crypto', 'has_progress_broadcaster': True, 'chart_images_count': 1, 'parameter_passing_issue': False}
[2025-09-29 16:50:45] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 14589.359} | Data: {}
[2025-09-29 16:50:45] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_98d0b880defc | Data: {}
[2025-09-29 16:50:45] [INFO] [progress_debug] 🔄 WORKFLOW_STEP | Session: async_98d0b880defc | Step: chart_analysis | Progress: 20% | Message: 📊 Chart analysis completed
[2025-09-29 16:50:45] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Mapped step 'chart_analysis' to chart_analysis | Data: {}
[2025-09-29 16:50:45] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Created progress update: ProgressUpdate(step=<WorkflowStep.CHART_ANALYSIS: 'chart_analysis'>, progress=20, message='📊 Chart analysis completed', timestamp='2025-09-29T11:20:45.750550', details={'node': 'chart_analysis'}, tool_results=None) | Data: {}
[2025-09-29 16:50:45] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔥 BROADCASTING update for session async_98d0b880defc | Data: {'step': 'chart_analysis', 'progress': 20, 'message': '📊 Chart analysis completed', 'has_clients': True, 'client_count': 1, 'broadcaster_id': 2644555443536, 'all_sessions': ['async_98d0b880defc']}
[2025-09-29 16:50:45] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Sent update to client 0 for session async_98d0b880defc | Data: {}
[2025-09-29 16:50:45] [INFO] [progress_debug] 📡 PROGRESS_BROADCAST | ✅ SUCCESS | Session: async_98d0b880defc | Step: chart_analysis | Details: {'workflow_step': 'chart_analysis', 'progress': 20, 'message': '📊 Chart analysis completed'}
[2025-09-29 16:50:45] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Received progress update: {'step': 'chart_analysis', 'progress': 20, 'message': '📊 Chart analysis completed', 'timestamp': '2025-09-29T11:20:45.750550', 'details': {'node': 'chart_analysis'}, 'tool_results': None} | Data: {}
[2025-09-29 16:50:45] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending SSE event: {'type': 'progress_update', 'data': {'step': 'chart_analysis', 'progress': 20, 'message': '📊 Chart analysis completed', 'timestamp': '2025-09-29T11:20:45.750550', 'details': {'node': 'chart_analysis'}, 'tool_results': None}} | Data: {}
[2025-09-29 16:50:45] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_98d0b880defc | Data: {}
[2025-09-29 16:50:47] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 14591.515} | Data: {}
[2025-09-29 16:50:47] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_98d0b880defc | Data: {}
[2025-09-29 16:50:52] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 14596.015} | Data: {}
[2025-09-29 16:50:52] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_98d0b880defc | Data: {}
[2025-09-29 16:50:57] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending keepalive ping: {'type': 'ping', 'timestamp': 14601.546} | Data: {}
[2025-09-29 16:50:57] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_98d0b880defc | Data: {}
[2025-09-29 16:50:57] [INFO] [progress_debug] 🔄 WORKFLOW_STEP | Session: async_98d0b880defc | Step: symbol_detection | Progress: 40% | Message: 🔍 Symbol detection completed
[2025-09-29 16:50:57] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Mapped step 'symbol_detection' to symbol_detection | Data: {}
[2025-09-29 16:50:57] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Created progress update: ProgressUpdate(step=<WorkflowStep.SYMBOL_DETECTION: 'symbol_detection'>, progress=40, message='🔍 Symbol detection completed', timestamp='2025-09-29T11:20:57.877158', details={'node': 'symbol_detection'}, tool_results=None) | Data: {}
[2025-09-29 16:50:57] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔥 BROADCASTING update for session async_98d0b880defc | Data: {'step': 'symbol_detection', 'progress': 40, 'message': '🔍 Symbol detection completed', 'has_clients': True, 'client_count': 1, 'broadcaster_id': 2644555443536, 'all_sessions': ['async_98d0b880defc']}
[2025-09-29 16:50:57] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Sent update to client 0 for session async_98d0b880defc | Data: {}
[2025-09-29 16:50:57] [INFO] [progress_debug] 📡 PROGRESS_BROADCAST | ✅ SUCCESS | Session: async_98d0b880defc | Step: symbol_detection | Details: {'workflow_step': 'symbol_detection', 'progress': 40, 'message': '🔍 Symbol detection completed'}
[2025-09-29 16:50:57] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Received progress update: {'step': 'symbol_detection', 'progress': 40, 'message': '🔍 Symbol detection completed', 'timestamp': '2025-09-29T11:20:57.877158', 'details': {'node': 'symbol_detection'}, 'tool_results': None} | Data: {}
[2025-09-29 16:50:57] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending SSE event: {'type': 'progress_update', 'data': {'step': 'symbol_detection', 'progress': 40, 'message': '🔍 Symbol detection completed', 'timestamp': '2025-09-29T11:20:57.877158', 'details': {'node': 'symbol_detection'}, 'tool_results': None}} | Data: {}
[2025-09-29 16:50:57] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_98d0b880defc | Data: {}
[2025-09-29 16:50:57] [INFO] [progress_debug] 🔄 WORKFLOW_STEP | Session: async_98d0b880defc | Step: tool_execution | Progress: 50% | Message: 🛠️ Data collection completed
[2025-09-29 16:50:57] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Mapped step 'tool_execution' to tool_execution | Data: {}
[2025-09-29 16:50:57] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Created progress update: ProgressUpdate(step=<WorkflowStep.TOOL_EXECUTION: 'tool_execution'>, progress=50, message='🛠️ Data collection completed', timestamp='2025-09-29T11:20:57.972171', details={'node': 'tool_execution'}, tool_results=None) | Data: {}
[2025-09-29 16:50:57] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔥 BROADCASTING update for session async_98d0b880defc | Data: {'step': 'tool_execution', 'progress': 50, 'message': '🛠️ Data collection completed', 'has_clients': True, 'client_count': 1, 'broadcaster_id': 2644555443536, 'all_sessions': ['async_98d0b880defc']}
[2025-09-29 16:50:57] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Sent update to client 0 for session async_98d0b880defc | Data: {}
[2025-09-29 16:50:57] [INFO] [progress_debug] 📡 PROGRESS_BROADCAST | ✅ SUCCESS | Session: async_98d0b880defc | Step: tool_execution | Details: {'workflow_step': 'tool_execution', 'progress': 50, 'message': '🛠️ Data collection completed'}
[2025-09-29 16:50:58] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Received progress update: {'step': 'tool_execution', 'progress': 50, 'message': '🛠️ Data collection completed', 'timestamp': '2025-09-29T11:20:57.972171', 'details': {'node': 'tool_execution'}, 'tool_results': None} | Data: {}
[2025-09-29 16:50:58] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending SSE event: {'type': 'progress_update', 'data': {'step': 'tool_execution', 'progress': 50, 'message': '🛠️ Data collection completed', 'timestamp': '2025-09-29T11:20:57.972171', 'details': {'node': 'tool_execution'}, 'tool_results': None}} | Data: {}
[2025-09-29 16:50:58] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_98d0b880defc | Data: {}
[2025-09-29 16:50:58] [INFO] [progress_debug] 🔄 WORKFLOW_STEP | Session: async_98d0b880defc | Step: tool_summarization | Progress: 60% | Message: 📋 Data processing completed
[2025-09-29 16:50:58] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Mapped step 'tool_summarization' to tool_summarization | Data: {}
[2025-09-29 16:50:58] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Created progress update: ProgressUpdate(step=<WorkflowStep.TOOL_SUMMARIZATION: 'tool_summarization'>, progress=60, message='📋 Data processing completed', timestamp='2025-09-29T11:20:58.881300', details={'node': 'tool_summarization'}, tool_results=None) | Data: {}
[2025-09-29 16:50:58] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔥 BROADCASTING update for session async_98d0b880defc | Data: {'step': 'tool_summarization', 'progress': 60, 'message': '📋 Data processing completed', 'has_clients': True, 'client_count': 1, 'broadcaster_id': 2644555443536, 'all_sessions': ['async_98d0b880defc']}
[2025-09-29 16:50:58] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Sent update to client 0 for session async_98d0b880defc | Data: {}
[2025-09-29 16:50:58] [INFO] [progress_debug] 📡 PROGRESS_BROADCAST | ✅ SUCCESS | Session: async_98d0b880defc | Step: tool_summarization | Details: {'workflow_step': 'tool_summarization', 'progress': 60, 'message': '📋 Data processing completed'}
[2025-09-29 16:50:58] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Received progress update: {'step': 'tool_summarization', 'progress': 60, 'message': '📋 Data processing completed', 'timestamp': '2025-09-29T11:20:58.881300', 'details': {'node': 'tool_summarization'}, 'tool_results': None} | Data: {}
[2025-09-29 16:50:58] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending SSE event: {'type': 'progress_update', 'data': {'step': 'tool_summarization', 'progress': 60, 'message': '📋 Data processing completed', 'timestamp': '2025-09-29T11:20:58.881300', 'details': {'node': 'tool_summarization'}, 'tool_results': None}} | Data: {}
[2025-09-29 16:50:58] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_98d0b880defc | Data: {}
[2025-09-29 16:50:58] [INFO] [progress_debug] 🔄 WORKFLOW_STEP | Session: async_98d0b880defc | Step: rag_integration | Progress: 70% | Message: 🧠 RAG integration completed
[2025-09-29 16:50:59] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Mapped step 'rag_integration' to rag_integration | Data: {}
[2025-09-29 16:50:59] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Created progress update: ProgressUpdate(step=<WorkflowStep.RAG_INTEGRATION: 'rag_integration'>, progress=70, message='🧠 RAG integration completed', timestamp='2025-09-29T11:20:59.753968', details={'node': 'rag_integration'}, tool_results=None) | Data: {}
[2025-09-29 16:50:59] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔥 BROADCASTING update for session async_98d0b880defc | Data: {'step': 'rag_integration', 'progress': 70, 'message': '🧠 RAG integration completed', 'has_clients': True, 'client_count': 1, 'broadcaster_id': 2644555443536, 'all_sessions': ['async_98d0b880defc']}
[2025-09-29 16:50:59] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Sent update to client 0 for session async_98d0b880defc | Data: {}
[2025-09-29 16:50:59] [INFO] [progress_debug] 📡 PROGRESS_BROADCAST | ✅ SUCCESS | Session: async_98d0b880defc | Step: rag_integration | Details: {'workflow_step': 'rag_integration', 'progress': 70, 'message': '🧠 RAG integration completed'}
[2025-09-29 16:50:59] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Received progress update: {'step': 'rag_integration', 'progress': 70, 'message': '🧠 RAG integration completed', 'timestamp': '2025-09-29T11:20:59.753968', 'details': {'node': 'rag_integration'}, 'tool_results': None} | Data: {}
[2025-09-29 16:50:59] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending SSE event: {'type': 'progress_update', 'data': {'step': 'rag_integration', 'progress': 70, 'message': '🧠 RAG integration completed', 'timestamp': '2025-09-29T11:20:59.753968', 'details': {'node': 'rag_integration'}, 'tool_results': None}} | Data: {}
[2025-09-29 16:50:59] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_98d0b880defc | Data: {}
[2025-09-29 16:50:59] [INFO] [progress_debug] 🔄 WORKFLOW_STEP | Session: async_98d0b880defc | Step: final_analysis | Progress: 80% | Message: 🎯 AI analysis completed
[2025-09-29 16:51:00] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Mapped step 'final_analysis' to final_analysis | Data: {}
[2025-09-29 16:51:00] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Created progress update: ProgressUpdate(step=<WorkflowStep.FINAL_ANALYSIS: 'final_analysis'>, progress=80, message='🎯 AI analysis completed', timestamp='2025-09-29T11:21:00.603835', details={'node': 'final_analysis'}, tool_results=None) | Data: {}
[2025-09-29 16:51:00] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔥 BROADCASTING update for session async_98d0b880defc | Data: {'step': 'final_analysis', 'progress': 80, 'message': '🎯 AI analysis completed', 'has_clients': True, 'client_count': 1, 'broadcaster_id': 2644555443536, 'all_sessions': ['async_98d0b880defc']}
[2025-09-29 16:51:00] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Sent update to client 0 for session async_98d0b880defc | Data: {}
[2025-09-29 16:51:00] [INFO] [progress_debug] 📡 PROGRESS_BROADCAST | ✅ SUCCESS | Session: async_98d0b880defc | Step: final_analysis | Details: {'workflow_step': 'final_analysis', 'progress': 80, 'message': '🎯 AI analysis completed'}
[2025-09-29 16:51:00] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Received progress update: {'step': 'final_analysis', 'progress': 80, 'message': '🎯 AI analysis completed', 'timestamp': '2025-09-29T11:21:00.603835', 'details': {'node': 'final_analysis'}, 'tool_results': None} | Data: {}
[2025-09-29 16:51:00] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending SSE event: {'type': 'progress_update', 'data': {'step': 'final_analysis', 'progress': 80, 'message': '🎯 AI analysis completed', 'timestamp': '2025-09-29T11:21:00.603835', 'details': {'node': 'final_analysis'}, 'tool_results': None}} | Data: {}
[2025-09-29 16:51:00] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_98d0b880defc | Data: {}
[2025-09-29 16:51:00] [INFO] [progress_debug] 🔄 WORKFLOW_STEP | Session: async_98d0b880defc | Step: memory_storage | Progress: 90% | Message: 💾 Storing analysis for future reference...
[2025-09-29 16:51:01] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Mapped step 'memory_storage' to memory_storage | Data: {}
[2025-09-29 16:51:01] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Created progress update: ProgressUpdate(step=<WorkflowStep.MEMORY_STORAGE: 'memory_storage'>, progress=90, message='💾 Storing analysis for future reference...', timestamp='2025-09-29T11:21:01.458044', details={'step': 'memory_storage'}, tool_results=None) | Data: {}
[2025-09-29 16:51:01] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔥 BROADCASTING update for session async_98d0b880defc | Data: {'step': 'memory_storage', 'progress': 90, 'message': '💾 Storing analysis for future reference...', 'has_clients': True, 'client_count': 1, 'broadcaster_id': 2644555443536, 'all_sessions': ['async_98d0b880defc']}
[2025-09-29 16:51:01] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Sent update to client 0 for session async_98d0b880defc | Data: {}
[2025-09-29 16:51:01] [INFO] [progress_debug] 📡 PROGRESS_BROADCAST | ✅ SUCCESS | Session: async_98d0b880defc | Step: memory_storage | Details: {'workflow_step': 'memory_storage', 'progress': 90, 'message': '💾 Storing analysis for future reference...'}
[2025-09-29 16:51:01] [INFO] [progress_debug] 🔄 WORKFLOW_STEP | Session: async_98d0b880defc | Step: complete | Progress: 100% | Message: 🎉 Analysis completed successfully!
[2025-09-29 16:51:01] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Received progress update: {'step': 'memory_storage', 'progress': 90, 'message': '💾 Storing analysis for future reference...', 'timestamp': '2025-09-29T11:21:01.458044', 'details': {'step': 'memory_storage'}, 'tool_results': None} | Data: {}
[2025-09-29 16:51:01] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending SSE event: {'type': 'progress_update', 'data': {'step': 'memory_storage', 'progress': 90, 'message': '💾 Storing analysis for future reference...', 'timestamp': '2025-09-29T11:21:01.458044', 'details': {'step': 'memory_storage'}, 'tool_results': None}} | Data: {}
[2025-09-29 16:51:01] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Waiting for progress update for session async_98d0b880defc | Data: {}
[2025-09-29 16:51:02] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Mapped step 'complete' to complete | Data: {}
[2025-09-29 16:51:02] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Created progress update: ProgressUpdate(step=<WorkflowStep.COMPLETE: 'complete'>, progress=100, message='🎉 Analysis completed successfully!', timestamp='2025-09-29T11:21:02.279211', details={'step': 'complete', 'workflow_status': 'complete'}, tool_results=None) | Data: {}
[2025-09-29 16:51:02] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔥 BROADCASTING update for session async_98d0b880defc | Data: {'step': 'complete', 'progress': 100, 'message': '🎉 Analysis completed successfully!', 'has_clients': True, 'client_count': 1, 'broadcaster_id': 2644555443536, 'all_sessions': ['async_98d0b880defc']}
[2025-09-29 16:51:02] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Sent update to client 0 for session async_98d0b880defc | Data: {}
[2025-09-29 16:51:02] [INFO] [progress_debug] 📡 PROGRESS_BROADCAST | ✅ SUCCESS | Session: async_98d0b880defc | Step: complete | Details: {'workflow_step': 'complete', 'progress': 100, 'message': '🎉 Analysis completed successfully!'}
[2025-09-29 16:51:02] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Received progress update: {'step': 'complete', 'progress': 100, 'message': '🎉 Analysis completed successfully!', 'timestamp': '2025-09-29T11:21:02.279211', 'details': {'step': 'complete', 'workflow_status': 'complete'}, 'tool_results': None} | Data: {}
[2025-09-29 16:51:02] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Sending SSE event: {'type': 'progress_update', 'data': {'step': 'complete', 'progress': 100, 'message': '🎉 Analysis completed successfully!', 'timestamp': '2025-09-29T11:21:02.279211', 'details': {'step': 'complete', 'workflow_status': 'complete'}, 'tool_results': None}} | Data: {}
[2025-09-29 16:51:02] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Workflow complete, sending: {'type': 'complete', 'session_id': 'async_98d0b880defc'} | Data: {}
[2025-09-29 16:51:02] [INFO] [progress_debug] 🔌 SSE_DISCONNECT | Session: async_98d0b880defc | Reason: event_generator_cleanup_normal_completion
[2025-09-29 16:51:02] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Removed client for session async_98d0b880defc | Data: {'remaining_clients': 0}
[2025-09-29 16:51:02] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Cleaned up empty client list for session async_98d0b880defc | Data: {}
[2025-09-29 16:51:02] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Keeping buffered updates for potential reconnection: async_98d0b880defc | Data: {}
[2025-09-29 16:51:02] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Buffered updates count: 11 | Data: {}
[2025-09-29 16:51:02] [DEBUG] [progress_debug] 🐛 DEBUG | SSE_GENERATOR | Successfully cleaned up client for session async_98d0b880defc | Data: {}
[2025-09-29 16:51:02] [INFO] [progress_debug] 🔄 WORKFLOW_STEP | Session: async_98d0b880defc | Step: memory_storage | Progress: 90% | Message: 💾 Memory storage completed
[2025-09-29 16:51:03] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Mapped step 'memory_storage' to memory_storage | Data: {}
[2025-09-29 16:51:03] [DEBUG] [progress_debug] 🐛 DEBUG | WORKFLOW | Created progress update: ProgressUpdate(step=<WorkflowStep.MEMORY_STORAGE: 'memory_storage'>, progress=90, message='💾 Memory storage completed', timestamp='2025-09-29T11:21:03.133901', details={'node': 'memory_storage'}, tool_results=None) | Data: {}
[2025-09-29 16:51:03] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔥 BROADCASTING update for session async_98d0b880defc | Data: {'step': 'memory_storage', 'progress': 90, 'message': '💾 Memory storage completed', 'has_clients': False, 'client_count': 0, 'broadcaster_id': 2644555443536, 'all_sessions': []}
[2025-09-29 16:51:03] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | No clients found for session async_98d0b880defc - update buffered (total: 12) | Data: {}
[2025-09-29 16:51:03] [INFO] [progress_debug] 📡 PROGRESS_BROADCAST | ✅ SUCCESS | Session: async_98d0b880defc | Step: memory_storage | Details: {'workflow_step': 'memory_storage', 'progress': 90, 'message': '💾 Memory storage completed'}
[2025-09-29 16:51:03] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔥 BROADCASTING update for session async_98d0b880defc | Data: {'step': 'complete', 'progress': 100, 'message': '✅ AI analysis workflow completed successfully!', 'has_clients': False, 'client_count': 0, 'broadcaster_id': 2644555443536, 'all_sessions': []}
[2025-09-29 16:51:03] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | No clients found for session async_98d0b880defc - update buffered (total: 13) | Data: {}
[2025-09-29 16:51:03] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔥 BROADCASTING update for session async_98d0b880defc | Data: {'step': 'complete', 'progress': 100, 'message': '✅ AI analysis workflow completed successfully!', 'has_clients': False, 'client_count': 0, 'broadcaster_id': 2644555443536, 'all_sessions': []}
[2025-09-29 16:51:03] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | No clients found for session async_98d0b880defc - update buffered (total: 14) | Data: {}
[2025-09-29 16:51:09] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | 🔥 BROADCASTING update for session async_98d0b880defc | Data: {'step': 'complete', 'progress': 100, 'message': '🎉 Analysis completed successfully!', 'has_clients': False, 'client_count': 0, 'broadcaster_id': 2644555443536, 'all_sessions': []}
[2025-09-29 16:51:09] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | No clients found for session async_98d0b880defc - update buffered (total: 15) | Data: {}
[2025-09-29 16:51:09] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Marked session async_98d0b880defc as complete | Data: {}
[2025-09-29 16:51:09] [DEBUG] [progress_debug] 🐛 DEBUG | BROADCASTER | Cleaned up buffered updates for completed session async_98d0b880defc | Data: {}
