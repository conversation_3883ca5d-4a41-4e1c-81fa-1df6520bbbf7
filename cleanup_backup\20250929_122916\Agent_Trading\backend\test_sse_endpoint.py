#!/usr/bin/env python3
"""
Test SSE endpoint directly to identify connection issues
"""

import sys
import os
import asyncio
import requests
import json
from datetime import datetime

# Add the backend directory to the path
sys.path.append(os.path.dirname(__file__))

async def test_sse_endpoint():
    """Test the SSE endpoint directly."""
    print("🔍 Testing SSE Endpoint...")
    
    try:
        # Test 1: Check if backend is running
        try:
            response = requests.get("http://localhost:8000/api/v1/health", timeout=5)
            if response.status_code == 200:
                print("✅ Backend is running and accessible")
            else:
                print(f"❌ Backend health check failed: {response.status_code}")
                return
        except requests.exceptions.RequestException as e:
            print(f"❌ Backend is not running: {e}")
            print("💡 Please start the backend first: python -m uvicorn main:app --reload --host 0.0.0.0 --port 8000")
            return
        
        # Test 2: Test SSE endpoint without authentication
        test_session_id = "test_sse_session_123"
        sse_url = f"http://localhost:8000/api/v1/progress/stream/{test_session_id}"
        
        print(f"🔗 Testing SSE endpoint: {sse_url}")
        
        try:
            # Try to connect to SSE endpoint
            response = requests.get(sse_url, stream=True, timeout=10)
            print(f"📊 SSE Response Status: {response.status_code}")
            print(f"📊 SSE Response Headers: {dict(response.headers)}")
            
            if response.status_code == 200:
                print("✅ SSE endpoint is accessible")
                
                # Read a few SSE events
                print("📡 Reading SSE events...")
                lines_read = 0
                for line in response.iter_lines(decode_unicode=True):
                    if line and lines_read < 5:
                        print(f"📨 SSE Event: {line}")
                        lines_read += 1
                    elif lines_read >= 5:
                        break
                        
            elif response.status_code == 401:
                print("❌ SSE endpoint requires authentication")
                print("💡 This explains why the frontend SSE connection fails!")
                
                # Test with a dummy token
                print("🔐 Testing with dummy token...")
                sse_url_with_token = f"{sse_url}?token=dummy_token"
                response2 = requests.get(sse_url_with_token, stream=True, timeout=5)
                print(f"📊 SSE Response with token: {response2.status_code}")
                
            else:
                print(f"❌ SSE endpoint error: {response.status_code}")
                print(f"📄 Response text: {response.text}")
                
        except requests.exceptions.RequestException as e:
            print(f"❌ SSE connection failed: {e}")
        
        # Test 3: Test progress broadcasting
        print("\n🔄 Testing progress broadcasting...")
        
        from app.helpers.workflow_management.progress_tracker import (
            get_progress_tracker, 
            ProgressBroadcaster,
            WorkflowStep
        )
        
        # Create a test session and broadcast updates
        tracker = get_progress_tracker()
        broadcaster = ProgressBroadcaster()
        
        session_id = "test_broadcast_session_456"
        tracker.create_session(session_id)
        
        # Simulate workflow steps
        steps = [
            (WorkflowStep.CHART_ANALYSIS, "🔍 Analyzing chart patterns..."),
            (WorkflowStep.SYMBOL_DETECTION, "🎯 Detecting trading symbol..."),
            (WorkflowStep.TOOL_EXECUTION, "📊 Fetching market data..."),
            (WorkflowStep.TOOL_SUMMARIZATION, "🧠 Processing data with AI..."),
            (WorkflowStep.FINAL_ANALYSIS, "⚡ Generating trading insights..."),
            (WorkflowStep.COMPLETE, "🎉 Analysis completed!")
        ]
        
        print(f"📡 Broadcasting {len(steps)} progress updates...")
        
        for step, message in steps:
            update = tracker.update_progress(session_id, step, message)
            await broadcaster.broadcast_update(session_id, update)
            print(f"✅ Broadcasted: {step.value} ({update.progress}%) - {message}")
            await asyncio.sleep(0.5)  # Small delay between updates
        
        print("\n🎉 SSE endpoint test completed!")
        print("📁 Check progress_debug.log for detailed debug output")
        
    except Exception as e:
        print(f"❌ SSE endpoint test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_sse_endpoint())
