#!/usr/bin/env python3
"""
Comprehensive Test for Refactored Agent Trading System
=====================================================

This script tests all the refactored components to ensure they work correctly:
1. Progress bar timing fixes
2. Unified logging system
3. Clean directory structure
4. End-to-end workflow functionality

Features:
- Real-time progress monitoring
- Logging system validation
- Performance metrics
- Detailed error reporting
"""

import asyncio
import requests
import json
import time
import base64
from datetime import datetime
from pathlib import Path
import subprocess
import sys

class RefactoredSystemTest:
    """Comprehensive test suite for the refactored system."""
    
    def __init__(self):
        self.base_url = "http://localhost:8000"
        self.frontend_url = "http://localhost:3001"
        self.test_results = {}
        self.session_id = None
        self.start_time = None
        
    def log_test(self, test_name: str, status: str, details: str = ""):
        """Log test results."""
        self.test_results[test_name] = {
            "status": status,
            "details": details,
            "timestamp": datetime.now().isoformat()
        }
        
        status_emoji = "✅" if status == "PASS" else "❌" if status == "FAIL" else "⚠️"
        print(f"{status_emoji} {test_name}: {status}")
        if details:
            print(f"   {details}")
    
    def test_unified_logging_system(self):
        """Test the new unified logging system."""
        print("\n📋 TESTING UNIFIED LOGGING SYSTEM")
        print("=" * 50)
        
        try:
            # Check if unified logging directory structure exists
            log_base = Path("Agent_Trading/backend/logs")
            expected_dirs = [
                "application",
                "analysis/workflows",
                "analysis/llm_interactions",
                "progress/sessions",
                "api",
                "errors",
                "archive"
            ]
            
            missing_dirs = []
            for dir_path in expected_dirs:
                full_path = log_base / dir_path
                if not full_path.exists():
                    missing_dirs.append(dir_path)
            
            if missing_dirs:
                self.log_test("Unified Logging Structure", "FAIL", 
                             f"Missing directories: {missing_dirs}")
            else:
                self.log_test("Unified Logging Structure", "PASS", 
                             "All required log directories exist")
            
            # Test unified logger import
            try:
                sys.path.append("Agent_Trading/backend")
                from app.core.unified_logging import get_unified_logger
                logger = get_unified_logger()
                
                # Test logging functionality
                logger.log_application("info", "Test message from refactored system")
                self.log_test("Unified Logger Import", "PASS", 
                             "Successfully imported and used unified logger")
            except Exception as e:
                self.log_test("Unified Logger Import", "FAIL", str(e))
                
        except Exception as e:
            self.log_test("Unified Logging System", "FAIL", str(e))
    
    def test_legacy_cleanup(self):
        """Test that legacy files have been cleaned up."""
        print("\n🧹 TESTING LEGACY FILE CLEANUP")
        print("=" * 50)
        
        # Check that legacy test files are removed
        legacy_files = [
            "Agent_Trading/test_data_transformation.py",
            "Agent_Trading/test_frontend_fixes.py",
            "Agent_Trading/test_progress_flow.py",
            "Agent_Trading/test_result_debug.py",
            "Agent_Trading/test_result_endpoint.py",
            "Agent_Trading/test_result_simple.py",
            "COMPREHENSIVE_SOLUTION_SUMMARY.md",
            "REFACTORING_COMPLETE_SUMMARY.md",
            "PROGRESS_BAR_ARCHITECTURE_ANALYSIS.md"
        ]
        
        remaining_files = []
        for file_path in legacy_files:
            if Path(file_path).exists():
                remaining_files.append(file_path)
        
        if remaining_files:
            self.log_test("Legacy File Cleanup", "WARN", 
                         f"Legacy files still exist: {remaining_files}")
        else:
            self.log_test("Legacy File Cleanup", "PASS", 
                         "All legacy files have been cleaned up")
        
        # Check for nested duplicate directories
        nested_dir = Path("Agent_Trading/backend/Agent_Trading")
        if nested_dir.exists():
            self.log_test("Nested Directory Cleanup", "FAIL", 
                         "Nested Agent_Trading directory still exists")
        else:
            self.log_test("Nested Directory Cleanup", "PASS", 
                         "No nested duplicate directories found")
    
    def test_authentication(self):
        """Test authentication system."""
        print("\n🔐 TESTING AUTHENTICATION")
        print("=" * 50)
        
        try:
            login_response = requests.post(f"{self.base_url}/api/v1/auth/login", json={
                "email": "<EMAIL>",
                "password": "Bunnych@1627"
            }, timeout=10)
            
            if login_response.status_code == 200:
                self.token = login_response.json()["access_token"]
                self.headers = {"Authorization": f"Bearer {self.token}"}
                self.log_test("Authentication", "PASS", "Login successful")
                return True
            else:
                self.log_test("Authentication", "FAIL", 
                             f"Login failed: {login_response.status_code}")
                return False
                
        except Exception as e:
            self.log_test("Authentication", "FAIL", str(e))
            return False
    
    def test_progress_bar_timing(self):
        """Test the progress bar timing fix."""
        print("\n⏱️ TESTING PROGRESS BAR TIMING FIX")
        print("=" * 50)
        
        if not hasattr(self, 'token'):
            self.log_test("Progress Bar Timing", "SKIP", "Authentication required")
            return
        
        try:
            # Create a simple test chart
            test_image = self.create_test_chart()
            
            # Start analysis and immediately monitor progress
            self.start_time = time.time()
            
            # Prepare request data for the correct endpoint
            request_data = {
                "chart_image_base64": base64.b64encode(test_image).decode('utf-8'),
                "analysis_type": "comprehensive",
                "include_sentiment": True,
                "include_technical": True,
                "include_fundamental": True
            }

            analysis_response = requests.post(
                f"{self.base_url}/api/v1/trading/async/start",
                json=request_data,
                headers=self.headers,
                timeout=30
            )
            
            if analysis_response.status_code == 200:
                result = analysis_response.json()
                self.session_id = result.get("session_id")
                
                if self.session_id:
                    self.log_test("Analysis Start", "PASS", 
                                 f"Analysis started with session: {self.session_id}")
                    
                    # Test progress monitoring
                    self.monitor_progress_timing()
                else:
                    self.log_test("Analysis Start", "FAIL", "No session ID returned")
            else:
                self.log_test("Analysis Start", "FAIL", 
                             f"Analysis failed: {analysis_response.status_code}")
                
        except Exception as e:
            self.log_test("Progress Bar Timing", "FAIL", str(e))
    
    def monitor_progress_timing(self):
        """Monitor progress updates to test timing fix."""
        if not self.session_id:
            return
        
        print(f"📊 Monitoring progress for session: {self.session_id}")
        
        progress_updates = []
        connection_time = None
        first_update_time = None
        
        try:
            # Test SSE connection timing
            sse_url = f"{self.base_url}/api/v1/progress/stream/{self.session_id}?token={self.token}"
            print(f"🔗 Connecting to SSE: {sse_url}")
            
            import sseclient  # You may need to install: pip install sseclient-py
            
            connection_start = time.time()
            response = requests.get(sse_url, headers=self.headers, stream=True, timeout=60)
            connection_time = time.time() - connection_start
            
            if response.status_code == 200:
                client = sseclient.SSEClient(response)
                
                for event in client.events():
                    if event.data:
                        try:
                            data = json.loads(event.data)
                            update_time = time.time() - self.start_time
                            
                            if first_update_time is None:
                                first_update_time = update_time
                            
                            progress_updates.append({
                                "time": update_time,
                                "step": data.get("step"),
                                "progress": data.get("progress"),
                                "message": data.get("message")
                            })
                            
                            print(f"   📈 {data.get('step')} ({data.get('progress')}%) - {data.get('message')}")
                            
                            # Stop monitoring after completion or timeout
                            if data.get("step") == "complete" or len(progress_updates) > 10:
                                break
                                
                        except json.JSONDecodeError:
                            continue
            
            # Analyze timing results
            self.analyze_progress_timing(connection_time, first_update_time, progress_updates)
            
        except ImportError:
            self.log_test("Progress Monitoring", "SKIP", 
                         "sseclient-py not installed. Install with: pip install sseclient-py")
        except Exception as e:
            self.log_test("Progress Monitoring", "FAIL", str(e))
    
    def analyze_progress_timing(self, connection_time, first_update_time, updates):
        """Analyze progress timing to verify the fix."""
        if not updates:
            self.log_test("Progress Updates", "FAIL", "No progress updates received")
            return
        
        # Check connection timing
        if connection_time and connection_time < 2.0:
            self.log_test("SSE Connection Speed", "PASS", 
                         f"Connected in {connection_time:.2f}s")
        else:
            self.log_test("SSE Connection Speed", "WARN", 
                         f"Connection took {connection_time:.2f}s")
        
        # Check if we received multiple progress steps
        unique_steps = set(update.get("step") for update in updates if update.get("step"))
        
        if len(unique_steps) >= 3:
            self.log_test("Progress Bar Fix", "PASS", 
                         f"Received {len(unique_steps)} different progress steps")
        elif len(unique_steps) == 1 and "complete" in unique_steps:
            self.log_test("Progress Bar Fix", "FAIL", 
                         "Only received completion message - timing issue persists")
        else:
            self.log_test("Progress Bar Fix", "WARN", 
                         f"Received {len(unique_steps)} progress steps")
        
        # Check progress sequence
        progress_values = [update.get("progress", 0) for update in updates if update.get("progress")]
        if len(progress_values) > 1 and progress_values == sorted(progress_values):
            self.log_test("Progress Sequence", "PASS", 
                         "Progress values increase correctly")
        else:
            self.log_test("Progress Sequence", "WARN", 
                         f"Progress sequence: {progress_values}")
    
    def create_test_chart(self):
        """Create a simple test chart image."""
        try:
            from PIL import Image, ImageDraw
            import io
            
            # Create a simple chart-like image
            img = Image.new('RGB', (800, 600), color='white')
            draw = ImageDraw.Draw(img)
            
            # Draw some chart-like elements
            draw.rectangle([100, 100, 700, 500], outline='black', width=2)
            draw.line([150, 450, 250, 350, 350, 300, 450, 250, 550, 200], fill='blue', width=3)
            draw.text((350, 50), "Test Chart", fill='black')
            
            # Convert to bytes
            img_bytes = io.BytesIO()
            img.save(img_bytes, format='PNG')
            return img_bytes.getvalue()
            
        except ImportError:
            # Fallback: create a minimal PNG
            return base64.b64decode(
                "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=="
            )
    
    def generate_test_report(self):
        """Generate comprehensive test report."""
        print("\n📊 REFACTORED SYSTEM TEST REPORT")
        print("=" * 60)
        
        total_tests = len(self.test_results)
        passed_tests = len([r for r in self.test_results.values() if r["status"] == "PASS"])
        failed_tests = len([r for r in self.test_results.values() if r["status"] == "FAIL"])
        warned_tests = len([r for r in self.test_results.values() if r["status"] == "WARN"])
        skipped_tests = len([r for r in self.test_results.values() if r["status"] == "SKIP"])
        
        print(f"📈 Test Summary:")
        print(f"   Total Tests: {total_tests}")
        print(f"   ✅ Passed: {passed_tests}")
        print(f"   ❌ Failed: {failed_tests}")
        print(f"   ⚠️  Warnings: {warned_tests}")
        print(f"   ⏭️  Skipped: {skipped_tests}")
        
        success_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0
        print(f"   🎯 Success Rate: {success_rate:.1f}%")
        
        print(f"\n📋 Detailed Results:")
        for test_name, result in self.test_results.items():
            status_emoji = {
                "PASS": "✅",
                "FAIL": "❌", 
                "WARN": "⚠️",
                "SKIP": "⏭️"
            }.get(result["status"], "❓")
            
            print(f"   {status_emoji} {test_name}: {result['status']}")
            if result["details"]:
                print(f"      {result['details']}")
        
        # Save report to file
        report_file = f"refactoring_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w') as f:
            json.dump({
                "test_timestamp": datetime.now().isoformat(),
                "summary": {
                    "total_tests": total_tests,
                    "passed": passed_tests,
                    "failed": failed_tests,
                    "warnings": warned_tests,
                    "skipped": skipped_tests,
                    "success_rate": success_rate
                },
                "detailed_results": self.test_results
            }, f, indent=2)
        
        print(f"\n📄 Report saved: {report_file}")
        
        return success_rate >= 80  # Consider 80%+ success rate as overall pass
    
    def run_all_tests(self):
        """Run all refactoring tests."""
        print("🧪 COMPREHENSIVE REFACTORED SYSTEM TEST")
        print("=" * 60)
        print(f"Start time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # Run tests in order
        self.test_unified_logging_system()
        self.test_legacy_cleanup()
        
        if self.test_authentication():
            self.test_progress_bar_timing()
        
        # Generate final report
        success = self.generate_test_report()
        
        if success:
            print("\n🎉 REFACTORING TESTS COMPLETED SUCCESSFULLY!")
            print("The refactored system is working correctly.")
        else:
            print("\n⚠️  REFACTORING TESTS COMPLETED WITH ISSUES")
            print("Some components may need additional attention.")
        
        return success

def main():
    """Main test function."""
    tester = RefactoredSystemTest()
    return tester.run_all_tests()

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
