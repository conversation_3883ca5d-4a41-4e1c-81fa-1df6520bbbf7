#!/usr/bin/env python3
"""
Test Progress Buffering System
==============================

This script tests the new progress buffering mechanism to ensure
that progress updates are properly buffered when no SSE clients
are connected and then sent when clients connect.
"""

import asyncio
import sys
import os

# Add backend to path
backend_path = os.path.join(os.path.dirname(__file__), '..', 'backend')
if backend_path not in sys.path:
    sys.path.insert(0, backend_path)

from app.helpers.workflow_management.progress_tracker import (
    get_progress_broadcaster,
    get_progress_tracker,
    WorkflowStep
)

async def test_progress_buffering():
    """Test that progress updates are buffered when no clients are connected"""
    print("🧪 Testing Progress Buffering System")
    print("=" * 50)
    
    # Get global instances
    broadcaster = get_progress_broadcaster()
    tracker = get_progress_tracker()
    
    # Create a test session
    session_id = "test_buffering_session"
    tracker.create_session(session_id)
    
    print(f"📊 Created test session: {session_id}")
    
    # Step 1: Send progress updates WITHOUT any connected clients
    print("\n1️⃣ Sending progress updates without connected clients...")
    
    test_steps = [
        (WorkflowStep.CHART_ANALYSIS, "🔍 Starting chart analysis..."),
        (WorkflowStep.SYMBOL_DETECTION, "🎯 Detecting trading symbol..."),
        (WorkflowStep.TOOL_EXECUTION, "⚙️ Executing market tools..."),
    ]
    
    for step, message in test_steps:
        update = tracker.update_progress(session_id, step, message)
        await broadcaster.broadcast_update(session_id, update)
        print(f"📤 Sent: {step.value} - {message}")
        await asyncio.sleep(0.1)
    
    # Check if updates were buffered
    if session_id in broadcaster.buffered_updates:
        buffered_count = len(broadcaster.buffered_updates[session_id])
        print(f"✅ {buffered_count} updates buffered successfully")
    else:
        print("❌ No updates were buffered")
        return False
    
    # Step 2: Connect a client and verify buffered updates are received
    print("\n2️⃣ Connecting SSE client...")
    
    client_queue = await broadcaster.add_client(session_id)
    print("✅ SSE client connected")
    
    # Step 3: Check if buffered updates were sent to the client
    print("\n3️⃣ Checking if buffered updates were received...")
    
    received_updates = []
    try:
        # Try to receive all buffered updates
        for i in range(buffered_count):
            update = await asyncio.wait_for(client_queue.get(), timeout=1.0)
            received_updates.append(update)
            print(f"📥 Received: {update.get('step')} - {update.get('message', 'No message')}")
    except asyncio.TimeoutError:
        print(f"⚠️ Timeout waiting for update {i+1}")
    
    # Step 4: Send new updates and verify they're received immediately
    print("\n4️⃣ Sending new updates with connected client...")
    
    new_steps = [
        (WorkflowStep.TOOL_SUMMARIZATION, "📊 Summarizing data..."),
        (WorkflowStep.FINAL_ANALYSIS, "📈 Generating analysis..."),
    ]
    
    for step, message in new_steps:
        update = tracker.update_progress(session_id, step, message)
        await broadcaster.broadcast_update(session_id, update)
        print(f"📤 Sent: {step.value} - {message}")
        
        try:
            received_update = await asyncio.wait_for(client_queue.get(), timeout=1.0)
            print(f"📥 Immediately received: {received_update.get('step')} - {received_update.get('message', 'No message')}")
        except asyncio.TimeoutError:
            print("❌ Failed to receive immediate update")
            return False
        
        await asyncio.sleep(0.1)
    
    # Step 5: Cleanup
    print("\n5️⃣ Cleaning up...")
    broadcaster.remove_client(session_id, client_queue)
    print("✅ Client removed")
    
    # Verify buffered updates were cleared
    if session_id not in broadcaster.buffered_updates:
        print("✅ Buffered updates cleared")
    else:
        print("⚠️ Buffered updates not cleared")
    
    print("\n🎉 Progress buffering test completed successfully!")
    return True

if __name__ == "__main__":
    try:
        result = asyncio.run(test_progress_buffering())
        if result:
            print("\n✅ All tests passed!")
        else:
            print("\n❌ Some tests failed!")
            sys.exit(1)
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
