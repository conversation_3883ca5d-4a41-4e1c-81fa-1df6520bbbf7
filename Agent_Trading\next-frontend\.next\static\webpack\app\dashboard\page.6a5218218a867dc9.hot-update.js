"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/hooks/useRealTimeProgress.ts":
/*!******************************************!*\
  !*** ./src/hooks/useRealTimeProgress.ts ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WORKFLOW_STEPS: () => (/* binding */ WORKFLOW_STEPS),\n/* harmony export */   useRealTimeProgress: () => (/* binding */ useRealTimeProgress)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/**\r\n * Real-time progress tracking hook for LangGraph AI analysis workflow\r\n * Uses Server-Sent Events ONLY for live updates (no polling fallback)\r\n */ \n// ✅ ALIGNED: Frontend mapping now matches actual LangGraph workflow execution\nconst WORKFLOW_STEPS = {\n    CHART_ANALYSIS: {\n        name: 'Chart Analysis',\n        progress: 20,\n        message: '📊 Starting comprehensive AI analysis workflow...'\n    },\n    SYMBOL_DETECTION: {\n        name: 'Symbol Detection',\n        progress: 40,\n        message: '✅ Symbol detected in chart'\n    },\n    TOOL_EXECUTION: {\n        name: 'Data Collection',\n        progress: 50,\n        message: '🛠️ Collecting data from tools...'\n    },\n    TOOL_SUMMARIZATION: {\n        name: 'Data Processing',\n        progress: 60,\n        message: '📋 Processing and summarizing data...'\n    },\n    RAG_INTEGRATION: {\n        name: 'RAG Integration',\n        progress: 70,\n        message: '🧠 Integrating historical context and patterns...'\n    },\n    FINAL_ANALYSIS: {\n        name: 'Final Analysis',\n        progress: 80,\n        message: '🎯 Generating final trading analysis...'\n    },\n    MEMORY_STORAGE: {\n        name: 'Database Storage',\n        progress: 90,\n        message: '💾 Saving analysis...'\n    },\n    COMPLETE: {\n        name: 'Complete',\n        progress: 100,\n        message: '🎉 Analysis complete!'\n    }\n};\nfunction useRealTimeProgress() {\n    const [state, setState] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({\n        sessionId: null,\n        currentStep: 'chart_analysis',\n        progress: 0,\n        message: 'Preparing for analysis...',\n        isConnected: false,\n        isComplete: false,\n        error: null,\n        updates: []\n    });\n    // Add retry state\n    const [retryCount, setRetryCount] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(0);\n    const maxRetries = 3;\n    // 🔧 CRITICAL FIX: Progress smoothing for rapid updates\n    const [progressQueue, setProgressQueue] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [isProcessingQueue, setIsProcessingQueue] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    // Process progress queue with smooth animations\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useRealTimeProgress.useEffect\": ()=>{\n            if (progressQueue.length > 0 && !isProcessingQueue) {\n                setIsProcessingQueue(true);\n                const processNext = {\n                    \"useRealTimeProgress.useEffect.processNext\": ()=>{\n                        setProgressQueue({\n                            \"useRealTimeProgress.useEffect.processNext\": (queue)=>{\n                                if (queue.length === 0) {\n                                    setIsProcessingQueue(false);\n                                    return queue;\n                                }\n                                const [nextUpdate, ...remaining] = queue;\n                                // Apply the update\n                                setState({\n                                    \"useRealTimeProgress.useEffect.processNext\": (prev)=>({\n                                            ...prev,\n                                            currentStep: nextUpdate.step,\n                                            progress: nextUpdate.progress,\n                                            message: nextUpdate.message\n                                        })\n                                }[\"useRealTimeProgress.useEffect.processNext\"]);\n                                // 🔧 IMPROVED: Dynamic delay based on queue length and progress difference\n                                let delay = 400 // Base delay reduced from 800ms to 400ms\n                                ;\n                                if (remaining.length > 0) {\n                                    const nextProgress = remaining[0].progress;\n                                    const currentProgress = nextUpdate.progress;\n                                    const progressDiff = nextProgress - currentProgress;\n                                    // Shorter delay for small progress jumps, longer for big jumps\n                                    if (progressDiff <= 10) {\n                                        delay = 300; // Fast updates for small increments\n                                    } else if (progressDiff <= 20) {\n                                        delay = 500; // Medium delay for medium increments\n                                    } else {\n                                        delay = 700; // Longer delay for big jumps\n                                    }\n                                    // If queue is getting long, speed up processing\n                                    if (remaining.length > 3) {\n                                        delay = Math.max(200, delay * 0.6); // Speed up but not too fast\n                                    }\n                                    console.log(\"\\uD83D\\uDCC8 FRONTEND_DEBUG: Processing queue - current: \".concat(currentProgress, \"%, next: \").concat(nextProgress, \"%, delay: \").concat(delay, \"ms, queue length: \").concat(remaining.length));\n                                    setTimeout(processNext, delay);\n                                } else {\n                                    setIsProcessingQueue(false);\n                                }\n                                return remaining;\n                            }\n                        }[\"useRealTimeProgress.useEffect.processNext\"]);\n                    }\n                }[\"useRealTimeProgress.useEffect.processNext\"];\n                processNext();\n            }\n        }\n    }[\"useRealTimeProgress.useEffect\"], [\n        progressQueue,\n        isProcessingQueue\n    ]);\n    const eventSourceRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const currentSessionRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    // Retry connection function\n    const retryConnection = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useRealTimeProgress.useCallback[retryConnection]\": async function(sessionId) {\n            let attempt = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 1;\n            if (attempt > maxRetries) {\n                console.error(\"❌ FRONTEND_DEBUG: Max retries (\".concat(maxRetries, \") exceeded for session \").concat(sessionId));\n                setState({\n                    \"useRealTimeProgress.useCallback[retryConnection]\": (prev)=>({\n                            ...prev,\n                            error: \"Connection failed after \".concat(maxRetries, \" attempts. Please refresh and try again.\")\n                        })\n                }[\"useRealTimeProgress.useCallback[retryConnection]\"]);\n                return;\n            }\n            console.log(\"\\uD83D\\uDD04 FRONTEND_DEBUG: Retry attempt \".concat(attempt, \"/\").concat(maxRetries, \" for session \").concat(sessionId));\n            setState({\n                \"useRealTimeProgress.useCallback[retryConnection]\": (prev)=>({\n                        ...prev,\n                        error: null,\n                        message: \"Reconnecting... (attempt \".concat(attempt, \"/\").concat(maxRetries, \")\")\n                    })\n            }[\"useRealTimeProgress.useCallback[retryConnection]\"]);\n            // Wait before retry (exponential backoff)\n            const delay = Math.min(1000 * Math.pow(2, attempt - 1), 5000);\n            await new Promise({\n                \"useRealTimeProgress.useCallback[retryConnection]\": (resolve)=>setTimeout(resolve, delay)\n            }[\"useRealTimeProgress.useCallback[retryConnection]\"]);\n            // Retry the connection\n            await connectToProgress(sessionId);\n        }\n    }[\"useRealTimeProgress.useCallback[retryConnection]\"], [\n        maxRetries\n    ]);\n    // Check if analysis is already complete\n    const checkAnalysisStatus = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useRealTimeProgress.useCallback[checkAnalysisStatus]\": async (sessionId)=>{\n            try {\n                const token = localStorage.getItem('access_token');\n                const baseURL = \"http://localhost:8000\" || 0;\n                const response = await fetch(\"\".concat(baseURL, \"/api/v1/async-trading/status/\").concat(sessionId), {\n                    headers: {\n                        'Authorization': \"Bearer \".concat(token),\n                        'Content-Type': 'application/json'\n                    }\n                });\n                if (response.ok) {\n                    const data = await response.json();\n                    if (data.status === 'completed') {\n                        setState({\n                            \"useRealTimeProgress.useCallback[checkAnalysisStatus]\": (prev)=>({\n                                    ...prev,\n                                    isComplete: true,\n                                    progress: 100,\n                                    currentStep: 'complete',\n                                    message: 'Analysis completed successfully!'\n                                })\n                        }[\"useRealTimeProgress.useCallback[checkAnalysisStatus]\"]);\n                        return true;\n                    }\n                }\n            } catch (error) {\n                console.error('Failed to check analysis status:', error);\n            }\n            return false;\n        }\n    }[\"useRealTimeProgress.useCallback[checkAnalysisStatus]\"], []);\n    const connectToProgress = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useRealTimeProgress.useCallback[connectToProgress]\": async (sessionId)=>{\n            // Close existing SSE connection\n            if (eventSourceRef.current) {\n                eventSourceRef.current.close();\n            }\n            // Store current session for retry purposes\n            currentSessionRef.current = sessionId;\n            // 🔧 CRITICAL FIX: Update state with the provided session ID IMMEDIATELY\n            // This ensures the UI shows the correct session ID right away\n            console.log('🔧 FRONTEND_DEBUG: Setting session ID immediately:', sessionId);\n            setState({\n                \"useRealTimeProgress.useCallback[connectToProgress]\": (prev)=>({\n                        ...prev,\n                        sessionId,\n                        error: null,\n                        currentStep: 'chart_analysis',\n                        progress: 0,\n                        message: 'Connecting to progress stream...',\n                        isConnected: false,\n                        isComplete: false\n                    })\n            }[\"useRealTimeProgress.useCallback[connectToProgress]\"]);\n            // Reset retry count for new connection\n            setRetryCount(0);\n            // First check if analysis is already complete\n            const isComplete = await checkAnalysisStatus(sessionId);\n            if (isComplete) {\n                return;\n            }\n            const token = localStorage.getItem('access_token');\n            const baseURL = \"http://localhost:8000\" || 0;\n            if (!token) {\n                console.error('❌ No access token found for SSE connection');\n                setState({\n                    \"useRealTimeProgress.useCallback[connectToProgress]\": (prev)=>({\n                            ...prev,\n                            error: 'Authentication required for progress tracking'\n                        })\n                }[\"useRealTimeProgress.useCallback[connectToProgress]\"]);\n                return;\n            }\n            // Try SSE first - Fixed URL construction and added better error handling\n            const sseUrl = \"\".concat(baseURL, \"/api/v1/progress/stream/\").concat(sessionId, \"?token=\").concat(encodeURIComponent(token));\n            console.log('🔗 FRONTEND_DEBUG: Attempting SSE connection');\n            console.log('🔗 FRONTEND_DEBUG: SSE URL:', sseUrl);\n            console.log('🔗 FRONTEND_DEBUG: Session ID:', sessionId);\n            console.log('🔗 FRONTEND_DEBUG: Token present:', !!token);\n            console.log('🔗 FRONTEND_DEBUG: Token length:', token === null || token === void 0 ? void 0 : token.length);\n            console.log('🔗 FRONTEND_DEBUG: Base URL:', baseURL);\n            // Create EventSource with proper error handling\n            let eventSource;\n            try {\n                eventSource = new EventSource(sseUrl);\n                console.log('✅ FRONTEND_DEBUG: EventSource created successfully');\n            } catch (error) {\n                console.error('❌ FRONTEND_DEBUG: Failed to create EventSource:', error);\n                setState({\n                    \"useRealTimeProgress.useCallback[connectToProgress]\": (prev)=>({\n                            ...prev,\n                            error: \"Failed to create SSE connection: \".concat(error)\n                        })\n                }[\"useRealTimeProgress.useCallback[connectToProgress]\"]);\n                return;\n            }\n            eventSourceRef.current = eventSource;\n            let sseConnected = false;\n            // SSE connection timeout (no polling fallback)\n            const sseTimeout = setTimeout({\n                \"useRealTimeProgress.useCallback[connectToProgress].sseTimeout\": ()=>{\n                    if (!sseConnected) {\n                        console.log('⚠️ SSE connection timeout - no polling fallback');\n                        eventSource.close();\n                        setState({\n                            \"useRealTimeProgress.useCallback[connectToProgress].sseTimeout\": (prev)=>({\n                                    ...prev,\n                                    isConnected: false,\n                                    error: 'SSE connection timeout - check backend connection'\n                                })\n                        }[\"useRealTimeProgress.useCallback[connectToProgress].sseTimeout\"]);\n                    }\n                }\n            }[\"useRealTimeProgress.useCallback[connectToProgress].sseTimeout\"], 10000) // Increased timeout to 10 seconds\n            ;\n            eventSource.onopen = ({\n                \"useRealTimeProgress.useCallback[connectToProgress]\": ()=>{\n                    console.log('✅ FRONTEND_DEBUG: SSE connection established');\n                    console.log('✅ FRONTEND_DEBUG: EventSource readyState:', eventSource.readyState);\n                    console.log('✅ FRONTEND_DEBUG: EventSource URL:', eventSource.url);\n                    sseConnected = true;\n                    clearTimeout(sseTimeout);\n                    setState({\n                        \"useRealTimeProgress.useCallback[connectToProgress]\": (prev)=>({\n                                ...prev,\n                                isConnected: true,\n                                error: null,\n                                message: 'Connected to progress stream'\n                            })\n                    }[\"useRealTimeProgress.useCallback[connectToProgress]\"]);\n                }\n            })[\"useRealTimeProgress.useCallback[connectToProgress]\"];\n            eventSource.onmessage = ({\n                \"useRealTimeProgress.useCallback[connectToProgress]\": (event)=>{\n                    try {\n                        console.log('📨 FRONTEND_DEBUG: Raw SSE message received:', event.data);\n                        const data = JSON.parse(event.data);\n                        console.log('📨 FRONTEND_DEBUG: Parsed SSE data:', data);\n                        console.log('📨 FRONTEND_DEBUG: Event type:', data.type);\n                        switch(data.type){\n                            case 'connected':\n                                console.log('✅ FRONTEND_DEBUG: Progress stream connected for session:', data.session_id);\n                                break;\n                            case 'progress_update':\n                                const update = data.data;\n                                console.log('📈 FRONTEND_DEBUG: SSE Progress update:', update);\n                                // Map backend step to frontend display\n                                const stepInfo = getStepInfo(update.step);\n                                console.log('📈 FRONTEND_DEBUG: Step info mapped:', stepInfo);\n                                // 🔧 CRITICAL DEBUG: Log the exact state update\n                                console.log('📈 FRONTEND_DEBUG: About to update state with:', {\n                                    currentStep: update.step,\n                                    progress: stepInfo.progress,\n                                    message: stepInfo.message,\n                                    backendProgress: update.progress,\n                                    frontendProgress: stepInfo.progress\n                                });\n                                // 🔧 CRITICAL FIX: Use progress queue for smooth animations\n                                setState({\n                                    \"useRealTimeProgress.useCallback[connectToProgress]\": (prev)=>{\n                                        // Only queue if this is actually a new step or higher progress\n                                        const isNewStep = update.step !== prev.currentStep;\n                                        const isHigherProgress = stepInfo.progress > prev.progress;\n                                        if (isNewStep || isHigherProgress) {\n                                            console.log('📈 FRONTEND_DEBUG: Queueing progress update:', stepInfo.progress, 'step:', update.step);\n                                            // Add to progress queue for smooth processing\n                                            setProgressQueue({\n                                                \"useRealTimeProgress.useCallback[connectToProgress]\": (queue)=>[\n                                                        ...queue,\n                                                        {\n                                                            step: update.step,\n                                                            progress: stepInfo.progress,\n                                                            message: stepInfo.message\n                                                        }\n                                                    ]\n                                            }[\"useRealTimeProgress.useCallback[connectToProgress]\"]);\n                                            // Update the updates array immediately\n                                            return {\n                                                ...prev,\n                                                updates: [\n                                                    ...prev.updates,\n                                                    update\n                                                ]\n                                            };\n                                        } else {\n                                            console.log('📈 FRONTEND_DEBUG: Skipping duplicate/lower progress update:', stepInfo.progress, 'current:', prev.progress);\n                                            return prev;\n                                        }\n                                    }\n                                }[\"useRealTimeProgress.useCallback[connectToProgress]\"]);\n                                break;\n                            case 'complete':\n                                console.log('🎉 SSE Analysis complete!');\n                                setState({\n                                    \"useRealTimeProgress.useCallback[connectToProgress]\": (prev)=>({\n                                            ...prev,\n                                            isComplete: true,\n                                            progress: 100,\n                                            currentStep: 'complete',\n                                            message: 'Analysis completed successfully!'\n                                        })\n                                }[\"useRealTimeProgress.useCallback[connectToProgress]\"]);\n                                eventSource.close();\n                                break;\n                            case 'ping':\n                                break;\n                            case 'error':\n                                console.error('❌ SSE Progress stream error:', data.error);\n                                setState({\n                                    \"useRealTimeProgress.useCallback[connectToProgress]\": (prev)=>({\n                                            ...prev,\n                                            error: data.error,\n                                            isConnected: false\n                                        })\n                                }[\"useRealTimeProgress.useCallback[connectToProgress]\"]);\n                                eventSource.close();\n                                break;\n                        }\n                    } catch (error) {\n                        console.error('Failed to parse SSE progress update:', error);\n                    }\n                }\n            })[\"useRealTimeProgress.useCallback[connectToProgress]\"];\n            eventSource.onerror = ({\n                \"useRealTimeProgress.useCallback[connectToProgress]\": (error)=>{\n                    console.error('❌ FRONTEND_DEBUG: SSE EventSource error:', error);\n                    console.error('❌ FRONTEND_DEBUG: EventSource readyState:', eventSource.readyState);\n                    console.error('❌ FRONTEND_DEBUG: EventSource URL:', sseUrl);\n                    clearTimeout(sseTimeout);\n                    setState({\n                        \"useRealTimeProgress.useCallback[connectToProgress]\": (prev)=>({\n                                ...prev,\n                                isConnected: false\n                            })\n                    }[\"useRealTimeProgress.useCallback[connectToProgress]\"]);\n                    eventSource.close();\n                    // Attempt retry if we have a current session and haven't exceeded max retries\n                    if (currentSessionRef.current && retryCount < maxRetries) {\n                        setRetryCount({\n                            \"useRealTimeProgress.useCallback[connectToProgress]\": (prev)=>prev + 1\n                        }[\"useRealTimeProgress.useCallback[connectToProgress]\"]);\n                        retryConnection(currentSessionRef.current, retryCount + 1);\n                    } else {\n                        // Provide more specific error messages based on readyState\n                        let errorMessage = 'SSE connection failed';\n                        if (eventSource.readyState === EventSource.CONNECTING) {\n                            errorMessage = 'Failed to establish SSE connection - check backend status';\n                        } else if (eventSource.readyState === EventSource.CLOSED) {\n                            errorMessage = 'SSE connection was closed unexpectedly';\n                        }\n                        setState({\n                            \"useRealTimeProgress.useCallback[connectToProgress]\": (prev)=>({\n                                    ...prev,\n                                    error: errorMessage\n                                })\n                        }[\"useRealTimeProgress.useCallback[connectToProgress]\"]);\n                    }\n                }\n            })[\"useRealTimeProgress.useCallback[connectToProgress]\"];\n        }\n    }[\"useRealTimeProgress.useCallback[connectToProgress]\"], []);\n    // Connection health check\n    const checkConnectionHealth = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useRealTimeProgress.useCallback[checkConnectionHealth]\": async ()=>{\n            try {\n                const baseURL = \"http://localhost:8000\" || 0;\n                const response = await fetch(\"\".concat(baseURL, \"/api/v1/health\"), {\n                    method: 'GET',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    }\n                });\n                if (response.ok) {\n                    console.log('✅ FRONTEND_DEBUG: Backend health check passed');\n                    return true;\n                } else {\n                    console.error('❌ FRONTEND_DEBUG: Backend health check failed:', response.status);\n                    return false;\n                }\n            } catch (error) {\n                console.error('❌ FRONTEND_DEBUG: Backend health check error:', error);\n                return false;\n            }\n        }\n    }[\"useRealTimeProgress.useCallback[checkConnectionHealth]\"], []);\n    // Helper function to map backend steps to frontend display\n    const getStepInfo = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useRealTimeProgress.useCallback[getStepInfo]\": (step)=>{\n            switch(step){\n                case 'chart_analysis':\n                    return WORKFLOW_STEPS.CHART_ANALYSIS;\n                case 'symbol_detection':\n                    return WORKFLOW_STEPS.SYMBOL_DETECTION;\n                case 'tool_execution':\n                    return WORKFLOW_STEPS.TOOL_EXECUTION;\n                case 'tool_summarization':\n                    return WORKFLOW_STEPS.TOOL_SUMMARIZATION;\n                case 'rag_integration':\n                    return WORKFLOW_STEPS.RAG_INTEGRATION;\n                case 'final_analysis':\n                    return WORKFLOW_STEPS.FINAL_ANALYSIS;\n                case 'memory_storage':\n                    return WORKFLOW_STEPS.MEMORY_STORAGE;\n                case 'complete':\n                    return WORKFLOW_STEPS.COMPLETE;\n                default:\n                    return {\n                        name: 'Processing',\n                        progress: 50,\n                        message: 'Processing...'\n                    };\n            }\n        }\n    }[\"useRealTimeProgress.useCallback[getStepInfo]\"], []);\n    // Polling removed - SSE only implementation\n    const startProgressTracking = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useRealTimeProgress.useCallback[startProgressTracking]\": async (backendSessionId)=>{\n            if (backendSessionId) {\n                // Use backend session ID directly\n                await connectToProgress(backendSessionId);\n                return backendSessionId;\n            } else {\n                // Fallback: generate frontend session ID if backend doesn't provide one\n                const sessionId = \"frontend_\".concat(Date.now(), \"_\").concat(Math.random().toString(36).substring(2, 11));\n                setState({\n                    \"useRealTimeProgress.useCallback[startProgressTracking]\": (prev)=>({\n                            ...prev,\n                            sessionId,\n                            error: null\n                        })\n                }[\"useRealTimeProgress.useCallback[startProgressTracking]\"]);\n                await connectToProgress(sessionId);\n                console.log('📊 Generated fallback progress session:', sessionId);\n                return sessionId;\n            }\n        }\n    }[\"useRealTimeProgress.useCallback[startProgressTracking]\"], [\n        connectToProgress\n    ]);\n    const cleanup = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useRealTimeProgress.useCallback[cleanup]\": async (sessionId)=>{\n            // Close EventSource connection\n            if (eventSourceRef.current) {\n                eventSourceRef.current.close();\n                eventSourceRef.current = null;\n            }\n            // No polling to clear - SSE only\n            console.log('📊 Progress session cleanup completed:', sessionId || state.sessionId);\n            // Reset state\n            setState({\n                sessionId: null,\n                currentStep: 'chart_analysis',\n                progress: 0,\n                message: 'Preparing for analysis...',\n                isConnected: false,\n                isComplete: false,\n                error: null,\n                updates: []\n            });\n        }\n    }[\"useRealTimeProgress.useCallback[cleanup]\"], [\n        state.sessionId\n    ]);\n    // Cleanup on unmount\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useRealTimeProgress.useEffect\": ()=>{\n            return ({\n                \"useRealTimeProgress.useEffect\": ()=>{\n                    if (eventSourceRef.current) {\n                        eventSourceRef.current.close();\n                    }\n                }\n            })[\"useRealTimeProgress.useEffect\"];\n        }\n    }[\"useRealTimeProgress.useEffect\"], []);\n    return {\n        ...state,\n        startProgressTracking,\n        connectToProgress,\n        cleanup,\n        checkConnectionHealth,\n        retryConnection,\n        WORKFLOW_STEPS\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useRealTimeProgress.ts\n"));

/***/ })

});