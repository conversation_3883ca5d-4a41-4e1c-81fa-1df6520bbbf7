#!/usr/bin/env python3
"""
Test LangGraph Streaming Fix
============================

This script tests the LangGraph streaming fix to ensure
real-time progress updates are sent during workflow execution.
"""

import sys
import os
import json
import asyncio
import time
import requests
from pathlib import Path
from datetime import datetime

class StreamingFixTester:
    def __init__(self):
        self.base_url = "http://localhost:8000"
        self.token = None
        self.session_id = None
        
    async def login(self):
        """Login with test credentials."""
        print("🔐 Logging in...")
        
        login_data = {
            "email": "<EMAIL>",  # 🔧 FIX: Use lowercase email (database stores emails in lowercase)
            "password": "Bunnych@1627"
        }
        
        try:
            response = requests.post(
                f"{self.base_url}/api/v1/auth/login",
                json=login_data,
                headers={"Content-Type": "application/json"}
            )
            
            if response.status_code == 200:
                data = response.json()
                self.token = data.get("access_token")
                print(f"✅ Login successful")
                return True
            else:
                print(f"❌ Login failed: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ Login error: {e}")
            return False
    
    async def start_analysis_and_monitor_sse(self):
        """Start analysis and monitor SSE for real-time updates."""
        if not self.token:
            print("❌ No authentication token")
            return False
        
        # 🔧 USE REAL TRADING CHART IMAGE for proper testing
        import base64
        from pathlib import Path

        # Look for real chart images in the project
        chart_paths = [
            "Agent_Trading/tests/test_data/sample_chart.png",
            "Agent_Trading/backend/test_data/sample_chart.png",
            "Agent_Trading/test_data/sample_chart.png",
            "test_data/sample_chart.png",
            "sample_chart.png"
        ]

        test_image_b64 = None
        for chart_path in chart_paths:
            if Path(chart_path).exists():
                print(f"📊 Using real chart image: {chart_path}")
                with open(chart_path, 'rb') as f:
                    test_image_b64 = base64.b64encode(f.read()).decode('utf-8')
                break

        if not test_image_b64:
            print("⚠️  No real chart images found, creating a larger test image...")
            # Create a more realistic test chart (512x512 pixels)
            from PIL import Image, ImageDraw
            import io
            import random

            img = Image.new('RGB', (512, 512), color='white')
            draw = ImageDraw.Draw(img)

            # Draw chart background
            draw.rectangle([50, 50, 462, 462], outline='black', width=2)

            # Draw candlestick-like chart data
            for i in range(20):
                x = 60 + i * 20
                high = random.randint(100, 200)
                low = random.randint(300, 400)
                open_price = random.randint(high, low)
                close_price = random.randint(high, low)

                # Draw candlestick
                draw.line([x, high, x, low], fill='black', width=1)
                color = 'green' if close_price > open_price else 'red'
                draw.rectangle([x-5, min(open_price, close_price), x+5, max(open_price, close_price)],
                             fill=color, outline='black')

            # Add title and labels
            draw.text((200, 20), "BTC/USDT Chart", fill='black')
            draw.text((20, 250), "Price", fill='black')

            img_buffer = io.BytesIO()
            img.save(img_buffer, format='PNG')
            test_image_b64 = base64.b64encode(img_buffer.getvalue()).decode('utf-8')
            print(f"📊 Created realistic test chart (512x512 pixels, {len(test_image_b64)} chars)")

        # Start analysis
        print("🚀 Starting analysis...")

        analysis_data = {
            "images_base64": [test_image_b64],
            "analysis_type": "Positional",  # 🔧 FIX: Use correct capitalization
            "market_specialization": "Crypto"
        }
        
        headers = {
            "Authorization": f"Bearer {self.token}",
            "Content-Type": "application/json"
        }
        
        try:
            # 🔧 FIX: Use correct endpoint for async analysis
            response = requests.post(
                f"{self.base_url}/api/v1/trading/async/start",
                json=analysis_data,
                headers=headers
            )
            
            if response.status_code == 200:
                data = response.json()
                self.session_id = data.get("session_id")
                print(f"✅ Analysis started: {self.session_id}")
                
                # Immediately start monitoring SSE
                await self.monitor_sse_real_time()
                
                return True
            else:
                print(f"❌ Analysis failed: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ Analysis error: {e}")
            return False
    
    async def monitor_sse_real_time(self):
        """Monitor SSE connection for real-time progress updates."""
        if not self.session_id or not self.token:
            print("❌ Missing session ID or token")
            return False
        
        print(f"\n📡 MONITORING SSE FOR REAL-TIME UPDATES")
        print(f"Session: {self.session_id}")
        print("=" * 60)
        
        sse_url = f"{self.base_url}/api/v1/progress/stream/{self.session_id}?token={self.token}"
        
        progress_updates = []
        start_time = time.time()
        last_update_time = start_time
        
        try:
            response = requests.get(sse_url, stream=True, timeout=120)
            
            if response.status_code != 200:
                print(f"❌ SSE connection failed: {response.status_code}")
                return False
            
            print(f"✅ SSE connection established")
            
            for line in response.iter_lines(decode_unicode=True):
                if line.startswith('data:'):
                    try:
                        current_time = time.time()
                        time_since_start = current_time - start_time
                        time_since_last = current_time - last_update_time
                        
                        data = json.loads(line[5:])  # Remove 'data:' prefix
                        event_type = data.get('type', 'unknown')
                        
                        if event_type == 'connected':
                            print(f"🔗 [{time_since_start:.1f}s] Connected to SSE stream")
                        
                        elif event_type == 'progress_update':
                            progress_data = data.get('data', {})
                            step = progress_data.get('step', 'unknown')
                            progress = progress_data.get('progress', 0)
                            message = progress_data.get('message', 'No message')
                            
                            # Record the update
                            update_info = {
                                'step': step,
                                'progress': progress,
                                'message': message,
                                'time_since_start': time_since_start,
                                'time_since_last': time_since_last
                            }
                            progress_updates.append(update_info)
                            
                            print(f"📈 [{time_since_start:.1f}s] (+{time_since_last:.1f}s) {step} ({progress}%) - {message}")
                            last_update_time = current_time
                        
                        elif event_type == 'complete':
                            print(f"🎉 [{time_since_start:.1f}s] Analysis completed!")
                            break
                        
                        elif event_type == 'ping':
                            # Don't log pings, but update last seen time
                            pass
                        
                        elif event_type == 'error':
                            error_msg = data.get('error', 'Unknown error')
                            print(f"❌ [{time_since_start:.1f}s] Error: {error_msg}")
                            break
                        
                    except json.JSONDecodeError:
                        continue
            
            # Analyze the results
            print(f"\n📊 STREAMING ANALYSIS RESULTS")
            print("=" * 60)
            
            total_time = time.time() - start_time
            print(f"Total analysis time: {total_time:.1f} seconds")
            print(f"Total progress updates received: {len(progress_updates)}")
            
            if len(progress_updates) == 0:
                print(f"❌ NO PROGRESS UPDATES RECEIVED!")
                print(f"   This indicates the streaming fix is NOT working")
                return False
            
            elif len(progress_updates) == 1:
                print(f"⚠️  ONLY ONE PROGRESS UPDATE RECEIVED!")
                print(f"   This suggests updates are still batched at the end")
                return False
            
            else:
                print(f"✅ MULTIPLE PROGRESS UPDATES RECEIVED!")
                print(f"   This indicates the streaming fix IS working")
                
                # Show timing analysis
                print(f"\n📈 Progress Update Timeline:")
                for i, update in enumerate(progress_updates):
                    print(f"   {i+1}. [{update['time_since_start']:.1f}s] {update['step']} ({update['progress']}%)")
                
                # Check if updates are spread out over time
                time_gaps = [update['time_since_last'] for update in progress_updates[1:]]
                avg_gap = sum(time_gaps) / len(time_gaps) if time_gaps else 0
                
                print(f"\n⏱️  Timing Analysis:")
                print(f"   Average time between updates: {avg_gap:.1f} seconds")
                
                if avg_gap > 5:
                    print(f"   ✅ Updates are spread out over time (real-time streaming)")
                    return True
                else:
                    print(f"   ⚠️  Updates came in rapid succession (possibly batched)")
                    return len(progress_updates) >= 3  # At least some progress
                
        except Exception as e:
            print(f"❌ SSE monitoring error: {e}")
            return False

async def main():
    """Main test function."""
    print("🔍 TESTING LANGGRAPH STREAMING FIX")
    print(f"Start time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    tester = StreamingFixTester()
    
    # Test login
    login_ok = await tester.login()
    if not login_ok:
        print("❌ Login failed, cannot test streaming")
        return False
    
    # Test streaming
    streaming_ok = await tester.start_analysis_and_monitor_sse()
    
    print(f"\n{'='*60}")
    print("📊 STREAMING FIX TEST SUMMARY:")
    print(f"   Authentication: {'✅ WORKING' if login_ok else '❌ FAILED'}")
    print(f"   Real-time Streaming: {'✅ WORKING' if streaming_ok else '❌ FAILED'}")
    
    if streaming_ok:
        print(f"\n🎉 SUCCESS! LangGraph streaming fix is working!")
        print(f"   Progress updates are now sent in real-time during workflow execution")
        print(f"   The frontend should show smooth progress bar progression")
    else:
        print(f"\n❌ STREAMING FIX FAILED!")
        print(f"   Progress updates are still being batched at the end")
        print(f"   The workflow is not using astream() correctly")
    
    return streaming_ok

if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
