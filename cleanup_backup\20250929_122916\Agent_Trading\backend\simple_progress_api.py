#!/usr/bin/env python3
"""
Simple polling-based progress API as SSE alternative
"""

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
import json
import time
from typing import Dict, Any

app = FastAPI(title="Simple Progress API")

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://127.0.0.1:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# In-memory progress storage
progress_store: Dict[str, Dict[str, Any]] = {}

@app.get("/")
async def root():
    return {"message": "Simple Progress API is running!"}

@app.get("/api/v1/progress/{session_id}")
async def get_progress(session_id: str):
    """Get current progress for a session."""
    if session_id not in progress_store:
        return {
            "session_id": session_id,
            "step": "not_started",
            "progress": 0,
            "message": "Analysis not started",
            "is_complete": False,
            "error": None
        }
    
    return progress_store[session_id]

@app.post("/api/v1/progress/{session_id}")
async def update_progress(session_id: str, update: dict):
    """Update progress for a session."""
    progress_store[session_id] = {
        "session_id": session_id,
        "step": update.get("step", "unknown"),
        "progress": update.get("progress", 0),
        "message": update.get("message", ""),
        "is_complete": update.get("progress", 0) >= 100,
        "error": update.get("error"),
        "timestamp": time.time()
    }
    
    return {"status": "updated", "data": progress_store[session_id]}

@app.post("/api/v1/progress/{session_id}/simulate")
async def simulate_progress(session_id: str):
    """Simulate a complete analysis workflow for testing."""
    import asyncio
    
    steps = [
        ("chart_analysis", 12.5, "🔍 Analyzing chart patterns..."),
        ("symbol_detection", 25, "🎯 Detecting trading symbol..."),
        ("tool_execution", 37.5, "📊 Fetching market data..."),
        ("tool_summarization", 50, "🧠 Processing data with AI..."),
        ("rag_integration", 62.5, "💾 Integrating historical context..."),
        ("final_analysis", 75, "⚡ Generating trading insights..."),
        ("memory_storage", 87.5, "💾 Storing analysis for future reference..."),
        ("complete", 100, "🎉 Analysis completed!")
    ]
    
    # Start background task to update progress
    async def update_progress_task():
        for step, progress, message in steps:
            progress_store[session_id] = {
                "session_id": session_id,
                "step": step,
                "progress": progress,
                "message": message,
                "is_complete": progress >= 100,
                "error": None,
                "timestamp": time.time()
            }
            await asyncio.sleep(2)  # 2 second delay between updates
    
    # Start the background task
    asyncio.create_task(update_progress_task())
    
    return {"status": "simulation_started", "session_id": session_id}

if __name__ == "__main__":
    import uvicorn
    print("🚀 Starting Simple Progress API...")
    print("📊 Progress endpoint: http://localhost:8001/api/v1/progress/{session_id}")
    print("🧪 Test simulation: http://localhost:8001/api/v1/progress/{session_id}/simulate")
    
    uvicorn.run(app, host="0.0.0.0", port=8001, log_level="info")
