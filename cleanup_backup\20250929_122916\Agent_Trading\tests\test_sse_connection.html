<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SSE Connection Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .log {
            background: #f5f5f5;
            border: 1px solid #ddd;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            max-height: 400px;
            overflow-y: auto;
        }
        .progress-bar {
            width: 100%;
            height: 20px;
            background: #f0f0f0;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4CAF50, #45a049);
            transition: width 0.3s ease;
            width: 0%;
        }
        .step {
            padding: 5px 10px;
            margin: 5px 0;
            border-radius: 4px;
            background: #e9e9e9;
        }
        .step.active {
            background: #4CAF50;
            color: white;
        }
        .step.completed {
            background: #2196F3;
            color: white;
        }
        button {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .start-btn {
            background: #4CAF50;
            color: white;
        }
        .test-btn {
            background: #2196F3;
            color: white;
        }
    </style>
</head>
<body>
    <h1>SSE Progress Tracking Test</h1>
    
    <div>
        <button class="test-btn" onclick="testSSEConnection()">Test SSE Connection</button>
        <button class="start-btn" onclick="startAnalysis()">Start Analysis & Monitor</button>
        <button onclick="clearLog()">Clear Log</button>
    </div>

    <div>
        <h3>Progress: <span id="progress-text">0%</span></h3>
        <div class="progress-bar">
            <div class="progress-fill" id="progress-fill"></div>
        </div>
        <div id="current-step">Ready to start...</div>
    </div>

    <div>
        <h3>Workflow Steps:</h3>
        <div id="steps-container">
            <div class="step" data-step="chart_analysis">Chart Analysis (20%)</div>
            <div class="step" data-step="symbol_detection">Symbol Detection (30%)</div>
            <div class="step" data-step="tool_execution">Tool Execution (50%)</div>
            <div class="step" data-step="tool_summarization">Tool Summarization (60%)</div>
            <div class="step" data-step="rag_integration">RAG Integration (70%)</div>
            <div class="step" data-step="final_analysis">Final Analysis (80%)</div>
            <div class="step" data-step="memory_storage">Memory Storage (90%)</div>
            <div class="step" data-step="complete">Complete (100%)</div>
        </div>
    </div>

    <div>
        <h3>Debug Log:</h3>
        <div class="log" id="log"></div>
    </div>

    <script>
        let eventSource = null;
        let currentSessionId = null;

        function log(message) {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}<br>`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }

        function updateProgress(step, progress, message) {
            document.getElementById('progress-text').textContent = `${progress}%`;
            document.getElementById('progress-fill').style.width = `${progress}%`;
            document.getElementById('current-step').textContent = `${step}: ${message}`;

            // Update step visualization
            const steps = document.querySelectorAll('.step');
            steps.forEach(stepEl => {
                const stepId = stepEl.getAttribute('data-step');
                stepEl.classList.remove('active', 'completed');
                
                if (stepId === step) {
                    stepEl.classList.add('active');
                } else {
                    // Mark previous steps as completed
                    const stepOrder = ['chart_analysis', 'symbol_detection', 'tool_execution', 'tool_summarization', 'rag_integration', 'final_analysis', 'memory_storage', 'complete'];
                    const currentIndex = stepOrder.indexOf(step);
                    const stepIndex = stepOrder.indexOf(stepId);
                    if (stepIndex < currentIndex) {
                        stepEl.classList.add('completed');
                    }
                }
            });
        }

        function testSSEConnection() {
            log('🧪 Testing SSE connection to backend...');
            
            // Test with a dummy session ID
            const testSessionId = 'test_' + Date.now();
            const baseURL = 'http://localhost:8000';
            const token = localStorage.getItem('access_token') || 'dummy_token';
            const sseUrl = `${baseURL}/api/v1/progress/stream/${testSessionId}?token=${encodeURIComponent(token)}`;
            
            log(`🔗 SSE URL: ${sseUrl}`);
            
            const testEventSource = new EventSource(sseUrl);
            
            testEventSource.onopen = () => {
                log('✅ SSE connection opened successfully');
                setTimeout(() => {
                    testEventSource.close();
                    log('🔌 Test SSE connection closed');
                }, 5000);
            };
            
            testEventSource.onmessage = (event) => {
                log(`📨 SSE message received: ${event.data}`);
            };
            
            testEventSource.onerror = (error) => {
                log(`❌ SSE connection error: ${error.type}`);
                testEventSource.close();
            };
        }

        async function startAnalysis() {
            log('🚀 Starting analysis...');

            try {
                // First, let's try to get a test token by logging in
                log('🔐 Attempting to get authentication token...');

                const loginResponse = await fetch('http://localhost:8000/api/v1/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: 'Bunnych@1627'
                    })
                });

                if (!loginResponse.ok) {
                    throw new Error(`Login failed: ${loginResponse.status} ${loginResponse.statusText}`);
                }

                const loginResult = await loginResponse.json();
                const token = loginResult.access_token;
                log(`✅ Authentication successful, token obtained`);

                // Create a simple test image
                const canvas = document.createElement('canvas');
                canvas.width = 100;
                canvas.height = 100;
                const ctx = canvas.getContext('2d');
                ctx.fillStyle = 'blue';
                ctx.fillRect(0, 0, 100, 100);

                // Convert canvas to base64
                const imageBase64 = canvas.toDataURL('image/jpeg').split(',')[1];

                const requestData = {
                    images_base64: [imageBase64],
                    analysis_type: 'Positional',
                    market_specialization: 'Crypto',
                    preferred_model: 'gemini-2.5-flash'
                };

                log('📤 Sending analysis request...');
                const response = await fetch('http://localhost:8000/api/v1/trading/async/start', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    body: JSON.stringify(requestData)
                });
                    
                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }
                    
                    const result = await response.json();
                    currentSessionId = result.session_id;
                    log(`✅ Analysis started with session: ${currentSessionId}`);

                    // Start monitoring progress
                    monitorProgress(currentSessionId);
                
            } catch (error) {
                log(`❌ Failed to start analysis: ${error.message}`);
                log(`❌ Error details: ${JSON.stringify(error)}`);
                console.error('Full error object:', error);
            }
        }

        function monitorProgress(sessionId) {
            log(`📡 Starting SSE monitoring for session: ${sessionId}`);
            
            const baseURL = 'http://localhost:8000';
            const token = localStorage.getItem('access_token') || 'dummy_token';
            const sseUrl = `${baseURL}/api/v1/progress/stream/${sessionId}?token=${encodeURIComponent(token)}`;
            
            log(`🔗 Connecting to: ${sseUrl}`);
            
            eventSource = new EventSource(sseUrl);
            
            eventSource.onopen = () => {
                log('✅ SSE connection established for progress monitoring');
            };
            
            eventSource.onmessage = (event) => {
                try {
                    log(`📨 Raw SSE message: ${event.data}`);
                    const data = JSON.parse(event.data);
                    log(`📨 Parsed SSE data: ${JSON.stringify(data)}`);
                    
                    switch (data.type) {
                        case 'connected':
                            log(`✅ Progress stream connected for session: ${data.session_id}`);
                            break;
                            
                        case 'progress_update':
                            const update = data.data;
                            log(`📈 Progress update: ${update.step} (${update.progress}%) - ${update.message}`);
                            updateProgress(update.step, update.progress, update.message);
                            break;
                            
                        case 'complete':
                            log('🎉 Analysis completed!');
                            updateProgress('complete', 100, 'Analysis completed successfully!');
                            eventSource.close();
                            break;
                            
                        case 'ping':
                            log('🏓 Keepalive ping received');
                            break;
                            
                        default:
                            log(`❓ Unknown SSE event type: ${data.type}`);
                    }
                } catch (error) {
                    log(`❌ Error parsing SSE message: ${error.message}`);
                }
            };
            
            eventSource.onerror = (error) => {
                log(`❌ SSE connection error: ${error.type}`);
                if (eventSource.readyState === EventSource.CLOSED) {
                    log('🔌 SSE connection closed');
                }
            };
        }

        // Initialize
        log('🔧 SSE Progress Tracking Test initialized');
        log('💡 Click "Test SSE Connection" to test the connection, or "Start Analysis & Monitor" to run a full test');
    </script>
</body>
</html>
