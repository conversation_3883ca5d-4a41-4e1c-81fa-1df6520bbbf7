{"workflow_id": "trading_analysis_1759092117", "start_time": "2025-09-29T02:11:57.053041", "context": {"analysis_mode": "positional", "market_specialization": "Crypto", "chart_count": 1}, "steps": [{"step_number": 1, "step_name": "Chart Analysis", "step_type": "llm_call", "timestamp": "2025-09-29T02:11:57.071631", "execution_time": null, "status": "success", "error": null, "input_summary": "Chart image for symbol detection"}, {"step_number": 2, "step_name": "Chart Analysis", "step_type": "llm_call", "timestamp": "2025-09-29T02:12:19.954571", "execution_time": 22.882939100265503, "status": "success", "error": null, "output_summary": "Dict with keys: ['detected_symbol', 'market_type', 'timeframe', 'current_price', 'trend_direction']..."}, {"step_number": 3, "step_name": "Step 2: Tool Selection", "step_type": "api_calls", "timestamp": "2025-09-29T02:12:19.968555", "execution_time": null, "status": "success", "error": null, "input_summary": "Market: crypto, Symbol: BTCUSD"}, {"step_number": 4, "step_name": "Step 2: Tool Selection", "step_type": "api_calls", "timestamp": "2025-09-29T02:12:25.048191", "execution_time": 5.079636573791504, "status": "success", "error": null, "output_summary": "Dict with keys: ['success', 'tool_results', 'tool_usage_log', 'execution_time']"}, {"step_number": 5, "step_name": "Step 3: Flash Summarization", "step_type": "llm_call", "timestamp": "2025-09-29T02:12:25.053677", "execution_time": null, "status": "success", "error": null, "input_summary": "Tool results for summarization"}, {"step_number": 6, "step_name": "Step 3: Flash Summarization", "step_type": "llm_call", "timestamp": "2025-09-29T02:12:39.570985", "execution_time": 14.517308235168457, "status": "success", "error": null, "output_summary": "Dict with keys: ['success', 'summarized_tools', 'tool_count', 'comprehensive_summary']"}, {"step_number": 7, "step_name": "Step 4: Final Analysis", "step_type": "llm_call", "timestamp": "2025-09-29T02:12:39.802627", "execution_time": null, "status": "success", "error": null, "input_summary": "Chart + Tool summaries + RAG tool available"}, {"step_number": 8, "step_name": "Final Analysis", "step_type": "llm_call", "timestamp": "2025-09-29T02:13:25.675509", "execution_time": 45.86487603187561, "status": "success", "error": null, "output_summary": "Dict with keys: ['success', 'analysis', 'analysis_notes', 'trading_signals', 'detected_symbol']..."}], "llm_calls": [], "summary": {"total_steps": 8, "total_llm_calls": 0, "total_tokens": 0, "total_cost": 0.0, "execution_time": 88.64196705818176}, "end_time": "2025-09-29T02:13:25.695009", "status": "success", "error": null}