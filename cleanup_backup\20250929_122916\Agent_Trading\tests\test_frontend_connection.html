<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Frontend Connection Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #1a1a1a;
            color: #ffffff;
        }
        .container {
            background-color: #2a2a2a;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #45a049;
        }
        .log {
            background-color: #000;
            color: #00ff00;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
            margin-top: 10px;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success { background-color: #4CAF50; }
        .error { background-color: #f44336; }
        .warning { background-color: #ff9800; }
    </style>
</head>
<body>
    <h1>🔧 Frontend Connection Test</h1>
    
    <div class="container">
        <h2>Test Frontend → Backend Connection</h2>
        <p>This tests if the frontend can properly connect to the backend and authenticate.</p>
        
        <button onclick="testBackendConnection()">Test Backend Connection</button>
        <button onclick="testAuthentication()">Test Authentication</button>
        <button onclick="testSSEConnection()">Test SSE Connection</button>
        <button onclick="testCompleteFlow()">Test Complete Flow</button>
        <button onclick="clearLog()">Clear Log</button>
        
        <div id="status" class="status"></div>
        <div id="log" class="log"></div>
    </div>

    <script>
        const BASE_URL = 'http://localhost:8000';
        const EMAIL = '<EMAIL>';
        const PASSWORD = 'Bunnych@1627';
        
        let accessToken = null;

        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            const logElement = document.getElementById('log');
            logElement.textContent += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(message);
        }

        function setStatus(message, type = 'success') {
            const statusElement = document.getElementById('status');
            statusElement.textContent = message;
            statusElement.className = `status ${type}`;
        }

        function clearLog() {
            document.getElementById('log').textContent = '';
            document.getElementById('status').textContent = '';
        }

        async function testBackendConnection() {
            log('🔗 Testing backend connection...');
            
            try {
                const response = await fetch(`${BASE_URL}/health`);
                const data = await response.json();
                
                if (response.ok) {
                    log('✅ Backend connection successful');
                    log(`📊 Backend status: ${data.status}`);
                    setStatus('Backend connection successful', 'success');
                } else {
                    log(`❌ Backend connection failed: ${response.status}`);
                    setStatus('Backend connection failed', 'error');
                }
            } catch (error) {
                log(`❌ Backend connection error: ${error.message}`);
                setStatus('Backend connection error', 'error');
            }
        }

        async function testAuthentication() {
            log('🔐 Testing authentication...');
            
            try {
                const response = await fetch(`${BASE_URL}/api/v1/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        email: EMAIL,
                        password: PASSWORD
                    })
                });

                if (response.ok) {
                    const data = await response.json();
                    accessToken = data.access_token;
                    
                    log('✅ Authentication successful');
                    log(`👤 User: ${data.user.email}`);
                    log(`🎫 Token length: ${accessToken.length}`);
                    log(`⏰ Expires in: ${data.expires_in} seconds`);
                    
                    // Store token for other tests
                    localStorage.setItem('access_token', accessToken);
                    
                    setStatus('Authentication successful', 'success');
                } else {
                    const error = await response.text();
                    log(`❌ Authentication failed: ${response.status} - ${error}`);
                    setStatus('Authentication failed', 'error');
                }
            } catch (error) {
                log(`❌ Authentication error: ${error.message}`);
                setStatus('Authentication error', 'error');
            }
        }

        async function testSSEConnection() {
            log('📡 Testing SSE connection...');
            
            if (!accessToken) {
                log('⚠️ No access token. Running authentication first...');
                await testAuthentication();
                if (!accessToken) {
                    setStatus('SSE test failed - no authentication', 'error');
                    return;
                }
            }

            const testSessionId = 'test_frontend_' + Date.now();
            const sseUrl = `${BASE_URL}/api/v1/progress/stream/${testSessionId}?token=${encodeURIComponent(accessToken)}`;
            
            log(`🔗 SSE URL: ${sseUrl}`);
            
            try {
                const eventSource = new EventSource(sseUrl);
                let connectionEstablished = false;
                
                const timeout = setTimeout(() => {
                    if (!connectionEstablished) {
                        log('⏰ SSE connection timeout');
                        eventSource.close();
                        setStatus('SSE connection timeout', 'warning');
                    }
                }, 10000);

                eventSource.onopen = () => {
                    connectionEstablished = true;
                    clearTimeout(timeout);
                    log('✅ SSE connection opened successfully');
                    setStatus('SSE connection successful', 'success');
                    
                    // Close after 5 seconds
                    setTimeout(() => {
                        eventSource.close();
                        log('🔌 SSE connection closed');
                    }, 5000);
                };

                eventSource.onmessage = (event) => {
                    log(`📨 SSE message: ${event.data}`);
                };

                eventSource.onerror = (error) => {
                    clearTimeout(timeout);
                    log(`❌ SSE connection error: ${error}`);
                    log(`📊 EventSource readyState: ${eventSource.readyState}`);
                    setStatus('SSE connection failed', 'error');
                    eventSource.close();
                };

            } catch (error) {
                log(`❌ SSE setup error: ${error.message}`);
                setStatus('SSE setup error', 'error');
            }
        }

        async function testCompleteFlow() {
            log('🚀 Testing complete flow...');
            
            // Step 1: Backend connection
            await testBackendConnection();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            // Step 2: Authentication
            await testAuthentication();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            // Step 3: SSE connection
            await testSSEConnection();
            
            log('🎉 Complete flow test finished');
        }

        // Auto-run basic tests on page load
        window.onload = () => {
            log('🔧 Frontend Connection Test initialized');
            log('💡 Click buttons above to test different components');
        };
    </script>
</body>
</html>
