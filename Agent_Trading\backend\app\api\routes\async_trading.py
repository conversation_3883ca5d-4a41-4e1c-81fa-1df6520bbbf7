"""
Async Trading Analysis Routes
============================

New async endpoints that avoid frontend timeouts by:
1. Starting analysis in background immediately 
2. Returning session ID to frontend
3. Streaming progress via SSE
4. Providing result retrieval endpoint
"""

import time
import uuid
import json
import asyncio
from typing import Dict, Any, Optional
from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks
from fastapi.responses import StreamingResponse
from pydantic import BaseModel

from app.core.security import get_current_active_user, get_current_user
from app.models.user import User
from app.core.database import get_db, AsyncSession
from app.api.validators import AnalyzeRequestValidator, ImageValidator
from app.services.async_trading_service import async_trading_service
from app.helpers.workflow_management.progress_tracker import get_progress_tracker, get_progress_broadcaster, WorkflowStep
from app.core.logging import get_logger
from app.core.debug_logger import backend_debug, progress_debug, result_debug
from app.core.exceptions import ValidationError, HTTPExceptions

logger = get_logger("async_trading")
router = APIRouter(prefix="/trading/async", tags=["Async Trading Analysis"])

class AsyncAnalysisStartResponse(BaseModel):
    """Response for starting async analysis."""
    success: bool
    session_id: str
    message: str
    progress_stream_url: str
    result_check_url: str

class AsyncAnalysisResultResponse(BaseModel):
    """Response for checking analysis result."""
    success: bool
    session_id: str
    status: str  # "running", "completed", "failed"
    data: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None

@router.post("/start", response_model=AsyncAnalysisStartResponse)
async def start_async_analysis(
    request: AnalyzeRequestValidator,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db),
    progress_tracker = Depends(get_progress_tracker),
    progress_broadcaster = Depends(get_progress_broadcaster)
):
    """
    Start trading analysis in background.
    Returns immediately with session ID for progress tracking.
    """
    
    try:
        # Generate session ID
        session_id = f"async_{uuid.uuid4().hex[:12]}"

        # Clean debug logging
        backend_debug.separator("NEW ANALYSIS REQUEST")
        backend_debug.session_created(session_id, current_user.email)
        backend_debug.info(f"Analysis Type: {request.analysis_type}, Market: {request.market_specialization}")
        backend_debug.info(f"Images: {len(request.images_base64)}")

        logger.info(
            "Starting async trading analysis",
            user_id=current_user.id,
            session_id=session_id,
            analysis_type=request.analysis_type,
            market_specialization=request.market_specialization,
            image_count=len(request.images_base64)
        )
        
        # Validate user tier permissions and enforce model restrictions
        if request.preferred_model == "gemini-2.5-pro" and current_user.tier == "free":
            logger.warning(
                "Free tier user attempted to use premium model",
                user_id=current_user.id,
                requested_model=request.preferred_model
            )
            # Downgrade to flash model for free tier
            request.preferred_model = "gemini-2.5-flash"
            
        # Handle force refresh request
        if request.force_refresh:
            request.context_hint = (request.context_hint or "") + " force_refresh"
            logger.info(
                "Force refresh requested - bypassing cache",
                user_id=current_user.id,
                session_id=session_id
            )
        
        # Quick validation of images (detailed validation happens in background)
        for i, img_b64 in enumerate(request.images_base64):
            try:
                ImageValidator.validate_base64_image(img_b64)
            except ValidationError as e:
                raise HTTPExceptions.validation_error(
                    f"Image {i+1} validation failed: {str(e)}"
                )
        
        # Initialize progress tracking
        update = progress_tracker.update_progress(
            session_id,
            WorkflowStep.CHART_UPLOAD,
            "Analysis request received, starting background processing...",
            details={
                "user_id": str(current_user.id),
                "analysis_type": request.analysis_type,
                "image_count": len(request.images_base64)
            }
        )
        await progress_broadcaster.broadcast_update(session_id, update)
        
        # 🔧 FIX: Don't start analysis immediately - wait for SSE connection
        # Store the analysis request for later execution when SSE connects
        await async_trading_service.prepare_analysis_task(
            session_id=session_id,
            request=request,
            user=current_user,
            progress_tracker=progress_tracker,
            progress_broadcaster=progress_broadcaster
        )
        
        # Prepare message with model information
        base_message = "Analysis started in background. Use the provided URLs to track progress and get results."
        if current_user.tier == "free" and request.preferred_model == "gemini-2.5-flash":
            base_message += f" Note: Using Gemini 2.5 Flash (free tier). Upgrade for premium models."
        
        # Return immediate response with tracking URLs
        return AsyncAnalysisStartResponse(
            success=True,
            session_id=session_id,
            message=base_message,
            progress_stream_url=f"/api/v1/progress/stream/{session_id}",
            result_check_url=f"/api/v1/trading/async/result/{session_id}"
        )
        
    except Exception as e:
        logger.error(
            "Failed to start async analysis",
            error=str(e),
            user_id=current_user.id
        )
        raise HTTPExceptions.internal_server_error(f"Failed to start analysis: {str(e)}")

@router.get("/results/{session_id}")
async def get_analysis_results(
    session_id: str,
    current_user: User = Depends(get_current_user)
):
    """Get analysis results for a session"""
    try:
        logger.info(f"Getting results for session: {session_id}")
        logger.info(f"Active tasks: {list(async_trading_service.active_tasks.keys())}")
        logger.info(f"Cached results: {list(async_trading_service.results_cache.keys())}")

        # Get result from async trading service cache
        result = async_trading_service.get_analysis_result(session_id)
        logger.info(f"Retrieved result for {session_id}: {result is not None}")

        if result:
            logger.info(f"🔍 DEBUG: Result structure: {list(result.keys()) if isinstance(result, dict) else 'not a dict'}")
            if isinstance(result, dict):
                logger.info(f"🔍 DEBUG: Result success: {result.get('success')}")
                logger.info(f"🔍 DEBUG: Result has data: {result.get('data') is not None}")
                if result.get('data'):
                    data_keys = list(result['data'].keys()) if isinstance(result['data'], dict) else 'not a dict'
                    logger.info(f"🔍 DEBUG: Result data keys: {data_keys}")

        if not result:
            # Check if task is still running
            if session_id in async_trading_service.active_tasks:
                return AsyncAnalysisResultResponse(
                    success=True,
                    session_id=session_id,
                    status="running",
                    data=None,
                    error=None,
                    metadata=None
                )
            else:
                return AsyncAnalysisResultResponse(
                    success=False,
                    session_id=session_id,
                    status="not_found",
                    data=None,
                    error="Analysis results not found or expired",
                    metadata=None
                )

        # Return the cached result
        if result.get("success"):
            return AsyncAnalysisResultResponse(
                success=True,
                session_id=session_id,
                status="completed",
                data=result.get("data"),
                error=None,
                metadata=result.get("metadata")
            )
        else:
            return AsyncAnalysisResultResponse(
                success=False,
                session_id=session_id,
                status="failed",
                data=None,
                error=result.get("error", "Analysis failed"),
                metadata=None
            )

    except Exception as e:
        logger.error(f"Failed to get results for session {session_id}: {e}")
        return AsyncAnalysisResultResponse(
            success=False,
            session_id=session_id,
            status="error",
            data=None,
            error=str(e),
            metadata=None
        )

@router.get("/result/{session_id}")
async def get_analysis_result(
    session_id: str,
    current_user: User = Depends(get_current_user)
):
    """Get analysis result for a session (frontend-compatible endpoint)"""
    # This endpoint matches what the frontend is calling
    try:
        # Clean debug logging
        result_debug.separator("RESULT ENDPOINT REQUEST")
        result_debug.info(f"Getting result for session: {session_id}")
        result_debug.info(f"Active tasks: {list(async_trading_service.active_tasks.keys())}")
        result_debug.info(f"Cached results: {list(async_trading_service.results_cache.keys())}")

        result = await get_analysis_results(session_id, current_user)

        # Log the response structure
        result_debug.info(f"Endpoint returning: success={result.success}, status={result.status}, has_data={result.data is not None}")
        if result.data:
            data_keys = list(result.data.keys()) if isinstance(result.data, dict) else 'not_dict'
            result_debug.info(f"Response data keys: {data_keys}")

        return result
    except Exception as e:
        logger.error(f"Failed to get result for session {session_id}: {e}")
        return AsyncAnalysisResultResponse(
            success=False,
            session_id=session_id,
            status="error",
            data=None,
            error=str(e),
            metadata=None
        )

# Removed conflicting SSE endpoint - using centralized progress.py endpoint instead

@router.delete("/cancel/{session_id}")
async def cancel_async_analysis(
    session_id: str,
    current_user: User = Depends(get_current_active_user)
):
    """Cancel a running async analysis and cleanup resources."""
    
    try:
        logger.info(
            "Cancelling async analysis",
            session_id=session_id,
            user_id=current_user.id
        )
        
        # Cancel and cleanup
        async_trading_service.cleanup_session(session_id)
        
        return {
            "success": True,
            "session_id": session_id,
            "message": "Analysis cancelled and resources cleaned up"
        }
        
    except Exception as e:
        logger.error(
            "Failed to cancel async analysis",
            error=str(e),
            session_id=session_id,
            user_id=current_user.id
        )
        raise HTTPExceptions.internal_server_error(f"Failed to cancel analysis: {str(e)}")

@router.get("/status")
async def get_async_service_status(
    current_user: User = Depends(get_current_active_user)
):
    """Get status of async trading service."""
    
    active_sessions = async_trading_service.get_active_sessions()
    
    return {
        "success": True,
        "service_status": "running",
        "active_sessions_count": len(active_sessions),
        "active_sessions": active_sessions[:10],  # Show first 10 for debugging
        "user_id": str(current_user.id)
    }
