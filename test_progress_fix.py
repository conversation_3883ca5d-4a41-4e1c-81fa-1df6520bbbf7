#!/usr/bin/env python3
"""
Quick Test for Progress Bar Fix
==============================

This script tests the specific progress bar timing fix to verify
that buffered updates are properly replayed to new SSE clients.
"""

import requests
import json
import time
import base64
from datetime import datetime

def create_test_chart():
    """Create a simple test chart image."""
    # Create a minimal PNG (1x1 pixel)
    return base64.b64decode(
        "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=="
    )

def test_progress_fix():
    """Test the progress bar timing fix."""
    print("🔧 TESTING PROGRESS BAR FIX")
    print("=" * 50)
    
    base_url = "http://localhost:8000"
    
    # Step 1: Login
    print("1️⃣ Logging in...")
    try:
        login_response = requests.post(f"{base_url}/api/v1/auth/login", json={
            "email": "<EMAIL>",
            "password": "Bunnych@1627"
        })
        
        if login_response.status_code != 200:
            print(f"❌ Login failed: {login_response.status_code}")
            return False
            
        token = login_response.json()["access_token"]
        headers = {"Authorization": f"Bearer {token}"}
        print("✅ Login successful")
    except Exception as e:
        print(f"❌ Login error: {e}")
        return False
    
    # Step 2: Start analysis
    print("\n2️⃣ Starting analysis...")
    try:
        test_image = create_test_chart()
        
        request_data = {
            "chart_image_base64": base64.b64encode(test_image).decode('utf-8'),
            "analysis_type": "comprehensive",
            "include_sentiment": True,
            "include_technical": True,
            "include_fundamental": True
        }
        
        analysis_response = requests.post(
            f"{base_url}/api/v1/trading/async/start",
            json=request_data,
            headers=headers,
            timeout=30
        )
        
        if analysis_response.status_code == 200:
            result = analysis_response.json()
            session_id = result.get("session_id")
            
            if session_id:
                print(f"✅ Analysis started with session: {session_id}")
                
                # Step 3: Wait a few seconds to let workflow start
                print("\n3️⃣ Waiting for workflow to start...")
                time.sleep(5)
                
                # Step 4: Connect to SSE (this should receive buffered updates)
                print("\n4️⃣ Connecting to SSE stream...")
                sse_url = f"{base_url}/api/v1/progress/stream/{session_id}?token={token}"
                print(f"🔗 SSE URL: {sse_url}")
                
                try:
                    response = requests.get(sse_url, headers=headers, stream=True, timeout=30)
                    
                    if response.status_code == 200:
                        print("✅ SSE connection established")
                        
                        # Read first few events
                        events_received = []
                        start_time = time.time()
                        
                        for line in response.iter_lines(decode_unicode=True):
                            if line.startswith('data: '):
                                try:
                                    data = json.loads(line[6:])  # Remove 'data: ' prefix
                                    events_received.append(data)
                                    
                                    if data.get('type') == 'progress_update':
                                        progress_data = data.get('data', {})
                                        step = progress_data.get('step', 'unknown')
                                        progress = progress_data.get('progress', 0)
                                        message = progress_data.get('message', '')
                                        
                                        print(f"📈 Progress: {step} ({progress}%) - {message}")
                                    
                                    # Stop after 10 events or 30 seconds
                                    if len(events_received) >= 10 or (time.time() - start_time) > 30:
                                        break
                                        
                                except json.JSONDecodeError:
                                    continue
                        
                        # Analyze results
                        print(f"\n5️⃣ Analysis Results:")
                        print(f"📊 Total events received: {len(events_received)}")
                        
                        progress_events = [e for e in events_received if e.get('type') == 'progress_update']
                        print(f"📈 Progress events: {len(progress_events)}")
                        
                        if len(progress_events) >= 2:
                            print("✅ SUCCESS: Multiple progress events received!")
                            print("🎯 Progress bar timing fix is working correctly")
                            
                            # Show progress sequence
                            print("\n📋 Progress sequence:")
                            for i, event in enumerate(progress_events[:5]):  # Show first 5
                                data = event.get('data', {})
                                step = data.get('step', 'unknown')
                                progress = data.get('progress', 0)
                                print(f"   {i+1}. {step} ({progress}%)")
                            
                            return True
                        else:
                            print("❌ FAILED: Only received few progress events")
                            print("🔧 Progress bar timing issue may still exist")
                            return False
                    else:
                        print(f"❌ SSE connection failed: {response.status_code}")
                        return False
                        
                except Exception as e:
                    print(f"❌ SSE connection error: {e}")
                    return False
            else:
                print("❌ No session ID returned")
                return False
        else:
            print(f"❌ Analysis failed: {analysis_response.status_code}")
            print(f"Response: {analysis_response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Analysis error: {e}")
        return False

def main():
    """Main test function."""
    print("🧪 PROGRESS BAR FIX TEST")
    print("Testing the enhanced buffering system for SSE progress updates")
    print(f"Start time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    success = test_progress_fix()
    
    print(f"\n{'='*50}")
    if success:
        print("🎉 PROGRESS BAR FIX TEST PASSED!")
        print("The buffering system is working correctly.")
    else:
        print("⚠️  PROGRESS BAR FIX TEST FAILED")
        print("The timing issue may still need attention.")
    
    return success

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
