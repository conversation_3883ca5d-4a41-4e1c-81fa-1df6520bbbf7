#!/usr/bin/env python3
"""
Comprehensive test to verify SSE connection fixes are working.
This test validates the complete flow from frontend to backend.
"""

import asyncio
import aiohttp
import json
import time
from datetime import datetime

async def test_sse_connection_fix():
    """Test the complete SSE connection flow with the fixes."""
    
    base_url = "http://localhost:8000"
    
    print("🧪 TESTING SSE CONNECTION FIXES")
    print("=" * 60)
    
    # Test 1: Backend Health Check
    print("\n1️⃣ Testing Backend Health Check...")
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(f"{base_url}/api/v1/health") as response:
                if response.status == 200:
                    print("✅ Backend health check: PASSED")
                else:
                    print(f"❌ Backend health check: FAILED ({response.status})")
                    return False
    except Exception as e:
        print(f"❌ Backend health check: ERROR - {e}")
        return False
    
    # Test 2: Authentication
    print("\n2️⃣ Testing Authentication...")
    auth_data = {
        "email": "<EMAIL>",
        "password": "test_password"
    }
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.post(f"{base_url}/api/v1/auth/login", json=auth_data) as response:
                if response.status == 200:
                    data = await response.json()
                    token = data.get("access_token")
                    if token:
                        print("✅ Authentication: PASSED")
                    else:
                        print("❌ Authentication: No token received")
                        return False
                else:
                    print(f"❌ Authentication: FAILED ({response.status})")
                    # Continue with a mock token for testing
                    token = "mock_token_for_testing"
    except Exception as e:
        print(f"❌ Authentication: ERROR - {e}")
        # Continue with a mock token for testing
        token = "mock_token_for_testing"
    
    # Test 3: Start Analysis
    print("\n3️⃣ Testing Analysis Start...")
    analysis_data = {
        "images_base64": ["iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=="],
        "analysis_type": "Positional",
        "market_specialization": "Crypto",
        "preferred_model": "gemini-2.5-flash",
        "timeframes": ["1h"],
        "ticker_hint": "BTC/USD",
        "context_hint": "Test for SSE connection fix"
    }
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    session_id = None
    try:
        async with aiohttp.ClientSession() as session:
            async with session.post(f"{base_url}/api/v1/trading/async/start", 
                                   json=analysis_data, headers=headers) as response:
                if response.status == 200:
                    data = await response.json()
                    session_id = data.get("session_id")
                    if session_id:
                        print(f"✅ Analysis started: {session_id}")
                    else:
                        print("❌ Analysis start: No session ID received")
                        return False
                else:
                    error_text = await response.text()
                    print(f"❌ Analysis start: FAILED ({response.status}) - {error_text}")
                    return False
    except Exception as e:
        print(f"❌ Analysis start: ERROR - {e}")
        return False
    
    # Test 4: SSE Connection
    print("\n4️⃣ Testing SSE Connection...")
    if not session_id:
        print("❌ Cannot test SSE without session ID")
        return False
    
    sse_url = f"{base_url}/api/v1/progress/stream/{session_id}?token={token}"
    print(f"🔗 SSE URL: {sse_url}")
    
    connection_established = False
    session_id_received = False
    progress_updates_received = 0
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(sse_url) as response:
                if response.status == 200:
                    print("✅ SSE connection established")
                    connection_established = True
                    
                    # Read SSE events for up to 30 seconds
                    start_time = time.time()
                    async for line in response.content:
                        if time.time() - start_time > 30:  # 30 second timeout
                            break
                            
                        line = line.decode('utf-8').strip()
                        
                        if line.startswith('data: '):
                            data_str = line[6:]
                            
                            try:
                                data = json.loads(data_str)
                                event_type = data.get('type')
                                
                                if event_type == 'connected':
                                    received_session = data.get('session_id')
                                    if received_session == session_id:
                                        print(f"✅ Session ID confirmed: {received_session}")
                                        session_id_received = True
                                    else:
                                        print(f"⚠️ Session ID mismatch: expected {session_id}, got {received_session}")
                                
                                elif event_type == 'progress_update':
                                    progress_data = data.get('data', {})
                                    step = progress_data.get('step', 'unknown')
                                    progress = progress_data.get('progress', 0)
                                    message = progress_data.get('message', '')
                                    
                                    progress_updates_received += 1
                                    print(f"📈 Progress update {progress_updates_received}: {step} ({progress}%) - {message}")
                                
                                elif event_type == 'complete':
                                    print("🎉 Analysis completed!")
                                    break
                                
                                elif event_type == 'ping':
                                    # Keepalive - ignore
                                    pass
                                
                                elif event_type == 'error':
                                    error_msg = data.get('error', 'Unknown error')
                                    print(f"❌ SSE error: {error_msg}")
                                    break
                                    
                            except json.JSONDecodeError:
                                # Skip invalid JSON
                                continue
                else:
                    print(f"❌ SSE connection failed: {response.status}")
                    return False
    except Exception as e:
        print(f"❌ SSE connection error: {e}")
        return False
    
    # Test Results Summary
    print("\n" + "=" * 60)
    print("🎯 TEST RESULTS SUMMARY")
    print("=" * 60)
    
    results = {
        "Backend Health": "✅ PASSED",
        "Authentication": "✅ PASSED" if token else "❌ FAILED",
        "Analysis Start": "✅ PASSED" if session_id else "❌ FAILED",
        "SSE Connection": "✅ PASSED" if connection_established else "❌ FAILED",
        "Session ID Received": "✅ PASSED" if session_id_received else "❌ FAILED",
        "Progress Updates": f"✅ PASSED ({progress_updates_received} updates)" if progress_updates_received > 0 else "❌ FAILED"
    }
    
    for test_name, result in results.items():
        print(f"{test_name:20}: {result}")
    
    # Overall result
    all_passed = all("✅" in result for result in results.values())
    
    print("\n" + "=" * 60)
    if all_passed:
        print("🎉 ALL TESTS PASSED - SSE CONNECTION FIX SUCCESSFUL!")
    else:
        print("❌ SOME TESTS FAILED - FURTHER INVESTIGATION NEEDED")
    print("=" * 60)
    
    return all_passed

if __name__ == "__main__":
    print("🚀 Starting SSE Connection Fix Test")
    print(f"⏰ Test started at: {datetime.now()}")
    
    try:
        result = asyncio.run(test_sse_connection_fix())
        exit_code = 0 if result else 1
    except KeyboardInterrupt:
        print("\n⚠️ Test interrupted by user")
        exit_code = 2
    except Exception as e:
        print(f"\n❌ Test failed with exception: {e}")
        exit_code = 3
    
    print(f"\n⏰ Test completed at: {datetime.now()}")
    exit(exit_code)
