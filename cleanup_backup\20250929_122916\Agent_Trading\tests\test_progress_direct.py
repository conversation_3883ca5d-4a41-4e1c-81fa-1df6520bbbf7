#!/usr/bin/env python3
"""
Direct Progress Tracking Test
============================

This script tests the progress tracking system directly by:
1. Logging in with your credentials
2. Starting a real analysis with a proper image
3. Monitoring progress via SSE
4. Verifying all 8 steps are received correctly
"""

import asyncio
import aiohttp
import json
import base64
from PIL import Image
import io
import time

# Your credentials
EMAIL = "<EMAIL>"
PASSWORD = "Bunnych@1627"
BASE_URL = "http://localhost:8000"

async def create_test_image():
    """Create a proper test chart image"""
    # Create a 200x200 blue square (simulating a chart)
    img = Image.new('RGB', (200, 200), color='blue')
    
    # Convert to base64
    buffer = io.BytesIO()
    img.save(buffer, format='JPEG')
    img_data = buffer.getvalue()
    img_base64 = base64.b64encode(img_data).decode('utf-8')
    
    return img_base64

async def login():
    """Login and get access token"""
    async with aiohttp.ClientSession() as session:
        login_data = {
            "email": EMAIL,
            "password": PASSWORD
        }
        
        async with session.post(f"{BASE_URL}/api/v1/auth/login", json=login_data) as response:
            if response.status == 200:
                result = await response.json()
                print(f"✅ Login successful for {EMAIL}")
                return result["access_token"]
            else:
                error = await response.text()
                print(f"❌ Login failed: {response.status} - {error}")
                return None

async def start_analysis(token):
    """Start analysis and get session ID"""
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    # Create test image
    test_image = await create_test_image()
    
    analysis_data = {
        "images_base64": [test_image],
        "analysis_type": "Positional",
        "market_specialization": "Crypto",
        "preferred_model": "gemini-2.5-flash"
    }
    
    async with aiohttp.ClientSession() as session:
        async with session.post(f"{BASE_URL}/api/v1/trading/async/start", 
                               headers=headers, json=analysis_data) as response:
            if response.status == 200:
                result = await response.json()
                session_id = result["session_id"]
                print(f"✅ Analysis started with session: {session_id}")
                return session_id
            else:
                error = await response.text()
                print(f"❌ Analysis failed: {response.status} - {error}")
                return None

async def monitor_progress(token, session_id):
    """Monitor progress via SSE"""
    headers = {
        "Authorization": f"Bearer {token}",
        "Accept": "text/event-stream",
        "Cache-Control": "no-cache"
    }
    
    progress_updates = []
    
    print(f"📡 Starting SSE monitoring for session: {session_id}")
    
    async with aiohttp.ClientSession() as session:
        try:
            async with session.get(f"{BASE_URL}/api/v1/progress/stream/{session_id}", 
                                 headers=headers) as response:
                print(f"🔗 SSE connection status: {response.status}")
                
                if response.status != 200:
                    error = await response.text()
                    print(f"❌ SSE connection failed: {error}")
                    return []
                
                async for line in response.content:
                    line = line.decode('utf-8').strip()
                    
                    if line.startswith('data: '):
                        data_str = line[6:]  # Remove 'data: ' prefix
                        
                        try:
                            data = json.loads(data_str)
                            print(f"📨 SSE Event: {data}")
                            
                            if data.get('type') == 'progress_update':
                                update = data.get('data', {})
                                step = update.get('step')
                                progress = update.get('progress')
                                message = update.get('message')
                                
                                print(f"📈 Progress: {step} ({progress}%) - {message}")
                                progress_updates.append(update)
                                
                            elif data.get('type') == 'complete':
                                print("🎉 Analysis completed!")
                                break
                                
                        except json.JSONDecodeError as e:
                            print(f"⚠️ Failed to parse SSE data: {data_str}")
                            
        except Exception as e:
            print(f"❌ SSE monitoring error: {e}")
    
    return progress_updates

async def main():
    """Main test function"""
    print("🚀 Starting Direct Progress Tracking Test")
    print("=" * 50)
    
    # Step 1: Login
    token = await login()
    if not token:
        return
    
    # Step 2: Start analysis
    session_id = await start_analysis(token)
    if not session_id:
        return
    
    # Step 3: Monitor progress
    progress_updates = await monitor_progress(token, session_id)
    
    # Step 4: Analyze results
    print("\n" + "=" * 50)
    print("📊 PROGRESS TRACKING ANALYSIS")
    print("=" * 50)
    
    if not progress_updates:
        print("❌ No progress updates received!")
        return
    
    print(f"✅ Received {len(progress_updates)} progress updates")
    
    expected_steps = [
        'chart_analysis', 'symbol_detection', 'tool_execution', 
        'tool_summarization', 'rag_integration', 'final_analysis', 
        'memory_storage', 'complete'
    ]
    
    received_steps = [update.get('step') for update in progress_updates]
    
    print(f"📋 Expected steps: {expected_steps}")
    print(f"📋 Received steps: {received_steps}")
    
    missing_steps = set(expected_steps) - set(received_steps)
    extra_steps = set(received_steps) - set(expected_steps)
    
    if missing_steps:
        print(f"❌ Missing steps: {missing_steps}")
    
    if extra_steps:
        print(f"⚠️ Extra steps: {extra_steps}")
    
    if not missing_steps and not extra_steps:
        print("✅ All expected steps received correctly!")
    
    # Check progress sequence
    progress_values = [update.get('progress') for update in progress_updates]
    print(f"📈 Progress sequence: {progress_values}")
    
    if progress_values == sorted(progress_values):
        print("✅ Progress values are in correct ascending order!")
    else:
        print("❌ Progress values are not in correct order!")

if __name__ == "__main__":
    asyncio.run(main())
