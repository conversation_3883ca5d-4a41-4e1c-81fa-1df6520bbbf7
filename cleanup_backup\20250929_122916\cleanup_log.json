{"cleanup_timestamp": "2025-09-29T12:29:26.493373", "project_root": ".", "backup_directory": "cleanup_backup\\20250929_122916", "operations": [{"action": "backup", "original": "Agent_Trading\\test_data_transformation.py", "backup": "cleanup_backup\\20250929_122916\\Agent_Trading\\test_data_transformation.py", "timestamp": "2025-09-29T12:29:22.535863"}, {"action": "delete", "file": "Agent_Trading\\test_data_transformation.py", "timestamp": "2025-09-29T12:29:22.536860"}, {"action": "backup", "original": "Agent_Trading\\test_frontend_fixes.py", "backup": "cleanup_backup\\20250929_122916\\Agent_Trading\\test_frontend_fixes.py", "timestamp": "2025-09-29T12:29:22.539846"}, {"action": "delete", "file": "Agent_Trading\\test_frontend_fixes.py", "timestamp": "2025-09-29T12:29:22.540847"}, {"action": "backup", "original": "Agent_Trading\\test_progress_flow.py", "backup": "cleanup_backup\\20250929_122916\\Agent_Trading\\test_progress_flow.py", "timestamp": "2025-09-29T12:29:22.543804"}, {"action": "delete", "file": "Agent_Trading\\test_progress_flow.py", "timestamp": "2025-09-29T12:29:22.543804"}, {"action": "backup", "original": "Agent_Trading\\test_result_debug.py", "backup": "cleanup_backup\\20250929_122916\\Agent_Trading\\test_result_debug.py", "timestamp": "2025-09-29T12:29:22.548353"}, {"action": "delete", "file": "Agent_Trading\\test_result_debug.py", "timestamp": "2025-09-29T12:29:22.548353"}, {"action": "backup", "original": "Agent_Trading\\test_result_endpoint.py", "backup": "cleanup_backup\\20250929_122916\\Agent_Trading\\test_result_endpoint.py", "timestamp": "2025-09-29T12:29:22.551349"}, {"action": "delete", "file": "Agent_Trading\\test_result_endpoint.py", "timestamp": "2025-09-29T12:29:22.551349"}, {"action": "backup", "original": "Agent_Trading\\test_result_simple.py", "backup": "cleanup_backup\\20250929_122916\\Agent_Trading\\test_result_simple.py", "timestamp": "2025-09-29T12:29:22.555923"}, {"action": "delete", "file": "Agent_Trading\\test_result_simple.py", "timestamp": "2025-09-29T12:29:22.555923"}, {"action": "backup", "original": "Agent_Trading\\backend\\test_debug_system.py", "backup": "cleanup_backup\\20250929_122916\\Agent_Trading\\backend\\test_debug_system.py", "timestamp": "2025-09-29T12:29:22.558916"}, {"action": "delete", "file": "Agent_Trading\\backend\\test_debug_system.py", "timestamp": "2025-09-29T12:29:22.558916"}, {"action": "backup", "original": "Agent_Trading\\backend\\test_sse_endpoint.py", "backup": "cleanup_backup\\20250929_122916\\Agent_Trading\\backend\\test_sse_endpoint.py", "timestamp": "2025-09-29T12:29:22.564353"}, {"action": "delete", "file": "Agent_Trading\\backend\\test_sse_endpoint.py", "timestamp": "2025-09-29T12:29:22.564353"}, {"action": "backup", "original": "Agent_Trading\\backend\\simple_progress_api.py", "backup": "cleanup_backup\\20250929_122916\\Agent_Trading\\backend\\simple_progress_api.py", "timestamp": "2025-09-29T12:29:22.567628"}, {"action": "delete", "file": "Agent_Trading\\backend\\simple_progress_api.py", "timestamp": "2025-09-29T12:29:22.567628"}, {"action": "backup", "original": "Agent_Trading\\tests\\test_analysis_completion.py", "backup": "cleanup_backup\\20250929_122916\\Agent_Trading\\tests\\test_analysis_completion.py", "timestamp": "2025-09-29T12:29:22.573640"}, {"action": "delete", "file": "Agent_Trading\\tests\\test_analysis_completion.py", "timestamp": "2025-09-29T12:29:22.573797"}, {"action": "backup", "original": "Agent_Trading\\tests\\test_both_fixes.py", "backup": "cleanup_backup\\20250929_122916\\Agent_Trading\\tests\\test_both_fixes.py", "timestamp": "2025-09-29T12:29:22.578897"}, {"action": "delete", "file": "Agent_Trading\\tests\\test_both_fixes.py", "timestamp": "2025-09-29T12:29:22.578897"}, {"action": "backup", "original": "Agent_Trading\\tests\\test_complete_fix.py", "backup": "cleanup_backup\\20250929_122916\\Agent_Trading\\tests\\test_complete_fix.py", "timestamp": "2025-09-29T12:29:22.583898"}, {"action": "delete", "file": "Agent_Trading\\tests\\test_complete_fix.py", "timestamp": "2025-09-29T12:29:22.583898"}, {"action": "backup", "original": "Agent_Trading\\tests\\test_complete_solution.py", "backup": "cleanup_backup\\20250929_122916\\Agent_Trading\\tests\\test_complete_solution.py", "timestamp": "2025-09-29T12:29:22.588072"}, {"action": "delete", "file": "Agent_Trading\\tests\\test_complete_solution.py", "timestamp": "2025-09-29T12:29:22.589084"}, {"action": "backup", "original": "Agent_Trading\\tests\\test_comprehensive_logging.py", "backup": "cleanup_backup\\20250929_122916\\Agent_Trading\\tests\\test_comprehensive_logging.py", "timestamp": "2025-09-29T12:29:22.593074"}, {"action": "delete", "file": "Agent_Trading\\tests\\test_comprehensive_logging.py", "timestamp": "2025-09-29T12:29:22.593074"}, {"action": "backup", "original": "Agent_Trading\\tests\\test_data_structure_fix.py", "backup": "cleanup_backup\\20250929_122916\\Agent_Trading\\tests\\test_data_structure_fix.py", "timestamp": "2025-09-29T12:29:22.596344"}, {"action": "delete", "file": "Agent_Trading\\tests\\test_data_structure_fix.py", "timestamp": "2025-09-29T12:29:22.596344"}, {"action": "backup", "original": "Agent_Trading\\tests\\test_fixes_verification.py", "backup": "cleanup_backup\\20250929_122916\\Agent_Trading\\tests\\test_fixes_verification.py", "timestamp": "2025-09-29T12:29:22.601575"}, {"action": "delete", "file": "Agent_Trading\\tests\\test_fixes_verification.py", "timestamp": "2025-09-29T12:29:22.602838"}, {"action": "backup", "original": "Agent_Trading\\tests\\test_import_fix.py", "backup": "cleanup_backup\\20250929_122916\\Agent_Trading\\tests\\test_import_fix.py", "timestamp": "2025-09-29T12:29:22.606840"}, {"action": "delete", "file": "Agent_Trading\\tests\\test_import_fix.py", "timestamp": "2025-09-29T12:29:22.606840"}, {"action": "backup", "original": "Agent_Trading\\tests\\test_parameter_passing.py", "backup": "cleanup_backup\\20250929_122916\\Agent_Trading\\tests\\test_parameter_passing.py", "timestamp": "2025-09-29T12:29:22.611793"}, {"action": "delete", "file": "Agent_Trading\\tests\\test_parameter_passing.py", "timestamp": "2025-09-29T12:29:22.611793"}, {"action": "backup", "original": "Agent_Trading\\tests\\test_progress_buffering.py", "backup": "cleanup_backup\\20250929_122916\\Agent_Trading\\tests\\test_progress_buffering.py", "timestamp": "2025-09-29T12:29:22.616470"}, {"action": "delete", "file": "Agent_Trading\\tests\\test_progress_buffering.py", "timestamp": "2025-09-29T12:29:22.616470"}, {"action": "backup", "original": "Agent_Trading\\tests\\test_progress_direct.py", "backup": "cleanup_backup\\20250929_122916\\Agent_Trading\\tests\\test_progress_direct.py", "timestamp": "2025-09-29T12:29:22.619483"}, {"action": "delete", "file": "Agent_Trading\\tests\\test_progress_direct.py", "timestamp": "2025-09-29T12:29:22.620481"}, {"action": "backup", "original": "Agent_Trading\\tests\\test_progress_simple.py", "backup": "cleanup_backup\\20250929_122916\\Agent_Trading\\tests\\test_progress_simple.py", "timestamp": "2025-09-29T12:29:22.623394"}, {"action": "delete", "file": "Agent_Trading\\tests\\test_progress_simple.py", "timestamp": "2025-09-29T12:29:22.624420"}, {"action": "backup", "original": "Agent_Trading\\tests\\test_progress_tracking_complete.py", "backup": "cleanup_backup\\20250929_122916\\Agent_Trading\\tests\\test_progress_tracking_complete.py", "timestamp": "2025-09-29T12:29:22.630229"}, {"action": "delete", "file": "Agent_Trading\\tests\\test_progress_tracking_complete.py", "timestamp": "2025-09-29T12:29:22.631235"}, {"action": "backup", "original": "Agent_Trading\\tests\\test_progress_tracking_fixes.py", "backup": "cleanup_backup\\20250929_122916\\Agent_Trading\\tests\\test_progress_tracking_fixes.py", "timestamp": "2025-09-29T12:29:22.634230"}, {"action": "delete", "file": "Agent_Trading\\tests\\test_progress_tracking_fixes.py", "timestamp": "2025-09-29T12:29:22.635228"}, {"action": "backup", "original": "Agent_Trading\\tests\\test_react_app_real_chart.py", "backup": "cleanup_backup\\20250929_122916\\Agent_Trading\\tests\\test_react_app_real_chart.py", "timestamp": "2025-09-29T12:29:22.638939"}, {"action": "delete", "file": "Agent_Trading\\tests\\test_react_app_real_chart.py", "timestamp": "2025-09-29T12:29:22.639936"}, {"action": "backup", "original": "Agent_Trading\\tests\\test_realtime_sse.py", "backup": "cleanup_backup\\20250929_122916\\Agent_Trading\\tests\\test_realtime_sse.py", "timestamp": "2025-09-29T12:29:22.642932"}, {"action": "delete", "file": "Agent_Trading\\tests\\test_realtime_sse.py", "timestamp": "2025-09-29T12:29:22.644018"}, {"action": "backup", "original": "Agent_Trading\\tests\\test_sse_progress_fix.py", "backup": "cleanup_backup\\20250929_122916\\Agent_Trading\\tests\\test_sse_progress_fix.py", "timestamp": "2025-09-29T12:29:22.648130"}, {"action": "delete", "file": "Agent_Trading\\tests\\test_sse_progress_fix.py", "timestamp": "2025-09-29T12:29:22.648130"}, {"action": "backup", "original": "Agent_Trading\\tests\\test_sse_via_api.py", "backup": "cleanup_backup\\20250929_122916\\Agent_Trading\\tests\\test_sse_via_api.py", "timestamp": "2025-09-29T12:29:22.652056"}, {"action": "delete", "file": "Agent_Trading\\tests\\test_sse_via_api.py", "timestamp": "2025-09-29T12:29:22.653073"}, {"action": "backup", "original": "Agent_Trading\\tests\\test_frontend_connection.html", "backup": "cleanup_backup\\20250929_122916\\Agent_Trading\\tests\\test_frontend_connection.html", "timestamp": "2025-09-29T12:29:22.912608"}, {"action": "delete", "file": "Agent_Trading\\tests\\test_frontend_connection.html", "timestamp": "2025-09-29T12:29:22.912608"}, {"action": "backup", "original": "Agent_Trading\\tests\\test_sse_connection.html", "backup": "cleanup_backup\\20250929_122916\\Agent_Trading\\tests\\test_sse_connection.html", "timestamp": "2025-09-29T12:29:22.972546"}, {"action": "delete", "file": "Agent_Trading\\tests\\test_sse_connection.html", "timestamp": "2025-09-29T12:29:22.972546"}, {"action": "backup", "original": "COMPLETE_FIXES_SUMMARY.md", "backup": "cleanup_backup\\20250929_122916\\COMPLETE_FIXES_SUMMARY.md", "timestamp": "2025-09-29T12:29:23.031348"}, {"action": "delete", "file": "COMPLETE_FIXES_SUMMARY.md", "timestamp": "2025-09-29T12:29:23.032316"}, {"action": "backup", "original": "FINAL_FIX_SUMMARY.md", "backup": "cleanup_backup\\20250929_122916\\FINAL_FIX_SUMMARY.md", "timestamp": "2025-09-29T12:29:23.047050"}, {"action": "delete", "file": "FINAL_FIX_SUMMARY.md", "timestamp": "2025-09-29T12:29:23.048055"}, {"action": "backup", "original": "PROGRESS_AND_DATA_FIXES_SUMMARY.md", "backup": "cleanup_backup\\20250929_122916\\PROGRESS_AND_DATA_FIXES_SUMMARY.md", "timestamp": "2025-09-29T12:29:23.069051"}, {"action": "delete", "file": "PROGRESS_AND_DATA_FIXES_SUMMARY.md", "timestamp": "2025-09-29T12:29:23.069051"}, {"action": "backup", "original": "PROGRESS_FIX_SUMMARY.md", "backup": "cleanup_backup\\20250929_122916\\PROGRESS_FIX_SUMMARY.md", "timestamp": "2025-09-29T12:29:23.107476"}, {"action": "delete", "file": "PROGRESS_FIX_SUMMARY.md", "timestamp": "2025-09-29T12:29:23.107476"}, {"action": "backup", "original": "FINAL_SOLUTION_TEST.py", "backup": "cleanup_backup\\20250929_122916\\FINAL_SOLUTION_TEST.py", "timestamp": "2025-09-29T12:29:23.110137"}, {"action": "delete", "file": "FINAL_SOLUTION_TEST.py", "timestamp": "2025-09-29T12:29:23.111139"}, {"action": "backup", "original": "Agent_Trading\\backend\\progress_debug.log", "backup": "cleanup_backup\\20250929_122916\\Agent_Trading\\backend\\progress_debug.log", "timestamp": "2025-09-29T12:29:25.983738"}, {"action": "delete", "file": "Agent_Trading\\backend\\progress_debug.log", "timestamp": "2025-09-29T12:29:25.984741"}], "summary": {"total_operations": 70, "files_deleted": 35, "directories_deleted": 0, "files_backed_up": 35}}