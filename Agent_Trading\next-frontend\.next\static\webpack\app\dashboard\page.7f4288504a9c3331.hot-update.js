"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/hooks/useRealTimeProgress.ts":
/*!******************************************!*\
  !*** ./src/hooks/useRealTimeProgress.ts ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WORKFLOW_STEPS: () => (/* binding */ WORKFLOW_STEPS),\n/* harmony export */   useRealTimeProgress: () => (/* binding */ useRealTimeProgress)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/**\r\n * Real-time progress tracking hook for LangGraph AI analysis workflow\r\n * Uses Server-Sent Events ONLY for live updates (no polling fallback)\r\n */ \n// ✅ ALIGNED: Frontend mapping now matches actual LangGraph workflow execution\nconst WORKFLOW_STEPS = {\n    CHART_ANALYSIS: {\n        name: 'Chart Analysis',\n        progress: 20,\n        message: '📊 Starting comprehensive AI analysis workflow...'\n    },\n    SYMBOL_DETECTION: {\n        name: 'Symbol Detection',\n        progress: 40,\n        message: '✅ Symbol detected in chart'\n    },\n    TOOL_EXECUTION: {\n        name: 'Data Collection',\n        progress: 50,\n        message: '🛠️ Collecting data from tools...'\n    },\n    TOOL_SUMMARIZATION: {\n        name: 'Data Processing',\n        progress: 60,\n        message: '📋 Processing and summarizing data...'\n    },\n    RAG_INTEGRATION: {\n        name: 'RAG Integration',\n        progress: 70,\n        message: '🧠 Integrating historical context and patterns...'\n    },\n    FINAL_ANALYSIS: {\n        name: 'Final Analysis',\n        progress: 80,\n        message: '🎯 Generating final trading analysis...'\n    },\n    MEMORY_STORAGE: {\n        name: 'Database Storage',\n        progress: 90,\n        message: '💾 Saving analysis...'\n    },\n    COMPLETE: {\n        name: 'Complete',\n        progress: 100,\n        message: '🎉 Analysis complete!'\n    }\n};\nfunction useRealTimeProgress() {\n    const [state, setState] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({\n        sessionId: null,\n        currentStep: 'chart_analysis',\n        progress: 0,\n        message: 'Preparing for analysis...',\n        isConnected: false,\n        isComplete: false,\n        error: null,\n        updates: []\n    });\n    // 🔧 CRITICAL FIX: Progress smoothing for rapid updates\n    const [progressQueue, setProgressQueue] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [isProcessingQueue, setIsProcessingQueue] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    // Process progress queue with smooth animations\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useRealTimeProgress.useEffect\": ()=>{\n            if (progressQueue.length > 0 && !isProcessingQueue) {\n                setIsProcessingQueue(true);\n                const processNext = {\n                    \"useRealTimeProgress.useEffect.processNext\": ()=>{\n                        setProgressQueue({\n                            \"useRealTimeProgress.useEffect.processNext\": (queue)=>{\n                                if (queue.length === 0) {\n                                    setIsProcessingQueue(false);\n                                    return queue;\n                                }\n                                const [nextUpdate, ...remaining] = queue;\n                                // Apply the update\n                                setState({\n                                    \"useRealTimeProgress.useEffect.processNext\": (prev)=>({\n                                            ...prev,\n                                            currentStep: nextUpdate.step,\n                                            progress: nextUpdate.progress,\n                                            message: nextUpdate.message\n                                        })\n                                }[\"useRealTimeProgress.useEffect.processNext\"]);\n                                // 🔧 IMPROVED: Dynamic delay based on queue length and progress difference\n                                let delay = 400 // Base delay reduced from 800ms to 400ms\n                                ;\n                                if (remaining.length > 0) {\n                                    const nextProgress = remaining[0].progress;\n                                    const currentProgress = nextUpdate.progress;\n                                    const progressDiff = nextProgress - currentProgress;\n                                    // Shorter delay for small progress jumps, longer for big jumps\n                                    if (progressDiff <= 10) {\n                                        delay = 300; // Fast updates for small increments\n                                    } else if (progressDiff <= 20) {\n                                        delay = 500; // Medium delay for medium increments\n                                    } else {\n                                        delay = 700; // Longer delay for big jumps\n                                    }\n                                    // If queue is getting long, speed up processing\n                                    if (remaining.length > 3) {\n                                        delay = Math.max(200, delay * 0.6); // Speed up but not too fast\n                                    }\n                                    console.log(\"\\uD83D\\uDCC8 FRONTEND_DEBUG: Processing queue - current: \".concat(currentProgress, \"%, next: \").concat(nextProgress, \"%, delay: \").concat(delay, \"ms, queue length: \").concat(remaining.length));\n                                    setTimeout(processNext, delay);\n                                } else {\n                                    setIsProcessingQueue(false);\n                                }\n                                return remaining;\n                            }\n                        }[\"useRealTimeProgress.useEffect.processNext\"]);\n                    }\n                }[\"useRealTimeProgress.useEffect.processNext\"];\n                processNext();\n            }\n        }\n    }[\"useRealTimeProgress.useEffect\"], [\n        progressQueue,\n        isProcessingQueue\n    ]);\n    const eventSourceRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    // Check if analysis is already complete\n    const checkAnalysisStatus = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useRealTimeProgress.useCallback[checkAnalysisStatus]\": async (sessionId)=>{\n            try {\n                const token = localStorage.getItem('access_token');\n                const baseURL = \"http://localhost:8000\" || 0;\n                const response = await fetch(\"\".concat(baseURL, \"/api/v1/async-trading/status/\").concat(sessionId), {\n                    headers: {\n                        'Authorization': \"Bearer \".concat(token),\n                        'Content-Type': 'application/json'\n                    }\n                });\n                if (response.ok) {\n                    const data = await response.json();\n                    if (data.status === 'completed') {\n                        setState({\n                            \"useRealTimeProgress.useCallback[checkAnalysisStatus]\": (prev)=>({\n                                    ...prev,\n                                    isComplete: true,\n                                    progress: 100,\n                                    currentStep: 'complete',\n                                    message: 'Analysis completed successfully!'\n                                })\n                        }[\"useRealTimeProgress.useCallback[checkAnalysisStatus]\"]);\n                        return true;\n                    }\n                }\n            } catch (error) {\n                console.error('Failed to check analysis status:', error);\n            }\n            return false;\n        }\n    }[\"useRealTimeProgress.useCallback[checkAnalysisStatus]\"], []);\n    const connectToProgress = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useRealTimeProgress.useCallback[connectToProgress]\": async (sessionId)=>{\n            // Close existing SSE connection\n            if (eventSourceRef.current) {\n                eventSourceRef.current.close();\n            }\n            // Update state with the provided session ID\n            setState({\n                \"useRealTimeProgress.useCallback[connectToProgress]\": (prev)=>({\n                        ...prev,\n                        sessionId,\n                        error: null,\n                        currentStep: 'chart_analysis',\n                        progress: 0,\n                        message: 'Starting analysis...'\n                    })\n            }[\"useRealTimeProgress.useCallback[connectToProgress]\"]);\n            // First check if analysis is already complete\n            const isComplete = await checkAnalysisStatus(sessionId);\n            if (isComplete) {\n                return;\n            }\n            const token = localStorage.getItem('access_token');\n            const baseURL = \"http://localhost:8000\" || 0;\n            if (!token) {\n                console.error('❌ No access token found for SSE connection');\n                setState({\n                    \"useRealTimeProgress.useCallback[connectToProgress]\": (prev)=>({\n                            ...prev,\n                            error: 'Authentication required for progress tracking'\n                        })\n                }[\"useRealTimeProgress.useCallback[connectToProgress]\"]);\n                return;\n            }\n            // Try SSE first - Fixed URL construction and added better error handling\n            const sseUrl = \"\".concat(baseURL, \"/api/v1/progress/stream/\").concat(sessionId, \"?token=\").concat(encodeURIComponent(token));\n            console.log('🔗 FRONTEND_DEBUG: Attempting SSE connection');\n            console.log('🔗 FRONTEND_DEBUG: SSE URL:', sseUrl);\n            console.log('🔗 FRONTEND_DEBUG: Session ID:', sessionId);\n            console.log('🔗 FRONTEND_DEBUG: Token present:', !!token);\n            console.log('🔗 FRONTEND_DEBUG: Token length:', token === null || token === void 0 ? void 0 : token.length);\n            console.log('🔗 FRONTEND_DEBUG: Base URL:', baseURL);\n            // Create EventSource with proper error handling\n            let eventSource;\n            try {\n                eventSource = new EventSource(sseUrl);\n                console.log('✅ FRONTEND_DEBUG: EventSource created successfully');\n            } catch (error) {\n                console.error('❌ FRONTEND_DEBUG: Failed to create EventSource:', error);\n                setState({\n                    \"useRealTimeProgress.useCallback[connectToProgress]\": (prev)=>({\n                            ...prev,\n                            error: \"Failed to create SSE connection: \".concat(error)\n                        })\n                }[\"useRealTimeProgress.useCallback[connectToProgress]\"]);\n                return;\n            }\n            eventSourceRef.current = eventSource;\n            let sseConnected = false;\n            // SSE connection timeout (no polling fallback)\n            const sseTimeout = setTimeout({\n                \"useRealTimeProgress.useCallback[connectToProgress].sseTimeout\": ()=>{\n                    if (!sseConnected) {\n                        console.log('⚠️ SSE connection timeout - no polling fallback');\n                        eventSource.close();\n                        setState({\n                            \"useRealTimeProgress.useCallback[connectToProgress].sseTimeout\": (prev)=>({\n                                    ...prev,\n                                    isConnected: false,\n                                    error: 'SSE connection timeout - check backend connection'\n                                })\n                        }[\"useRealTimeProgress.useCallback[connectToProgress].sseTimeout\"]);\n                    }\n                }\n            }[\"useRealTimeProgress.useCallback[connectToProgress].sseTimeout\"], 10000) // Increased timeout to 10 seconds\n            ;\n            eventSource.onopen = ({\n                \"useRealTimeProgress.useCallback[connectToProgress]\": ()=>{\n                    console.log('✅ FRONTEND_DEBUG: SSE connection established');\n                    console.log('✅ FRONTEND_DEBUG: EventSource readyState:', eventSource.readyState);\n                    console.log('✅ FRONTEND_DEBUG: EventSource URL:', eventSource.url);\n                    sseConnected = true;\n                    clearTimeout(sseTimeout);\n                    setState({\n                        \"useRealTimeProgress.useCallback[connectToProgress]\": (prev)=>({\n                                ...prev,\n                                isConnected: true,\n                                error: null\n                            })\n                    }[\"useRealTimeProgress.useCallback[connectToProgress]\"]);\n                }\n            })[\"useRealTimeProgress.useCallback[connectToProgress]\"];\n            eventSource.onmessage = ({\n                \"useRealTimeProgress.useCallback[connectToProgress]\": (event)=>{\n                    try {\n                        console.log('📨 FRONTEND_DEBUG: Raw SSE message received:', event.data);\n                        const data = JSON.parse(event.data);\n                        console.log('📨 FRONTEND_DEBUG: Parsed SSE data:', data);\n                        console.log('📨 FRONTEND_DEBUG: Event type:', data.type);\n                        switch(data.type){\n                            case 'connected':\n                                console.log('✅ FRONTEND_DEBUG: Progress stream connected for session:', data.session_id);\n                                break;\n                            case 'progress_update':\n                                const update = data.data;\n                                console.log('📈 FRONTEND_DEBUG: SSE Progress update:', update);\n                                // Map backend step to frontend display\n                                const stepInfo = getStepInfo(update.step);\n                                console.log('📈 FRONTEND_DEBUG: Step info mapped:', stepInfo);\n                                // 🔧 CRITICAL DEBUG: Log the exact state update\n                                console.log('📈 FRONTEND_DEBUG: About to update state with:', {\n                                    currentStep: update.step,\n                                    progress: stepInfo.progress,\n                                    message: stepInfo.message,\n                                    backendProgress: update.progress,\n                                    frontendProgress: stepInfo.progress\n                                });\n                                // 🔧 CRITICAL FIX: Use progress queue for smooth animations\n                                setState({\n                                    \"useRealTimeProgress.useCallback[connectToProgress]\": (prev)=>{\n                                        // Only queue if this is actually a new step or higher progress\n                                        const isNewStep = update.step !== prev.currentStep;\n                                        const isHigherProgress = stepInfo.progress > prev.progress;\n                                        if (isNewStep || isHigherProgress) {\n                                            console.log('📈 FRONTEND_DEBUG: Queueing progress update:', stepInfo.progress, 'step:', update.step);\n                                            // Add to progress queue for smooth processing\n                                            setProgressQueue({\n                                                \"useRealTimeProgress.useCallback[connectToProgress]\": (queue)=>[\n                                                        ...queue,\n                                                        {\n                                                            step: update.step,\n                                                            progress: stepInfo.progress,\n                                                            message: stepInfo.message\n                                                        }\n                                                    ]\n                                            }[\"useRealTimeProgress.useCallback[connectToProgress]\"]);\n                                            // Update the updates array immediately\n                                            return {\n                                                ...prev,\n                                                updates: [\n                                                    ...prev.updates,\n                                                    update\n                                                ]\n                                            };\n                                        } else {\n                                            console.log('📈 FRONTEND_DEBUG: Skipping duplicate/lower progress update:', stepInfo.progress, 'current:', prev.progress);\n                                            return prev;\n                                        }\n                                    }\n                                }[\"useRealTimeProgress.useCallback[connectToProgress]\"]);\n                                break;\n                            case 'complete':\n                                console.log('🎉 SSE Analysis complete!');\n                                setState({\n                                    \"useRealTimeProgress.useCallback[connectToProgress]\": (prev)=>({\n                                            ...prev,\n                                            isComplete: true,\n                                            progress: 100,\n                                            currentStep: 'complete',\n                                            message: 'Analysis completed successfully!'\n                                        })\n                                }[\"useRealTimeProgress.useCallback[connectToProgress]\"]);\n                                eventSource.close();\n                                break;\n                            case 'ping':\n                                break;\n                            case 'error':\n                                console.error('❌ SSE Progress stream error:', data.error);\n                                setState({\n                                    \"useRealTimeProgress.useCallback[connectToProgress]\": (prev)=>({\n                                            ...prev,\n                                            error: data.error,\n                                            isConnected: false\n                                        })\n                                }[\"useRealTimeProgress.useCallback[connectToProgress]\"]);\n                                eventSource.close();\n                                break;\n                        }\n                    } catch (error) {\n                        console.error('Failed to parse SSE progress update:', error);\n                    }\n                }\n            })[\"useRealTimeProgress.useCallback[connectToProgress]\"];\n            eventSource.onerror = ({\n                \"useRealTimeProgress.useCallback[connectToProgress]\": (error)=>{\n                    console.error('❌ FRONTEND_DEBUG: SSE EventSource error:', error);\n                    console.error('❌ FRONTEND_DEBUG: EventSource readyState:', eventSource.readyState);\n                    console.error('❌ FRONTEND_DEBUG: EventSource URL:', sseUrl);\n                    clearTimeout(sseTimeout);\n                    setState({\n                        \"useRealTimeProgress.useCallback[connectToProgress]\": (prev)=>({\n                                ...prev,\n                                isConnected: false,\n                                error: 'SSE connection failed - no polling fallback as requested'\n                            })\n                    }[\"useRealTimeProgress.useCallback[connectToProgress]\"]);\n                    eventSource.close();\n                }\n            })[\"useRealTimeProgress.useCallback[connectToProgress]\"];\n        }\n    }[\"useRealTimeProgress.useCallback[connectToProgress]\"], []);\n    // Helper function to map backend steps to frontend display\n    const getStepInfo = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useRealTimeProgress.useCallback[getStepInfo]\": (step)=>{\n            switch(step){\n                case 'chart_analysis':\n                    return WORKFLOW_STEPS.CHART_ANALYSIS;\n                case 'symbol_detection':\n                    return WORKFLOW_STEPS.SYMBOL_DETECTION;\n                case 'tool_execution':\n                    return WORKFLOW_STEPS.TOOL_EXECUTION;\n                case 'tool_summarization':\n                    return WORKFLOW_STEPS.TOOL_SUMMARIZATION;\n                case 'rag_integration':\n                    return WORKFLOW_STEPS.RAG_INTEGRATION;\n                case 'final_analysis':\n                    return WORKFLOW_STEPS.FINAL_ANALYSIS;\n                case 'memory_storage':\n                    return WORKFLOW_STEPS.MEMORY_STORAGE;\n                case 'complete':\n                    return WORKFLOW_STEPS.COMPLETE;\n                default:\n                    return {\n                        name: 'Processing',\n                        progress: 50,\n                        message: 'Processing...'\n                    };\n            }\n        }\n    }[\"useRealTimeProgress.useCallback[getStepInfo]\"], []);\n    // Polling removed - SSE only implementation\n    const startProgressTracking = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useRealTimeProgress.useCallback[startProgressTracking]\": async (backendSessionId)=>{\n            if (backendSessionId) {\n                // Use backend session ID directly\n                await connectToProgress(backendSessionId);\n                return backendSessionId;\n            } else {\n                // Fallback: generate frontend session ID if backend doesn't provide one\n                const sessionId = \"frontend_\".concat(Date.now(), \"_\").concat(Math.random().toString(36).substring(2, 11));\n                setState({\n                    \"useRealTimeProgress.useCallback[startProgressTracking]\": (prev)=>({\n                            ...prev,\n                            sessionId,\n                            error: null\n                        })\n                }[\"useRealTimeProgress.useCallback[startProgressTracking]\"]);\n                await connectToProgress(sessionId);\n                console.log('📊 Generated fallback progress session:', sessionId);\n                return sessionId;\n            }\n        }\n    }[\"useRealTimeProgress.useCallback[startProgressTracking]\"], [\n        connectToProgress\n    ]);\n    const cleanup = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useRealTimeProgress.useCallback[cleanup]\": async (sessionId)=>{\n            // Close EventSource connection\n            if (eventSourceRef.current) {\n                eventSourceRef.current.close();\n                eventSourceRef.current = null;\n            }\n            // No polling to clear - SSE only\n            console.log('📊 Progress session cleanup completed:', sessionId || state.sessionId);\n            // Reset state\n            setState({\n                sessionId: null,\n                currentStep: 'chart_analysis',\n                progress: 0,\n                message: 'Preparing for analysis...',\n                isConnected: false,\n                isComplete: false,\n                error: null,\n                updates: []\n            });\n        }\n    }[\"useRealTimeProgress.useCallback[cleanup]\"], [\n        state.sessionId\n    ]);\n    // Cleanup on unmount\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useRealTimeProgress.useEffect\": ()=>{\n            return ({\n                \"useRealTimeProgress.useEffect\": ()=>{\n                    if (eventSourceRef.current) {\n                        eventSourceRef.current.close();\n                    }\n                }\n            })[\"useRealTimeProgress.useEffect\"];\n        }\n    }[\"useRealTimeProgress.useEffect\"], []);\n    return {\n        ...state,\n        startProgressTracking,\n        connectToProgress,\n        cleanup,\n        WORKFLOW_STEPS\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useRealTimeProgress.ts\n"));

/***/ })

});