#!/usr/bin/env python3
"""
Rollback to Simple Progress System
=================================

This script removes the complex SSE system and implements a simple,
reliable polling-based progress system that actually works.
"""

import sys
import os
import shutil
from pathlib import Path
from datetime import datetime

def rollback_progress_system():
    """Rollback to a simple, working progress system."""
    print("🔄 ROLLING BACK TO SIMPLE PROGRESS SYSTEM")
    print("=" * 50)
    
    backend_dir = Path("Agent_Trading/backend")
    
    # 1. Simplify progress_tracker.py - remove SSE complexity
    print("1️⃣ Simplifying progress_tracker.py...")
    
    simple_progress_tracker = '''"""
Simple, reliable progress tracking without SSE complexity.
Uses database storage and simple polling for progress updates.
"""

import json
import uuid
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from enum import Enum
from app.core.unified_logging import get_unified_logger
import logging

logger = logging.getLogger(__name__)

class WorkflowStep(Enum):
    """Workflow steps for trading analysis."""
    CHART_UPLOAD = "chart_upload"
    CHART_ANALYSIS = "chart_analysis"
    SYMBOL_DETECTION = "symbol_detection"
    TOOL_EXECUTION = "tool_execution"
    TOOL_SUMMARIZATION = "tool_summarization"
    RAG_INTEGRATION = "rag_integration"
    FINAL_ANALYSIS = "final_analysis"
    MEMORY_STORAGE = "memory_storage"
    COMPLETE = "complete"

@dataclass
class ProgressUpdate:
    """Progress update data structure."""
    step: WorkflowStep
    progress: int
    message: str
    timestamp: datetime
    details: Optional[Dict[str, Any]] = None
    tool_results: Optional[Dict[str, Any]] = None

class SimpleProgressTracker:
    """Simple progress tracker using database storage."""
    
    def __init__(self):
        self.active_sessions: Dict[str, Dict] = {}
        self.unified_logger = get_unified_logger()
        
    def update_progress(self, session_id: str, step: WorkflowStep, message: str, details: Dict = None) -> ProgressUpdate:
        """Update progress for a session."""
        
        # Calculate progress percentage based on step
        step_progress = {
            WorkflowStep.CHART_UPLOAD: 10,
            WorkflowStep.CHART_ANALYSIS: 20,
            WorkflowStep.SYMBOL_DETECTION: 30,
            WorkflowStep.TOOL_EXECUTION: 50,
            WorkflowStep.TOOL_SUMMARIZATION: 60,
            WorkflowStep.RAG_INTEGRATION: 70,
            WorkflowStep.FINAL_ANALYSIS: 80,
            WorkflowStep.MEMORY_STORAGE: 90,
            WorkflowStep.COMPLETE: 100
        }
        
        progress = step_progress.get(step, 50)
        
        update = ProgressUpdate(
            step=step,
            progress=progress,
            message=message,
            timestamp=datetime.now(),
            details=details or {}
        )
        
        # Store in session
        if session_id not in self.active_sessions:
            self.active_sessions[session_id] = {
                "progress_updates": [],
                "current_step": step.value,
                "current_progress": progress,
                "current_message": message,
                "status": "running",
                "result": None
            }
        
        session_data = self.active_sessions[session_id]
        session_data["progress_updates"].append(asdict(update))
        session_data["current_step"] = step.value
        session_data["current_progress"] = progress
        session_data["current_message"] = message
        session_data["last_updated"] = datetime.now().isoformat()
        
        # Log to unified logging
        self.unified_logger.log_progress(
            session_id=session_id,
            step=step.value,
            progress=progress,
            message=message
        )
        
        logger.info(f"📊 Progress: {session_id} - {step.value} ({progress}%) - {message}")
        
        return update
    
    def get_progress(self, session_id: str) -> Dict:
        """Get current progress for a session."""
        session_data = self.active_sessions.get(session_id, {})
        
        if not session_data:
            return {
                "session_id": session_id,
                "status": "not_found",
                "current_step": "unknown",
                "current_progress": 0,
                "current_message": "Session not found",
                "last_updated": None
            }
        
        return {
            "session_id": session_id,
            "status": session_data.get("status", "running"),
            "current_step": session_data.get("current_step", "unknown"),
            "current_progress": session_data.get("current_progress", 0),
            "current_message": session_data.get("current_message", "Processing..."),
            "last_updated": session_data.get("last_updated"),
            "progress_updates": session_data.get("progress_updates", [])
        }
    
    def store_result(self, session_id: str, result: Dict):
        """Store analysis result."""
        if session_id in self.active_sessions:
            self.active_sessions[session_id]["result"] = result
            self.active_sessions[session_id]["status"] = "completed"
        
        logger.info(f"💾 Result stored for session: {session_id}")
    
    def get_result(self, session_id: str) -> Dict:
        """Get stored result for session."""
        session_data = self.active_sessions.get(session_id, {})
        return session_data.get("result")
    
    def cleanup_session(self, session_id: str):
        """Clean up completed session."""
        if session_id in self.active_sessions:
            del self.active_sessions[session_id]
            logger.info(f"🧹 Cleaned up session: {session_id}")

# Global progress tracker instance
simple_progress_tracker = SimpleProgressTracker()

def get_progress_tracker() -> SimpleProgressTracker:
    """Get the global progress tracker instance."""
    return simple_progress_tracker
'''
    
    # Write the simplified progress tracker
    progress_tracker_file = backend_dir / "app" / "helpers" / "workflow_management" / "progress_tracker.py"
    with open(progress_tracker_file, 'w') as f:
        f.write(simple_progress_tracker)
    
    print("✅ Simplified progress_tracker.py")
    
    # 2. Create simple progress API routes
    print("2️⃣ Creating simple progress API routes...")
    
    simple_progress_routes = '''"""
Simple progress API routes using polling instead of SSE.
Much more reliable and easier to debug.
"""

from fastapi import APIRouter, HTTPException, Depends
from app.helpers.workflow_management.progress_tracker import get_progress_tracker, SimpleProgressTracker
from app.core.security import get_current_user
from app.models.user import User as DBUser

router = APIRouter(prefix="/progress", tags=["Progress Tracking"])

@router.get("/status/{session_id}")
async def get_progress_status(
    session_id: str,
    current_user: DBUser = Depends(get_current_user),
    tracker: SimpleProgressTracker = Depends(get_progress_tracker)
):
    """Get current progress status for a session."""
    try:
        progress_data = tracker.get_progress(session_id)
        return {
            "success": True,
            "data": progress_data
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/session/{session_id}")
async def cleanup_progress_session(
    session_id: str,
    current_user: DBUser = Depends(get_current_user),
    tracker: SimpleProgressTracker = Depends(get_progress_tracker)
):
    """Clean up a completed progress session."""
    try:
        tracker.cleanup_session(session_id)
        return {
            "success": True,
            "message": f"Session {session_id} cleaned up"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
'''
    
    # Write the simplified progress routes
    progress_routes_file = backend_dir / "app" / "api" / "routes" / "progress.py"
    with open(progress_routes_file, 'w') as f:
        f.write(simple_progress_routes)
    
    print("✅ Created simple progress API routes")
    
    # 3. Update async_trading_service.py to use simple progress
    print("3️⃣ Updating async_trading_service.py...")
    
    # Read current file
    async_trading_file = backend_dir / "app" / "services" / "async_trading_service.py"
    with open(async_trading_file, 'r') as f:
        content = f.read()
    
    # Replace complex progress broadcasting with simple updates
    content = content.replace(
        "from app.helpers.workflow_management.progress_tracker import WorkflowProgressTracker, ProgressBroadcaster, WorkflowStep",
        "from app.helpers.workflow_management.progress_tracker import get_progress_tracker, WorkflowStep"
    )
    
    content = content.replace(
        "progress_tracker = WorkflowProgressTracker()",
        "progress_tracker = get_progress_tracker()"
    )
    
    content = content.replace(
        "progress_broadcaster = ProgressBroadcaster()",
        "# Removed complex SSE broadcaster"
    )
    
    # Remove all broadcast_update calls
    lines = content.split('\n')
    filtered_lines = []
    for line in lines:
        if "await progress_broadcaster.broadcast_update" not in line and "progress_broadcaster.mark_session_complete" not in line:
            filtered_lines.append(line)
    
    content = '\n'.join(filtered_lines)
    
    with open(async_trading_file, 'w') as f:
        f.write(content)
    
    print("✅ Updated async_trading_service.py")
    
    print("\n🎉 ROLLBACK COMPLETED!")
    print("=" * 50)
    print("✅ Removed complex SSE system")
    print("✅ Implemented simple polling-based progress")
    print("✅ Much more reliable and easier to debug")
    print("\n📝 Frontend should now use:")
    print("   GET /api/v1/progress/status/{session_id}")
    print("   Poll every 1-2 seconds for progress updates")

if __name__ == "__main__":
    rollback_progress_system()
