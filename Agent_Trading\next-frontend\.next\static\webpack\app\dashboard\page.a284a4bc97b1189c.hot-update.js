"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/hooks/useRealTimeProgress.ts":
/*!******************************************!*\
  !*** ./src/hooks/useRealTimeProgress.ts ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WORKFLOW_STEPS: () => (/* binding */ WORKFLOW_STEPS),\n/* harmony export */   useRealTimeProgress: () => (/* binding */ useRealTimeProgress)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/**\r\n * Real-time progress tracking hook for LangGraph AI analysis workflow\r\n * Uses Server-Sent Events ONLY for live updates (no polling fallback)\r\n */ \n// Optimized 8-step LangGraph workflow with meaningful progress distribution\nconst WORKFLOW_STEPS = {\n    CHART_ANALYSIS: {\n        name: 'Chart Analysis',\n        progress: 20,\n        message: 'Analyzing chart patterns and structure'\n    },\n    SYMBOL_DETECTION: {\n        name: 'Symbol Detection',\n        progress: 30,\n        message: 'Detecting trading symbol and market type'\n    },\n    TOOL_EXECUTION: {\n        name: 'Data Collection',\n        progress: 50,\n        message: 'Fetching market data and news'\n    },\n    TOOL_SUMMARIZATION: {\n        name: 'Data Processing',\n        progress: 60,\n        message: 'Processing and summarizing market data'\n    },\n    RAG_INTEGRATION: {\n        name: 'RAG Integration',\n        progress: 70,\n        message: 'Integrating historical context and patterns'\n    },\n    FINAL_ANALYSIS: {\n        name: 'AI Analysis',\n        progress: 80,\n        message: 'Generating trading insights and recommendations'\n    },\n    MEMORY_STORAGE: {\n        name: 'Memory Storage',\n        progress: 90,\n        message: 'Storing analysis for future reference'\n    },\n    COMPLETE: {\n        name: 'Complete',\n        progress: 100,\n        message: 'Analysis completed successfully!'\n    }\n};\nfunction useRealTimeProgress() {\n    const [state, setState] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({\n        sessionId: null,\n        currentStep: 'chart_analysis',\n        progress: 0,\n        message: 'Preparing for analysis...',\n        isConnected: false,\n        isComplete: false,\n        error: null,\n        updates: []\n    });\n    // 🔧 CRITICAL FIX: Progress smoothing for rapid updates\n    const [progressQueue, setProgressQueue] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [isProcessingQueue, setIsProcessingQueue] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    // Process progress queue with smooth animations\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useRealTimeProgress.useEffect\": ()=>{\n            if (progressQueue.length > 0 && !isProcessingQueue) {\n                setIsProcessingQueue(true);\n                const processNext = {\n                    \"useRealTimeProgress.useEffect.processNext\": ()=>{\n                        setProgressQueue({\n                            \"useRealTimeProgress.useEffect.processNext\": (queue)=>{\n                                if (queue.length === 0) {\n                                    setIsProcessingQueue(false);\n                                    return queue;\n                                }\n                                const [nextUpdate, ...remaining] = queue;\n                                // Apply the update\n                                setState({\n                                    \"useRealTimeProgress.useEffect.processNext\": (prev)=>({\n                                            ...prev,\n                                            currentStep: nextUpdate.step,\n                                            progress: nextUpdate.progress,\n                                            message: nextUpdate.message\n                                        })\n                                }[\"useRealTimeProgress.useEffect.processNext\"]);\n                                // 🔧 IMPROVED: Dynamic delay based on queue length and progress difference\n                                let delay = 400 // Base delay reduced from 800ms to 400ms\n                                ;\n                                if (remaining.length > 0) {\n                                    const nextProgress = remaining[0].progress;\n                                    const currentProgress = nextUpdate.progress;\n                                    const progressDiff = nextProgress - currentProgress;\n                                    // Shorter delay for small progress jumps, longer for big jumps\n                                    if (progressDiff <= 10) {\n                                        delay = 300; // Fast updates for small increments\n                                    } else if (progressDiff <= 20) {\n                                        delay = 500; // Medium delay for medium increments\n                                    } else {\n                                        delay = 700; // Longer delay for big jumps\n                                    }\n                                    // If queue is getting long, speed up processing\n                                    if (remaining.length > 3) {\n                                        delay = Math.max(200, delay * 0.6); // Speed up but not too fast\n                                    }\n                                    console.log(\"\\uD83D\\uDCC8 FRONTEND_DEBUG: Processing queue - current: \".concat(currentProgress, \"%, next: \").concat(nextProgress, \"%, delay: \").concat(delay, \"ms, queue length: \").concat(remaining.length));\n                                    setTimeout(processNext, delay);\n                                } else {\n                                    setIsProcessingQueue(false);\n                                }\n                                return remaining;\n                            }\n                        }[\"useRealTimeProgress.useEffect.processNext\"]);\n                    }\n                }[\"useRealTimeProgress.useEffect.processNext\"];\n                processNext();\n            }\n        }\n    }[\"useRealTimeProgress.useEffect\"], [\n        progressQueue,\n        isProcessingQueue\n    ]);\n    const eventSourceRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    // Check if analysis is already complete\n    const checkAnalysisStatus = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useRealTimeProgress.useCallback[checkAnalysisStatus]\": async (sessionId)=>{\n            try {\n                const token = localStorage.getItem('access_token');\n                const baseURL = \"http://localhost:8000\" || 0;\n                const response = await fetch(\"\".concat(baseURL, \"/api/v1/async-trading/status/\").concat(sessionId), {\n                    headers: {\n                        'Authorization': \"Bearer \".concat(token),\n                        'Content-Type': 'application/json'\n                    }\n                });\n                if (response.ok) {\n                    const data = await response.json();\n                    if (data.status === 'completed') {\n                        setState({\n                            \"useRealTimeProgress.useCallback[checkAnalysisStatus]\": (prev)=>({\n                                    ...prev,\n                                    isComplete: true,\n                                    progress: 100,\n                                    currentStep: 'complete',\n                                    message: 'Analysis completed successfully!'\n                                })\n                        }[\"useRealTimeProgress.useCallback[checkAnalysisStatus]\"]);\n                        return true;\n                    }\n                }\n            } catch (error) {\n                console.error('Failed to check analysis status:', error);\n            }\n            return false;\n        }\n    }[\"useRealTimeProgress.useCallback[checkAnalysisStatus]\"], []);\n    const connectToProgress = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useRealTimeProgress.useCallback[connectToProgress]\": async (sessionId)=>{\n            // Close existing SSE connection\n            if (eventSourceRef.current) {\n                eventSourceRef.current.close();\n            }\n            // Update state with the provided session ID\n            setState({\n                \"useRealTimeProgress.useCallback[connectToProgress]\": (prev)=>({\n                        ...prev,\n                        sessionId,\n                        error: null,\n                        currentStep: 'chart_analysis',\n                        progress: 0,\n                        message: 'Starting analysis...'\n                    })\n            }[\"useRealTimeProgress.useCallback[connectToProgress]\"]);\n            // First check if analysis is already complete\n            const isComplete = await checkAnalysisStatus(sessionId);\n            if (isComplete) {\n                return;\n            }\n            const token = localStorage.getItem('access_token');\n            const baseURL = \"http://localhost:8000\" || 0;\n            if (!token) {\n                console.error('❌ No access token found for SSE connection');\n                setState({\n                    \"useRealTimeProgress.useCallback[connectToProgress]\": (prev)=>({\n                            ...prev,\n                            error: 'Authentication required for progress tracking'\n                        })\n                }[\"useRealTimeProgress.useCallback[connectToProgress]\"]);\n                return;\n            }\n            // Try SSE first - Fixed URL construction and added better error handling\n            const sseUrl = \"\".concat(baseURL, \"/api/v1/progress/stream/\").concat(sessionId, \"?token=\").concat(encodeURIComponent(token));\n            console.log('🔗 FRONTEND_DEBUG: Attempting SSE connection');\n            console.log('🔗 FRONTEND_DEBUG: SSE URL:', sseUrl);\n            console.log('🔗 FRONTEND_DEBUG: Session ID:', sessionId);\n            console.log('🔗 FRONTEND_DEBUG: Token present:', !!token);\n            console.log('🔗 FRONTEND_DEBUG: Token length:', token === null || token === void 0 ? void 0 : token.length);\n            console.log('🔗 FRONTEND_DEBUG: Base URL:', baseURL);\n            // Create EventSource with proper error handling\n            let eventSource;\n            try {\n                eventSource = new EventSource(sseUrl);\n                console.log('✅ FRONTEND_DEBUG: EventSource created successfully');\n            } catch (error) {\n                console.error('❌ FRONTEND_DEBUG: Failed to create EventSource:', error);\n                setState({\n                    \"useRealTimeProgress.useCallback[connectToProgress]\": (prev)=>({\n                            ...prev,\n                            error: \"Failed to create SSE connection: \".concat(error)\n                        })\n                }[\"useRealTimeProgress.useCallback[connectToProgress]\"]);\n                return;\n            }\n            eventSourceRef.current = eventSource;\n            let sseConnected = false;\n            // SSE connection timeout (no polling fallback)\n            const sseTimeout = setTimeout({\n                \"useRealTimeProgress.useCallback[connectToProgress].sseTimeout\": ()=>{\n                    if (!sseConnected) {\n                        console.log('⚠️ SSE connection timeout - no polling fallback');\n                        eventSource.close();\n                        setState({\n                            \"useRealTimeProgress.useCallback[connectToProgress].sseTimeout\": (prev)=>({\n                                    ...prev,\n                                    isConnected: false,\n                                    error: 'SSE connection timeout - check backend connection'\n                                })\n                        }[\"useRealTimeProgress.useCallback[connectToProgress].sseTimeout\"]);\n                    }\n                }\n            }[\"useRealTimeProgress.useCallback[connectToProgress].sseTimeout\"], 10000) // Increased timeout to 10 seconds\n            ;\n            eventSource.onopen = ({\n                \"useRealTimeProgress.useCallback[connectToProgress]\": ()=>{\n                    console.log('✅ FRONTEND_DEBUG: SSE connection established');\n                    console.log('✅ FRONTEND_DEBUG: EventSource readyState:', eventSource.readyState);\n                    console.log('✅ FRONTEND_DEBUG: EventSource URL:', eventSource.url);\n                    sseConnected = true;\n                    clearTimeout(sseTimeout);\n                    setState({\n                        \"useRealTimeProgress.useCallback[connectToProgress]\": (prev)=>({\n                                ...prev,\n                                isConnected: true,\n                                error: null\n                            })\n                    }[\"useRealTimeProgress.useCallback[connectToProgress]\"]);\n                }\n            })[\"useRealTimeProgress.useCallback[connectToProgress]\"];\n            eventSource.onmessage = ({\n                \"useRealTimeProgress.useCallback[connectToProgress]\": (event)=>{\n                    try {\n                        console.log('📨 FRONTEND_DEBUG: Raw SSE message received:', event.data);\n                        const data = JSON.parse(event.data);\n                        console.log('📨 FRONTEND_DEBUG: Parsed SSE data:', data);\n                        console.log('📨 FRONTEND_DEBUG: Event type:', data.type);\n                        switch(data.type){\n                            case 'connected':\n                                console.log('✅ FRONTEND_DEBUG: Progress stream connected for session:', data.session_id);\n                                break;\n                            case 'progress_update':\n                                const update = data.data;\n                                console.log('📈 FRONTEND_DEBUG: SSE Progress update:', update);\n                                // Map backend step to frontend display\n                                const stepInfo = getStepInfo(update.step);\n                                console.log('📈 FRONTEND_DEBUG: Step info mapped:', stepInfo);\n                                // 🔧 CRITICAL DEBUG: Log the exact state update\n                                console.log('📈 FRONTEND_DEBUG: About to update state with:', {\n                                    currentStep: update.step,\n                                    progress: stepInfo.progress,\n                                    message: stepInfo.message,\n                                    backendProgress: update.progress,\n                                    frontendProgress: stepInfo.progress\n                                });\n                                // 🔧 CRITICAL FIX: Use progress queue for smooth animations\n                                setState({\n                                    \"useRealTimeProgress.useCallback[connectToProgress]\": (prev)=>{\n                                        // Only queue if this is actually a new step or higher progress\n                                        const isNewStep = update.step !== prev.currentStep;\n                                        const isHigherProgress = stepInfo.progress > prev.progress;\n                                        if (isNewStep || isHigherProgress) {\n                                            console.log('📈 FRONTEND_DEBUG: Queueing progress update:', stepInfo.progress, 'step:', update.step);\n                                            // Add to progress queue for smooth processing\n                                            setProgressQueue({\n                                                \"useRealTimeProgress.useCallback[connectToProgress]\": (queue)=>[\n                                                        ...queue,\n                                                        {\n                                                            step: update.step,\n                                                            progress: stepInfo.progress,\n                                                            message: stepInfo.message\n                                                        }\n                                                    ]\n                                            }[\"useRealTimeProgress.useCallback[connectToProgress]\"]);\n                                            // Update the updates array immediately\n                                            return {\n                                                ...prev,\n                                                updates: [\n                                                    ...prev.updates,\n                                                    update\n                                                ]\n                                            };\n                                        } else {\n                                            console.log('📈 FRONTEND_DEBUG: Skipping duplicate/lower progress update:', stepInfo.progress, 'current:', prev.progress);\n                                            return prev;\n                                        }\n                                    }\n                                }[\"useRealTimeProgress.useCallback[connectToProgress]\"]);\n                                break;\n                            case 'complete':\n                                console.log('🎉 SSE Analysis complete!');\n                                setState({\n                                    \"useRealTimeProgress.useCallback[connectToProgress]\": (prev)=>({\n                                            ...prev,\n                                            isComplete: true,\n                                            progress: 100,\n                                            currentStep: 'complete',\n                                            message: 'Analysis completed successfully!'\n                                        })\n                                }[\"useRealTimeProgress.useCallback[connectToProgress]\"]);\n                                eventSource.close();\n                                break;\n                            case 'ping':\n                                break;\n                            case 'error':\n                                console.error('❌ SSE Progress stream error:', data.error);\n                                setState({\n                                    \"useRealTimeProgress.useCallback[connectToProgress]\": (prev)=>({\n                                            ...prev,\n                                            error: data.error,\n                                            isConnected: false\n                                        })\n                                }[\"useRealTimeProgress.useCallback[connectToProgress]\"]);\n                                eventSource.close();\n                                break;\n                        }\n                    } catch (error) {\n                        console.error('Failed to parse SSE progress update:', error);\n                    }\n                }\n            })[\"useRealTimeProgress.useCallback[connectToProgress]\"];\n            eventSource.onerror = ({\n                \"useRealTimeProgress.useCallback[connectToProgress]\": (error)=>{\n                    console.error('❌ FRONTEND_DEBUG: SSE EventSource error:', error);\n                    console.error('❌ FRONTEND_DEBUG: EventSource readyState:', eventSource.readyState);\n                    console.error('❌ FRONTEND_DEBUG: EventSource URL:', sseUrl);\n                    clearTimeout(sseTimeout);\n                    setState({\n                        \"useRealTimeProgress.useCallback[connectToProgress]\": (prev)=>({\n                                ...prev,\n                                isConnected: false,\n                                error: 'SSE connection failed - no polling fallback as requested'\n                            })\n                    }[\"useRealTimeProgress.useCallback[connectToProgress]\"]);\n                    eventSource.close();\n                }\n            })[\"useRealTimeProgress.useCallback[connectToProgress]\"];\n        }\n    }[\"useRealTimeProgress.useCallback[connectToProgress]\"], []);\n    // Helper function to map backend steps to frontend display\n    const getStepInfo = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useRealTimeProgress.useCallback[getStepInfo]\": (step)=>{\n            switch(step){\n                case 'chart_analysis':\n                    return WORKFLOW_STEPS.CHART_ANALYSIS;\n                case 'symbol_detection':\n                    return WORKFLOW_STEPS.SYMBOL_DETECTION;\n                case 'tool_execution':\n                    return WORKFLOW_STEPS.TOOL_EXECUTION;\n                case 'tool_summarization':\n                    return WORKFLOW_STEPS.TOOL_SUMMARIZATION;\n                case 'rag_integration':\n                    return WORKFLOW_STEPS.RAG_INTEGRATION;\n                case 'final_analysis':\n                    return WORKFLOW_STEPS.FINAL_ANALYSIS;\n                case 'memory_storage':\n                    return WORKFLOW_STEPS.MEMORY_STORAGE;\n                case 'complete':\n                    return WORKFLOW_STEPS.COMPLETE;\n                default:\n                    return {\n                        name: 'Processing',\n                        progress: 50,\n                        message: 'Processing...'\n                    };\n            }\n        }\n    }[\"useRealTimeProgress.useCallback[getStepInfo]\"], []);\n    // Polling removed - SSE only implementation\n    const startProgressTracking = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useRealTimeProgress.useCallback[startProgressTracking]\": async (backendSessionId)=>{\n            if (backendSessionId) {\n                // Use backend session ID directly\n                await connectToProgress(backendSessionId);\n                return backendSessionId;\n            } else {\n                // Fallback: generate frontend session ID if backend doesn't provide one\n                const sessionId = \"frontend_\".concat(Date.now(), \"_\").concat(Math.random().toString(36).substring(2, 11));\n                setState({\n                    \"useRealTimeProgress.useCallback[startProgressTracking]\": (prev)=>({\n                            ...prev,\n                            sessionId,\n                            error: null\n                        })\n                }[\"useRealTimeProgress.useCallback[startProgressTracking]\"]);\n                await connectToProgress(sessionId);\n                console.log('📊 Generated fallback progress session:', sessionId);\n                return sessionId;\n            }\n        }\n    }[\"useRealTimeProgress.useCallback[startProgressTracking]\"], [\n        connectToProgress\n    ]);\n    const cleanup = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useRealTimeProgress.useCallback[cleanup]\": async (sessionId)=>{\n            // Close EventSource connection\n            if (eventSourceRef.current) {\n                eventSourceRef.current.close();\n                eventSourceRef.current = null;\n            }\n            // No polling to clear - SSE only\n            console.log('📊 Progress session cleanup completed:', sessionId || state.sessionId);\n            // Reset state\n            setState({\n                sessionId: null,\n                currentStep: 'chart_analysis',\n                progress: 0,\n                message: 'Preparing for analysis...',\n                isConnected: false,\n                isComplete: false,\n                error: null,\n                updates: []\n            });\n        }\n    }[\"useRealTimeProgress.useCallback[cleanup]\"], [\n        state.sessionId\n    ]);\n    // Cleanup on unmount\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useRealTimeProgress.useEffect\": ()=>{\n            return ({\n                \"useRealTimeProgress.useEffect\": ()=>{\n                    if (eventSourceRef.current) {\n                        eventSourceRef.current.close();\n                    }\n                }\n            })[\"useRealTimeProgress.useEffect\"];\n        }\n    }[\"useRealTimeProgress.useEffect\"], []);\n    return {\n        ...state,\n        startProgressTracking,\n        connectToProgress,\n        cleanup,\n        WORKFLOW_STEPS\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useRealTimeProgress.ts\n"));

/***/ })

});