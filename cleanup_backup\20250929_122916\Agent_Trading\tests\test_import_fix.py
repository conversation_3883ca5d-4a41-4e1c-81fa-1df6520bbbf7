#!/usr/bin/env python3
"""
Test Import Fix - Quick test to see if the import error is resolved
"""

import requests
import base64

def test_import_fix():
    print("🔧 TESTING IMPORT FIX")
    print("=" * 40)
    
    base_url = "http://localhost:8000"
    
    # Step 1: Login
    print("1️⃣ Logging in...")
    try:
        login_response = requests.post(f"{base_url}/api/v1/auth/login", json={
            "email": "<EMAIL>",
            "password": "Bunnych@1627"
        })
        
        if login_response.status_code != 200:
            print(f"❌ Login failed: {login_response.status_code}")
            return
            
        token = login_response.json()["access_token"]
        headers = {"Authorization": f"Bearer {token}"}
        print("✅ Login successful")
    except Exception as e:
        print(f"❌ Login error: {e}")
        return
    
    # Step 2: Prepare minimal image data
    print("\n2️⃣ Preparing image data...")
    try:
        with open("Agent_Trading/Testing_images/Screenshot 2025-08-03 190956.png", "rb") as f:
            image_data = f.read()
            image_base64 = base64.b64encode(image_data).decode('utf-8')
        
        request_data = {
            "images_base64": [image_base64],
            "analysis_type": "Positional",
            "market_specialization": "Crypto",
            "preferred_model": "gemini-2.5-flash",
            "timeframes": ["15m"],
            "ticker_hint": "",
            "context_hint": ""
        }
        
        print("✅ Image data prepared")
    except Exception as e:
        print(f"❌ Image preparation error: {e}")
        return
    
    # Step 3: Start analysis and check for import error
    print("\n3️⃣ Testing analysis start...")
    try:
        analysis_response = requests.post(
            f"{base_url}/api/v1/trading/async/start",
            json=request_data,
            headers=headers
        )
        
        print(f"📊 Response Status: {analysis_response.status_code}")
        
        if analysis_response.status_code == 200:
            response_data = analysis_response.json()
            session_id = response_data.get("session_id", "unknown")
            print(f"✅ Analysis started successfully!")
            print(f"📝 Session ID: {session_id}")
            print("🎉 Import error is FIXED!")
            
        else:
            print(f"❌ Analysis failed: {analysis_response.status_code}")
            try:
                error_data = analysis_response.json()
                print(f"Error details: {error_data}")
                
                # Check if it's still an import error
                if "cannot import name" in str(error_data):
                    print("🚨 Still has import error!")
                else:
                    print("✅ Import error is fixed, but there's another issue")
                    
            except:
                print(f"Response text: {analysis_response.text}")
                
    except Exception as e:
        print(f"❌ Analysis error: {e}")
        
        # Check if it's an import error
        if "cannot import name" in str(e):
            print("🚨 Still has import error!")
        else:
            print("✅ Import error is fixed, but there's another issue")

if __name__ == "__main__":
    test_import_fix()
