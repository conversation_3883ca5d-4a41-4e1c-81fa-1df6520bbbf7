/**
 * Real-time progress tracking hook for LangGraph AI analysis workflow
 * Uses Server-Sent Events ONLY for live updates (no polling fallback)
 */

import { useState, useEffect, useRef, useCallback } from 'react'

export interface ProgressUpdate {
  step: string
  progress: number
  message: string
  timestamp: string
  details?: Record<string, any>
}

export interface ProgressState {
  sessionId: string | null
  currentStep: string
  progress: number
  message: string
  isConnected: boolean
  isComplete: boolean
  error: string | null
  updates: ProgressUpdate[]
}

// Optimized 8-step LangGraph workflow with meaningful progress distribution
export const WORKFLOW_STEPS = {
  CHART_ANALYSIS: { name: 'Chart Analysis', progress: 20, message: 'Analyzing chart patterns and structure' },
  SYMBOL_DETECTION: { name: 'Symbol Detection', progress: 30, message: 'Detecting trading symbol and market type' },
  TOOL_EXECUTION: { name: 'Data Collection', progress: 50, message: 'Fetching market data and news' },
  TOOL_SUMMARIZATION: { name: 'Data Processing', progress: 60, message: 'Processing and summarizing market data' },
  RAG_INTEGRATION: { name: 'RAG Integration', progress: 70, message: 'Integrating historical context and patterns' },
  FINAL_ANALYSIS: { name: 'AI Analysis', progress: 80, message: 'Generating trading insights and recommendations' },
  MEMORY_STORAGE: { name: 'Memory Storage', progress: 90, message: 'Storing analysis for future reference' },
  COMPLETE: { name: 'Complete', progress: 100, message: 'Analysis completed successfully!' }
}

export function useRealTimeProgress() {
  const [state, setState] = useState<ProgressState>({
    sessionId: null,
    currentStep: 'chart_analysis',
    progress: 0,
    message: 'Preparing for analysis...',
    isConnected: false,
    isComplete: false,
    error: null,
    updates: []
  })

  // 🔧 CRITICAL FIX: Progress smoothing for rapid updates
  const [progressQueue, setProgressQueue] = useState<Array<{step: string, progress: number, message: string}>>([])
  const [isProcessingQueue, setIsProcessingQueue] = useState(false)

  // Process progress queue with smooth animations
  useEffect(() => {
    if (progressQueue.length > 0 && !isProcessingQueue) {
      setIsProcessingQueue(true)

      const processNext = () => {
        setProgressQueue(queue => {
          if (queue.length === 0) {
            setIsProcessingQueue(false)
            return queue
          }

          const [nextUpdate, ...remaining] = queue

          // Apply the update
          setState(prev => ({
            ...prev,
            currentStep: nextUpdate.step,
            progress: nextUpdate.progress,
            message: nextUpdate.message
          }))

          // 🔧 IMPROVED: Dynamic delay based on queue length and progress difference
          let delay = 400 // Base delay reduced from 800ms to 400ms

          if (remaining.length > 0) {
            const nextProgress = remaining[0].progress
            const currentProgress = nextUpdate.progress
            const progressDiff = nextProgress - currentProgress

            // Shorter delay for small progress jumps, longer for big jumps
            if (progressDiff <= 10) {
              delay = 300 // Fast updates for small increments
            } else if (progressDiff <= 20) {
              delay = 500 // Medium delay for medium increments
            } else {
              delay = 700 // Longer delay for big jumps
            }

            // If queue is getting long, speed up processing
            if (remaining.length > 3) {
              delay = Math.max(200, delay * 0.6) // Speed up but not too fast
            }

            console.log(`📈 FRONTEND_DEBUG: Processing queue - current: ${currentProgress}%, next: ${nextProgress}%, delay: ${delay}ms, queue length: ${remaining.length}`)

            setTimeout(processNext, delay)
          } else {
            setIsProcessingQueue(false)
          }

          return remaining
        })
      }

      processNext()
    }
  }, [progressQueue, isProcessingQueue])

  const eventSourceRef = useRef<EventSource | null>(null)

  // Check if analysis is already complete
  const checkAnalysisStatus = useCallback(async (sessionId: string) => {
    try {
      const token = localStorage.getItem('access_token')
      const baseURL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000'

      const response = await fetch(`${baseURL}/api/v1/async-trading/status/${sessionId}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      })

      if (response.ok) {
        const data = await response.json()
        if (data.status === 'completed') {
          setState(prev => ({
            ...prev,
            isComplete: true,
            progress: 100,
            currentStep: 'complete',
            message: 'Analysis completed successfully!'
          }))
          return true
        }
      }
    } catch (error) {
      console.error('Failed to check analysis status:', error)
    }
    return false
  }, [])

  const connectToProgress = useCallback(async (sessionId: string) => {
    // Close existing SSE connection
    if (eventSourceRef.current) {
      eventSourceRef.current.close()
    }

    // Update state with the provided session ID
    setState(prev => ({
      ...prev,
      sessionId,
      error: null,
      currentStep: 'chart_analysis',
      progress: 0,
      message: 'Starting analysis...'
    }))

    // First check if analysis is already complete
    const isComplete = await checkAnalysisStatus(sessionId)
    if (isComplete) {
      return
    }

    const token = localStorage.getItem('access_token')
    const baseURL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000'

    if (!token) {
      console.error('❌ No access token found for SSE connection')
      setState(prev => ({
        ...prev,
        error: 'Authentication required for progress tracking'
      }))
      return
    }

    // Try SSE first - Fixed URL construction and added better error handling
    const sseUrl = `${baseURL}/api/v1/progress/stream/${sessionId}?token=${encodeURIComponent(token)}`
    console.log('🔗 FRONTEND_DEBUG: Attempting SSE connection')
    console.log('🔗 FRONTEND_DEBUG: SSE URL:', sseUrl)
    console.log('🔗 FRONTEND_DEBUG: Session ID:', sessionId)
    console.log('🔗 FRONTEND_DEBUG: Token present:', !!token)
    console.log('🔗 FRONTEND_DEBUG: Token length:', token?.length)
    console.log('🔗 FRONTEND_DEBUG: Base URL:', baseURL)

    // Create EventSource with proper error handling
    let eventSource: EventSource
    try {
      eventSource = new EventSource(sseUrl)
      console.log('✅ FRONTEND_DEBUG: EventSource created successfully')
    } catch (error) {
      console.error('❌ FRONTEND_DEBUG: Failed to create EventSource:', error)
      setState(prev => ({
        ...prev,
        error: `Failed to create SSE connection: ${error}`
      }))
      return
    }
    eventSourceRef.current = eventSource

    let sseConnected = false

    // SSE connection timeout (no polling fallback)
    const sseTimeout = setTimeout(() => {
      if (!sseConnected) {
        console.log('⚠️ SSE connection timeout - no polling fallback')
        eventSource.close()
        setState(prev => ({
          ...prev,
          isConnected: false,
          error: 'SSE connection timeout - check backend connection'
        }))
      }
    }, 10000) // Increased timeout to 10 seconds

    eventSource.onopen = () => {
      console.log('✅ FRONTEND_DEBUG: SSE connection established')
      console.log('✅ FRONTEND_DEBUG: EventSource readyState:', eventSource.readyState)
      console.log('✅ FRONTEND_DEBUG: EventSource URL:', eventSource.url)
      sseConnected = true
      clearTimeout(sseTimeout)
      setState(prev => ({
        ...prev,
        isConnected: true,
        error: null
      }))
    }

    eventSource.onmessage = (event) => {
      try {
        console.log('📨 FRONTEND_DEBUG: Raw SSE message received:', event.data)
        const data = JSON.parse(event.data)
        console.log('📨 FRONTEND_DEBUG: Parsed SSE data:', data)
        console.log('📨 FRONTEND_DEBUG: Event type:', data.type)

        switch (data.type) {
          case 'connected':
            console.log('✅ FRONTEND_DEBUG: Progress stream connected for session:', data.session_id)
            break

          case 'progress_update':
            const update: ProgressUpdate = data.data
            console.log('📈 FRONTEND_DEBUG: SSE Progress update:', update)

            // Map backend step to frontend display
            const stepInfo = getStepInfo(update.step)
            console.log('📈 FRONTEND_DEBUG: Step info mapped:', stepInfo)

            // 🔧 CRITICAL DEBUG: Log the exact state update
            console.log('📈 FRONTEND_DEBUG: About to update state with:', {
              currentStep: update.step,
              progress: stepInfo.progress,
              message: stepInfo.message,
              backendProgress: update.progress,
              frontendProgress: stepInfo.progress
            })

            // 🔧 CRITICAL FIX: Use progress queue for smooth animations
            setState(prev => {
              // Only queue if this is actually a new step or higher progress
              const isNewStep = update.step !== prev.currentStep
              const isHigherProgress = stepInfo.progress > prev.progress

              if (isNewStep || isHigherProgress) {
                console.log('📈 FRONTEND_DEBUG: Queueing progress update:', stepInfo.progress, 'step:', update.step)

                // Add to progress queue for smooth processing
                setProgressQueue(queue => [...queue, {
                  step: update.step,
                  progress: stepInfo.progress,
                  message: stepInfo.message
                }])

                // Update the updates array immediately
                return {
                  ...prev,
                  updates: [...prev.updates, update]
                }
              } else {
                console.log('📈 FRONTEND_DEBUG: Skipping duplicate/lower progress update:', stepInfo.progress, 'current:', prev.progress)
                return prev
              }
            })
            break

          case 'complete':
            console.log('🎉 SSE Analysis complete!')
            setState(prev => ({
              ...prev,
              isComplete: true,
              progress: 100,
              currentStep: 'complete',
              message: 'Analysis completed successfully!'
            }))
            eventSource.close()
            break

          case 'ping':
            // Keepalive - do nothing
            break

          case 'error':
            console.error('❌ SSE Progress stream error:', data.error)
            setState(prev => ({
              ...prev,
              error: data.error,
              isConnected: false
            }))
            eventSource.close()
            // No polling fallback - SSE only
            break
        }
      } catch (error) {
        console.error('Failed to parse SSE progress update:', error)
      }
    }

    eventSource.onerror = (error) => {
      console.error('❌ FRONTEND_DEBUG: SSE EventSource error:', error)
      console.error('❌ FRONTEND_DEBUG: EventSource readyState:', eventSource.readyState)
      console.error('❌ FRONTEND_DEBUG: EventSource URL:', sseUrl)
      clearTimeout(sseTimeout)

      setState(prev => ({
        ...prev,
        isConnected: false,
        error: 'SSE connection failed - no polling fallback as requested'
      }))
      eventSource.close()
    }
  }, [])

  // Helper function to map backend steps to frontend display
  const getStepInfo = useCallback((step: string) => {
    switch (step) {
      case 'chart_analysis':
        return WORKFLOW_STEPS.CHART_ANALYSIS
      case 'symbol_detection':
        return WORKFLOW_STEPS.SYMBOL_DETECTION
      case 'tool_execution':
        return WORKFLOW_STEPS.TOOL_EXECUTION
      case 'tool_summarization':
        return WORKFLOW_STEPS.TOOL_SUMMARIZATION
      case 'rag_integration':
        return WORKFLOW_STEPS.RAG_INTEGRATION
      case 'final_analysis':
        return WORKFLOW_STEPS.FINAL_ANALYSIS
      case 'memory_storage':
        return WORKFLOW_STEPS.MEMORY_STORAGE
      case 'complete':
        return WORKFLOW_STEPS.COMPLETE
      default:
        return { name: 'Processing', progress: 50, message: 'Processing...' }
    }
  }, [])

  // Polling removed - SSE only implementation

  const startProgressTracking = useCallback(async (backendSessionId?: string) => {
    if (backendSessionId) {
      // Use backend session ID directly
      await connectToProgress(backendSessionId)
      return backendSessionId
    } else {
      // Fallback: generate frontend session ID if backend doesn't provide one
      const sessionId = `frontend_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`
      setState(prev => ({
        ...prev,
        sessionId,
        error: null
      }))
      await connectToProgress(sessionId)
      console.log('📊 Generated fallback progress session:', sessionId)
      return sessionId
    }
  }, [connectToProgress])

  const cleanup = useCallback(async (sessionId?: string) => {
    // Close EventSource connection
    if (eventSourceRef.current) {
      eventSourceRef.current.close()
      eventSourceRef.current = null
    }

    // No polling to clear - SSE only

    console.log('📊 Progress session cleanup completed:', sessionId || state.sessionId)

    // Reset state
    setState({
      sessionId: null,
      currentStep: 'chart_analysis',
      progress: 0,
      message: 'Preparing for analysis...',
      isConnected: false,
      isComplete: false,
      error: null,
      updates: []
    })
  }, [state.sessionId])

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (eventSourceRef.current) {
        eventSourceRef.current.close()
      }
    }
  }, [])

  return {
    ...state,
    startProgressTracking,
    connectToProgress,
    cleanup,
    WORKFLOW_STEPS
  }
}
