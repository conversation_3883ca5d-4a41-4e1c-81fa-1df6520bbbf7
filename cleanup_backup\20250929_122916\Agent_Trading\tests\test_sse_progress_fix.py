#!/usr/bin/env python3
"""
Comprehensive test for the fixed SSE progress tracking system.
Tests parameter passing, progress step synchronization, and end-to-end flow.
"""

import asyncio
import requests
import json
import base64
import time
import threading
from datetime import datetime
from typing import List, Dict, Any

# Test configuration
BASE_URL = "http://localhost:8000"
TEST_USER_EMAIL = "<EMAIL>"
TEST_USER_PASSWORD = "testpassword123"

class ProgressMonitor:
    """Monitor SSE progress updates in real-time"""
    
    def __init__(self, session_id: str, auth_token: str):
        self.session_id = session_id
        self.auth_token = auth_token
        self.progress_updates = []
        self.is_monitoring = False
        self.connection_status = "disconnected"
        
    def start_monitoring(self):
        """Start monitoring progress in a separate thread"""
        self.is_monitoring = True
        thread = threading.Thread(target=self._monitor_progress, daemon=True)
        thread.start()
        return thread
        
    def _monitor_progress(self):
        """Monitor SSE progress stream"""
        try:
            url = f"{BASE_URL}/api/v1/progress/stream/{self.session_id}"
            headers = {
                "Authorization": f"Bearer {self.auth_token}",
                "Accept": "text/event-stream",
                "Cache-Control": "no-cache"
            }
            
            print(f"🔗 Connecting to SSE stream: {url}")
            response = requests.get(url, headers=headers, stream=True, timeout=120)
            
            if response.status_code == 200:
                self.connection_status = "connected"
                print("✅ SSE connection established")
                
                for line in response.iter_lines(decode_unicode=True):
                    if not self.is_monitoring:
                        break
                        
                    if line.startswith("data: "):
                        try:
                            data = json.loads(line[6:])  # Remove "data: " prefix
                            self.progress_updates.append({
                                "timestamp": datetime.now().isoformat(),
                                "data": data
                            })
                            
                            # Print progress update
                            step = data.get("step", "unknown")
                            progress = data.get("progress", 0)
                            message = data.get("message", "")
                            
                            print(f"📈 Progress: {step} ({progress}%) - {message}")
                            
                        except json.JSONDecodeError as e:
                            print(f"⚠️ Failed to parse SSE data: {e}")
                            
            else:
                self.connection_status = "failed"
                print(f"❌ SSE connection failed: {response.status_code}")
                
        except Exception as e:
            self.connection_status = "error"
            print(f"❌ SSE monitoring error: {e}")
            
    def stop_monitoring(self):
        """Stop monitoring progress"""
        self.is_monitoring = False
        
    def get_progress_summary(self) -> Dict[str, Any]:
        """Get summary of progress updates"""
        if not self.progress_updates:
            return {"status": "no_updates", "updates": []}
            
        steps_seen = set()
        progress_values = []
        
        for update in self.progress_updates:
            data = update["data"]
            steps_seen.add(data.get("step", "unknown"))
            progress_values.append(data.get("progress", 0))
            
        return {
            "status": "success",
            "total_updates": len(self.progress_updates),
            "unique_steps": len(steps_seen),
            "steps_seen": list(steps_seen),
            "progress_range": [min(progress_values), max(progress_values)] if progress_values else [0, 0],
            "final_progress": progress_values[-1] if progress_values else 0,
            "updates": self.progress_updates
        }

def authenticate() -> str:
    """Authenticate and get access token"""
    print("🔐 Authenticating...")
    
    auth_data = {
        "username": TEST_USER_EMAIL,
        "password": TEST_USER_PASSWORD
    }
    
    response = requests.post(f"{BASE_URL}/api/v1/auth/login", data=auth_data)
    
    if response.status_code == 200:
        token = response.json()["access_token"]
        print("✅ Authentication successful")
        return token
    else:
        raise Exception(f"Authentication failed: {response.status_code} - {response.text}")

def load_test_image() -> str:
    """Load and encode test chart image"""
    try:
        with open("test_chart.png", "rb") as f:
            image_data = f.read()
            return base64.b64encode(image_data).decode('utf-8')
    except FileNotFoundError:
        # Create a minimal test image if file doesn't exist
        print("⚠️ test_chart.png not found, using placeholder")
        return "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=="

def test_sse_progress_tracking():
    """Main test function for SSE progress tracking"""
    print("🚀 Starting comprehensive SSE progress tracking test")
    print("=" * 60)
    
    try:
        # 1. Authenticate
        auth_token = authenticate()
        
        # 2. Prepare test data
        print("\n📊 Preparing test data...")
        test_image = load_test_image()
        
        # 3. Start analysis with progress monitoring
        print("\n🔄 Starting analysis...")
        
        analysis_data = {
            "chart_images": [test_image],
            "analysis_mode": "positional",
            "user_query": "Test SSE progress tracking system",
            "market_specialization": "General"
        }
        
        headers = {
            "Authorization": f"Bearer {auth_token}",
            "Content-Type": "application/json"
        }
        
        # Start async analysis
        response = requests.post(
            f"{BASE_URL}/api/v1/trading/analyze-async",
            json=analysis_data,
            headers=headers
        )
        
        if response.status_code != 200:
            raise Exception(f"Analysis request failed: {response.status_code} - {response.text}")
            
        result = response.json()
        session_id = result["session_id"]
        
        print(f"✅ Analysis started with session ID: {session_id}")
        
        # 4. Start progress monitoring
        print("\n🔍 Starting progress monitoring...")
        progress_monitor = ProgressMonitor(session_id, auth_token)
        monitor_thread = progress_monitor.start_monitoring()
        
        # 5. Wait for completion
        print("\n⏳ Waiting for analysis completion...")
        
        for i in range(120):  # Wait up to 2 minutes
            time.sleep(1)
            
            # Check if analysis is complete
            status_response = requests.get(
                f"{BASE_URL}/api/v1/trading/result/{session_id}",
                headers=headers
            )
            
            if status_response.status_code == 200:
                result_data = status_response.json()
                if result_data.get("status") == "completed":
                    print("✅ Analysis completed!")
                    break
            elif i % 10 == 0:  # Print status every 10 seconds
                print(f"⏳ Still waiting... ({i}s elapsed)")
                
        else:
            print("⚠️ Analysis did not complete within timeout")
            
        # 6. Stop monitoring and analyze results
        progress_monitor.stop_monitoring()
        time.sleep(1)  # Allow final updates to be processed
        
        # 7. Analyze progress tracking results
        print("\n📊 Analyzing progress tracking results...")
        summary = progress_monitor.get_progress_summary()
        
        print(f"Connection Status: {progress_monitor.connection_status}")
        print(f"Total Updates: {summary['total_updates']}")
        print(f"Unique Steps: {summary['unique_steps']}")
        print(f"Steps Seen: {summary['steps_seen']}")
        print(f"Progress Range: {summary['progress_range']}")
        print(f"Final Progress: {summary['final_progress']}%")
        
        # 8. Validate results
        print("\n✅ Validation Results:")
        
        # Check if we got progress updates
        if summary['total_updates'] == 0:
            print("❌ FAIL: No progress updates received")
            return False
            
        # Check if we got the expected 8 steps
        expected_steps = {
            "chart_analysis", "symbol_detection", "tool_execution", 
            "tool_summarization", "rag_integration", "final_analysis", 
            "memory_storage", "complete"
        }
        
        steps_received = set(summary['steps_seen'])
        missing_steps = expected_steps - steps_received
        
        if missing_steps:
            print(f"⚠️ PARTIAL: Missing steps: {missing_steps}")
        else:
            print("✅ PASS: All 8 expected steps received")
            
        # Check progress distribution
        if summary['final_progress'] == 100:
            print("✅ PASS: Analysis reached 100% completion")
        else:
            print(f"⚠️ PARTIAL: Final progress was {summary['final_progress']}%")
            
        # Check connection status
        if progress_monitor.connection_status == "connected":
            print("✅ PASS: SSE connection was successful")
        else:
            print(f"❌ FAIL: SSE connection status: {progress_monitor.connection_status}")
            
        print("\n🎉 Test completed!")
        return True
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        return False

if __name__ == "__main__":
    success = test_sse_progress_tracking()
    exit(0 if success else 1)
