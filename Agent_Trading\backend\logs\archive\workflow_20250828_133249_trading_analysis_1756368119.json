{"workflow_id": "trading_analysis_1756368119", "start_time": "2025-08-28T13:31:59.299585", "context": {"analysis_mode": "positional", "market_specialization": "Crypto", "chart_count": 2}, "steps": [{"step_number": 1, "step_name": "Chart Analysis", "step_type": "llm_call", "timestamp": "2025-08-28T13:31:59.299841", "execution_time": null, "status": "success", "error": null, "input_summary": "Chart image for symbol detection"}, {"step_number": 2, "step_name": "Chart Analysis", "step_type": "llm_call", "timestamp": "2025-08-28T13:32:12.803327", "execution_time": 13.***************, "status": "success", "error": null, "output_summary": "Dict with keys: ['detected_symbol', 'market_type', 'timeframe', 'all_timeframes', 'current_price']..."}, {"step_number": 3, "step_name": "Step 2: Tool Selection", "step_type": "api_calls", "timestamp": "2025-08-28T13:32:12.803675", "execution_time": null, "status": "success", "error": null, "input_summary": "Market: indian, Symbol: BANKNIFTY"}, {"step_number": 4, "step_name": "Step 2: Tool Selection", "step_type": "api_calls", "timestamp": "2025-08-28T13:32:18.570512", "execution_time": 5.***************, "status": "success", "error": null, "output_summary": "Dict with keys: ['success', 'tool_results', 'tool_usage_log', 'execution_time']"}, {"step_number": 5, "step_name": "Step 3: Flash Summarization", "step_type": "llm_call", "timestamp": "2025-08-28T13:32:18.570774", "execution_time": null, "status": "success", "error": null, "input_summary": "Tool results for summarization"}, {"step_number": 6, "step_name": "Step 3: Flash Summarization", "step_type": "llm_call", "timestamp": "2025-08-28T13:32:29.888621", "execution_time": 11.***************, "status": "success", "error": null, "output_summary": "Dict with keys: ['success', 'summarized_tools', 'tool_count']"}, {"step_number": 7, "step_name": "Step 4: Final Analysis", "step_type": "llm_call", "timestamp": "2025-08-28T13:32:31.567472", "execution_time": null, "status": "success", "error": null, "input_summary": "Chart + Tool summaries + RAG tool available"}, {"step_number": 8, "step_name": "Final Analysis", "step_type": "llm_call", "timestamp": "2025-08-28T13:32:49.568722", "execution_time": 18.000466346740723, "status": "success", "error": null, "output_summary": "Dict with keys: ['success', 'analysis', 'analysis_notes', 'trading_signals', 'status']..."}], "llm_calls": [], "summary": {"total_steps": 8, "total_llm_calls": 0, "total_tokens": 0, "total_cost": 0.0, "execution_time": 50.27487778663635}, "end_time": "2025-08-28T13:32:49.574475", "status": "success", "error": null}