{"workflow_id": "trading_analysis_1757183597", "start_time": "2025-09-07T00:03:17.594009", "context": {"analysis_mode": "scalp", "market_specialization": "Crypto", "chart_count": 1}, "steps": [{"step_number": 1, "step_name": "Chart Analysis", "step_type": "llm_call", "timestamp": "2025-09-07T00:03:17.595573", "execution_time": null, "status": "success", "error": null, "input_summary": "Chart image for symbol detection"}, {"step_number": 2, "step_name": "Chart Analysis", "step_type": "llm_call", "timestamp": "2025-09-07T00:03:19.790023", "execution_time": 12.758070707321167, "status": "success", "error": null, "output_summary": "Dict with keys: ['detected_symbol', 'market_type', 'timeframe', 'all_timeframes', 'current_price']..."}, {"step_number": 3, "step_name": "Step 2: Tool Selection", "step_type": "api_calls", "timestamp": "2025-09-07T00:03:19.790023", "execution_time": null, "status": "success", "error": null, "input_summary": "Market: crypto, Symbol: BTCUSD"}, {"step_number": 4, "step_name": "Chart Analysis", "step_type": "llm_call", "timestamp": "2025-09-07T00:03:31.763133", "execution_time": 14.16756010055542, "status": "success", "error": null, "output_summary": "Dict with keys: ['detected_symbol', 'market_type', 'timeframe', 'all_timeframes', 'current_price']..."}, {"step_number": 5, "step_name": "Step 2: Tool Selection", "step_type": "api_calls", "timestamp": "2025-09-07T00:03:31.763133", "execution_time": null, "status": "success", "error": null, "input_summary": "Market: crypto, Symbol: BTCUSD"}, {"step_number": 6, "step_name": "Final Analysis", "step_type": "llm_call", "timestamp": "2025-09-07T00:03:33.125424", "execution_time": 35.130972385406494, "status": "success", "error": null, "output_summary": "Dict with keys: ['success', 'analysis', 'analysis_notes', 'trading_signals', 'detected_symbol']..."}], "llm_calls": [], "summary": {"total_steps": 6, "total_llm_calls": 0, "total_tokens": 0, "total_cost": 0.0, "execution_time": 15.548133134841919}, "end_time": "2025-09-07T00:03:33.142142", "status": "success", "error": null}