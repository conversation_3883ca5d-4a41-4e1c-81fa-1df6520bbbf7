#!/usr/bin/env python3
"""
Test script to verify the SSE progress bar race condition fix.

This script tests:
1. Race condition fix - SSE connection established before workflow starts
2. Smooth progress updates - no rapid jumps from 60% to 100%
3. Proper session management between frontend and backend

Usage:
    python test_progress_fix.py
"""

import asyncio
import aiohttp
import json
import time
import base64
from pathlib import Path

class ProgressFixTester:
    def __init__(self):
        self.base_url = "http://localhost:8000"
        self.session_id = None
        self.token = None
        self.progress_updates = []
        
    async def authenticate(self):
        """Authenticate and get access token."""
        print("🔐 Authenticating...")
        
        auth_data = {
            "username": "<EMAIL>",
            "password": "test_password"
        }
        
        async with aiohttp.ClientSession() as session:
            async with session.post(f"{self.base_url}/api/v1/auth/login", json=auth_data) as response:
                if response.status == 200:
                    data = await response.json()
                    self.token = data.get("access_token")
                    print(f"✅ Authentication successful")
                    return True
                else:
                    error = await response.text()
                    print(f"❌ Authentication failed: {response.status} - {error}")
                    return False
    
    async def start_analysis(self):
        """Start analysis and get session ID."""
        print("\n📊 Starting analysis...")
        
        # Load test image
        test_image_path = Path("test_chart.png")
        if not test_image_path.exists():
            # Create a dummy base64 image for testing
            dummy_image = "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=="
        else:
            with open(test_image_path, "rb") as f:
                dummy_image = base64.b64encode(f.read()).decode()
        
        analysis_data = {
            "images_base64": [dummy_image],
            "analysis_type": "Positional",
            "market_specialization": "Crypto",
            "preferred_model": "gemini-2.5-flash",
            "timeframes": ["1h", "4h"],
            "ticker_hint": "BTC/USD",
            "context_hint": "Test analysis for progress fix"
        }
        
        headers = {
            "Authorization": f"Bearer {self.token}",
            "Content-Type": "application/json"
        }
        
        async with aiohttp.ClientSession() as session:
            async with session.post(f"{self.base_url}/api/v1/trading/async/start", 
                                   json=analysis_data, headers=headers) as response:
                if response.status == 200:
                    data = await response.json()
                    self.session_id = data.get("session_id")
                    print(f"✅ Analysis started: {self.session_id}")
                    return True
                else:
                    error = await response.text()
                    print(f"❌ Analysis failed: {response.status} - {error}")
                    return False
    
    async def monitor_progress(self):
        """Monitor SSE progress updates and verify smooth progression."""
        print(f"\n📡 Monitoring progress for session: {self.session_id}")
        print("=" * 60)
        
        sse_url = f"{self.base_url}/api/v1/progress/stream/{self.session_id}?token={self.token}"
        
        start_time = time.time()
        last_progress = 0
        connection_established_time = None
        first_update_time = None
        
        async with aiohttp.ClientSession() as session:
            try:
                async with session.get(sse_url) as response:
                    if response.status != 200:
                        print(f"❌ SSE connection failed: {response.status}")
                        return False
                    
                    print(f"✅ SSE connection established")
                    connection_established_time = time.time()
                    
                    async for line in response.content:
                        line = line.decode('utf-8').strip()
                        
                        if line.startswith('data: '):
                            data_str = line[6:]  # Remove 'data: ' prefix
                            
                            try:
                                data = json.loads(data_str)
                                current_time = time.time()
                                
                                if data.get('type') == 'connected':
                                    print(f"🔗 SSE connection confirmed for session: {data.get('session_id')}")
                                    
                                elif data.get('type') == 'progress_update':
                                    update = data.get('data', {})
                                    progress = update.get('progress', 0)
                                    step = update.get('step', 'unknown')
                                    message = update.get('message', '')
                                    
                                    if first_update_time is None:
                                        first_update_time = current_time
                                        delay_after_connection = first_update_time - connection_established_time
                                        print(f"⏱️ First update received {delay_after_connection:.2f}s after SSE connection")
                                    
                                    # Check for progress jumps
                                    progress_jump = progress - last_progress
                                    time_since_last = current_time - (self.progress_updates[-1]['timestamp'] if self.progress_updates else start_time)
                                    
                                    print(f"📈 {step}: {progress}% - {message}")
                                    print(f"   └─ Jump: +{progress_jump}%, Time: {time_since_last:.2f}s")
                                    
                                    # Flag concerning jumps
                                    if progress_jump > 20 and time_since_last < 1.0:
                                        print(f"   ⚠️ LARGE PROGRESS JUMP DETECTED: +{progress_jump}% in {time_since_last:.2f}s")
                                    
                                    self.progress_updates.append({
                                        'step': step,
                                        'progress': progress,
                                        'message': message,
                                        'timestamp': current_time,
                                        'jump': progress_jump,
                                        'time_since_last': time_since_last
                                    })
                                    
                                    last_progress = progress
                                    
                                elif data.get('type') == 'complete':
                                    print(f"🎉 Analysis completed!")
                                    break
                                    
                            except json.JSONDecodeError:
                                continue  # Skip invalid JSON
                                
            except Exception as e:
                print(f"❌ SSE monitoring error: {e}")
                return False
        
        return True
    
    def analyze_results(self):
        """Analyze the progress updates to verify the fix worked."""
        print(f"\n📊 ANALYSIS RESULTS")
        print("=" * 60)
        
        if not self.progress_updates:
            print("❌ No progress updates received")
            return False
        
        # Check for race condition fix
        first_update = self.progress_updates[0]
        if first_update['progress'] > 10:
            print(f"⚠️ POTENTIAL RACE CONDITION: First update was {first_update['progress']}% (should be ≤10%)")
        else:
            print(f"✅ RACE CONDITION FIXED: First update was {first_update['progress']}%")
        
        # Check for large progress jumps
        large_jumps = [u for u in self.progress_updates if u['jump'] > 20 and u['time_since_last'] < 1.0]
        if large_jumps:
            print(f"⚠️ LARGE JUMPS DETECTED: {len(large_jumps)} jumps > 20% in < 1s")
            for jump in large_jumps:
                print(f"   └─ {jump['step']}: +{jump['jump']}% in {jump['time_since_last']:.2f}s")
        else:
            print(f"✅ SMOOTH PROGRESSION: No large jumps detected")
        
        # Check total progression time
        total_time = self.progress_updates[-1]['timestamp'] - self.progress_updates[0]['timestamp']
        print(f"📊 Total progression time: {total_time:.2f}s")
        
        if total_time < 5:
            print(f"⚠️ PROGRESSION TOO FAST: {total_time:.2f}s (should be > 5s for good UX)")
        else:
            print(f"✅ GOOD PROGRESSION TIMING: {total_time:.2f}s")
        
        # Summary
        print(f"\n📋 SUMMARY:")
        print(f"   • Total updates: {len(self.progress_updates)}")
        print(f"   • Progress range: {first_update['progress']}% → {self.progress_updates[-1]['progress']}%")
        print(f"   • Average time between updates: {total_time / len(self.progress_updates):.2f}s")
        
        return len(large_jumps) == 0 and first_update['progress'] <= 10
    
    async def run_test(self):
        """Run the complete test."""
        print("🧪 TESTING SSE PROGRESS BAR RACE CONDITION FIX")
        print("=" * 60)
        
        # Step 1: Authenticate
        if not await self.authenticate():
            return False
        
        # Step 2: Start analysis
        if not await self.start_analysis():
            return False
        
        # Step 3: Monitor progress
        if not await self.monitor_progress():
            return False
        
        # Step 4: Analyze results
        success = self.analyze_results()
        
        if success:
            print(f"\n🎉 TEST PASSED: Progress bar fix is working correctly!")
        else:
            print(f"\n❌ TEST FAILED: Issues detected with progress bar")
        
        return success

async def main():
    tester = ProgressFixTester()
    success = await tester.run_test()
    exit(0 if success else 1)

if __name__ == "__main__":
    asyncio.run(main())
