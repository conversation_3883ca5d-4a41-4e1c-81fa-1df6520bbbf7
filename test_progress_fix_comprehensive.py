#!/usr/bin/env python3
"""
Comprehensive Progress Bar Fix Test
==================================

This script tests the complete fix for both:
1. Progress bar stuck at step 1 issue
2. Analysis results not displaying issue

Tests the enhanced buffering system with session completion tracking.
"""

import sys
import os
import json
import asyncio
import time
import requests
from pathlib import Path
from datetime import datetime

# Add the backend directory to the Python path
backend_dir = Path(__file__).parent / "Agent_Trading" / "backend"
sys.path.insert(0, str(backend_dir))

class ProgressBarFixTester:
    def __init__(self):
        self.base_url = "http://localhost:8000"
        self.token = None
        self.session_id = None
        
    async def login(self):
        """Login with test credentials."""
        print("🔐 Logging in with test credentials...")
        
        login_data = {
            "email": "<EMAIL>",
            "password": "Bunnych@1627"
        }

        try:
            response = requests.post(
                f"{self.base_url}/api/v1/auth/login",
                json=login_data,
                headers={"Content-Type": "application/json"}
            )
            
            if response.status_code == 200:
                data = response.json()
                self.token = data.get("access_token")
                print(f"✅ Login successful")
                return True
            else:
                print(f"❌ Login failed: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ Login error: {e}")
            return False
    
    async def test_broadcaster_state(self):
        """Test the current state of the ProgressBroadcaster."""
        print("\n🔍 TESTING BROADCASTER STATE")
        print("=" * 50)
        
        try:
            from app.helpers.workflow_management.progress_tracker import get_progress_broadcaster, WorkflowStep, ProgressUpdate
            
            # Get the real singleton instance
            broadcaster = get_progress_broadcaster()
            
            print(f"📊 Broadcaster ID: {id(broadcaster)}")
            print(f"📊 Active sessions: {list(broadcaster.clients.keys())}")
            print(f"📊 Buffered sessions: {list(broadcaster.buffered_updates.keys())}")
            print(f"📊 Completed sessions: {list(broadcaster.completed_sessions)}")
            print(f"📊 Session timestamps: {len(broadcaster.session_timestamps)}")
            
            # Test the enhanced buffering system
            test_session_id = f"test_enhanced_{int(datetime.now().timestamp())}"
            
            print(f"\n🧪 TESTING ENHANCED BUFFERING SYSTEM")
            print(f"📝 Test session: {test_session_id}")
            
            # Create test progress updates
            updates = [
                ProgressUpdate(
                    step=WorkflowStep.CHART_ANALYSIS,
                    progress=20,
                    message="Test chart analysis",
                    timestamp=datetime.now()
                ),
                ProgressUpdate(
                    step=WorkflowStep.SYMBOL_DETECTION,
                    progress=30,
                    message="Test symbol detection",
                    timestamp=datetime.now()
                ),
                ProgressUpdate(
                    step=WorkflowStep.TOOL_EXECUTION,
                    progress=50,
                    message="Test tool execution",
                    timestamp=datetime.now()
                )
            ]
            
            # Broadcast updates without any clients connected
            print(f"📡 Broadcasting {len(updates)} updates without clients...")
            for i, update in enumerate(updates):
                await broadcaster.broadcast_update(test_session_id, update)
                print(f"   {i+1}. {update.step.value} ({update.progress}%)")
            
            # Check if updates were buffered
            buffered_count = len(broadcaster.buffered_updates.get(test_session_id, []))
            print(f"📦 Buffered updates: {buffered_count}")
            
            if buffered_count == len(updates):
                print(f"✅ All updates buffered correctly")
            else:
                print(f"❌ Expected {len(updates)} buffered updates, got {buffered_count}")
                return False
            
            # Test client connection and replay
            print(f"\n📱 Testing client connection and replay...")
            client_queue = await broadcaster.add_client(test_session_id)
            
            # Try to receive all buffered updates
            received_updates = []
            for i in range(buffered_count):
                try:
                    update = await asyncio.wait_for(client_queue.get(), timeout=1.0)
                    received_updates.append(update)
                    step = update.get('step', 'unknown')
                    progress = update.get('progress', 0)
                    print(f"   ✅ Received: {step} ({progress}%)")
                except asyncio.TimeoutError:
                    print(f"   ⏰ Timeout waiting for update {i+1}")
                    break
            
            if len(received_updates) == buffered_count:
                print(f"✅ All buffered updates replayed successfully")
            else:
                print(f"❌ Expected {buffered_count} replayed updates, got {len(received_updates)}")
                return False
            
            # Test client disconnection (should NOT delete buffered updates)
            print(f"\n🔌 Testing client disconnection...")
            broadcaster.remove_client(test_session_id, client_queue)
            
            # Check if buffered updates are still there
            buffered_after_disconnect = len(broadcaster.buffered_updates.get(test_session_id, []))
            print(f"📦 Buffered updates after disconnect: {buffered_after_disconnect}")
            
            if buffered_after_disconnect == buffered_count:
                print(f"✅ Buffered updates preserved after client disconnect")
            else:
                print(f"❌ Buffered updates were deleted after disconnect")
                return False
            
            # Test session completion
            print(f"\n🏁 Testing session completion...")
            broadcaster.mark_session_complete(test_session_id)
            
            # Check if session is marked as complete
            if test_session_id in broadcaster.completed_sessions:
                print(f"✅ Session marked as complete")
            else:
                print(f"❌ Session not marked as complete")
                return False
            
            # Test cleanup of completed session
            print(f"\n🧹 Testing cleanup of completed session...")
            # Since no clients are connected, buffered updates should be cleaned up
            if test_session_id not in broadcaster.clients:
                # Manually trigger cleanup for completed session
                if test_session_id in broadcaster.buffered_updates:
                    del broadcaster.buffered_updates[test_session_id]
                    print(f"✅ Buffered updates cleaned up for completed session")
                
                if test_session_id in broadcaster.session_timestamps:
                    del broadcaster.session_timestamps[test_session_id]
                    print(f"✅ Session timestamp cleaned up")
                
                broadcaster.completed_sessions.discard(test_session_id)
                print(f"✅ Session removed from completed sessions")
            
            return True
            
        except Exception as e:
            print(f"❌ Error testing broadcaster state: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    async def test_end_to_end_flow(self):
        """Test the complete end-to-end flow with a real analysis."""
        print(f"\n🔄 TESTING END-TO-END FLOW")
        print("=" * 50)
        
        if not self.token:
            print(f"❌ No authentication token available")
            return False
        
        # Create a simple test image (base64 encoded 1x1 pixel PNG)
        test_image_b64 = "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=="
        
        # Start analysis
        print(f"🚀 Starting test analysis...")
        
        analysis_data = {
            "images_base64": [test_image_b64],
            "analysis_type": "positional",
            "market_specialization": "Crypto"
        }
        
        headers = {
            "Authorization": f"Bearer {self.token}",
            "Content-Type": "application/json"
        }
        
        try:
            response = requests.post(
                f"{self.base_url}/api/v1/async-trading/analyze",
                json=analysis_data,
                headers=headers
            )
            
            if response.status_code == 200:
                data = response.json()
                self.session_id = data.get("session_id")
                print(f"✅ Analysis started: {self.session_id}")
                
                # Test SSE connection immediately
                await self.test_sse_connection()
                
                return True
            else:
                print(f"❌ Analysis start failed: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ Analysis start error: {e}")
            return False
    
    async def test_sse_connection(self):
        """Test SSE connection and progress reception."""
        if not self.session_id or not self.token:
            print(f"❌ Missing session ID or token for SSE test")
            return False
        
        print(f"\n📡 TESTING SSE CONNECTION")
        print(f"Session: {self.session_id}")
        
        # Use a simple HTTP request to test SSE endpoint
        sse_url = f"{self.base_url}/api/v1/progress/stream/{self.session_id}?token={self.token}"
        
        try:
            # Test if SSE endpoint is accessible
            response = requests.get(sse_url, stream=True, timeout=5)
            
            if response.status_code == 200:
                print(f"✅ SSE endpoint accessible")
                
                # Read a few events
                events_received = 0
                for line in response.iter_lines(decode_unicode=True):
                    if line.startswith('data:'):
                        try:
                            data = json.loads(line[5:])  # Remove 'data:' prefix
                            event_type = data.get('type', 'unknown')
                            print(f"📨 Received SSE event: {event_type}")
                            
                            if event_type == 'progress_update':
                                progress_data = data.get('data', {})
                                step = progress_data.get('step', 'unknown')
                                progress = progress_data.get('progress', 0)
                                print(f"   📈 Progress: {step} ({progress}%)")
                            
                            events_received += 1
                            if events_received >= 3:  # Stop after a few events
                                break
                                
                        except json.JSONDecodeError:
                            continue
                
                print(f"✅ Received {events_received} SSE events")
                return True
            else:
                print(f"❌ SSE endpoint failed: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ SSE connection error: {e}")
            return False

async def main():
    """Main test function."""
    print("🔍 COMPREHENSIVE PROGRESS BAR FIX TEST")
    print(f"Start time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"Working directory: {os.getcwd()}")
    
    tester = ProgressBarFixTester()
    
    # Test 1: Broadcaster state and enhanced buffering
    broadcaster_ok = await tester.test_broadcaster_state()
    
    # Test 2: Authentication
    login_ok = await tester.login()
    
    # Test 3: End-to-end flow (if login successful)
    e2e_ok = False
    if login_ok:
        e2e_ok = await tester.test_end_to_end_flow()
    
    print(f"\n{'='*50}")
    print("📊 COMPREHENSIVE TEST SUMMARY:")
    print(f"   Enhanced Buffering System: {'✅ WORKING' if broadcaster_ok else '❌ ISSUES FOUND'}")
    print(f"   Authentication: {'✅ WORKING' if login_ok else '❌ FAILED'}")
    print(f"   End-to-End Flow: {'✅ WORKING' if e2e_ok else '❌ FAILED'}")
    
    if broadcaster_ok and login_ok and e2e_ok:
        print(f"\n🎉 ALL TESTS PASSED! Progress bar fix is working correctly.")
        return True
    else:
        print(f"\n⚠️  Some tests failed. Check the output above for details.")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
