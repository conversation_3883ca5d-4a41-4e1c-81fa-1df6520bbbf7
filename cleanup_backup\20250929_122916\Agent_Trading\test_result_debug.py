#!/usr/bin/env python3
"""
Debug script to test the result endpoint and see what's happening
"""
import requests
import json
import time

def test_result_endpoint():
    """Test the result endpoint with the actual session ID from logs"""
    
    base_url = "http://localhost:8000"
    session_id = "async_e549415c6691"  # From the logs
    
    # Test the result endpoint
    url = f"{base_url}/api/v1/trading/async/result/{session_id}"
    
    print(f"🧪 Testing result endpoint: {url}")
    
    try:
        # Test without auth first
        response = requests.get(url)
        print(f"📊 Status Code: {response.status_code}")
        print(f"📊 Response Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Result endpoint working!")
            print(f"📊 Response structure: {list(data.keys())}")
            print(f"📊 Success: {data.get('success')}")
            print(f"📊 Status: {data.get('status')}")
            print(f"📊 Has Data: {data.get('data') is not None}")
            print(f"📊 Error: {data.get('error')}")
            
            if data.get('data'):
                print(f"📊 Data keys: {list(data['data'].keys())}")
                
        elif response.status_code == 401:
            print("🔐 Authentication required - this is expected")
            print("📊 Response:", response.text)
        else:
            print(f"❌ Result endpoint failed with status {response.status_code}")
            print(f"📊 Response: {response.text}")
            
    except Exception as e:
        print(f"❌ Error testing endpoint: {e}")

def test_service_status():
    """Test the service status endpoint"""
    
    base_url = "http://localhost:8000"
    url = f"{base_url}/api/v1/trading/async/status"
    
    print(f"\n🧪 Testing service status: {url}")
    
    try:
        response = requests.get(url)
        print(f"📊 Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Service status endpoint working!")
            print(f"📊 Active sessions: {data.get('active_sessions_count', 0)}")
            print(f"📊 Service status: {data.get('service_status')}")
            
        elif response.status_code == 401:
            print("🔐 Authentication required - this is expected")
        else:
            print(f"❌ Service status failed with status {response.status_code}")
            print(f"📊 Response: {response.text}")
            
    except Exception as e:
        print(f"❌ Error testing service status: {e}")

if __name__ == "__main__":
    print("🧪 Testing result endpoint and service status...")
    test_result_endpoint()
    test_service_status()
    print("\n✅ Debug test completed!")
