#!/usr/bin/env python3
"""
Test Both Fixes - Progress Broadcasting & Data Display
"""

import requests
import time
import json

def test_both_fixes():
    print("🧪 TESTING BOTH FIXES")
    print("=" * 50)
    
    base_url = "http://localhost:8000"
    
    # Step 1: Login
    print("1️⃣ Logging in...")
    login_response = requests.post(f"{base_url}/api/v1/auth/login", json={
        "email": "<EMAIL>",
        "password": "Bunnych@1627"
    })
    
    if login_response.status_code != 200:
        print(f"❌ Login failed: {login_response.status_code}")
        return
        
    token = login_response.json()["access_token"]
    headers = {"Authorization": f"Bearer {token}"}
    print("✅ Login successful")
    
    # Step 2: Start analysis
    print("\n2️⃣ Starting analysis...")
    with open("Agent_Trading/Testing_images/Screenshot 2025-08-03 190956.png", "rb") as f:
        files = {"chart_images": f}
        data = {
            "analysis_mode": "positional",
            "market_specialization": "Crypto"
        }
        
        analysis_response = requests.post(
            f"{base_url}/api/v1/trading/analyze-async",
            files=files,
            data=data,
            headers=headers
        )
    
    if analysis_response.status_code != 200:
        print(f"❌ Analysis failed: {analysis_response.status_code}")
        return
        
    session_id = analysis_response.json()["session_id"]
    print(f"✅ Analysis started with session: {session_id}")
    
    # Step 3: Monitor progress
    print("\n3️⃣ Monitoring progress...")
    start_time = time.time()
    
    while True:
        result_response = requests.get(
            f"{base_url}/api/v1/trading/result/{session_id}",
            headers=headers
        )
        
        if result_response.status_code != 200:
            print(f"❌ Result check failed: {result_response.status_code}")
            break
            
        result_data = result_response.json()
        status = result_data.get("status", "unknown")
        
        if status == "completed":
            elapsed = time.time() - start_time
            print(f"🎉 Analysis completed in {elapsed:.1f} seconds!")
            
            # Immediately debug the data structure
            print("\n4️⃣ DEBUGGING DATA STRUCTURE:")
            print("=" * 50)
            
            # Check the complete structure
            data = result_data.get('data', {})
            print(f"📊 Top-level keys: {list(data.keys())}")
            
            if 'data' in data:
                nested_data = data['data']
                print(f"📊 Nested data keys: {list(nested_data.keys())}")
                
                # Check for the key fields
                symbol = nested_data.get('detected_symbol', 'NOT_FOUND')
                market = nested_data.get('market_type', 'NOT_FOUND')
                print(f"   🎯 Symbol: {symbol}")
                print(f"   🎯 Market: {market}")
                
                # Check trading signals
                if 'trading_signals' in nested_data:
                    signals = nested_data['trading_signals']
                    print(f"   📊 Trading signals keys: {list(signals.keys())}")
                    
                    # Check analysis summary
                    summary = signals.get('analysis_summary', '')
                    if summary:
                        print(f"   ✅ Analysis summary found: {len(summary)} chars")
                        print(f"   📝 Preview: '{summary[:100]}...'")
                    else:
                        print(f"   ❌ Analysis summary empty: {repr(summary)}")
                        
                    # Check trade ideas
                    trade_ideas = signals.get('trade_ideas', [])
                    print(f"   📈 Trade ideas: {len(trade_ideas)} found")
                    
                    # Check key levels
                    key_levels = signals.get('key_levels', {})
                    if key_levels:
                        support = key_levels.get('support', [])
                        resistance = key_levels.get('resistance', [])
                        print(f"   🎯 Support levels: {len(support)}")
                        print(f"   🎯 Resistance levels: {len(resistance)}")
                else:
                    print("   ❌ No trading_signals found")
            else:
                print("   ❌ No nested data found")
            
            break
        elif status == "running":
            elapsed = time.time() - start_time
            print(f"⏳ Status: running ({elapsed:.1f}s)")
        else:
            print(f"❓ Status: {status}")
            
        time.sleep(3)
        
        if time.time() - start_time > 120:  # 2 minute timeout
            print("⏰ Timeout reached")
            break
    
    # Step 4: Check progress logs
    print(f"\n5️⃣ Checking progress logs for session: {session_id}")
    try:
        with open("Agent_Trading/backend/logs/progress_debug.log", "r") as f:
            logs = f.read()
            
        # Look for our session
        session_short = session_id.replace("async_", "")
        if session_short in logs:
            print("   ✅ Session found in progress logs")
            
            # Check for progress broadcaster
            if f"Session: {session_id}" in logs:
                print("   ✅ Full session ID found in logs")
            elif "Session: unknown" in logs:
                print("   ❌ Session showing as 'unknown' in logs")
                
            # Check for has_progress_broadcaster
            if "'has_progress_broadcaster': True" in logs:
                print("   ✅ Progress broadcaster is working")
            elif "'has_progress_broadcaster': False" in logs:
                print("   ❌ Progress broadcaster is False")
                
        else:
            print("   ❌ Session not found in progress logs")
            
    except Exception as e:
        print(f"   ❌ Error reading logs: {e}")
    
    # Step 5: Check backend logs for our debug message
    print(f"\n6️⃣ Checking backend logs for debug info...")
    try:
        with open("Agent_Trading/backend/logs/backend_debug.log", "r") as f:
            logs = f.read()
            
        if "WORKFLOW_PARAMS" in logs:
            print("   ✅ Found workflow params debug info")
            # Extract the last few WORKFLOW_PARAMS entries
            lines = logs.split('\n')
            for line in reversed(lines):
                if "WORKFLOW_PARAMS" in line:
                    print(f"   📝 {line}")
                    break
        else:
            print("   ❌ No workflow params debug info found")
            
    except Exception as e:
        print(f"   ❌ Error reading backend logs: {e}")

if __name__ == "__main__":
    test_both_fixes()
