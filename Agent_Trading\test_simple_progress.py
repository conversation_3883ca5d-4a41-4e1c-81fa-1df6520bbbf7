#!/usr/bin/env python3
"""
Simple test to verify the SSE progress fix is working.
This test checks if the race condition is fixed by monitoring the timing.
"""

import asyncio
import aiohttp
import json
import time

async def test_progress_timing():
    """Test if progress updates are properly delayed until SSE connection."""
    
    base_url = "http://localhost:8000"
    
    # Test data
    analysis_data = {
        "images_base64": ["iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=="],
        "analysis_type": "Positional",
        "market_specialization": "Crypto",
        "preferred_model": "gemini-2.5-flash",
        "timeframes": ["1h"],
        "ticker_hint": "BTC/USD",
        "context_hint": "Test for progress fix"
    }
    
    # Get auth token (you'll need to replace this with a valid token)
    token = "your_auth_token_here"  # Replace with actual token
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    print("🧪 Testing SSE Progress Timing Fix")
    print("=" * 50)
    
    try:
        # Start analysis
        async with aiohttp.ClientSession() as session:
            print("📊 Starting analysis...")
            start_time = time.time()
            
            async with session.post(f"{base_url}/api/v1/trading/async/start", 
                                   json=analysis_data, headers=headers) as response:
                if response.status == 200:
                    data = await response.json()
                    session_id = data.get("session_id")
                    print(f"✅ Analysis started: {session_id}")
                    
                    # Immediately connect to SSE
                    sse_url = f"{base_url}/api/v1/progress/stream/{session_id}?token={token}"
                    print(f"🔗 Connecting to SSE: {sse_url}")
                    
                    connection_time = time.time()
                    
                    async with session.get(sse_url) as sse_response:
                        if sse_response.status == 200:
                            print(f"✅ SSE connected after {connection_time - start_time:.2f}s")
                            
                            first_update_time = None
                            update_count = 0
                            
                            async for line in sse_response.content:
                                line = line.decode('utf-8').strip()
                                
                                if line.startswith('data: '):
                                    data_str = line[6:]
                                    
                                    try:
                                        data = json.loads(data_str)
                                        current_time = time.time()
                                        
                                        if data.get('type') == 'progress_update':
                                            update = data.get('data', {})
                                            progress = update.get('progress', 0)
                                            step = update.get('step', 'unknown')
                                            
                                            if first_update_time is None:
                                                first_update_time = current_time
                                                delay_after_connection = first_update_time - connection_time
                                                print(f"⏱️ First update after {delay_after_connection:.2f}s")
                                                
                                                if delay_after_connection > 0.5:
                                                    print(f"✅ RACE CONDITION FIXED: Proper delay detected")
                                                else:
                                                    print(f"⚠️ POTENTIAL ISSUE: Very quick first update")
                                            
                                            update_count += 1
                                            print(f"📈 Update {update_count}: {step} ({progress}%)")
                                            
                                            if data.get('type') == 'complete' or progress >= 100:
                                                print(f"🎉 Analysis completed after {update_count} updates")
                                                break
                                                
                                    except json.JSONDecodeError:
                                        continue
                        else:
                            print(f"❌ SSE connection failed: {sse_response.status}")
                else:
                    error = await response.text()
                    print(f"❌ Analysis failed: {response.status} - {error}")
                    
    except Exception as e:
        print(f"❌ Test error: {e}")

if __name__ == "__main__":
    print("Note: You need to replace 'your_auth_token_here' with a valid token")
    print("You can get a token by logging into the frontend and checking localStorage")
    print()
    # asyncio.run(test_progress_timing())
