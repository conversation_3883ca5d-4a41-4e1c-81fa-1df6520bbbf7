#!/usr/bin/env python3
"""
FINAL SOLUTION TEST
Tests both progress broadcasting and data display with comprehensive debugging
"""

import requests
import time
import json
import subprocess

def test_final_solution():
    print("🎯 FINAL SOLUTION TEST")
    print("=" * 60)
    
    base_url = "http://localhost:8000"
    
    # Step 1: Login
    print("1️⃣ Logging in...")
    try:
        login_response = requests.post(f"{base_url}/api/v1/auth/login", json={
            "email": "<EMAIL>",
            "password": "Bunnych@1627"
        })
        
        if login_response.status_code != 200:
            print(f"❌ Login failed: {login_response.status_code}")
            return
            
        token = login_response.json()["access_token"]
        headers = {"Authorization": f"Bearer {token}"}
        print("✅ Login successful")
    except Exception as e:
        print(f"❌ Login error: {e}")
        return
    
    # Step 2: Start analysis
    print("\n2️⃣ Starting analysis...")
    try:
        with open("Agent_Trading/Testing_images/Screenshot 2025-08-03 190956.png", "rb") as f:
            files = {"chart_images": f}
            data = {
                "analysis_mode": "positional",
                "market_specialization": "Crypto"
            }
            
            analysis_response = requests.post(
                f"{base_url}/api/v1/trading/analyze-async",
                files=files,
                data=data,
                headers=headers
            )
        
        if analysis_response.status_code != 200:
            print(f"❌ Analysis failed: {analysis_response.status_code}")
            print(f"Response: {analysis_response.text}")
            return
            
        session_id = analysis_response.json()["session_id"]
        print(f"✅ Analysis started with session: {session_id}")
    except Exception as e:
        print(f"❌ Analysis error: {e}")
        return
    
    # Step 3: Monitor progress with detailed tracking
    print("\n3️⃣ Monitoring progress...")
    start_time = time.time()
    progress_updates = []
    
    while True:
        try:
            result_response = requests.get(
                f"{base_url}/api/v1/trading/result/{session_id}",
                headers=headers
            )
            
            if result_response.status_code != 200:
                print(f"❌ Result check failed: {result_response.status_code}")
                break
                
            result_data = result_response.json()
            status = result_data.get("status", "unknown")
            elapsed = time.time() - start_time
            
            if status == "completed":
                print(f"🎉 Analysis completed in {elapsed:.1f} seconds!")
                
                # IMMEDIATE DATA STRUCTURE ANALYSIS
                print("\n4️⃣ IMMEDIATE DATA STRUCTURE ANALYSIS:")
                print("=" * 60)
                
                # Check the complete structure
                data = result_data.get('data', {})
                print(f"📊 Top-level keys: {list(data.keys())}")
                
                if 'data' in data:
                    nested_data = data['data']
                    print(f"📊 Nested data keys: {list(nested_data.keys())}")
                    
                    # Extract key information
                    symbol = nested_data.get('detected_symbol', 'NOT_FOUND')
                    market = nested_data.get('market_type', 'NOT_FOUND')
                    success = nested_data.get('success', False)
                    
                    print(f"   🎯 Symbol: {symbol}")
                    print(f"   🎯 Market: {market}")
                    print(f"   🎯 Success: {success}")
                    
                    # Check trading signals
                    if 'trading_signals' in nested_data:
                        signals = nested_data['trading_signals']
                        print(f"   📊 Trading signals keys: {list(signals.keys())}")
                        
                        # Check each field
                        status_field = signals.get('status', 'NOT_FOUND')
                        summary = signals.get('analysis_summary', '')
                        trade_ideas = signals.get('trade_ideas', [])
                        key_levels = signals.get('key_levels', {})
                        market_context = signals.get('market_context', '')
                        
                        print(f"   📝 Status: {status_field}")
                        print(f"   📝 Summary length: {len(summary) if summary else 0} chars")
                        print(f"   📈 Trade ideas: {len(trade_ideas)} found")
                        print(f"   🎯 Key levels: {list(key_levels.keys()) if key_levels else 'None'}")
                        print(f"   🌍 Market context: {len(market_context) if market_context else 0} chars")
                        
                        # Show preview of content
                        if summary:
                            print(f"   📖 Summary preview: '{summary[:150]}...'")
                        if trade_ideas:
                            print(f"   💡 First trade idea: {trade_ideas[0] if trade_ideas else 'None'}")
                            
                    else:
                        print("   ❌ No trading_signals found")
                        
                    # Check for any other analysis fields
                    other_fields = [k for k in nested_data.keys() if k not in ['detected_symbol', 'market_type', 'success', 'trading_signals']]
                    if other_fields:
                        print(f"   📋 Other fields: {other_fields}")
                        
                else:
                    print("   ❌ No nested data found")
                    
                break
                
            elif status == "running":
                print(f"⏳ Status: running ({elapsed:.1f}s)")
                progress_updates.append(f"{elapsed:.1f}s")
            else:
                print(f"❓ Status: {status} ({elapsed:.1f}s)")
                
            time.sleep(3)
            
            if elapsed > 120:  # 2 minute timeout
                print("⏰ Timeout reached")
                break
                
        except Exception as e:
            print(f"❌ Monitoring error: {e}")
            break
    
    # Step 4: Check progress logs
    print(f"\n5️⃣ PROGRESS LOGS ANALYSIS:")
    print("=" * 60)
    
    try:
        # Check for our session in progress logs
        result = subprocess.run(
            ["grep", "-A", "3", "-B", "3", session_id.replace("async_", ""), "Agent_Trading/backend/logs/progress_debug.log"],
            capture_output=True, text=True
        )
        
        if result.returncode == 0 and result.stdout:
            print("✅ Session found in progress logs:")
            print(result.stdout)
        else:
            print("❌ Session not found in progress logs")
            
        # Check for workflow params debug
        result2 = subprocess.run(
            ["grep", "WORKFLOW_RECEIVED", "Agent_Trading/backend/logs/backend_debug.log"],
            capture_output=True, text=True
        )
        
        if result2.returncode == 0 and result2.stdout:
            print("\n✅ Workflow params debug found:")
            lines = result2.stdout.strip().split('\n')
            for line in lines[-3:]:  # Show last 3 entries
                print(f"   {line}")
        else:
            print("\n❌ No workflow params debug found")
            
    except Exception as e:
        print(f"❌ Log analysis error: {e}")
    
    print(f"\n6️⃣ SUMMARY:")
    print("=" * 60)
    print(f"📊 Progress updates tracked: {len(progress_updates)}")
    print(f"⏱️  Total execution time: {elapsed:.1f}s")
    
    # Final recommendations
    print(f"\n🎯 NEXT STEPS:")
    if len(progress_updates) < 5:
        print("❌ Progress bar issue: Too few progress updates detected")
        print("   → Check if workflow is receiving progress_broadcaster correctly")
    else:
        print("✅ Progress bar: Multiple updates detected")
        
    print("📱 Frontend testing:")
    print("   → Open http://localhost:3000")
    print("   → Login and run analysis")
    print("   → Check browser console for data structure logs")
    print("   → Verify analysis display shows rich content")

if __name__ == "__main__":
    test_final_solution()
