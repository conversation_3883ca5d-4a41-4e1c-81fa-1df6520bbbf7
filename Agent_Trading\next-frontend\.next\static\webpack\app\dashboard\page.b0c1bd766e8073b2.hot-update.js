"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/hooks/useRealTimeProgress.ts":
/*!******************************************!*\
  !*** ./src/hooks/useRealTimeProgress.ts ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WORKFLOW_STEPS: () => (/* binding */ WORKFLOW_STEPS),\n/* harmony export */   useRealTimeProgress: () => (/* binding */ useRealTimeProgress)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/**\r\n * Real-time progress tracking hook for LangGraph AI analysis workflow\r\n * Uses Server-Sent Events ONLY for live updates (no polling fallback)\r\n */ \n// ✅ ALIGNED: Frontend mapping now matches actual LangGraph workflow execution\nconst WORKFLOW_STEPS = {\n    CHART_ANALYSIS: {\n        name: 'Chart Analysis',\n        progress: 20,\n        message: '📊 Starting comprehensive AI analysis workflow...'\n    },\n    SYMBOL_DETECTION: {\n        name: 'Symbol Detection',\n        progress: 40,\n        message: '✅ Symbol detected in chart'\n    },\n    TOOL_EXECUTION: {\n        name: 'Data Collection',\n        progress: 50,\n        message: '🛠️ Collecting data from tools...'\n    },\n    TOOL_SUMMARIZATION: {\n        name: 'Data Processing',\n        progress: 60,\n        message: '📋 Processing and summarizing data...'\n    },\n    RAG_INTEGRATION: {\n        name: 'RAG Integration',\n        progress: 70,\n        message: '🧠 Integrating historical context and patterns...'\n    },\n    FINAL_ANALYSIS: {\n        name: 'Final Analysis',\n        progress: 80,\n        message: '🎯 Generating final trading analysis...'\n    },\n    MEMORY_STORAGE: {\n        name: 'Database Storage',\n        progress: 90,\n        message: '💾 Saving analysis...'\n    },\n    COMPLETE: {\n        name: 'Complete',\n        progress: 100,\n        message: '🎉 Analysis complete!'\n    }\n};\nfunction useRealTimeProgress() {\n    const [state, setState] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({\n        sessionId: null,\n        currentStep: 'chart_analysis',\n        progress: 0,\n        message: 'Preparing for analysis...',\n        isConnected: false,\n        isComplete: false,\n        error: null,\n        updates: []\n    });\n    // Add retry state\n    const [retryCount, setRetryCount] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(0);\n    const maxRetries = 3;\n    // 🔧 CRITICAL FIX: Progress smoothing for rapid updates\n    const [progressQueue, setProgressQueue] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [isProcessingQueue, setIsProcessingQueue] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    // Process progress queue with smooth animations\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useRealTimeProgress.useEffect\": ()=>{\n            if (progressQueue.length > 0 && !isProcessingQueue) {\n                setIsProcessingQueue(true);\n                const processNext = {\n                    \"useRealTimeProgress.useEffect.processNext\": ()=>{\n                        setProgressQueue({\n                            \"useRealTimeProgress.useEffect.processNext\": (queue)=>{\n                                if (queue.length === 0) {\n                                    setIsProcessingQueue(false);\n                                    return queue;\n                                }\n                                const [nextUpdate, ...remaining] = queue;\n                                // Apply the update\n                                setState({\n                                    \"useRealTimeProgress.useEffect.processNext\": (prev)=>({\n                                            ...prev,\n                                            currentStep: nextUpdate.step,\n                                            progress: nextUpdate.progress,\n                                            message: nextUpdate.message\n                                        })\n                                }[\"useRealTimeProgress.useEffect.processNext\"]);\n                                // 🔧 IMPROVED: Dynamic delay based on queue length and progress difference\n                                let delay = 400 // Base delay reduced from 800ms to 400ms\n                                ;\n                                if (remaining.length > 0) {\n                                    const nextProgress = remaining[0].progress;\n                                    const currentProgress = nextUpdate.progress;\n                                    const progressDiff = nextProgress - currentProgress;\n                                    // Shorter delay for small progress jumps, longer for big jumps\n                                    if (progressDiff <= 10) {\n                                        delay = 300; // Fast updates for small increments\n                                    } else if (progressDiff <= 20) {\n                                        delay = 500; // Medium delay for medium increments\n                                    } else {\n                                        delay = 700; // Longer delay for big jumps\n                                    }\n                                    // If queue is getting long, speed up processing\n                                    if (remaining.length > 3) {\n                                        delay = Math.max(200, delay * 0.6); // Speed up but not too fast\n                                    }\n                                    console.log(\"\\uD83D\\uDCC8 FRONTEND_DEBUG: Processing queue - current: \".concat(currentProgress, \"%, next: \").concat(nextProgress, \"%, delay: \").concat(delay, \"ms, queue length: \").concat(remaining.length));\n                                    setTimeout(processNext, delay);\n                                } else {\n                                    setIsProcessingQueue(false);\n                                }\n                                return remaining;\n                            }\n                        }[\"useRealTimeProgress.useEffect.processNext\"]);\n                    }\n                }[\"useRealTimeProgress.useEffect.processNext\"];\n                processNext();\n            }\n        }\n    }[\"useRealTimeProgress.useEffect\"], [\n        progressQueue,\n        isProcessingQueue\n    ]);\n    const eventSourceRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const currentSessionRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    // Retry connection function\n    const retryConnection = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useRealTimeProgress.useCallback[retryConnection]\": async function(sessionId) {\n            let attempt = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 1;\n            if (attempt > maxRetries) {\n                console.error(\"❌ FRONTEND_DEBUG: Max retries (\".concat(maxRetries, \") exceeded for session \").concat(sessionId));\n                setState({\n                    \"useRealTimeProgress.useCallback[retryConnection]\": (prev)=>({\n                            ...prev,\n                            error: \"Connection failed after \".concat(maxRetries, \" attempts. Please refresh and try again.\")\n                        })\n                }[\"useRealTimeProgress.useCallback[retryConnection]\"]);\n                return;\n            }\n            console.log(\"\\uD83D\\uDD04 FRONTEND_DEBUG: Retry attempt \".concat(attempt, \"/\").concat(maxRetries, \" for session \").concat(sessionId));\n            setState({\n                \"useRealTimeProgress.useCallback[retryConnection]\": (prev)=>({\n                        ...prev,\n                        error: null,\n                        message: \"Reconnecting... (attempt \".concat(attempt, \"/\").concat(maxRetries, \")\")\n                    })\n            }[\"useRealTimeProgress.useCallback[retryConnection]\"]);\n            // Wait before retry (exponential backoff)\n            const delay = Math.min(1000 * Math.pow(2, attempt - 1), 5000);\n            await new Promise({\n                \"useRealTimeProgress.useCallback[retryConnection]\": (resolve)=>setTimeout(resolve, delay)\n            }[\"useRealTimeProgress.useCallback[retryConnection]\"]);\n            // Retry the connection\n            await connectToProgress(sessionId);\n        }\n    }[\"useRealTimeProgress.useCallback[retryConnection]\"], [\n        maxRetries\n    ]);\n    // Check if analysis is already complete\n    const checkAnalysisStatus = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useRealTimeProgress.useCallback[checkAnalysisStatus]\": async (sessionId)=>{\n            try {\n                const token = localStorage.getItem('access_token');\n                const baseURL = \"http://localhost:8000\" || 0;\n                const response = await fetch(\"\".concat(baseURL, \"/api/v1/async-trading/status/\").concat(sessionId), {\n                    headers: {\n                        'Authorization': \"Bearer \".concat(token),\n                        'Content-Type': 'application/json'\n                    }\n                });\n                if (response.ok) {\n                    const data = await response.json();\n                    if (data.status === 'completed') {\n                        setState({\n                            \"useRealTimeProgress.useCallback[checkAnalysisStatus]\": (prev)=>({\n                                    ...prev,\n                                    isComplete: true,\n                                    progress: 100,\n                                    currentStep: 'complete',\n                                    message: 'Analysis completed successfully!'\n                                })\n                        }[\"useRealTimeProgress.useCallback[checkAnalysisStatus]\"]);\n                        return true;\n                    }\n                }\n            } catch (error) {\n                console.error('Failed to check analysis status:', error);\n            }\n            return false;\n        }\n    }[\"useRealTimeProgress.useCallback[checkAnalysisStatus]\"], []);\n    const connectToProgress = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useRealTimeProgress.useCallback[connectToProgress]\": async (sessionId)=>{\n            // Close existing SSE connection\n            if (eventSourceRef.current) {\n                eventSourceRef.current.close();\n            }\n            // 🔧 CRITICAL FIX: Update state with the provided session ID IMMEDIATELY\n            // This ensures the UI shows the correct session ID right away\n            console.log('🔧 FRONTEND_DEBUG: Setting session ID immediately:', sessionId);\n            setState({\n                \"useRealTimeProgress.useCallback[connectToProgress]\": (prev)=>({\n                        ...prev,\n                        sessionId,\n                        error: null,\n                        currentStep: 'chart_analysis',\n                        progress: 0,\n                        message: 'Connecting to progress stream...',\n                        isConnected: false,\n                        isComplete: false\n                    })\n            }[\"useRealTimeProgress.useCallback[connectToProgress]\"]);\n            // First check if analysis is already complete\n            const isComplete = await checkAnalysisStatus(sessionId);\n            if (isComplete) {\n                return;\n            }\n            const token = localStorage.getItem('access_token');\n            const baseURL = \"http://localhost:8000\" || 0;\n            if (!token) {\n                console.error('❌ No access token found for SSE connection');\n                setState({\n                    \"useRealTimeProgress.useCallback[connectToProgress]\": (prev)=>({\n                            ...prev,\n                            error: 'Authentication required for progress tracking'\n                        })\n                }[\"useRealTimeProgress.useCallback[connectToProgress]\"]);\n                return;\n            }\n            // Try SSE first - Fixed URL construction and added better error handling\n            const sseUrl = \"\".concat(baseURL, \"/api/v1/progress/stream/\").concat(sessionId, \"?token=\").concat(encodeURIComponent(token));\n            console.log('🔗 FRONTEND_DEBUG: Attempting SSE connection');\n            console.log('🔗 FRONTEND_DEBUG: SSE URL:', sseUrl);\n            console.log('🔗 FRONTEND_DEBUG: Session ID:', sessionId);\n            console.log('🔗 FRONTEND_DEBUG: Token present:', !!token);\n            console.log('🔗 FRONTEND_DEBUG: Token length:', token === null || token === void 0 ? void 0 : token.length);\n            console.log('🔗 FRONTEND_DEBUG: Base URL:', baseURL);\n            // Create EventSource with proper error handling\n            let eventSource;\n            try {\n                eventSource = new EventSource(sseUrl);\n                console.log('✅ FRONTEND_DEBUG: EventSource created successfully');\n            } catch (error) {\n                console.error('❌ FRONTEND_DEBUG: Failed to create EventSource:', error);\n                setState({\n                    \"useRealTimeProgress.useCallback[connectToProgress]\": (prev)=>({\n                            ...prev,\n                            error: \"Failed to create SSE connection: \".concat(error)\n                        })\n                }[\"useRealTimeProgress.useCallback[connectToProgress]\"]);\n                return;\n            }\n            eventSourceRef.current = eventSource;\n            let sseConnected = false;\n            // SSE connection timeout (no polling fallback)\n            const sseTimeout = setTimeout({\n                \"useRealTimeProgress.useCallback[connectToProgress].sseTimeout\": ()=>{\n                    if (!sseConnected) {\n                        console.log('⚠️ SSE connection timeout - no polling fallback');\n                        eventSource.close();\n                        setState({\n                            \"useRealTimeProgress.useCallback[connectToProgress].sseTimeout\": (prev)=>({\n                                    ...prev,\n                                    isConnected: false,\n                                    error: 'SSE connection timeout - check backend connection'\n                                })\n                        }[\"useRealTimeProgress.useCallback[connectToProgress].sseTimeout\"]);\n                    }\n                }\n            }[\"useRealTimeProgress.useCallback[connectToProgress].sseTimeout\"], 10000) // Increased timeout to 10 seconds\n            ;\n            eventSource.onopen = ({\n                \"useRealTimeProgress.useCallback[connectToProgress]\": ()=>{\n                    console.log('✅ FRONTEND_DEBUG: SSE connection established');\n                    console.log('✅ FRONTEND_DEBUG: EventSource readyState:', eventSource.readyState);\n                    console.log('✅ FRONTEND_DEBUG: EventSource URL:', eventSource.url);\n                    sseConnected = true;\n                    clearTimeout(sseTimeout);\n                    setState({\n                        \"useRealTimeProgress.useCallback[connectToProgress]\": (prev)=>({\n                                ...prev,\n                                isConnected: true,\n                                error: null,\n                                message: 'Connected to progress stream'\n                            })\n                    }[\"useRealTimeProgress.useCallback[connectToProgress]\"]);\n                }\n            })[\"useRealTimeProgress.useCallback[connectToProgress]\"];\n            eventSource.onmessage = ({\n                \"useRealTimeProgress.useCallback[connectToProgress]\": (event)=>{\n                    try {\n                        console.log('📨 FRONTEND_DEBUG: Raw SSE message received:', event.data);\n                        const data = JSON.parse(event.data);\n                        console.log('📨 FRONTEND_DEBUG: Parsed SSE data:', data);\n                        console.log('📨 FRONTEND_DEBUG: Event type:', data.type);\n                        switch(data.type){\n                            case 'connected':\n                                console.log('✅ FRONTEND_DEBUG: Progress stream connected for session:', data.session_id);\n                                break;\n                            case 'progress_update':\n                                const update = data.data;\n                                console.log('📈 FRONTEND_DEBUG: SSE Progress update:', update);\n                                // Map backend step to frontend display\n                                const stepInfo = getStepInfo(update.step);\n                                console.log('📈 FRONTEND_DEBUG: Step info mapped:', stepInfo);\n                                // 🔧 CRITICAL DEBUG: Log the exact state update\n                                console.log('📈 FRONTEND_DEBUG: About to update state with:', {\n                                    currentStep: update.step,\n                                    progress: stepInfo.progress,\n                                    message: stepInfo.message,\n                                    backendProgress: update.progress,\n                                    frontendProgress: stepInfo.progress\n                                });\n                                // 🔧 CRITICAL FIX: Use progress queue for smooth animations\n                                setState({\n                                    \"useRealTimeProgress.useCallback[connectToProgress]\": (prev)=>{\n                                        // Only queue if this is actually a new step or higher progress\n                                        const isNewStep = update.step !== prev.currentStep;\n                                        const isHigherProgress = stepInfo.progress > prev.progress;\n                                        if (isNewStep || isHigherProgress) {\n                                            console.log('📈 FRONTEND_DEBUG: Queueing progress update:', stepInfo.progress, 'step:', update.step);\n                                            // Add to progress queue for smooth processing\n                                            setProgressQueue({\n                                                \"useRealTimeProgress.useCallback[connectToProgress]\": (queue)=>[\n                                                        ...queue,\n                                                        {\n                                                            step: update.step,\n                                                            progress: stepInfo.progress,\n                                                            message: stepInfo.message\n                                                        }\n                                                    ]\n                                            }[\"useRealTimeProgress.useCallback[connectToProgress]\"]);\n                                            // Update the updates array immediately\n                                            return {\n                                                ...prev,\n                                                updates: [\n                                                    ...prev.updates,\n                                                    update\n                                                ]\n                                            };\n                                        } else {\n                                            console.log('📈 FRONTEND_DEBUG: Skipping duplicate/lower progress update:', stepInfo.progress, 'current:', prev.progress);\n                                            return prev;\n                                        }\n                                    }\n                                }[\"useRealTimeProgress.useCallback[connectToProgress]\"]);\n                                break;\n                            case 'complete':\n                                console.log('🎉 SSE Analysis complete!');\n                                setState({\n                                    \"useRealTimeProgress.useCallback[connectToProgress]\": (prev)=>({\n                                            ...prev,\n                                            isComplete: true,\n                                            progress: 100,\n                                            currentStep: 'complete',\n                                            message: 'Analysis completed successfully!'\n                                        })\n                                }[\"useRealTimeProgress.useCallback[connectToProgress]\"]);\n                                eventSource.close();\n                                break;\n                            case 'ping':\n                                break;\n                            case 'error':\n                                console.error('❌ SSE Progress stream error:', data.error);\n                                setState({\n                                    \"useRealTimeProgress.useCallback[connectToProgress]\": (prev)=>({\n                                            ...prev,\n                                            error: data.error,\n                                            isConnected: false\n                                        })\n                                }[\"useRealTimeProgress.useCallback[connectToProgress]\"]);\n                                eventSource.close();\n                                break;\n                        }\n                    } catch (error) {\n                        console.error('Failed to parse SSE progress update:', error);\n                    }\n                }\n            })[\"useRealTimeProgress.useCallback[connectToProgress]\"];\n            eventSource.onerror = ({\n                \"useRealTimeProgress.useCallback[connectToProgress]\": (error)=>{\n                    console.error('❌ FRONTEND_DEBUG: SSE EventSource error:', error);\n                    console.error('❌ FRONTEND_DEBUG: EventSource readyState:', eventSource.readyState);\n                    console.error('❌ FRONTEND_DEBUG: EventSource URL:', sseUrl);\n                    clearTimeout(sseTimeout);\n                    // Provide more specific error messages based on readyState\n                    let errorMessage = 'SSE connection failed';\n                    if (eventSource.readyState === EventSource.CONNECTING) {\n                        errorMessage = 'Failed to establish SSE connection - check backend status';\n                    } else if (eventSource.readyState === EventSource.CLOSED) {\n                        errorMessage = 'SSE connection was closed unexpectedly';\n                    }\n                    setState({\n                        \"useRealTimeProgress.useCallback[connectToProgress]\": (prev)=>({\n                                ...prev,\n                                isConnected: false,\n                                error: errorMessage\n                            })\n                    }[\"useRealTimeProgress.useCallback[connectToProgress]\"]);\n                    eventSource.close();\n                }\n            })[\"useRealTimeProgress.useCallback[connectToProgress]\"];\n        }\n    }[\"useRealTimeProgress.useCallback[connectToProgress]\"], []);\n    // Helper function to map backend steps to frontend display\n    const getStepInfo = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useRealTimeProgress.useCallback[getStepInfo]\": (step)=>{\n            switch(step){\n                case 'chart_analysis':\n                    return WORKFLOW_STEPS.CHART_ANALYSIS;\n                case 'symbol_detection':\n                    return WORKFLOW_STEPS.SYMBOL_DETECTION;\n                case 'tool_execution':\n                    return WORKFLOW_STEPS.TOOL_EXECUTION;\n                case 'tool_summarization':\n                    return WORKFLOW_STEPS.TOOL_SUMMARIZATION;\n                case 'rag_integration':\n                    return WORKFLOW_STEPS.RAG_INTEGRATION;\n                case 'final_analysis':\n                    return WORKFLOW_STEPS.FINAL_ANALYSIS;\n                case 'memory_storage':\n                    return WORKFLOW_STEPS.MEMORY_STORAGE;\n                case 'complete':\n                    return WORKFLOW_STEPS.COMPLETE;\n                default:\n                    return {\n                        name: 'Processing',\n                        progress: 50,\n                        message: 'Processing...'\n                    };\n            }\n        }\n    }[\"useRealTimeProgress.useCallback[getStepInfo]\"], []);\n    // Polling removed - SSE only implementation\n    const startProgressTracking = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useRealTimeProgress.useCallback[startProgressTracking]\": async (backendSessionId)=>{\n            if (backendSessionId) {\n                // Use backend session ID directly\n                await connectToProgress(backendSessionId);\n                return backendSessionId;\n            } else {\n                // Fallback: generate frontend session ID if backend doesn't provide one\n                const sessionId = \"frontend_\".concat(Date.now(), \"_\").concat(Math.random().toString(36).substring(2, 11));\n                setState({\n                    \"useRealTimeProgress.useCallback[startProgressTracking]\": (prev)=>({\n                            ...prev,\n                            sessionId,\n                            error: null\n                        })\n                }[\"useRealTimeProgress.useCallback[startProgressTracking]\"]);\n                await connectToProgress(sessionId);\n                console.log('📊 Generated fallback progress session:', sessionId);\n                return sessionId;\n            }\n        }\n    }[\"useRealTimeProgress.useCallback[startProgressTracking]\"], [\n        connectToProgress\n    ]);\n    const cleanup = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useRealTimeProgress.useCallback[cleanup]\": async (sessionId)=>{\n            // Close EventSource connection\n            if (eventSourceRef.current) {\n                eventSourceRef.current.close();\n                eventSourceRef.current = null;\n            }\n            // No polling to clear - SSE only\n            console.log('📊 Progress session cleanup completed:', sessionId || state.sessionId);\n            // Reset state\n            setState({\n                sessionId: null,\n                currentStep: 'chart_analysis',\n                progress: 0,\n                message: 'Preparing for analysis...',\n                isConnected: false,\n                isComplete: false,\n                error: null,\n                updates: []\n            });\n        }\n    }[\"useRealTimeProgress.useCallback[cleanup]\"], [\n        state.sessionId\n    ]);\n    // Cleanup on unmount\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useRealTimeProgress.useEffect\": ()=>{\n            return ({\n                \"useRealTimeProgress.useEffect\": ()=>{\n                    if (eventSourceRef.current) {\n                        eventSourceRef.current.close();\n                    }\n                }\n            })[\"useRealTimeProgress.useEffect\"];\n        }\n    }[\"useRealTimeProgress.useEffect\"], []);\n    return {\n        ...state,\n        startProgressTracking,\n        connectToProgress,\n        cleanup,\n        WORKFLOW_STEPS\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9ob29rcy91c2VSZWFsVGltZVByb2dyZXNzLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBQTs7O0NBR0MsR0FFK0Q7QUFxQmhFLDhFQUE4RTtBQUN2RSxNQUFNSSxpQkFBaUI7SUFDNUJDLGdCQUFnQjtRQUFFQyxNQUFNO1FBQWtCQyxVQUFVO1FBQUlDLFNBQVM7SUFBb0Q7SUFDckhDLGtCQUFrQjtRQUFFSCxNQUFNO1FBQW9CQyxVQUFVO1FBQUlDLFNBQVM7SUFBNkI7SUFDbEdFLGdCQUFnQjtRQUFFSixNQUFNO1FBQW1CQyxVQUFVO1FBQUlDLFNBQVM7SUFBb0M7SUFDdEdHLG9CQUFvQjtRQUFFTCxNQUFNO1FBQW1CQyxVQUFVO1FBQUlDLFNBQVM7SUFBd0M7SUFDOUdJLGlCQUFpQjtRQUFFTixNQUFNO1FBQW1CQyxVQUFVO1FBQUlDLFNBQVM7SUFBb0Q7SUFDdkhLLGdCQUFnQjtRQUFFUCxNQUFNO1FBQWtCQyxVQUFVO1FBQUlDLFNBQVM7SUFBMEM7SUFDM0dNLGdCQUFnQjtRQUFFUixNQUFNO1FBQW9CQyxVQUFVO1FBQUlDLFNBQVM7SUFBd0I7SUFDM0ZPLFVBQVU7UUFBRVQsTUFBTTtRQUFZQyxVQUFVO1FBQUtDLFNBQVM7SUFBd0I7QUFDaEYsRUFBQztBQUVNLFNBQVNRO0lBQ2QsTUFBTSxDQUFDQyxPQUFPQyxTQUFTLEdBQUdsQiwrQ0FBUUEsQ0FBZ0I7UUFDaERtQixXQUFXO1FBQ1hDLGFBQWE7UUFDYmIsVUFBVTtRQUNWQyxTQUFTO1FBQ1RhLGFBQWE7UUFDYkMsWUFBWTtRQUNaQyxPQUFPO1FBQ1BDLFNBQVMsRUFBRTtJQUNiO0lBRUEsa0JBQWtCO0lBQ2xCLE1BQU0sQ0FBQ0MsWUFBWUMsY0FBYyxHQUFHMUIsK0NBQVFBLENBQUM7SUFDN0MsTUFBTTJCLGFBQWE7SUFFbkIsd0RBQXdEO0lBQ3hELE1BQU0sQ0FBQ0MsZUFBZUMsaUJBQWlCLEdBQUc3QiwrQ0FBUUEsQ0FBMkQsRUFBRTtJQUMvRyxNQUFNLENBQUM4QixtQkFBbUJDLHFCQUFxQixHQUFHL0IsK0NBQVFBLENBQUM7SUFFM0QsZ0RBQWdEO0lBQ2hEQyxnREFBU0E7eUNBQUM7WUFDUixJQUFJMkIsY0FBY0ksTUFBTSxHQUFHLEtBQUssQ0FBQ0YsbUJBQW1CO2dCQUNsREMscUJBQXFCO2dCQUVyQixNQUFNRTtpRUFBYzt3QkFDbEJKO3lFQUFpQkssQ0FBQUE7Z0NBQ2YsSUFBSUEsTUFBTUYsTUFBTSxLQUFLLEdBQUc7b0NBQ3RCRCxxQkFBcUI7b0NBQ3JCLE9BQU9HO2dDQUNUO2dDQUVBLE1BQU0sQ0FBQ0MsWUFBWSxHQUFHQyxVQUFVLEdBQUdGO2dDQUVuQyxtQkFBbUI7Z0NBQ25CaEI7aUZBQVNtQixDQUFBQSxPQUFTOzRDQUNoQixHQUFHQSxJQUFJOzRDQUNQakIsYUFBYWUsV0FBV0csSUFBSTs0Q0FDNUIvQixVQUFVNEIsV0FBVzVCLFFBQVE7NENBQzdCQyxTQUFTMkIsV0FBVzNCLE9BQU87d0NBQzdCOztnQ0FFQSwyRUFBMkU7Z0NBQzNFLElBQUkrQixRQUFRLElBQUkseUNBQXlDOztnQ0FFekQsSUFBSUgsVUFBVUosTUFBTSxHQUFHLEdBQUc7b0NBQ3hCLE1BQU1RLGVBQWVKLFNBQVMsQ0FBQyxFQUFFLENBQUM3QixRQUFRO29DQUMxQyxNQUFNa0Msa0JBQWtCTixXQUFXNUIsUUFBUTtvQ0FDM0MsTUFBTW1DLGVBQWVGLGVBQWVDO29DQUVwQywrREFBK0Q7b0NBQy9ELElBQUlDLGdCQUFnQixJQUFJO3dDQUN0QkgsUUFBUSxLQUFJLG9DQUFvQztvQ0FDbEQsT0FBTyxJQUFJRyxnQkFBZ0IsSUFBSTt3Q0FDN0JILFFBQVEsS0FBSSxxQ0FBcUM7b0NBQ25ELE9BQU87d0NBQ0xBLFFBQVEsS0FBSSw2QkFBNkI7b0NBQzNDO29DQUVBLGdEQUFnRDtvQ0FDaEQsSUFBSUgsVUFBVUosTUFBTSxHQUFHLEdBQUc7d0NBQ3hCTyxRQUFRSSxLQUFLQyxHQUFHLENBQUMsS0FBS0wsUUFBUSxNQUFLLDRCQUE0QjtvQ0FDakU7b0NBRUFNLFFBQVFDLEdBQUcsQ0FBQyw0REFBNkVOLE9BQTNCQyxpQkFBZ0IsYUFBb0NGLE9BQXpCQyxjQUFhLGNBQXNDSixPQUExQkcsT0FBTSxzQkFBcUMsT0FBakJILFVBQVVKLE1BQU07b0NBRTVKZSxXQUFXZCxhQUFhTTtnQ0FDMUIsT0FBTztvQ0FDTFIscUJBQXFCO2dDQUN2QjtnQ0FFQSxPQUFPSzs0QkFDVDs7b0JBQ0Y7O2dCQUVBSDtZQUNGO1FBQ0Y7d0NBQUc7UUFBQ0w7UUFBZUU7S0FBa0I7SUFFckMsTUFBTWtCLGlCQUFpQjlDLDZDQUFNQSxDQUFxQjtJQUNsRCxNQUFNK0Msb0JBQW9CL0MsNkNBQU1BLENBQWdCO0lBRWhELDRCQUE0QjtJQUM1QixNQUFNZ0Qsa0JBQWtCL0Msa0RBQVdBOzREQUFDLGVBQU9nQjtnQkFBbUJnQywyRUFBa0I7WUFDOUUsSUFBSUEsVUFBVXhCLFlBQVk7Z0JBQ3hCa0IsUUFBUXRCLEtBQUssQ0FBQyxrQ0FBc0VKLE9BQXBDUSxZQUFXLDJCQUFtQyxPQUFWUjtnQkFDcEZEO3dFQUFTbUIsQ0FBQUEsT0FBUzs0QkFDaEIsR0FBR0EsSUFBSTs0QkFDUGQsT0FBTywyQkFBc0MsT0FBWEksWUFBVzt3QkFDL0M7O2dCQUNBO1lBQ0Y7WUFFQWtCLFFBQVFDLEdBQUcsQ0FBQyw4Q0FBK0NuQixPQUFYd0IsU0FBUSxLQUE2QmhDLE9BQTFCUSxZQUFXLGlCQUF5QixPQUFWUjtZQUNyRkQ7b0VBQVNtQixDQUFBQSxPQUFTO3dCQUNoQixHQUFHQSxJQUFJO3dCQUNQZCxPQUFPO3dCQUNQZixTQUFTLDRCQUF1Q21CLE9BQVh3QixTQUFRLEtBQWMsT0FBWHhCLFlBQVc7b0JBQzdEOztZQUVBLDBDQUEwQztZQUMxQyxNQUFNWSxRQUFRSSxLQUFLUyxHQUFHLENBQUMsT0FBT1QsS0FBS1UsR0FBRyxDQUFDLEdBQUdGLFVBQVUsSUFBSTtZQUN4RCxNQUFNLElBQUlHO29FQUFRQyxDQUFBQSxVQUFXUixXQUFXUSxTQUFTaEI7O1lBRWpELHVCQUF1QjtZQUN2QixNQUFNaUIsa0JBQWtCckM7UUFDMUI7MkRBQUc7UUFBQ1E7S0FBVztJQUVmLHdDQUF3QztJQUN4QyxNQUFNOEIsc0JBQXNCdEQsa0RBQVdBO2dFQUFDLE9BQU9nQjtZQUM3QyxJQUFJO2dCQUNGLE1BQU11QyxRQUFRQyxhQUFhQyxPQUFPLENBQUM7Z0JBQ25DLE1BQU1DLFVBQVVDLHVCQUErQixJQUFJLENBQXVCO2dCQUUxRSxNQUFNRyxXQUFXLE1BQU1DLE1BQU0sR0FBMEMvQyxPQUF2QzBDLFNBQVEsaUNBQXlDLE9BQVYxQyxZQUFhO29CQUNsRmdELFNBQVM7d0JBQ1AsaUJBQWlCLFVBQWdCLE9BQU5UO3dCQUMzQixnQkFBZ0I7b0JBQ2xCO2dCQUNGO2dCQUVBLElBQUlPLFNBQVNHLEVBQUUsRUFBRTtvQkFDZixNQUFNQyxPQUFPLE1BQU1KLFNBQVNLLElBQUk7b0JBQ2hDLElBQUlELEtBQUtFLE1BQU0sS0FBSyxhQUFhO3dCQUMvQnJEO29GQUFTbUIsQ0FBQUEsT0FBUztvQ0FDaEIsR0FBR0EsSUFBSTtvQ0FDUGYsWUFBWTtvQ0FDWmYsVUFBVTtvQ0FDVmEsYUFBYTtvQ0FDYlosU0FBUztnQ0FDWDs7d0JBQ0EsT0FBTztvQkFDVDtnQkFDRjtZQUNGLEVBQUUsT0FBT2UsT0FBTztnQkFDZHNCLFFBQVF0QixLQUFLLENBQUMsb0NBQW9DQTtZQUNwRDtZQUNBLE9BQU87UUFDVDsrREFBRyxFQUFFO0lBRUwsTUFBTWlDLG9CQUFvQnJELGtEQUFXQTs4REFBQyxPQUFPZ0I7WUFDM0MsZ0NBQWdDO1lBQ2hDLElBQUk2QixlQUFld0IsT0FBTyxFQUFFO2dCQUMxQnhCLGVBQWV3QixPQUFPLENBQUNDLEtBQUs7WUFDOUI7WUFFQSx5RUFBeUU7WUFDekUsOERBQThEO1lBQzlENUIsUUFBUUMsR0FBRyxDQUFDLHNEQUFzRDNCO1lBQ2xFRDtzRUFBU21CLENBQUFBLE9BQVM7d0JBQ2hCLEdBQUdBLElBQUk7d0JBQ1BsQjt3QkFDQUksT0FBTzt3QkFDUEgsYUFBYTt3QkFDYmIsVUFBVTt3QkFDVkMsU0FBUzt3QkFDVGEsYUFBYTt3QkFDYkMsWUFBWTtvQkFDZDs7WUFFQSw4Q0FBOEM7WUFDOUMsTUFBTUEsYUFBYSxNQUFNbUMsb0JBQW9CdEM7WUFDN0MsSUFBSUcsWUFBWTtnQkFDZDtZQUNGO1lBRUEsTUFBTW9DLFFBQVFDLGFBQWFDLE9BQU8sQ0FBQztZQUNuQyxNQUFNQyxVQUFVQyx1QkFBK0IsSUFBSSxDQUF1QjtZQUUxRSxJQUFJLENBQUNKLE9BQU87Z0JBQ1ZiLFFBQVF0QixLQUFLLENBQUM7Z0JBQ2RMOzBFQUFTbUIsQ0FBQUEsT0FBUzs0QkFDaEIsR0FBR0EsSUFBSTs0QkFDUGQsT0FBTzt3QkFDVDs7Z0JBQ0E7WUFDRjtZQUVBLHlFQUF5RTtZQUN6RSxNQUFNbUQsU0FBUyxHQUFxQ3ZELE9BQWxDMEMsU0FBUSw0QkFBNkNjLE9BQW5CeEQsV0FBVSxXQUFtQyxPQUExQndELG1CQUFtQmpCO1lBQzFGYixRQUFRQyxHQUFHLENBQUM7WUFDWkQsUUFBUUMsR0FBRyxDQUFDLCtCQUErQjRCO1lBQzNDN0IsUUFBUUMsR0FBRyxDQUFDLGtDQUFrQzNCO1lBQzlDMEIsUUFBUUMsR0FBRyxDQUFDLHFDQUFxQyxDQUFDLENBQUNZO1lBQ25EYixRQUFRQyxHQUFHLENBQUMsb0NBQW9DWSxrQkFBQUEsNEJBQUFBLE1BQU8xQixNQUFNO1lBQzdEYSxRQUFRQyxHQUFHLENBQUMsZ0NBQWdDZTtZQUU1QyxnREFBZ0Q7WUFDaEQsSUFBSWU7WUFDSixJQUFJO2dCQUNGQSxjQUFjLElBQUlDLFlBQVlIO2dCQUM5QjdCLFFBQVFDLEdBQUcsQ0FBQztZQUNkLEVBQUUsT0FBT3ZCLE9BQU87Z0JBQ2RzQixRQUFRdEIsS0FBSyxDQUFDLG1EQUFtREE7Z0JBQ2pFTDswRUFBU21CLENBQUFBLE9BQVM7NEJBQ2hCLEdBQUdBLElBQUk7NEJBQ1BkLE9BQU8sb0NBQTBDLE9BQU5BO3dCQUM3Qzs7Z0JBQ0E7WUFDRjtZQUNBeUIsZUFBZXdCLE9BQU8sR0FBR0k7WUFFekIsSUFBSUUsZUFBZTtZQUVuQiwrQ0FBK0M7WUFDL0MsTUFBTUMsYUFBYWhDO2lGQUFXO29CQUM1QixJQUFJLENBQUMrQixjQUFjO3dCQUNqQmpDLFFBQVFDLEdBQUcsQ0FBQzt3QkFDWjhCLFlBQVlILEtBQUs7d0JBQ2pCdkQ7NkZBQVNtQixDQUFBQSxPQUFTO29DQUNoQixHQUFHQSxJQUFJO29DQUNQaEIsYUFBYTtvQ0FDYkUsT0FBTztnQ0FDVDs7b0JBQ0Y7Z0JBQ0Y7Z0ZBQUcsT0FBTyxrQ0FBa0M7O1lBRTVDcUQsWUFBWUksTUFBTTtzRUFBRztvQkFDbkJuQyxRQUFRQyxHQUFHLENBQUM7b0JBQ1pELFFBQVFDLEdBQUcsQ0FBQyw2Q0FBNkM4QixZQUFZSyxVQUFVO29CQUMvRXBDLFFBQVFDLEdBQUcsQ0FBQyxzQ0FBc0M4QixZQUFZTSxHQUFHO29CQUNqRUosZUFBZTtvQkFDZkssYUFBYUo7b0JBQ2I3RDs4RUFBU21CLENBQUFBLE9BQVM7Z0NBQ2hCLEdBQUdBLElBQUk7Z0NBQ1BoQixhQUFhO2dDQUNiRSxPQUFPO2dDQUNQZixTQUFTOzRCQUNYOztnQkFDRjs7WUFFQW9FLFlBQVlRLFNBQVM7c0VBQUcsQ0FBQ0M7b0JBQ3ZCLElBQUk7d0JBQ0Z4QyxRQUFRQyxHQUFHLENBQUMsZ0RBQWdEdUMsTUFBTWhCLElBQUk7d0JBQ3RFLE1BQU1BLE9BQU9pQixLQUFLQyxLQUFLLENBQUNGLE1BQU1oQixJQUFJO3dCQUNsQ3hCLFFBQVFDLEdBQUcsQ0FBQyx1Q0FBdUN1Qjt3QkFDbkR4QixRQUFRQyxHQUFHLENBQUMsa0NBQWtDdUIsS0FBS21CLElBQUk7d0JBRXZELE9BQVFuQixLQUFLbUIsSUFBSTs0QkFDZixLQUFLO2dDQUNIM0MsUUFBUUMsR0FBRyxDQUFDLDREQUE0RHVCLEtBQUtvQixVQUFVO2dDQUN2Rjs0QkFFRixLQUFLO2dDQUNILE1BQU1DLFNBQXlCckIsS0FBS0EsSUFBSTtnQ0FDeEN4QixRQUFRQyxHQUFHLENBQUMsMkNBQTJDNEM7Z0NBRXZELHVDQUF1QztnQ0FDdkMsTUFBTUMsV0FBV0MsWUFBWUYsT0FBT3BELElBQUk7Z0NBQ3hDTyxRQUFRQyxHQUFHLENBQUMsd0NBQXdDNkM7Z0NBRXBELGdEQUFnRDtnQ0FDaEQ5QyxRQUFRQyxHQUFHLENBQUMsa0RBQWtEO29DQUM1RDFCLGFBQWFzRSxPQUFPcEQsSUFBSTtvQ0FDeEIvQixVQUFVb0YsU0FBU3BGLFFBQVE7b0NBQzNCQyxTQUFTbUYsU0FBU25GLE9BQU87b0NBQ3pCcUYsaUJBQWlCSCxPQUFPbkYsUUFBUTtvQ0FDaEN1RixrQkFBa0JILFNBQVNwRixRQUFRO2dDQUNyQztnQ0FFQSw0REFBNEQ7Z0NBQzVEVzswRkFBU21CLENBQUFBO3dDQUNQLCtEQUErRDt3Q0FDL0QsTUFBTTBELFlBQVlMLE9BQU9wRCxJQUFJLEtBQUtELEtBQUtqQixXQUFXO3dDQUNsRCxNQUFNNEUsbUJBQW1CTCxTQUFTcEYsUUFBUSxHQUFHOEIsS0FBSzlCLFFBQVE7d0NBRTFELElBQUl3RixhQUFhQyxrQkFBa0I7NENBQ2pDbkQsUUFBUUMsR0FBRyxDQUFDLGdEQUFnRDZDLFNBQVNwRixRQUFRLEVBQUUsU0FBU21GLE9BQU9wRCxJQUFJOzRDQUVuRyw4Q0FBOEM7NENBQzlDVDtzR0FBaUJLLENBQUFBLFFBQVM7MkRBQUlBO3dEQUFPOzREQUNuQ0ksTUFBTW9ELE9BQU9wRCxJQUFJOzREQUNqQi9CLFVBQVVvRixTQUFTcEYsUUFBUTs0REFDM0JDLFNBQVNtRixTQUFTbkYsT0FBTzt3REFDM0I7cURBQUU7OzRDQUVGLHVDQUF1Qzs0Q0FDdkMsT0FBTztnREFDTCxHQUFHNkIsSUFBSTtnREFDUGIsU0FBUzt1REFBSWEsS0FBS2IsT0FBTztvREFBRWtFO2lEQUFPOzRDQUNwQzt3Q0FDRixPQUFPOzRDQUNMN0MsUUFBUUMsR0FBRyxDQUFDLGdFQUFnRTZDLFNBQVNwRixRQUFRLEVBQUUsWUFBWThCLEtBQUs5QixRQUFROzRDQUN4SCxPQUFPOEI7d0NBQ1Q7b0NBQ0Y7O2dDQUNBOzRCQUVGLEtBQUs7Z0NBQ0hRLFFBQVFDLEdBQUcsQ0FBQztnQ0FDWjVCOzBGQUFTbUIsQ0FBQUEsT0FBUzs0Q0FDaEIsR0FBR0EsSUFBSTs0Q0FDUGYsWUFBWTs0Q0FDWmYsVUFBVTs0Q0FDVmEsYUFBYTs0Q0FDYlosU0FBUzt3Q0FDWDs7Z0NBQ0FvRSxZQUFZSCxLQUFLO2dDQUNqQjs0QkFFRixLQUFLO2dDQUVIOzRCQUVGLEtBQUs7Z0NBQ0g1QixRQUFRdEIsS0FBSyxDQUFDLGdDQUFnQzhDLEtBQUs5QyxLQUFLO2dDQUN4REw7MEZBQVNtQixDQUFBQSxPQUFTOzRDQUNoQixHQUFHQSxJQUFJOzRDQUNQZCxPQUFPOEMsS0FBSzlDLEtBQUs7NENBQ2pCRixhQUFhO3dDQUNmOztnQ0FDQXVELFlBQVlILEtBQUs7Z0NBRWpCO3dCQUNKO29CQUNGLEVBQUUsT0FBT2xELE9BQU87d0JBQ2RzQixRQUFRdEIsS0FBSyxDQUFDLHdDQUF3Q0E7b0JBQ3hEO2dCQUNGOztZQUVBcUQsWUFBWXFCLE9BQU87c0VBQUcsQ0FBQzFFO29CQUNyQnNCLFFBQVF0QixLQUFLLENBQUMsNENBQTRDQTtvQkFDMURzQixRQUFRdEIsS0FBSyxDQUFDLDZDQUE2Q3FELFlBQVlLLFVBQVU7b0JBQ2pGcEMsUUFBUXRCLEtBQUssQ0FBQyxzQ0FBc0NtRDtvQkFDcERTLGFBQWFKO29CQUViLDJEQUEyRDtvQkFDM0QsSUFBSW1CLGVBQWU7b0JBQ25CLElBQUl0QixZQUFZSyxVQUFVLEtBQUtKLFlBQVlzQixVQUFVLEVBQUU7d0JBQ3JERCxlQUFlO29CQUNqQixPQUFPLElBQUl0QixZQUFZSyxVQUFVLEtBQUtKLFlBQVl1QixNQUFNLEVBQUU7d0JBQ3hERixlQUFlO29CQUNqQjtvQkFFQWhGOzhFQUFTbUIsQ0FBQUEsT0FBUztnQ0FDaEIsR0FBR0EsSUFBSTtnQ0FDUGhCLGFBQWE7Z0NBQ2JFLE9BQU8yRTs0QkFDVDs7b0JBQ0F0QixZQUFZSCxLQUFLO2dCQUNuQjs7UUFDRjs2REFBRyxFQUFFO0lBRUwsMkRBQTJEO0lBQzNELE1BQU1tQixjQUFjekYsa0RBQVdBO3dEQUFDLENBQUNtQztZQUMvQixPQUFRQTtnQkFDTixLQUFLO29CQUNILE9BQU9sQyxlQUFlQyxjQUFjO2dCQUN0QyxLQUFLO29CQUNILE9BQU9ELGVBQWVLLGdCQUFnQjtnQkFDeEMsS0FBSztvQkFDSCxPQUFPTCxlQUFlTSxjQUFjO2dCQUN0QyxLQUFLO29CQUNILE9BQU9OLGVBQWVPLGtCQUFrQjtnQkFDMUMsS0FBSztvQkFDSCxPQUFPUCxlQUFlUSxlQUFlO2dCQUN2QyxLQUFLO29CQUNILE9BQU9SLGVBQWVTLGNBQWM7Z0JBQ3RDLEtBQUs7b0JBQ0gsT0FBT1QsZUFBZVUsY0FBYztnQkFDdEMsS0FBSztvQkFDSCxPQUFPVixlQUFlVyxRQUFRO2dCQUNoQztvQkFDRSxPQUFPO3dCQUFFVCxNQUFNO3dCQUFjQyxVQUFVO3dCQUFJQyxTQUFTO29CQUFnQjtZQUN4RTtRQUNGO3VEQUFHLEVBQUU7SUFFTCw0Q0FBNEM7SUFFNUMsTUFBTTZGLHdCQUF3QmxHLGtEQUFXQTtrRUFBQyxPQUFPbUc7WUFDL0MsSUFBSUEsa0JBQWtCO2dCQUNwQixrQ0FBa0M7Z0JBQ2xDLE1BQU05QyxrQkFBa0I4QztnQkFDeEIsT0FBT0E7WUFDVCxPQUFPO2dCQUNMLHdFQUF3RTtnQkFDeEUsTUFBTW5GLFlBQVksWUFBMEJ3QixPQUFkNEQsS0FBS0MsR0FBRyxJQUFHLEtBQStDLE9BQTVDN0QsS0FBSzhELE1BQU0sR0FBR0MsUUFBUSxDQUFDLElBQUlDLFNBQVMsQ0FBQyxHQUFHO2dCQUNwRnpGOzhFQUFTbUIsQ0FBQUEsT0FBUzs0QkFDaEIsR0FBR0EsSUFBSTs0QkFDUGxCOzRCQUNBSSxPQUFPO3dCQUNUOztnQkFDQSxNQUFNaUMsa0JBQWtCckM7Z0JBQ3hCMEIsUUFBUUMsR0FBRyxDQUFDLDJDQUEyQzNCO2dCQUN2RCxPQUFPQTtZQUNUO1FBQ0Y7aUVBQUc7UUFBQ3FDO0tBQWtCO0lBRXRCLE1BQU1vRCxVQUFVekcsa0RBQVdBO29EQUFDLE9BQU9nQjtZQUNqQywrQkFBK0I7WUFDL0IsSUFBSTZCLGVBQWV3QixPQUFPLEVBQUU7Z0JBQzFCeEIsZUFBZXdCLE9BQU8sQ0FBQ0MsS0FBSztnQkFDNUJ6QixlQUFld0IsT0FBTyxHQUFHO1lBQzNCO1lBRUEsaUNBQWlDO1lBRWpDM0IsUUFBUUMsR0FBRyxDQUFDLDBDQUEwQzNCLGFBQWFGLE1BQU1FLFNBQVM7WUFFbEYsY0FBYztZQUNkRCxTQUFTO2dCQUNQQyxXQUFXO2dCQUNYQyxhQUFhO2dCQUNiYixVQUFVO2dCQUNWQyxTQUFTO2dCQUNUYSxhQUFhO2dCQUNiQyxZQUFZO2dCQUNaQyxPQUFPO2dCQUNQQyxTQUFTLEVBQUU7WUFDYjtRQUNGO21EQUFHO1FBQUNQLE1BQU1FLFNBQVM7S0FBQztJQUVwQixxQkFBcUI7SUFDckJsQixnREFBU0E7eUNBQUM7WUFDUjtpREFBTztvQkFDTCxJQUFJK0MsZUFBZXdCLE9BQU8sRUFBRTt3QkFDMUJ4QixlQUFld0IsT0FBTyxDQUFDQyxLQUFLO29CQUM5QjtnQkFDRjs7UUFDRjt3Q0FBRyxFQUFFO0lBRUwsT0FBTztRQUNMLEdBQUd4RCxLQUFLO1FBQ1JvRjtRQUNBN0M7UUFDQW9EO1FBQ0F4RztJQUNGO0FBQ0YiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcbmlzaGFcXERlc2t0b3BcXEFnZW50X1RyYWRpbmdcXEFnZW50X1RyYWRpbmdcXG5leHQtZnJvbnRlbmRcXHNyY1xcaG9va3NcXHVzZVJlYWxUaW1lUHJvZ3Jlc3MudHMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXHJcbiAqIFJlYWwtdGltZSBwcm9ncmVzcyB0cmFja2luZyBob29rIGZvciBMYW5nR3JhcGggQUkgYW5hbHlzaXMgd29ya2Zsb3dcclxuICogVXNlcyBTZXJ2ZXItU2VudCBFdmVudHMgT05MWSBmb3IgbGl2ZSB1cGRhdGVzIChubyBwb2xsaW5nIGZhbGxiYWNrKVxyXG4gKi9cclxuXHJcbmltcG9ydCB7IHVzZVN0YXRlLCB1c2VFZmZlY3QsIHVzZVJlZiwgdXNlQ2FsbGJhY2sgfSBmcm9tICdyZWFjdCdcclxuXHJcbmV4cG9ydCBpbnRlcmZhY2UgUHJvZ3Jlc3NVcGRhdGUge1xyXG4gIHN0ZXA6IHN0cmluZ1xyXG4gIHByb2dyZXNzOiBudW1iZXJcclxuICBtZXNzYWdlOiBzdHJpbmdcclxuICB0aW1lc3RhbXA6IHN0cmluZ1xyXG4gIGRldGFpbHM/OiBSZWNvcmQ8c3RyaW5nLCBhbnk+XHJcbn1cclxuXHJcbmV4cG9ydCBpbnRlcmZhY2UgUHJvZ3Jlc3NTdGF0ZSB7XHJcbiAgc2Vzc2lvbklkOiBzdHJpbmcgfCBudWxsXHJcbiAgY3VycmVudFN0ZXA6IHN0cmluZ1xyXG4gIHByb2dyZXNzOiBudW1iZXJcclxuICBtZXNzYWdlOiBzdHJpbmdcclxuICBpc0Nvbm5lY3RlZDogYm9vbGVhblxyXG4gIGlzQ29tcGxldGU6IGJvb2xlYW5cclxuICBlcnJvcjogc3RyaW5nIHwgbnVsbFxyXG4gIHVwZGF0ZXM6IFByb2dyZXNzVXBkYXRlW11cclxufVxyXG5cclxuLy8g4pyFIEFMSUdORUQ6IEZyb250ZW5kIG1hcHBpbmcgbm93IG1hdGNoZXMgYWN0dWFsIExhbmdHcmFwaCB3b3JrZmxvdyBleGVjdXRpb25cclxuZXhwb3J0IGNvbnN0IFdPUktGTE9XX1NURVBTID0ge1xyXG4gIENIQVJUX0FOQUxZU0lTOiB7IG5hbWU6ICdDaGFydCBBbmFseXNpcycsIHByb2dyZXNzOiAyMCwgbWVzc2FnZTogJ/Cfk4ogU3RhcnRpbmcgY29tcHJlaGVuc2l2ZSBBSSBhbmFseXNpcyB3b3JrZmxvdy4uLicgfSxcclxuICBTWU1CT0xfREVURUNUSU9OOiB7IG5hbWU6ICdTeW1ib2wgRGV0ZWN0aW9uJywgcHJvZ3Jlc3M6IDQwLCBtZXNzYWdlOiAn4pyFIFN5bWJvbCBkZXRlY3RlZCBpbiBjaGFydCcgfSxcclxuICBUT09MX0VYRUNVVElPTjogeyBuYW1lOiAnRGF0YSBDb2xsZWN0aW9uJywgcHJvZ3Jlc3M6IDUwLCBtZXNzYWdlOiAn8J+boO+4jyBDb2xsZWN0aW5nIGRhdGEgZnJvbSB0b29scy4uLicgfSxcclxuICBUT09MX1NVTU1BUklaQVRJT046IHsgbmFtZTogJ0RhdGEgUHJvY2Vzc2luZycsIHByb2dyZXNzOiA2MCwgbWVzc2FnZTogJ/Cfk4sgUHJvY2Vzc2luZyBhbmQgc3VtbWFyaXppbmcgZGF0YS4uLicgfSxcclxuICBSQUdfSU5URUdSQVRJT046IHsgbmFtZTogJ1JBRyBJbnRlZ3JhdGlvbicsIHByb2dyZXNzOiA3MCwgbWVzc2FnZTogJ/Cfp6AgSW50ZWdyYXRpbmcgaGlzdG9yaWNhbCBjb250ZXh0IGFuZCBwYXR0ZXJucy4uLicgfSxcclxuICBGSU5BTF9BTkFMWVNJUzogeyBuYW1lOiAnRmluYWwgQW5hbHlzaXMnLCBwcm9ncmVzczogODAsIG1lc3NhZ2U6ICfwn46vIEdlbmVyYXRpbmcgZmluYWwgdHJhZGluZyBhbmFseXNpcy4uLicgfSxcclxuICBNRU1PUllfU1RPUkFHRTogeyBuYW1lOiAnRGF0YWJhc2UgU3RvcmFnZScsIHByb2dyZXNzOiA5MCwgbWVzc2FnZTogJ/Cfkr4gU2F2aW5nIGFuYWx5c2lzLi4uJyB9LFxyXG4gIENPTVBMRVRFOiB7IG5hbWU6ICdDb21wbGV0ZScsIHByb2dyZXNzOiAxMDAsIG1lc3NhZ2U6ICfwn46JIEFuYWx5c2lzIGNvbXBsZXRlIScgfVxyXG59XHJcblxyXG5leHBvcnQgZnVuY3Rpb24gdXNlUmVhbFRpbWVQcm9ncmVzcygpIHtcclxuICBjb25zdCBbc3RhdGUsIHNldFN0YXRlXSA9IHVzZVN0YXRlPFByb2dyZXNzU3RhdGU+KHtcclxuICAgIHNlc3Npb25JZDogbnVsbCxcclxuICAgIGN1cnJlbnRTdGVwOiAnY2hhcnRfYW5hbHlzaXMnLFxyXG4gICAgcHJvZ3Jlc3M6IDAsXHJcbiAgICBtZXNzYWdlOiAnUHJlcGFyaW5nIGZvciBhbmFseXNpcy4uLicsXHJcbiAgICBpc0Nvbm5lY3RlZDogZmFsc2UsXHJcbiAgICBpc0NvbXBsZXRlOiBmYWxzZSxcclxuICAgIGVycm9yOiBudWxsLFxyXG4gICAgdXBkYXRlczogW11cclxuICB9KVxyXG5cclxuICAvLyBBZGQgcmV0cnkgc3RhdGVcclxuICBjb25zdCBbcmV0cnlDb3VudCwgc2V0UmV0cnlDb3VudF0gPSB1c2VTdGF0ZSgwKVxyXG4gIGNvbnN0IG1heFJldHJpZXMgPSAzXHJcblxyXG4gIC8vIPCflKcgQ1JJVElDQUwgRklYOiBQcm9ncmVzcyBzbW9vdGhpbmcgZm9yIHJhcGlkIHVwZGF0ZXNcclxuICBjb25zdCBbcHJvZ3Jlc3NRdWV1ZSwgc2V0UHJvZ3Jlc3NRdWV1ZV0gPSB1c2VTdGF0ZTxBcnJheTx7c3RlcDogc3RyaW5nLCBwcm9ncmVzczogbnVtYmVyLCBtZXNzYWdlOiBzdHJpbmd9Pj4oW10pXHJcbiAgY29uc3QgW2lzUHJvY2Vzc2luZ1F1ZXVlLCBzZXRJc1Byb2Nlc3NpbmdRdWV1ZV0gPSB1c2VTdGF0ZShmYWxzZSlcclxuXHJcbiAgLy8gUHJvY2VzcyBwcm9ncmVzcyBxdWV1ZSB3aXRoIHNtb290aCBhbmltYXRpb25zXHJcbiAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgIGlmIChwcm9ncmVzc1F1ZXVlLmxlbmd0aCA+IDAgJiYgIWlzUHJvY2Vzc2luZ1F1ZXVlKSB7XHJcbiAgICAgIHNldElzUHJvY2Vzc2luZ1F1ZXVlKHRydWUpXHJcblxyXG4gICAgICBjb25zdCBwcm9jZXNzTmV4dCA9ICgpID0+IHtcclxuICAgICAgICBzZXRQcm9ncmVzc1F1ZXVlKHF1ZXVlID0+IHtcclxuICAgICAgICAgIGlmIChxdWV1ZS5sZW5ndGggPT09IDApIHtcclxuICAgICAgICAgICAgc2V0SXNQcm9jZXNzaW5nUXVldWUoZmFsc2UpXHJcbiAgICAgICAgICAgIHJldHVybiBxdWV1ZVxyXG4gICAgICAgICAgfVxyXG5cclxuICAgICAgICAgIGNvbnN0IFtuZXh0VXBkYXRlLCAuLi5yZW1haW5pbmddID0gcXVldWVcclxuXHJcbiAgICAgICAgICAvLyBBcHBseSB0aGUgdXBkYXRlXHJcbiAgICAgICAgICBzZXRTdGF0ZShwcmV2ID0+ICh7XHJcbiAgICAgICAgICAgIC4uLnByZXYsXHJcbiAgICAgICAgICAgIGN1cnJlbnRTdGVwOiBuZXh0VXBkYXRlLnN0ZXAsXHJcbiAgICAgICAgICAgIHByb2dyZXNzOiBuZXh0VXBkYXRlLnByb2dyZXNzLFxyXG4gICAgICAgICAgICBtZXNzYWdlOiBuZXh0VXBkYXRlLm1lc3NhZ2VcclxuICAgICAgICAgIH0pKVxyXG5cclxuICAgICAgICAgIC8vIPCflKcgSU1QUk9WRUQ6IER5bmFtaWMgZGVsYXkgYmFzZWQgb24gcXVldWUgbGVuZ3RoIGFuZCBwcm9ncmVzcyBkaWZmZXJlbmNlXHJcbiAgICAgICAgICBsZXQgZGVsYXkgPSA0MDAgLy8gQmFzZSBkZWxheSByZWR1Y2VkIGZyb20gODAwbXMgdG8gNDAwbXNcclxuXHJcbiAgICAgICAgICBpZiAocmVtYWluaW5nLmxlbmd0aCA+IDApIHtcclxuICAgICAgICAgICAgY29uc3QgbmV4dFByb2dyZXNzID0gcmVtYWluaW5nWzBdLnByb2dyZXNzXHJcbiAgICAgICAgICAgIGNvbnN0IGN1cnJlbnRQcm9ncmVzcyA9IG5leHRVcGRhdGUucHJvZ3Jlc3NcclxuICAgICAgICAgICAgY29uc3QgcHJvZ3Jlc3NEaWZmID0gbmV4dFByb2dyZXNzIC0gY3VycmVudFByb2dyZXNzXHJcblxyXG4gICAgICAgICAgICAvLyBTaG9ydGVyIGRlbGF5IGZvciBzbWFsbCBwcm9ncmVzcyBqdW1wcywgbG9uZ2VyIGZvciBiaWcganVtcHNcclxuICAgICAgICAgICAgaWYgKHByb2dyZXNzRGlmZiA8PSAxMCkge1xyXG4gICAgICAgICAgICAgIGRlbGF5ID0gMzAwIC8vIEZhc3QgdXBkYXRlcyBmb3Igc21hbGwgaW5jcmVtZW50c1xyXG4gICAgICAgICAgICB9IGVsc2UgaWYgKHByb2dyZXNzRGlmZiA8PSAyMCkge1xyXG4gICAgICAgICAgICAgIGRlbGF5ID0gNTAwIC8vIE1lZGl1bSBkZWxheSBmb3IgbWVkaXVtIGluY3JlbWVudHNcclxuICAgICAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgICBkZWxheSA9IDcwMCAvLyBMb25nZXIgZGVsYXkgZm9yIGJpZyBqdW1wc1xyXG4gICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICAvLyBJZiBxdWV1ZSBpcyBnZXR0aW5nIGxvbmcsIHNwZWVkIHVwIHByb2Nlc3NpbmdcclxuICAgICAgICAgICAgaWYgKHJlbWFpbmluZy5sZW5ndGggPiAzKSB7XHJcbiAgICAgICAgICAgICAgZGVsYXkgPSBNYXRoLm1heCgyMDAsIGRlbGF5ICogMC42KSAvLyBTcGVlZCB1cCBidXQgbm90IHRvbyBmYXN0XHJcbiAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKGDwn5OIIEZST05URU5EX0RFQlVHOiBQcm9jZXNzaW5nIHF1ZXVlIC0gY3VycmVudDogJHtjdXJyZW50UHJvZ3Jlc3N9JSwgbmV4dDogJHtuZXh0UHJvZ3Jlc3N9JSwgZGVsYXk6ICR7ZGVsYXl9bXMsIHF1ZXVlIGxlbmd0aDogJHtyZW1haW5pbmcubGVuZ3RofWApXHJcblxyXG4gICAgICAgICAgICBzZXRUaW1lb3V0KHByb2Nlc3NOZXh0LCBkZWxheSlcclxuICAgICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAgIHNldElzUHJvY2Vzc2luZ1F1ZXVlKGZhbHNlKVxyXG4gICAgICAgICAgfVxyXG5cclxuICAgICAgICAgIHJldHVybiByZW1haW5pbmdcclxuICAgICAgICB9KVxyXG4gICAgICB9XHJcblxyXG4gICAgICBwcm9jZXNzTmV4dCgpXHJcbiAgICB9XHJcbiAgfSwgW3Byb2dyZXNzUXVldWUsIGlzUHJvY2Vzc2luZ1F1ZXVlXSlcclxuXHJcbiAgY29uc3QgZXZlbnRTb3VyY2VSZWYgPSB1c2VSZWY8RXZlbnRTb3VyY2UgfCBudWxsPihudWxsKVxyXG4gIGNvbnN0IGN1cnJlbnRTZXNzaW9uUmVmID0gdXNlUmVmPHN0cmluZyB8IG51bGw+KG51bGwpXHJcblxyXG4gIC8vIFJldHJ5IGNvbm5lY3Rpb24gZnVuY3Rpb25cclxuICBjb25zdCByZXRyeUNvbm5lY3Rpb24gPSB1c2VDYWxsYmFjayhhc3luYyAoc2Vzc2lvbklkOiBzdHJpbmcsIGF0dGVtcHQ6IG51bWJlciA9IDEpID0+IHtcclxuICAgIGlmIChhdHRlbXB0ID4gbWF4UmV0cmllcykge1xyXG4gICAgICBjb25zb2xlLmVycm9yKGDinYwgRlJPTlRFTkRfREVCVUc6IE1heCByZXRyaWVzICgke21heFJldHJpZXN9KSBleGNlZWRlZCBmb3Igc2Vzc2lvbiAke3Nlc3Npb25JZH1gKVxyXG4gICAgICBzZXRTdGF0ZShwcmV2ID0+ICh7XHJcbiAgICAgICAgLi4ucHJldixcclxuICAgICAgICBlcnJvcjogYENvbm5lY3Rpb24gZmFpbGVkIGFmdGVyICR7bWF4UmV0cmllc30gYXR0ZW1wdHMuIFBsZWFzZSByZWZyZXNoIGFuZCB0cnkgYWdhaW4uYFxyXG4gICAgICB9KSlcclxuICAgICAgcmV0dXJuXHJcbiAgICB9XHJcblxyXG4gICAgY29uc29sZS5sb2coYPCflIQgRlJPTlRFTkRfREVCVUc6IFJldHJ5IGF0dGVtcHQgJHthdHRlbXB0fS8ke21heFJldHJpZXN9IGZvciBzZXNzaW9uICR7c2Vzc2lvbklkfWApXHJcbiAgICBzZXRTdGF0ZShwcmV2ID0+ICh7XHJcbiAgICAgIC4uLnByZXYsXHJcbiAgICAgIGVycm9yOiBudWxsLFxyXG4gICAgICBtZXNzYWdlOiBgUmVjb25uZWN0aW5nLi4uIChhdHRlbXB0ICR7YXR0ZW1wdH0vJHttYXhSZXRyaWVzfSlgXHJcbiAgICB9KSlcclxuXHJcbiAgICAvLyBXYWl0IGJlZm9yZSByZXRyeSAoZXhwb25lbnRpYWwgYmFja29mZilcclxuICAgIGNvbnN0IGRlbGF5ID0gTWF0aC5taW4oMTAwMCAqIE1hdGgucG93KDIsIGF0dGVtcHQgLSAxKSwgNTAwMClcclxuICAgIGF3YWl0IG5ldyBQcm9taXNlKHJlc29sdmUgPT4gc2V0VGltZW91dChyZXNvbHZlLCBkZWxheSkpXHJcblxyXG4gICAgLy8gUmV0cnkgdGhlIGNvbm5lY3Rpb25cclxuICAgIGF3YWl0IGNvbm5lY3RUb1Byb2dyZXNzKHNlc3Npb25JZClcclxuICB9LCBbbWF4UmV0cmllc10pXHJcblxyXG4gIC8vIENoZWNrIGlmIGFuYWx5c2lzIGlzIGFscmVhZHkgY29tcGxldGVcclxuICBjb25zdCBjaGVja0FuYWx5c2lzU3RhdHVzID0gdXNlQ2FsbGJhY2soYXN5bmMgKHNlc3Npb25JZDogc3RyaW5nKSA9PiB7XHJcbiAgICB0cnkge1xyXG4gICAgICBjb25zdCB0b2tlbiA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCdhY2Nlc3NfdG9rZW4nKVxyXG4gICAgICBjb25zdCBiYXNlVVJMID0gcHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfQVBJX1VSTCB8fCAnaHR0cDovL2xvY2FsaG9zdDo4MDAwJ1xyXG5cclxuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaChgJHtiYXNlVVJMfS9hcGkvdjEvYXN5bmMtdHJhZGluZy9zdGF0dXMvJHtzZXNzaW9uSWR9YCwge1xyXG4gICAgICAgIGhlYWRlcnM6IHtcclxuICAgICAgICAgICdBdXRob3JpemF0aW9uJzogYEJlYXJlciAke3Rva2VufWAsXHJcbiAgICAgICAgICAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nXHJcbiAgICAgICAgfVxyXG4gICAgICB9KVxyXG5cclxuICAgICAgaWYgKHJlc3BvbnNlLm9rKSB7XHJcbiAgICAgICAgY29uc3QgZGF0YSA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKVxyXG4gICAgICAgIGlmIChkYXRhLnN0YXR1cyA9PT0gJ2NvbXBsZXRlZCcpIHtcclxuICAgICAgICAgIHNldFN0YXRlKHByZXYgPT4gKHtcclxuICAgICAgICAgICAgLi4ucHJldixcclxuICAgICAgICAgICAgaXNDb21wbGV0ZTogdHJ1ZSxcclxuICAgICAgICAgICAgcHJvZ3Jlc3M6IDEwMCxcclxuICAgICAgICAgICAgY3VycmVudFN0ZXA6ICdjb21wbGV0ZScsXHJcbiAgICAgICAgICAgIG1lc3NhZ2U6ICdBbmFseXNpcyBjb21wbGV0ZWQgc3VjY2Vzc2Z1bGx5ISdcclxuICAgICAgICAgIH0pKVxyXG4gICAgICAgICAgcmV0dXJuIHRydWVcclxuICAgICAgICB9XHJcbiAgICAgIH1cclxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0ZhaWxlZCB0byBjaGVjayBhbmFseXNpcyBzdGF0dXM6JywgZXJyb3IpXHJcbiAgICB9XHJcbiAgICByZXR1cm4gZmFsc2VcclxuICB9LCBbXSlcclxuXHJcbiAgY29uc3QgY29ubmVjdFRvUHJvZ3Jlc3MgPSB1c2VDYWxsYmFjayhhc3luYyAoc2Vzc2lvbklkOiBzdHJpbmcpID0+IHtcclxuICAgIC8vIENsb3NlIGV4aXN0aW5nIFNTRSBjb25uZWN0aW9uXHJcbiAgICBpZiAoZXZlbnRTb3VyY2VSZWYuY3VycmVudCkge1xyXG4gICAgICBldmVudFNvdXJjZVJlZi5jdXJyZW50LmNsb3NlKClcclxuICAgIH1cclxuXHJcbiAgICAvLyDwn5SnIENSSVRJQ0FMIEZJWDogVXBkYXRlIHN0YXRlIHdpdGggdGhlIHByb3ZpZGVkIHNlc3Npb24gSUQgSU1NRURJQVRFTFlcclxuICAgIC8vIFRoaXMgZW5zdXJlcyB0aGUgVUkgc2hvd3MgdGhlIGNvcnJlY3Qgc2Vzc2lvbiBJRCByaWdodCBhd2F5XHJcbiAgICBjb25zb2xlLmxvZygn8J+UpyBGUk9OVEVORF9ERUJVRzogU2V0dGluZyBzZXNzaW9uIElEIGltbWVkaWF0ZWx5OicsIHNlc3Npb25JZClcclxuICAgIHNldFN0YXRlKHByZXYgPT4gKHtcclxuICAgICAgLi4ucHJldixcclxuICAgICAgc2Vzc2lvbklkLFxyXG4gICAgICBlcnJvcjogbnVsbCxcclxuICAgICAgY3VycmVudFN0ZXA6ICdjaGFydF9hbmFseXNpcycsXHJcbiAgICAgIHByb2dyZXNzOiAwLFxyXG4gICAgICBtZXNzYWdlOiAnQ29ubmVjdGluZyB0byBwcm9ncmVzcyBzdHJlYW0uLi4nLFxyXG4gICAgICBpc0Nvbm5lY3RlZDogZmFsc2UsXHJcbiAgICAgIGlzQ29tcGxldGU6IGZhbHNlXHJcbiAgICB9KSlcclxuXHJcbiAgICAvLyBGaXJzdCBjaGVjayBpZiBhbmFseXNpcyBpcyBhbHJlYWR5IGNvbXBsZXRlXHJcbiAgICBjb25zdCBpc0NvbXBsZXRlID0gYXdhaXQgY2hlY2tBbmFseXNpc1N0YXR1cyhzZXNzaW9uSWQpXHJcbiAgICBpZiAoaXNDb21wbGV0ZSkge1xyXG4gICAgICByZXR1cm5cclxuICAgIH1cclxuXHJcbiAgICBjb25zdCB0b2tlbiA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCdhY2Nlc3NfdG9rZW4nKVxyXG4gICAgY29uc3QgYmFzZVVSTCA9IHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX0FQSV9VUkwgfHwgJ2h0dHA6Ly9sb2NhbGhvc3Q6ODAwMCdcclxuXHJcbiAgICBpZiAoIXRva2VuKSB7XHJcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCBObyBhY2Nlc3MgdG9rZW4gZm91bmQgZm9yIFNTRSBjb25uZWN0aW9uJylcclxuICAgICAgc2V0U3RhdGUocHJldiA9PiAoe1xyXG4gICAgICAgIC4uLnByZXYsXHJcbiAgICAgICAgZXJyb3I6ICdBdXRoZW50aWNhdGlvbiByZXF1aXJlZCBmb3IgcHJvZ3Jlc3MgdHJhY2tpbmcnXHJcbiAgICAgIH0pKVxyXG4gICAgICByZXR1cm5cclxuICAgIH1cclxuXHJcbiAgICAvLyBUcnkgU1NFIGZpcnN0IC0gRml4ZWQgVVJMIGNvbnN0cnVjdGlvbiBhbmQgYWRkZWQgYmV0dGVyIGVycm9yIGhhbmRsaW5nXHJcbiAgICBjb25zdCBzc2VVcmwgPSBgJHtiYXNlVVJMfS9hcGkvdjEvcHJvZ3Jlc3Mvc3RyZWFtLyR7c2Vzc2lvbklkfT90b2tlbj0ke2VuY29kZVVSSUNvbXBvbmVudCh0b2tlbil9YFxyXG4gICAgY29uc29sZS5sb2coJ/CflJcgRlJPTlRFTkRfREVCVUc6IEF0dGVtcHRpbmcgU1NFIGNvbm5lY3Rpb24nKVxyXG4gICAgY29uc29sZS5sb2coJ/CflJcgRlJPTlRFTkRfREVCVUc6IFNTRSBVUkw6Jywgc3NlVXJsKVxyXG4gICAgY29uc29sZS5sb2coJ/CflJcgRlJPTlRFTkRfREVCVUc6IFNlc3Npb24gSUQ6Jywgc2Vzc2lvbklkKVxyXG4gICAgY29uc29sZS5sb2coJ/CflJcgRlJPTlRFTkRfREVCVUc6IFRva2VuIHByZXNlbnQ6JywgISF0b2tlbilcclxuICAgIGNvbnNvbGUubG9nKCfwn5SXIEZST05URU5EX0RFQlVHOiBUb2tlbiBsZW5ndGg6JywgdG9rZW4/Lmxlbmd0aClcclxuICAgIGNvbnNvbGUubG9nKCfwn5SXIEZST05URU5EX0RFQlVHOiBCYXNlIFVSTDonLCBiYXNlVVJMKVxyXG5cclxuICAgIC8vIENyZWF0ZSBFdmVudFNvdXJjZSB3aXRoIHByb3BlciBlcnJvciBoYW5kbGluZ1xyXG4gICAgbGV0IGV2ZW50U291cmNlOiBFdmVudFNvdXJjZVxyXG4gICAgdHJ5IHtcclxuICAgICAgZXZlbnRTb3VyY2UgPSBuZXcgRXZlbnRTb3VyY2Uoc3NlVXJsKVxyXG4gICAgICBjb25zb2xlLmxvZygn4pyFIEZST05URU5EX0RFQlVHOiBFdmVudFNvdXJjZSBjcmVhdGVkIHN1Y2Nlc3NmdWxseScpXHJcbiAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICBjb25zb2xlLmVycm9yKCfinYwgRlJPTlRFTkRfREVCVUc6IEZhaWxlZCB0byBjcmVhdGUgRXZlbnRTb3VyY2U6JywgZXJyb3IpXHJcbiAgICAgIHNldFN0YXRlKHByZXYgPT4gKHtcclxuICAgICAgICAuLi5wcmV2LFxyXG4gICAgICAgIGVycm9yOiBgRmFpbGVkIHRvIGNyZWF0ZSBTU0UgY29ubmVjdGlvbjogJHtlcnJvcn1gXHJcbiAgICAgIH0pKVxyXG4gICAgICByZXR1cm5cclxuICAgIH1cclxuICAgIGV2ZW50U291cmNlUmVmLmN1cnJlbnQgPSBldmVudFNvdXJjZVxyXG5cclxuICAgIGxldCBzc2VDb25uZWN0ZWQgPSBmYWxzZVxyXG5cclxuICAgIC8vIFNTRSBjb25uZWN0aW9uIHRpbWVvdXQgKG5vIHBvbGxpbmcgZmFsbGJhY2spXHJcbiAgICBjb25zdCBzc2VUaW1lb3V0ID0gc2V0VGltZW91dCgoKSA9PiB7XHJcbiAgICAgIGlmICghc3NlQ29ubmVjdGVkKSB7XHJcbiAgICAgICAgY29uc29sZS5sb2coJ+KaoO+4jyBTU0UgY29ubmVjdGlvbiB0aW1lb3V0IC0gbm8gcG9sbGluZyBmYWxsYmFjaycpXHJcbiAgICAgICAgZXZlbnRTb3VyY2UuY2xvc2UoKVxyXG4gICAgICAgIHNldFN0YXRlKHByZXYgPT4gKHtcclxuICAgICAgICAgIC4uLnByZXYsXHJcbiAgICAgICAgICBpc0Nvbm5lY3RlZDogZmFsc2UsXHJcbiAgICAgICAgICBlcnJvcjogJ1NTRSBjb25uZWN0aW9uIHRpbWVvdXQgLSBjaGVjayBiYWNrZW5kIGNvbm5lY3Rpb24nXHJcbiAgICAgICAgfSkpXHJcbiAgICAgIH1cclxuICAgIH0sIDEwMDAwKSAvLyBJbmNyZWFzZWQgdGltZW91dCB0byAxMCBzZWNvbmRzXHJcblxyXG4gICAgZXZlbnRTb3VyY2Uub25vcGVuID0gKCkgPT4ge1xyXG4gICAgICBjb25zb2xlLmxvZygn4pyFIEZST05URU5EX0RFQlVHOiBTU0UgY29ubmVjdGlvbiBlc3RhYmxpc2hlZCcpXHJcbiAgICAgIGNvbnNvbGUubG9nKCfinIUgRlJPTlRFTkRfREVCVUc6IEV2ZW50U291cmNlIHJlYWR5U3RhdGU6JywgZXZlbnRTb3VyY2UucmVhZHlTdGF0ZSlcclxuICAgICAgY29uc29sZS5sb2coJ+KchSBGUk9OVEVORF9ERUJVRzogRXZlbnRTb3VyY2UgVVJMOicsIGV2ZW50U291cmNlLnVybClcclxuICAgICAgc3NlQ29ubmVjdGVkID0gdHJ1ZVxyXG4gICAgICBjbGVhclRpbWVvdXQoc3NlVGltZW91dClcclxuICAgICAgc2V0U3RhdGUocHJldiA9PiAoe1xyXG4gICAgICAgIC4uLnByZXYsXHJcbiAgICAgICAgaXNDb25uZWN0ZWQ6IHRydWUsXHJcbiAgICAgICAgZXJyb3I6IG51bGwsXHJcbiAgICAgICAgbWVzc2FnZTogJ0Nvbm5lY3RlZCB0byBwcm9ncmVzcyBzdHJlYW0nXHJcbiAgICAgIH0pKVxyXG4gICAgfVxyXG5cclxuICAgIGV2ZW50U291cmNlLm9ubWVzc2FnZSA9IChldmVudCkgPT4ge1xyXG4gICAgICB0cnkge1xyXG4gICAgICAgIGNvbnNvbGUubG9nKCfwn5OoIEZST05URU5EX0RFQlVHOiBSYXcgU1NFIG1lc3NhZ2UgcmVjZWl2ZWQ6JywgZXZlbnQuZGF0YSlcclxuICAgICAgICBjb25zdCBkYXRhID0gSlNPTi5wYXJzZShldmVudC5kYXRhKVxyXG4gICAgICAgIGNvbnNvbGUubG9nKCfwn5OoIEZST05URU5EX0RFQlVHOiBQYXJzZWQgU1NFIGRhdGE6JywgZGF0YSlcclxuICAgICAgICBjb25zb2xlLmxvZygn8J+TqCBGUk9OVEVORF9ERUJVRzogRXZlbnQgdHlwZTonLCBkYXRhLnR5cGUpXHJcblxyXG4gICAgICAgIHN3aXRjaCAoZGF0YS50eXBlKSB7XHJcbiAgICAgICAgICBjYXNlICdjb25uZWN0ZWQnOlxyXG4gICAgICAgICAgICBjb25zb2xlLmxvZygn4pyFIEZST05URU5EX0RFQlVHOiBQcm9ncmVzcyBzdHJlYW0gY29ubmVjdGVkIGZvciBzZXNzaW9uOicsIGRhdGEuc2Vzc2lvbl9pZClcclxuICAgICAgICAgICAgYnJlYWtcclxuXHJcbiAgICAgICAgICBjYXNlICdwcm9ncmVzc191cGRhdGUnOlxyXG4gICAgICAgICAgICBjb25zdCB1cGRhdGU6IFByb2dyZXNzVXBkYXRlID0gZGF0YS5kYXRhXHJcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKCfwn5OIIEZST05URU5EX0RFQlVHOiBTU0UgUHJvZ3Jlc3MgdXBkYXRlOicsIHVwZGF0ZSlcclxuXHJcbiAgICAgICAgICAgIC8vIE1hcCBiYWNrZW5kIHN0ZXAgdG8gZnJvbnRlbmQgZGlzcGxheVxyXG4gICAgICAgICAgICBjb25zdCBzdGVwSW5mbyA9IGdldFN0ZXBJbmZvKHVwZGF0ZS5zdGVwKVxyXG4gICAgICAgICAgICBjb25zb2xlLmxvZygn8J+TiCBGUk9OVEVORF9ERUJVRzogU3RlcCBpbmZvIG1hcHBlZDonLCBzdGVwSW5mbylcclxuXHJcbiAgICAgICAgICAgIC8vIPCflKcgQ1JJVElDQUwgREVCVUc6IExvZyB0aGUgZXhhY3Qgc3RhdGUgdXBkYXRlXHJcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKCfwn5OIIEZST05URU5EX0RFQlVHOiBBYm91dCB0byB1cGRhdGUgc3RhdGUgd2l0aDonLCB7XHJcbiAgICAgICAgICAgICAgY3VycmVudFN0ZXA6IHVwZGF0ZS5zdGVwLFxyXG4gICAgICAgICAgICAgIHByb2dyZXNzOiBzdGVwSW5mby5wcm9ncmVzcyxcclxuICAgICAgICAgICAgICBtZXNzYWdlOiBzdGVwSW5mby5tZXNzYWdlLFxyXG4gICAgICAgICAgICAgIGJhY2tlbmRQcm9ncmVzczogdXBkYXRlLnByb2dyZXNzLFxyXG4gICAgICAgICAgICAgIGZyb250ZW5kUHJvZ3Jlc3M6IHN0ZXBJbmZvLnByb2dyZXNzXHJcbiAgICAgICAgICAgIH0pXHJcblxyXG4gICAgICAgICAgICAvLyDwn5SnIENSSVRJQ0FMIEZJWDogVXNlIHByb2dyZXNzIHF1ZXVlIGZvciBzbW9vdGggYW5pbWF0aW9uc1xyXG4gICAgICAgICAgICBzZXRTdGF0ZShwcmV2ID0+IHtcclxuICAgICAgICAgICAgICAvLyBPbmx5IHF1ZXVlIGlmIHRoaXMgaXMgYWN0dWFsbHkgYSBuZXcgc3RlcCBvciBoaWdoZXIgcHJvZ3Jlc3NcclxuICAgICAgICAgICAgICBjb25zdCBpc05ld1N0ZXAgPSB1cGRhdGUuc3RlcCAhPT0gcHJldi5jdXJyZW50U3RlcFxyXG4gICAgICAgICAgICAgIGNvbnN0IGlzSGlnaGVyUHJvZ3Jlc3MgPSBzdGVwSW5mby5wcm9ncmVzcyA+IHByZXYucHJvZ3Jlc3NcclxuXHJcbiAgICAgICAgICAgICAgaWYgKGlzTmV3U3RlcCB8fCBpc0hpZ2hlclByb2dyZXNzKSB7XHJcbiAgICAgICAgICAgICAgICBjb25zb2xlLmxvZygn8J+TiCBGUk9OVEVORF9ERUJVRzogUXVldWVpbmcgcHJvZ3Jlc3MgdXBkYXRlOicsIHN0ZXBJbmZvLnByb2dyZXNzLCAnc3RlcDonLCB1cGRhdGUuc3RlcClcclxuXHJcbiAgICAgICAgICAgICAgICAvLyBBZGQgdG8gcHJvZ3Jlc3MgcXVldWUgZm9yIHNtb290aCBwcm9jZXNzaW5nXHJcbiAgICAgICAgICAgICAgICBzZXRQcm9ncmVzc1F1ZXVlKHF1ZXVlID0+IFsuLi5xdWV1ZSwge1xyXG4gICAgICAgICAgICAgICAgICBzdGVwOiB1cGRhdGUuc3RlcCxcclxuICAgICAgICAgICAgICAgICAgcHJvZ3Jlc3M6IHN0ZXBJbmZvLnByb2dyZXNzLFxyXG4gICAgICAgICAgICAgICAgICBtZXNzYWdlOiBzdGVwSW5mby5tZXNzYWdlXHJcbiAgICAgICAgICAgICAgICB9XSlcclxuXHJcbiAgICAgICAgICAgICAgICAvLyBVcGRhdGUgdGhlIHVwZGF0ZXMgYXJyYXkgaW1tZWRpYXRlbHlcclxuICAgICAgICAgICAgICAgIHJldHVybiB7XHJcbiAgICAgICAgICAgICAgICAgIC4uLnByZXYsXHJcbiAgICAgICAgICAgICAgICAgIHVwZGF0ZXM6IFsuLi5wcmV2LnVwZGF0ZXMsIHVwZGF0ZV1cclxuICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICAgICAgY29uc29sZS5sb2coJ/Cfk4ggRlJPTlRFTkRfREVCVUc6IFNraXBwaW5nIGR1cGxpY2F0ZS9sb3dlciBwcm9ncmVzcyB1cGRhdGU6Jywgc3RlcEluZm8ucHJvZ3Jlc3MsICdjdXJyZW50OicsIHByZXYucHJvZ3Jlc3MpXHJcbiAgICAgICAgICAgICAgICByZXR1cm4gcHJldlxyXG4gICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgfSlcclxuICAgICAgICAgICAgYnJlYWtcclxuXHJcbiAgICAgICAgICBjYXNlICdjb21wbGV0ZSc6XHJcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKCfwn46JIFNTRSBBbmFseXNpcyBjb21wbGV0ZSEnKVxyXG4gICAgICAgICAgICBzZXRTdGF0ZShwcmV2ID0+ICh7XHJcbiAgICAgICAgICAgICAgLi4ucHJldixcclxuICAgICAgICAgICAgICBpc0NvbXBsZXRlOiB0cnVlLFxyXG4gICAgICAgICAgICAgIHByb2dyZXNzOiAxMDAsXHJcbiAgICAgICAgICAgICAgY3VycmVudFN0ZXA6ICdjb21wbGV0ZScsXHJcbiAgICAgICAgICAgICAgbWVzc2FnZTogJ0FuYWx5c2lzIGNvbXBsZXRlZCBzdWNjZXNzZnVsbHkhJ1xyXG4gICAgICAgICAgICB9KSlcclxuICAgICAgICAgICAgZXZlbnRTb3VyY2UuY2xvc2UoKVxyXG4gICAgICAgICAgICBicmVha1xyXG5cclxuICAgICAgICAgIGNhc2UgJ3BpbmcnOlxyXG4gICAgICAgICAgICAvLyBLZWVwYWxpdmUgLSBkbyBub3RoaW5nXHJcbiAgICAgICAgICAgIGJyZWFrXHJcblxyXG4gICAgICAgICAgY2FzZSAnZXJyb3InOlxyXG4gICAgICAgICAgICBjb25zb2xlLmVycm9yKCfinYwgU1NFIFByb2dyZXNzIHN0cmVhbSBlcnJvcjonLCBkYXRhLmVycm9yKVxyXG4gICAgICAgICAgICBzZXRTdGF0ZShwcmV2ID0+ICh7XHJcbiAgICAgICAgICAgICAgLi4ucHJldixcclxuICAgICAgICAgICAgICBlcnJvcjogZGF0YS5lcnJvcixcclxuICAgICAgICAgICAgICBpc0Nvbm5lY3RlZDogZmFsc2VcclxuICAgICAgICAgICAgfSkpXHJcbiAgICAgICAgICAgIGV2ZW50U291cmNlLmNsb3NlKClcclxuICAgICAgICAgICAgLy8gTm8gcG9sbGluZyBmYWxsYmFjayAtIFNTRSBvbmx5XHJcbiAgICAgICAgICAgIGJyZWFrXHJcbiAgICAgICAgfVxyXG4gICAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ0ZhaWxlZCB0byBwYXJzZSBTU0UgcHJvZ3Jlc3MgdXBkYXRlOicsIGVycm9yKVxyXG4gICAgICB9XHJcbiAgICB9XHJcblxyXG4gICAgZXZlbnRTb3VyY2Uub25lcnJvciA9IChlcnJvcikgPT4ge1xyXG4gICAgICBjb25zb2xlLmVycm9yKCfinYwgRlJPTlRFTkRfREVCVUc6IFNTRSBFdmVudFNvdXJjZSBlcnJvcjonLCBlcnJvcilcclxuICAgICAgY29uc29sZS5lcnJvcign4p2MIEZST05URU5EX0RFQlVHOiBFdmVudFNvdXJjZSByZWFkeVN0YXRlOicsIGV2ZW50U291cmNlLnJlYWR5U3RhdGUpXHJcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCBGUk9OVEVORF9ERUJVRzogRXZlbnRTb3VyY2UgVVJMOicsIHNzZVVybClcclxuICAgICAgY2xlYXJUaW1lb3V0KHNzZVRpbWVvdXQpXHJcblxyXG4gICAgICAvLyBQcm92aWRlIG1vcmUgc3BlY2lmaWMgZXJyb3IgbWVzc2FnZXMgYmFzZWQgb24gcmVhZHlTdGF0ZVxyXG4gICAgICBsZXQgZXJyb3JNZXNzYWdlID0gJ1NTRSBjb25uZWN0aW9uIGZhaWxlZCdcclxuICAgICAgaWYgKGV2ZW50U291cmNlLnJlYWR5U3RhdGUgPT09IEV2ZW50U291cmNlLkNPTk5FQ1RJTkcpIHtcclxuICAgICAgICBlcnJvck1lc3NhZ2UgPSAnRmFpbGVkIHRvIGVzdGFibGlzaCBTU0UgY29ubmVjdGlvbiAtIGNoZWNrIGJhY2tlbmQgc3RhdHVzJ1xyXG4gICAgICB9IGVsc2UgaWYgKGV2ZW50U291cmNlLnJlYWR5U3RhdGUgPT09IEV2ZW50U291cmNlLkNMT1NFRCkge1xyXG4gICAgICAgIGVycm9yTWVzc2FnZSA9ICdTU0UgY29ubmVjdGlvbiB3YXMgY2xvc2VkIHVuZXhwZWN0ZWRseSdcclxuICAgICAgfVxyXG5cclxuICAgICAgc2V0U3RhdGUocHJldiA9PiAoe1xyXG4gICAgICAgIC4uLnByZXYsXHJcbiAgICAgICAgaXNDb25uZWN0ZWQ6IGZhbHNlLFxyXG4gICAgICAgIGVycm9yOiBlcnJvck1lc3NhZ2VcclxuICAgICAgfSkpXHJcbiAgICAgIGV2ZW50U291cmNlLmNsb3NlKClcclxuICAgIH1cclxuICB9LCBbXSlcclxuXHJcbiAgLy8gSGVscGVyIGZ1bmN0aW9uIHRvIG1hcCBiYWNrZW5kIHN0ZXBzIHRvIGZyb250ZW5kIGRpc3BsYXlcclxuICBjb25zdCBnZXRTdGVwSW5mbyA9IHVzZUNhbGxiYWNrKChzdGVwOiBzdHJpbmcpID0+IHtcclxuICAgIHN3aXRjaCAoc3RlcCkge1xyXG4gICAgICBjYXNlICdjaGFydF9hbmFseXNpcyc6XHJcbiAgICAgICAgcmV0dXJuIFdPUktGTE9XX1NURVBTLkNIQVJUX0FOQUxZU0lTXHJcbiAgICAgIGNhc2UgJ3N5bWJvbF9kZXRlY3Rpb24nOlxyXG4gICAgICAgIHJldHVybiBXT1JLRkxPV19TVEVQUy5TWU1CT0xfREVURUNUSU9OXHJcbiAgICAgIGNhc2UgJ3Rvb2xfZXhlY3V0aW9uJzpcclxuICAgICAgICByZXR1cm4gV09SS0ZMT1dfU1RFUFMuVE9PTF9FWEVDVVRJT05cclxuICAgICAgY2FzZSAndG9vbF9zdW1tYXJpemF0aW9uJzpcclxuICAgICAgICByZXR1cm4gV09SS0ZMT1dfU1RFUFMuVE9PTF9TVU1NQVJJWkFUSU9OXHJcbiAgICAgIGNhc2UgJ3JhZ19pbnRlZ3JhdGlvbic6XHJcbiAgICAgICAgcmV0dXJuIFdPUktGTE9XX1NURVBTLlJBR19JTlRFR1JBVElPTlxyXG4gICAgICBjYXNlICdmaW5hbF9hbmFseXNpcyc6XHJcbiAgICAgICAgcmV0dXJuIFdPUktGTE9XX1NURVBTLkZJTkFMX0FOQUxZU0lTXHJcbiAgICAgIGNhc2UgJ21lbW9yeV9zdG9yYWdlJzpcclxuICAgICAgICByZXR1cm4gV09SS0ZMT1dfU1RFUFMuTUVNT1JZX1NUT1JBR0VcclxuICAgICAgY2FzZSAnY29tcGxldGUnOlxyXG4gICAgICAgIHJldHVybiBXT1JLRkxPV19TVEVQUy5DT01QTEVURVxyXG4gICAgICBkZWZhdWx0OlxyXG4gICAgICAgIHJldHVybiB7IG5hbWU6ICdQcm9jZXNzaW5nJywgcHJvZ3Jlc3M6IDUwLCBtZXNzYWdlOiAnUHJvY2Vzc2luZy4uLicgfVxyXG4gICAgfVxyXG4gIH0sIFtdKVxyXG5cclxuICAvLyBQb2xsaW5nIHJlbW92ZWQgLSBTU0Ugb25seSBpbXBsZW1lbnRhdGlvblxyXG5cclxuICBjb25zdCBzdGFydFByb2dyZXNzVHJhY2tpbmcgPSB1c2VDYWxsYmFjayhhc3luYyAoYmFja2VuZFNlc3Npb25JZD86IHN0cmluZykgPT4ge1xyXG4gICAgaWYgKGJhY2tlbmRTZXNzaW9uSWQpIHtcclxuICAgICAgLy8gVXNlIGJhY2tlbmQgc2Vzc2lvbiBJRCBkaXJlY3RseVxyXG4gICAgICBhd2FpdCBjb25uZWN0VG9Qcm9ncmVzcyhiYWNrZW5kU2Vzc2lvbklkKVxyXG4gICAgICByZXR1cm4gYmFja2VuZFNlc3Npb25JZFxyXG4gICAgfSBlbHNlIHtcclxuICAgICAgLy8gRmFsbGJhY2s6IGdlbmVyYXRlIGZyb250ZW5kIHNlc3Npb24gSUQgaWYgYmFja2VuZCBkb2Vzbid0IHByb3ZpZGUgb25lXHJcbiAgICAgIGNvbnN0IHNlc3Npb25JZCA9IGBmcm9udGVuZF8ke0RhdGUubm93KCl9XyR7TWF0aC5yYW5kb20oKS50b1N0cmluZygzNikuc3Vic3RyaW5nKDIsIDExKX1gXHJcbiAgICAgIHNldFN0YXRlKHByZXYgPT4gKHtcclxuICAgICAgICAuLi5wcmV2LFxyXG4gICAgICAgIHNlc3Npb25JZCxcclxuICAgICAgICBlcnJvcjogbnVsbFxyXG4gICAgICB9KSlcclxuICAgICAgYXdhaXQgY29ubmVjdFRvUHJvZ3Jlc3Moc2Vzc2lvbklkKVxyXG4gICAgICBjb25zb2xlLmxvZygn8J+TiiBHZW5lcmF0ZWQgZmFsbGJhY2sgcHJvZ3Jlc3Mgc2Vzc2lvbjonLCBzZXNzaW9uSWQpXHJcbiAgICAgIHJldHVybiBzZXNzaW9uSWRcclxuICAgIH1cclxuICB9LCBbY29ubmVjdFRvUHJvZ3Jlc3NdKVxyXG5cclxuICBjb25zdCBjbGVhbnVwID0gdXNlQ2FsbGJhY2soYXN5bmMgKHNlc3Npb25JZD86IHN0cmluZykgPT4ge1xyXG4gICAgLy8gQ2xvc2UgRXZlbnRTb3VyY2UgY29ubmVjdGlvblxyXG4gICAgaWYgKGV2ZW50U291cmNlUmVmLmN1cnJlbnQpIHtcclxuICAgICAgZXZlbnRTb3VyY2VSZWYuY3VycmVudC5jbG9zZSgpXHJcbiAgICAgIGV2ZW50U291cmNlUmVmLmN1cnJlbnQgPSBudWxsXHJcbiAgICB9XHJcblxyXG4gICAgLy8gTm8gcG9sbGluZyB0byBjbGVhciAtIFNTRSBvbmx5XHJcblxyXG4gICAgY29uc29sZS5sb2coJ/Cfk4ogUHJvZ3Jlc3Mgc2Vzc2lvbiBjbGVhbnVwIGNvbXBsZXRlZDonLCBzZXNzaW9uSWQgfHwgc3RhdGUuc2Vzc2lvbklkKVxyXG5cclxuICAgIC8vIFJlc2V0IHN0YXRlXHJcbiAgICBzZXRTdGF0ZSh7XHJcbiAgICAgIHNlc3Npb25JZDogbnVsbCxcclxuICAgICAgY3VycmVudFN0ZXA6ICdjaGFydF9hbmFseXNpcycsXHJcbiAgICAgIHByb2dyZXNzOiAwLFxyXG4gICAgICBtZXNzYWdlOiAnUHJlcGFyaW5nIGZvciBhbmFseXNpcy4uLicsXHJcbiAgICAgIGlzQ29ubmVjdGVkOiBmYWxzZSxcclxuICAgICAgaXNDb21wbGV0ZTogZmFsc2UsXHJcbiAgICAgIGVycm9yOiBudWxsLFxyXG4gICAgICB1cGRhdGVzOiBbXVxyXG4gICAgfSlcclxuICB9LCBbc3RhdGUuc2Vzc2lvbklkXSlcclxuXHJcbiAgLy8gQ2xlYW51cCBvbiB1bm1vdW50XHJcbiAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgIHJldHVybiAoKSA9PiB7XHJcbiAgICAgIGlmIChldmVudFNvdXJjZVJlZi5jdXJyZW50KSB7XHJcbiAgICAgICAgZXZlbnRTb3VyY2VSZWYuY3VycmVudC5jbG9zZSgpXHJcbiAgICAgIH1cclxuICAgIH1cclxuICB9LCBbXSlcclxuXHJcbiAgcmV0dXJuIHtcclxuICAgIC4uLnN0YXRlLFxyXG4gICAgc3RhcnRQcm9ncmVzc1RyYWNraW5nLFxyXG4gICAgY29ubmVjdFRvUHJvZ3Jlc3MsXHJcbiAgICBjbGVhbnVwLFxyXG4gICAgV09SS0ZMT1dfU1RFUFNcclxuICB9XHJcbn1cclxuIl0sIm5hbWVzIjpbInVzZVN0YXRlIiwidXNlRWZmZWN0IiwidXNlUmVmIiwidXNlQ2FsbGJhY2siLCJXT1JLRkxPV19TVEVQUyIsIkNIQVJUX0FOQUxZU0lTIiwibmFtZSIsInByb2dyZXNzIiwibWVzc2FnZSIsIlNZTUJPTF9ERVRFQ1RJT04iLCJUT09MX0VYRUNVVElPTiIsIlRPT0xfU1VNTUFSSVpBVElPTiIsIlJBR19JTlRFR1JBVElPTiIsIkZJTkFMX0FOQUxZU0lTIiwiTUVNT1JZX1NUT1JBR0UiLCJDT01QTEVURSIsInVzZVJlYWxUaW1lUHJvZ3Jlc3MiLCJzdGF0ZSIsInNldFN0YXRlIiwic2Vzc2lvbklkIiwiY3VycmVudFN0ZXAiLCJpc0Nvbm5lY3RlZCIsImlzQ29tcGxldGUiLCJlcnJvciIsInVwZGF0ZXMiLCJyZXRyeUNvdW50Iiwic2V0UmV0cnlDb3VudCIsIm1heFJldHJpZXMiLCJwcm9ncmVzc1F1ZXVlIiwic2V0UHJvZ3Jlc3NRdWV1ZSIsImlzUHJvY2Vzc2luZ1F1ZXVlIiwic2V0SXNQcm9jZXNzaW5nUXVldWUiLCJsZW5ndGgiLCJwcm9jZXNzTmV4dCIsInF1ZXVlIiwibmV4dFVwZGF0ZSIsInJlbWFpbmluZyIsInByZXYiLCJzdGVwIiwiZGVsYXkiLCJuZXh0UHJvZ3Jlc3MiLCJjdXJyZW50UHJvZ3Jlc3MiLCJwcm9ncmVzc0RpZmYiLCJNYXRoIiwibWF4IiwiY29uc29sZSIsImxvZyIsInNldFRpbWVvdXQiLCJldmVudFNvdXJjZVJlZiIsImN1cnJlbnRTZXNzaW9uUmVmIiwicmV0cnlDb25uZWN0aW9uIiwiYXR0ZW1wdCIsIm1pbiIsInBvdyIsIlByb21pc2UiLCJyZXNvbHZlIiwiY29ubmVjdFRvUHJvZ3Jlc3MiLCJjaGVja0FuYWx5c2lzU3RhdHVzIiwidG9rZW4iLCJsb2NhbFN0b3JhZ2UiLCJnZXRJdGVtIiwiYmFzZVVSTCIsInByb2Nlc3MiLCJlbnYiLCJORVhUX1BVQkxJQ19BUElfVVJMIiwicmVzcG9uc2UiLCJmZXRjaCIsImhlYWRlcnMiLCJvayIsImRhdGEiLCJqc29uIiwic3RhdHVzIiwiY3VycmVudCIsImNsb3NlIiwic3NlVXJsIiwiZW5jb2RlVVJJQ29tcG9uZW50IiwiZXZlbnRTb3VyY2UiLCJFdmVudFNvdXJjZSIsInNzZUNvbm5lY3RlZCIsInNzZVRpbWVvdXQiLCJvbm9wZW4iLCJyZWFkeVN0YXRlIiwidXJsIiwiY2xlYXJUaW1lb3V0Iiwib25tZXNzYWdlIiwiZXZlbnQiLCJKU09OIiwicGFyc2UiLCJ0eXBlIiwic2Vzc2lvbl9pZCIsInVwZGF0ZSIsInN0ZXBJbmZvIiwiZ2V0U3RlcEluZm8iLCJiYWNrZW5kUHJvZ3Jlc3MiLCJmcm9udGVuZFByb2dyZXNzIiwiaXNOZXdTdGVwIiwiaXNIaWdoZXJQcm9ncmVzcyIsIm9uZXJyb3IiLCJlcnJvck1lc3NhZ2UiLCJDT05ORUNUSU5HIiwiQ0xPU0VEIiwic3RhcnRQcm9ncmVzc1RyYWNraW5nIiwiYmFja2VuZFNlc3Npb25JZCIsIkRhdGUiLCJub3ciLCJyYW5kb20iLCJ0b1N0cmluZyIsInN1YnN0cmluZyIsImNsZWFudXAiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useRealTimeProgress.ts\n"));

/***/ })

});